# Recurring Events Configuration

The VIP Calendar Bot now supports modular recurring events that can be easily configured without modifying code.

## Configuration File

Recurring events are configured in `config/recurring-events.json`. This file allows you to define events that happen on a regular schedule.

## Event Structure

Each recurring event has the following structure:

```json
{
  "id": "unique-event-id",
  "name": "Event Name",
  "recurrence": {
    "frequency": "weekly" | "monthly",
    "dayOfWeek": 0-6,  // 0 = Sunday, 6 = Saturday
    "weekOfMonth": 1-4 | -1  // For monthly events: 1-4 for specific week, -1 for last week
  },
  "eventDetails": {
    "type": "isha-satsang" | "group-sit" | "service" | "3-hour" | "1-day",
    "location": "Location Name",
    "address": "Full Address",
    "startTime": "HH:MM AM/PM",
    "endTime": "HH:MM AM/PM",
    "description": "Event Description"
  }
}
```

## Examples

### Monthly Event (Second Saturday)
```json
{
  "id": "isha-satsang",
  "name": "<PERSON><PERSON>ang",
  "recurrence": {
    "frequency": "monthly",
    "weekOfMonth": 2,
    "dayOfWeek": 6
  },
  "eventDetails": {
    "type": "isha-satsang",
    "location": "South Bay Vipassana Hall",
    "address": "3080 Olcott St #D232, Santa Clara, CA",
    "startTime": "7:00 PM",
    "endTime": "9:00 PM",
    "description": "Isha Satsang - Monthly spiritual gathering"
  }
}
```

### Weekly Event (Every Wednesday)
```json
{
  "id": "weekly-meditation",
  "name": "Weekly Wednesday Meditation",
  "recurrence": {
    "frequency": "weekly",
    "dayOfWeek": 3
  },
  "eventDetails": {
    "type": "group-sit",
    "location": "Laguna Beach",
    "address": "789 Beach Rd, Laguna Beach, CA",
    "startTime": "7:00 PM",
    "endTime": "8:00 PM",
    "description": "Weekly Wednesday Evening Meditation"
  }
}
```

### Last Friday of Month
```json
{
  "id": "last-friday-gathering",
  "name": "Last Friday Gathering",
  "recurrence": {
    "frequency": "monthly",
    "weekOfMonth": -1,
    "dayOfWeek": 5
  },
  "eventDetails": {
    "type": "group-sit",
    "location": "North Irvine",
    "address": "456 Meditation Way, Irvine, CA",
    "startTime": "6:00 PM",
    "endTime": "7:30 PM",
    "description": "Last Friday of Month Gathering"
  }
}
```

## Adding New Recurring Events

1. Edit `config/recurring-events.json`
2. Add your new event to the `recurringEvents` array
3. Restart the application or wait for the next scheduled run
4. The event will automatically appear in weekend reports when applicable

## Event Types

- `isha-satsang`: Isha Satsang gatherings
- `group-sit`: Group meditation sessions
- `service`: Service periods
- `3-hour`: Three-hour meditation sits
- `1-day`: One-day meditation courses

## Locations

Supported locations include:
- `South Bay Vipassana Hall`
- `North Irvine`
- `Laguna Beach`
- `East Bay Vipassana Hall`
- `Dhamma Mahavana (North Fork, CA)`
- `Center Land`

## Notes

- Recurring events are automatically merged with calendar events
- If the config file is not found, the system defaults to the standard Isha Satsang configuration
- Events only appear in weekend reports (Saturday/Sunday)
- Weekly events that fall on weekdays won't appear in weekend reports