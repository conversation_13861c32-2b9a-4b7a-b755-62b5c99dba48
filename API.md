# VIP API Documentation

## Overview

While VIP is primarily a CLI application, it exposes a comprehensive API that can be used programmatically.

## Core APIs

### CalendarParser

Parses calendar text and extracts event information.

```typescript
import {CalendarParser} from './src/parser/CalendarParser';

const parser = new CalendarParser();

// Parse calendar text
const events = parser.parseCalendar(calendarText);

// Get events for specific date
const dateEvents = parser.getEventsForDate(events, new Date('2025-07-12'));

// Get location details
const locationInfo = parser.getLocationDetails(Location.SOUTH_BAY);
```

#### Methods

##### `parseCalendar(calendarText: string): Event[]`

Parses raw calendar text and returns structured events.

**Parameters:**

- `calendarText`: Raw text from calendar document

**Returns:** Array of Event objects

**Example:**

```typescript
const text = `SOUTH BAY VIPASSANA HALL
Saturday July 12 (9:00 AM - 12:00 PM) - 3 HOUR SIT`;

const events = parser.parseCalendar(text);
// Returns: [{ date, type, location, startTime, endTime, address }]
```

##### `getEventsForDate(events: Event[], targetDate: Date): Event[]`

Filters events for a specific date.

**Parameters:**

- `events`: Array of parsed events
- `targetDate`: Date to filter by

**Returns:** Events occurring on target date

### WeekendEventService

Main service for querying weekend events.

```typescript
import {WeekendEventService} from './src/services/WeekendEventService';

const service = new WeekendEventService();

// Get weekend events
const weekendEvents = await service.getWeekendEvents(referenceDate);

// Get formatted report
const report = await service.formatWeekendReport(referenceDate);
```

#### Methods

##### `getWeekendEvents(referenceDate?: Date): Promise<WeekendEvents>`

Gets all events for the weekend containing the reference date.

**Parameters:**

- `referenceDate`: Optional date (defaults to today)

**Returns:**

```typescript
{
    saturday: Event[],
        sunday
:
    Event[]
}
```

##### `formatWeekendReport(referenceDate?: Date): Promise<string>`

Generates a formatted WhatsApp-ready weekend report.

**Parameters:**

- `referenceDate`: Optional date (defaults to today)

**Returns:** Formatted string with weekend events

### WhatsAppService

Handles WhatsApp messaging with retry logic.

```typescript
import {WhatsAppService} from './src/services/WhatsAppService';

const whatsapp = new WhatsAppService();

// Send message
const success = await whatsapp.sendWhatsAppMessage('Hello!');

// Send weekend events
const sent = await whatsapp.sendWeekendEventsMessage(weekendReport);

// Send error notification
await whatsapp.sendErrorNotification('Something went wrong');

// Health check
const health = await whatsapp.performHealthCheck();
```

#### Methods

##### `sendWhatsAppMessage(message: string): Promise<boolean>`

Sends a WhatsApp message with automatic retry.

**Parameters:**

- `message`: Message content

**Returns:** Success boolean

**Features:**

- 3 retry attempts with exponential backoff
- Health check before sending
- Automatic error logging

##### `performHealthCheck(): Promise<HealthCheckResult>`

Checks if WhatsApp service is operational.

**Returns:**

```typescript
{
    healthy: boolean,
        message
:
    string
}
```

### LocationService

IP-based location detection with caching.

```typescript
import {LocationService} from './src/services/LocationService';

const location = new LocationService();

// Check if in California
const inCA = await location.isInCalifornia();

// Get detailed location
const info = await location.getLocationInfo();
```

#### Methods

##### `isInCalifornia(): Promise<boolean>`

Checks if current IP is in California.

**Returns:** Boolean indicating CA location

**Features:**

- 24-hour cache
- Fallback on error
- VPN detection

##### `getLocationInfo(): Promise<LocationInfo | null>`

Gets detailed location information.

**Returns:**

```typescript
{
    ip: string,
        city
:
    string,
        region
:
    string,
        region_code
:
    string,
        country
:
    string,
        timezone
:
    string
}
```

### ModularEventSystem

Plugin-based event generation system.

```typescript
import {ModularEventSystem} from './src/core/ModularEventSystem';

// Create with default plugins
const system = ModularEventSystem.createDefault();

// Or create with custom config
const customSystem = new ModularEventSystem();
await customSystem.loadConfiguration('./custom-config.json');

// Get events
const events = system.getEventsForDate(new Date('2025-07-12'));
```

#### Plugin Interface

```typescript
interface EventPlugin {
    id: string;
    name: string;

    shouldGenerateEvents(date: Date): boolean;

    generateEvents(date: Date): Event[];

    configure?(config: any): void;
}
```

#### Built-in Plugins

##### NthWeekdayPlugin

Generates events on nth occurrence of a weekday.

```typescript
const plugin = new NthWeekdayPlugin();
plugin.configure({
    nth: 2,              // 2nd
    weekday: 0,          // Sunday (0-6)
    eventType: 'ISHA_SATSANG',
    location: 'SOUTH_BAY',
    startTime: '7:00 PM',
    endTime: '9:00 PM'
});
```

##### WeeklyPlugin

Generates weekly recurring events.

```typescript
plugin.configure({
    weekdays: [6],       // Every Saturday
    eventType: 'THREE_HOUR',
    location: 'SOUTH_BAY',
    startTime: '9:00 AM',
    endTime: '12:00 PM'
});
```

### Date Utilities

Helper functions for date manipulation.

```typescript
import {
    getWeekendDates,
    formatEventDate,
    isWeekend
} from './src/utils/dateUtils';

// Get weekend dates
const {saturday, sunday} = getWeekendDates(new Date());

// Format date for display
const formatted = formatEventDate(new Date()); // "Saturday, July 12 2025"

// Check if weekend
const isWeekendDay = isWeekend(new Date());
```

### Logger

Structured logging with performance tracking.

```typescript
import {logger, startTimer, endTimer} from './src/utils/logger';

// Basic logging
logger.info('Operation started', {module: 'MyModule'});
logger.error('Operation failed', new Error('Oops'));

// Performance tracking
startTimer('myOperation');
// ... do work ...
const duration = endTimer('myOperation');
logger.info(`Operation took ${duration}ms`);

// Test mode
logger.setTestMode(true); // Enable verbose logging
```

## Event Types

```typescript
enum EventType {
    THREE_HOUR = '3_HOUR',
    ONE_DAY = '1_DAY',
    TEN_DAY = '10_DAY',
    SATIPATTHANA = 'SATIPATTHANA',
    SPECIAL = 'SPECIAL',
    SERVICE = 'SERVICE',
    GROUP_SIT = 'GROUP_SIT',
    ISHA_SATSANG = 'ISHA_SATSANG'
}

enum Location {
    SOUTH_BAY = 'SOUTH_BAY',
    EAST_BAY = 'EAST_BAY',
    CENTER_LAND = 'CENTER_LAND'
}

interface Event {
    date: Date;
    type: EventType;
    location: Location;
    address: string;
    startTime: string;
    endTime: string;
    description?: string;
    source?: 'calendar' | 'recurring' | 'modular';
}
```

## Configuration API

### ConfigValidator

Validates and manages application configuration.

```typescript
import {config} from './src/utils/config';

// Validate and load configuration
const appConfig = config.validateAndLoad();

// Access configuration
console.log(appConfig.twilio.accountSid);
console.log(appConfig.googleDocId);
console.log(appConfig.locationBypass);

// Perform health check
const health = await config.performHealthCheck();
if (!health.healthy) {
    console.error('System unhealthy:', health.checks);
}
```

### SetupWizard

Interactive setup for first-time users.

```typescript
import {SetupWizard} from './src/utils/setupWizard';

const wizard = new SetupWizard();

// Check if setup needed
if (wizard.isFirstTimeSetup()) {
    // Run interactive setup
    await wizard.checkAndSetup();
}
```

## Error Handling

All APIs follow consistent error handling:

```typescript
try {
    const result = await service.operation();
} catch (error) {
    if (error instanceof ConfigurationError) {
        // Handle missing configuration
    } else if (error instanceof NetworkError) {
        // Handle network issues
    } else {
        // Handle unexpected errors
    }
}
```

## Usage Examples

### Example 1: Get This Weekend's Events

```typescript
import {WeekendEventService} from './src/services/WeekendEventService';

async function getThisWeekend() {
    const service = new WeekendEventService();
    const events = await service.getWeekendEvents();

    console.log('Saturday Events:', events.saturday);
    console.log('Sunday Events:', events.sunday);
}
```

### Example 2: Send Custom WhatsApp Message

```typescript
import {WhatsAppService} from './src/services/WhatsAppService';

async function sendCustomMessage() {
    const whatsapp = new WhatsAppService();

    // Check health first
    const health = await whatsapp.performHealthCheck();
    if (!health.healthy) {
        throw new Error(`WhatsApp unhealthy: ${health.message}`);
    }

    // Send message
    const success = await whatsapp.sendWhatsAppMessage(
        'Custom reminder: Meditation session tomorrow!'
    );

    return success;
}
```

### Example 3: Parse Custom Calendar

```typescript
import {CalendarParser} from './src/parser/CalendarParser';

function parseCustomCalendar(text: string) {
    const parser = new CalendarParser();
    const events = parser.parseCalendar(text);

    // Group by location
    const byLocation = events.reduce((acc, event) => {
        if (!acc[event.location]) acc[event.location] = [];
        acc[event.location].push(event);
        return acc;
    }, {});

    return byLocation;
}
```

### Example 4: Create Custom Plugin

```typescript
import {EventPlugin} from './src/core/EventPlugin';

class CustomPlugin implements EventPlugin {
    id = 'custom';
    name = 'Custom Event Plugin';
    private config: any;

    configure(config: any): void {
        this.config = config;
    }

    shouldGenerateEvents(date: Date): boolean {
        // Custom logic
        return date.getDay() === this.config.dayOfWeek;
    }

    generateEvents(date: Date): Event[] {
        return [{
            date,
            type: EventType.SPECIAL,
            location: this.config.location,
            address: 'Custom Address',
            startTime: this.config.startTime,
            endTime: this.config.endTime,
            description: this.config.description
        }];
    }
}
```

## Rate Limiting

Some services implement rate limiting:

- **LocationService**: Caches for 24 hours
- **WhatsApp Health Check**: Caches for 1 minute
- **Web Fetcher**: No built-in rate limit (be responsible)

## Testing Your Integration

```typescript
import {config} from './src/utils/config';

// Validate environment
try {
    config.validateAndLoad();
    console.log('✅ Configuration valid');
} catch (error) {
    console.error('❌ Configuration error:', error.message);
}

// Test WhatsApp
const whatsapp = new WhatsAppService();
const testSent = await whatsapp.testConnection();
console.log('WhatsApp test:', testSent ? '✅' : '❌');

// Test location
const location = new LocationService();
const inCA = await location.isInCalifornia();
console.log('In California:', inCA ? '✅' : '❌');
```