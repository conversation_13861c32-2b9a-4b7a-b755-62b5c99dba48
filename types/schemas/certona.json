{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://cms.gap.com/schema/partials/v1/certona-recommendations.json", "title": "Certona Recommendation", "type": "object", "properties": {"resonance": {"type": "object", "properties": {"schemes": {"type": "array", "items": {"type": "object", "properties": {"scheme": {"type": "string"}, "explanation": {"type": "string"}, "display": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"ID": {"type": "string"}, "ProductName": {"type": "string"}, "ImageURL": {"type": "string"}, "LightWeightImageURL": {"type": "string"}, "DetailURL": {"type": "string"}, "instock": {"type": "string"}, "OriginalPrice": {"type": "string"}, "CurrentPrice": {"type": "string"}, "MarketingFlag": {"type": "string"}, "PromotionDisplay": {"type": "string"}, "Rating": {"type": "string"}, "ReviewCount": {"type": "string"}, "variantId": {"type": "string"}, "SizeRangeA": {"type": "string"}, "SizeRangeB": {"type": "string"}, "SizeRangeC": {"type": "string"}}, "required": ["ID", "ProductName", "ImageURL", "LightWeightImageURL", "DetailURL", "instock", "OriginalPrice", "CurrentPrice", "MarketingFlag", "PromotionDisplay", "Rating", "ReviewCount", "variantId", "SizeRangeA", "SizeRangeB", "SizeRangeC"]}}}, "required": ["scheme", "explanation", "display", "items"]}}}, "required": ["schemes"]}}, "required": ["resonance"]}