version: 1-stable
appName: router
pcfAppName: none
team: pt-webapps
environment: test
httpRouterOnly: true
affectedCI: sitewide-app
externalServices:
  - host: stage.api.azeus.gaptech.com
  - host: test.api.azeus.gaptech.com
  - host: cdn.optimizely.com
  - host: secure-internal-azeus-ecom-api.live.stage.gaptechol.com
  - host: azeus-brol.live.stage.gaptechol.com
  - host: collector.newrelic.com
  - host: internal-azeus-ecom-api.live.test.gaptechol.com
  - host: readservices-b2c.powerreviews.com
  - host: ws-catalog-api-service.stage.azeus.gaptech.com
  - host: endpoint.dlp-webservices.prod.dlp.adeptmind.net
  - host: catalog-apis-omni-product-service.aks.stage.azeus.gaptech.com
  - host: pmcs.aks.stage.azeus.gaptech.com
httpRouting:
  - route:
      - destination:
          host: sitewide-next
