{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitOverride": true, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "src/test-templates/**"]}