#!/usr/bin/env node


const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

const COVERAGE_THRESHOLD = 90.0;
const COVERAGE_FILE = path.join(__dirname, '..', 'coverage', 'coverage-summary.json');

function checkCoverage() {
    // Check if coverage file exists
    if (!fs.existsSync(COVERAGE_FILE)) {
        console.log('Coverage file not found. Running jest coverage...');
        try {
            execSync('npm run test:coverage', {stdio: 'inherit'});
        } catch (error) {
            console.error('Failed to run coverage tests');
            process.exit(1);
        }
    }

    // Check if file exists after potentially running coverage
    if (!fs.existsSync(COVERAGE_FILE)) {
        console.error('Coverage file not found. Please ensure jest coverage is configured correctly.');
        process.exit(1);
    }

    // Read and parse coverage data
    let coverageData;
    try {
        const coverageJson = fs.readFileSync(COVERAGE_FILE, 'utf8');
        coverageData = JSON.parse(coverageJson);
    } catch (error) {
        console.error('Error reading coverage file:', error.message);
        process.exit(1);
    }

    // Check if coverage data has the expected structure
    if (!coverageData || !coverageData.total || !coverageData.total.lines || !coverageData.total.statements ||
        !coverageData.total.functions || !coverageData.total.branches) {
        console.error('Error reading coverage file: Invalid coverage data structure');
        process.exit(1);
    }

    // Extract coverage percentages
    const coverage = {
        lines: coverageData.total.lines.pct,
        statements: coverageData.total.statements.pct,
        functions: coverageData.total.functions.pct,
        branches: coverageData.total.branches.pct
    };

    // Check if overall statement coverage is below threshold
    const overallCoverage = coverage.statements;
    const failedMetrics = [];

    if (overallCoverage < COVERAGE_THRESHOLD) {
        failedMetrics.push(`Overall coverage (${overallCoverage.toFixed(2)}%) is below ${COVERAGE_THRESHOLD}%`);
    }

    // Report results
    if (failedMetrics.length > 0) {
        console.error('❌ Coverage check failed:');
        failedMetrics.forEach(metric => console.error(`  - ${metric}`));
        process.exit(1);
    } else {
        console.log('✅ Coverage check passed!');
        console.log(`  Overall (Statements): ${coverage.statements.toFixed(2)}%`);
        console.log('  Details:');
        console.log(`    Lines: ${coverage.lines.toFixed(2)}%`);
        console.log(`    Functions: ${coverage.functions.toFixed(2)}%`);
        console.log(`    Branches: ${coverage.branches.toFixed(2)}%`);
    }
}

// Run the check only if this is the main module
if (require.main === module) {
    checkCoverage();
}

module.exports = {checkCoverage};