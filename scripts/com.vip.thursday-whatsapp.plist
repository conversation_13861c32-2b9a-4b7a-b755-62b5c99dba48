<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Job identifier (must be unique) -->
    <key>Label</key>
    <string>com.vip.thursday-whatsapp</string>
    
    <!-- Program to run -->
    <key>Program</key>
    <string>/Users/<USER>/WebstormProjects/vip/scripts/thursday-whatsapp.sh</string>
    
    <!-- Run every Thursday at 6:00 PM -->
    <key>StartCalendarInterval</key>
    <dict>
        <key>Weekday</key>
        <integer>5</integer>  <!-- Thursday = 5 (Sunday = 1) -->
        <key>Hour</key>
        <integer>18</integer>  <!-- 6 PM -->
        <key>Minute</key>
        <integer>0</integer>  <!-- 0 minutes -->
    </dict>
    
    <!-- Run at load (when system starts) to catch missed jobs -->
    <key>RunAtLoad</key>
    <true/>
    
    <!-- Check for missed jobs every hour after 6 PM on Thursday -->
    <key>StartInterval</key>
    <integer>3600</integer>  <!-- 1 hour in seconds -->
    
    <!-- Keep job alive if it crashes -->
    <key>KeepAlive</key>
    <false/>
    
    <!-- Standard output and error logs -->
    <key>StandardOutPath</key>
    <string>/Users/<USER>/WebstormProjects/vip/logs/thursday-launchd-stdout.log</string>
    
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/WebstormProjects/vip/logs/thursday-launchd-stderr.log</string>
    
    <!-- Environment variables -->
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/usr/local/bin:/usr/bin:/bin:/opt/homebrew/bin:/Users/<USER>/.nvm/versions/node/v22.9.0/bin</string>
        <key>NODE_ENV</key>
        <string>production</string>
    </dict>
    
    <!-- Working directory -->
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/WebstormProjects/vip</string>
    
    <!-- Process type -->
    <key>ProcessType</key>
    <string>Background</string>
</dict>
</plist>