#!/bin/bash

# VIP Performance Benchmark Script
# Measures execution time and resource usage of cron scripts

PROJECT_DIR="/Users/<USER>/WebstormProjects/vip"
LOG_FILE="$PROJECT_DIR/logs/benchmark.log"

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Function to log messages with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to run benchmark
run_benchmark() {
    local script_name=$1
    local test_name=$2
    local setup_function=$3
    
    log_message "🔥 Running benchmark: $test_name"
    
    # Setup test conditions
    if [ -n "$setup_function" ]; then
        $setup_function
    fi
    
    # Measure execution time
    local start_time=$(date +%s.%N)
    
    # Run the script
    local output
    local exit_code
    if [ "$script_name" = "monday" ]; then
        output=$(./scripts/monday-whatsapp.sh 2>&1)
        exit_code=$?
    elif [ "$script_name" = "thursday" ]; then
        output=$(./scripts/thursday-whatsapp.sh 2>&1)
        exit_code=$?
    fi
    
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    # Log results
    log_message "  ⏱️  Duration: ${duration}s"
    log_message "  📊 Exit code: $exit_code"
    log_message "  📝 Output lines: $(echo "$output" | wc -l)"
    
    # Check for optimization indicators
    if echo "$output" | grep -q "FAST EXIT"; then
        log_message "  ⚡ FAST EXIT detected - optimization working"
    fi
    
    if echo "$output" | grep -q "Build up to date, skipping"; then
        log_message "  ⚡ Build skip detected - optimization working"
    fi
    
    if echo "$output" | grep -q "Using cached location"; then
        log_message "  ⚡ Location cache hit - optimization working"
    fi
    
    if echo "$output" | grep -q "Dependencies up to date, skipping"; then
        log_message "  ⚡ Dependency skip detected - optimization working"
    fi
    
    echo "$duration"
}

# Setup functions for different test scenarios
setup_already_sent() {
    local day=$1
    local timestamp=$(date +%s)
    echo "$timestamp" > "$PROJECT_DIR/logs/last-$day-run.txt"
}

setup_build_cache() {
    # Ensure dist is newer than src
    if [ -d "dist" ]; then
        touch dist/index.js
    fi
}

setup_location_cache() {
    # Create location cache
    local cache_file="$PROJECT_DIR/logs/location-cache.json"
    echo '{"region":"CA","city":"San Francisco"}' > "$cache_file"
    touch "$cache_file"
}

setup_clean_slate() {
    # Remove all cache files
    rm -f "$PROJECT_DIR/logs/last-monday-run.txt"
    rm -f "$PROJECT_DIR/logs/last-thursday-run.txt"
    rm -f "$PROJECT_DIR/logs/location-cache.json"
}

# Main benchmark execution
main() {
    log_message "🚀 Starting VIP Performance Benchmark"
    log_message "📅 $(date)"
    log_message "🖥️  System: $(uname -a)"
    
    cd "$PROJECT_DIR" || {
        log_message "❌ ERROR: Could not change to project directory"
        exit 1
    }
    
    # Benchmark 1: Already sent today (fastest case)
    log_message ""
    log_message "📊 BENCHMARK 1: Already sent today (should be fastest)"
    setup_already_sent "monday"
    duration1=$(run_benchmark "monday" "Already sent - Monday" "")
    
    setup_already_sent "thursday"
    duration2=$(run_benchmark "thursday" "Already sent - Thursday" "")
    
    # Benchmark 2: Build cache hit
    log_message ""
    log_message "📊 BENCHMARK 2: Build cache optimization"
    setup_build_cache
    duration3=$(run_benchmark "monday" "Build cache - Monday" "")
    
    # Benchmark 3: Location cache hit
    log_message ""
    log_message "📊 BENCHMARK 3: Location cache optimization"
    setup_location_cache
    setup_clean_slate
    duration4=$(run_benchmark "monday" "Location cache - Monday" "")
    
    # Benchmark 4: Cold start (no cache)
    log_message ""
    log_message "📊 BENCHMARK 4: Cold start (no optimizations)"
    setup_clean_slate
    rm -rf dist  # Force rebuild
    duration5=$(run_benchmark "monday" "Cold start - Monday" "")
    
    # Summary
    log_message ""
    log_message "📈 BENCHMARK SUMMARY"
    log_message "  🏆 Already sent (Monday): ${duration1}s"
    log_message "  🏆 Already sent (Thursday): ${duration2}s"
    log_message "  🏗️  Build cache hit: ${duration3}s"
    log_message "  📍 Location cache hit: ${duration4}s"
    log_message "  🧊 Cold start: ${duration5}s"
    
    # Performance comparison
    log_message ""
    log_message "🚀 PERFORMANCE GAINS"
    local fastest=$duration1
    local slowest=$duration5
    
    if [ -n "$fastest" ] && [ -n "$slowest" ]; then
        local improvement=$(echo "scale=2; ($slowest - $fastest) / $slowest * 100" | bc)
        log_message "  📊 Fastest vs Slowest: ${improvement}% improvement"
    fi
    
    log_message ""
    log_message "✅ Benchmark completed successfully"
    log_message "📄 Full results saved to: $LOG_FILE"
}

# Check if bc is available for calculations
if ! command -v bc &> /dev/null; then
    log_message "⚠️  WARNING: bc not found, some calculations will be skipped"
fi

# Run the benchmark
main "$@"