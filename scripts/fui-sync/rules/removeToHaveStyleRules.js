function transformer(node<PERSON><PERSON>, j) {
  return nodePath
    .find(j.CallExpression, {
      callee: {
        property: {
          name: 'toHaveStyleRules',
        },
      },
    })
    .replaceWith(p => {
      const root = j(p.value);
      const obj = root.find(j.ObjectExpression);
      const expectNode = root
        .find(j.CallExpression, {
          callee: {
            name: 'expect',
          },
        })
        .nodes()[0];
      const props = obj
        .nodes()
        .map(node => node.properties)
        .flat()
        .map(node => {
          if (!node || !node.value) {
            throw new Error('node.value is undefined');
          }
          return [j.literal(node.key.name), node.value];
        });

      // return props.map(args => j.callExpression(
      //   j.memberExpression(expectNode, j.identifier('toHavStyleRule')), args
      // ))

      return j.blockStatement(
        props.map(args => j.expressionStatement(j.callExpression(j.memberExpression(expectNode, j.identifier('toHaveStyleRule')), args)))
      );
    });
}

module.exports = transformer;
