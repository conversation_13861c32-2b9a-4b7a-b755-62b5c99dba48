#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get the last tag or use initial commit
function getLastTag() {
  try {
    return execSync('git describe --tags --abbrev=0', { encoding: 'utf8' }).trim();
  } catch (error) {
    // No tags found, use first commit
    return execSync('git rev-list --max-parents=0 HEAD', { encoding: 'utf8' }).trim();
  }
}

// Get current version from package.json
function getCurrentVersion() {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  return packageJson.version;
}

// Parse commit message to categorize changes
function categorizeCommit(message) {
  const categories = {
    feat: 'Added',
    fix: 'Fixed',
    docs: 'Documentation',
    style: 'Style',
    refactor: 'Changed',
    perf: 'Performance',
    test: 'Tests',
    chore: 'Maintenance'
  };

  const match = message.match(/^(\w+)(?:\(([^)]+)\))?:\s*(.+)/);
  if (match) {
    const [, type, scope, description] = match;
    return {
      category: categories[type] || 'Other',
      scope,
      description
    };
  }

  // Default category for non-conventional commits
  return {
    category: 'Changed',
    description: message
  };
}

// Generate changelog entry
function generateChangelog() {
  const lastTag = getLastTag();
  const currentVersion = getCurrentVersion();
  const date = new Date().toISOString().split('T')[0];

  console.log(`📝 Generating changelog from ${lastTag} to HEAD...`);

  // Get commits since last tag
  const commits = execSync(`git log ${lastTag}..HEAD --pretty=format:"%H|%s|%b"`, { encoding: 'utf8' })
    .split('\n')
    .filter(Boolean)
    .map(line => {
      const [hash, subject, body] = line.split('|');
      return { hash, subject, body };
    });

  // Group commits by category
  const grouped = {};
  commits.forEach(commit => {
    const { category, scope, description } = categorizeCommit(commit.subject);
    if (!grouped[category]) {
      grouped[category] = [];
    }
    grouped[category].push({
      scope,
      description: description || commit.subject,
      hash: commit.hash.substring(0, 7),
      body: commit.body
    });
  });

  // Read existing changelog
  const changelogPath = path.join(__dirname, '..', 'CHANGELOG.md');
  let existingChangelog = '';
  if (fs.existsSync(changelogPath)) {
    existingChangelog = fs.readFileSync(changelogPath, 'utf8');
  } else {
    // Create initial changelog structure
    existingChangelog = `# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

`;
  }

  // Generate new entry
  let newEntry = `## [${currentVersion}] - ${date}\n\n`;

  // Add categorized commits
  const categoryOrder = ['Added', 'Changed', 'Fixed', 'Documentation', 'Performance', 'Tests', 'Maintenance', 'Other'];
  
  categoryOrder.forEach(category => {
    if (grouped[category] && grouped[category].length > 0) {
      newEntry += `### ${category}\n`;
      grouped[category].forEach(commit => {
        const scope = commit.scope ? `**${commit.scope}**: ` : '';
        newEntry += `- ${scope}${commit.description} ([${commit.hash}])\n`;
        
        // Add body if it contains additional details
        if (commit.body && commit.body.trim()) {
          const bodyLines = commit.body.trim().split('\n');
          bodyLines.forEach(line => {
            if (line.trim() && !line.includes('Generated with') && !line.includes('Co-Authored-By')) {
              newEntry += `  ${line.trim()}\n`;
            }
          });
        }
      });
      newEntry += '\n';
    }
  });

  // Insert new entry after [Unreleased] section
  const unreleased = '## [Unreleased]';
  const insertIndex = existingChangelog.indexOf(unreleased) + unreleased.length;
  
  if (insertIndex > unreleased.length) {
    const updatedChangelog = 
      existingChangelog.slice(0, insertIndex) + 
      '\n\n' + 
      newEntry + 
      existingChangelog.slice(insertIndex);
    
    fs.writeFileSync(changelogPath, updatedChangelog);
    console.log(`✅ Changelog updated for version ${currentVersion}`);
    console.log(`📄 Changes written to ${changelogPath}`);
  } else {
    console.error('❌ Could not find [Unreleased] section in changelog');
  }
}

// Add git tag creation helper
function createTag() {
  const version = getCurrentVersion();
  const tagName = `v${version}`;
  
  try {
    execSync(`git tag -a ${tagName} -m "Release ${version}"`, { stdio: 'inherit' });
    console.log(`✅ Created tag ${tagName}`);
    console.log(`📤 Run 'git push origin ${tagName}' to push the tag`);
  } catch (error) {
    console.error(`❌ Failed to create tag: ${error.message}`);
  }
}

// Main execution
const command = process.argv[2];

if (command === 'tag') {
  createTag();
} else {
  generateChangelog();
  console.log('\n💡 Tip: Run "npm run changelog:tag" to create a version tag');
}