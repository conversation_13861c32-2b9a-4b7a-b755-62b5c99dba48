echo "🔍 Running pre-commit checks..."

# 1. Run ESLint
echo "📝 Checking lint..."
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ Lint check failed. Please fix ESLint errors before committing."
  exit 1
fi

# 2. Run TypeScript type checking
echo "🔷 Checking TypeScript..."
npx tsc --noEmit
if [ $? -ne 0 ]; then
  echo "❌ TypeScript check failed. Please fix type errors before committing."
  exit 1
fi

# 3. Run tests with coverage check
echo "🧪 Running tests and checking coverage..."
node scripts/check-coverage.js
if [ $? -ne 0 ]; then
  echo "❌ Coverage check failed. Coverage must be at least 90%."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
