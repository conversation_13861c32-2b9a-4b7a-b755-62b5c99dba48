# Claude Code Configuration

## Project Overview
This project uses TypeScript and is configured for optimal Claude Code development experience.

## Commands
- `npm run build` - Build the project
- `npm run dev` - Start development server
- `npm run test` - Run tests
- `npm run lint` - Run linting
- `npm run typecheck` - Run TypeScript type checking

## Development Guidelines
- Follow TypeScript best practices
- Use existing code patterns and conventions
- Run lint and typecheck before committing
- Keep code concise and well-structured

## File Structure
- `src/` - Source code
- `src/index.ts` - Main entry point
- `tsconfig.json` - TypeScript configuration
- `package.json` - Dependencies and scripts

## Memory Management
This file helps Claude Code remember project context and preferences across sessions.

## Testing
Check package.json for available test scripts and testing framework.

## Linting & Type Checking
Always run these commands before committing:
- `npm run lint` (if available)
- `npm run typecheck` (if available)

## Notes
- Project is set up with TypeScript
- Uses standard Node.js project structure
- Git repository initialized