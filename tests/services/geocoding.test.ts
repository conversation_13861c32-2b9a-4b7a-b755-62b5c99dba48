import { GeocodingService } from '../../src/services/geocoding';
import { GeocodingError, ApiError, NetworkError } from '../../src/models/Errors';
import { Coordinates } from '../../src/models/Business';

// Mock fetch globally
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

describe('GeocodingService', () => {
  let geocodingService: GeocodingService;

  beforeEach(() => {
    geocodingService = new GeocodingService();
    mockFetch.mockClear();
  });

  describe('zipCodeToCoordinates', () => {
    const validZipCode = '12345';
    const mockApiResponse = {
      results: [
        {
          geometry: {
            location: {
              lat: 40.7128,
              lng: -74.0060
            }
          },
          formatted_address: 'New York, NY 12345, USA'
        }
      ],
      status: 'OK'
    };

    it('should convert valid zip code to coordinates', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      } as Response);

      const result = await geocodingService.zipCodeToCoordinates(validZipCode);

      expect(result).toEqual({
        latitude: 40.7128,
        longitude: -74.0060
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('geocode/json'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should handle API errors gracefully', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates(validZipCode))
        .rejects.toThrow(ApiError);
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(geocodingService.zipCodeToCoordinates(validZipCode))
        .rejects.toThrow(NetworkError);
    });

    it('should handle zero results from API', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [],
          status: 'ZERO_RESULTS'
        })
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates(validZipCode))
        .rejects.toThrow(GeocodingError);
    });

    it('should handle invalid API response format', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [{ invalid: 'format' }],
          status: 'OK'
        })
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates(validZipCode))
        .rejects.toThrow(GeocodingError);
    });

    it('should cache successful results', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockApiResponse
      } as Response);

      // First call
      const result1 = await geocodingService.zipCodeToCoordinates(validZipCode);
      
      // Second call should use cache
      const result2 = await geocodingService.zipCodeToCoordinates(validZipCode);

      expect(result1).toEqual(result2);
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });

    it('should validate zip code format before making API call', async () => {
      await expect(geocodingService.zipCodeToCoordinates('invalid'))
        .rejects.toThrow(GeocodingError);

      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should handle rate limiting', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests'
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates(validZipCode))
        .rejects.toThrow(ApiError);
    });
  });

  describe('coordinatesToZipCode', () => {
    const validCoordinates: Coordinates = {
      latitude: 40.7128,
      longitude: -74.0060
    };

    const mockReverseApiResponse = {
      results: [
        {
          address_components: [
            {
              long_name: '12345',
              short_name: '12345',
              types: ['postal_code']
            }
          ],
          formatted_address: 'New York, NY 12345, USA'
        }
      ],
      status: 'OK'
    };

    it('should convert coordinates to zip code', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockReverseApiResponse
      } as Response);

      const result = await geocodingService.coordinatesToZipCode(validCoordinates);

      expect(result).toBe('12345');
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('geocode/json'),
        expect.objectContaining({
          method: 'GET'
        })
      );
    });

    it('should handle coordinates with no postal code', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            {
              address_components: [
                {
                  long_name: 'New York',
                  short_name: 'NY',
                  types: ['locality']
                }
              ]
            }
          ],
          status: 'OK'
        })
      } as Response);

      await expect(geocodingService.coordinatesToZipCode(validCoordinates))
        .rejects.toThrow(GeocodingError);
    });
  });

  describe('clearCache', () => {
    it('should clear the geocoding cache', async () => {
      // Add something to cache first
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            {
              geometry: {
                location: { lat: 40.7128, lng: -74.0060 }
              }
            }
          ],
          status: 'OK'
        })
      } as Response);

      await geocodingService.zipCodeToCoordinates('12345');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // Clear cache
      geocodingService.clearCache();

      // Should make API call again
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            {
              geometry: {
                location: { lat: 40.7128, lng: -74.0060 }
              }
            }
          ],
          status: 'OK'
        })
      } as Response);

      await geocodingService.zipCodeToCoordinates('12345');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('getCacheSize', () => {
    it('should return current cache size', async () => {
      const initialSize = geocodingService.getCacheSize();
      expect(initialSize).toBe(0);

      // Add something to cache
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            {
              geometry: {
                location: { lat: 40.7128, lng: -74.0060 }
              }
            }
          ],
          status: 'OK'
        })
      } as Response);

      await geocodingService.zipCodeToCoordinates('12345');

      const newSize = geocodingService.getCacheSize();
      expect(newSize).toBe(1);
    });
  });

  describe('error handling edge cases', () => {
    it('should handle API status errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          status: 'REQUEST_DENIED',
          error_message: 'API key invalid'
        })
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates('12345'))
        .rejects.toThrow('API key invalid');
    });

    it('should handle OVER_QUERY_LIMIT status', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          status: 'OVER_QUERY_LIMIT',
          error_message: 'Query limit exceeded'
        })
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates('12345'))
        .rejects.toThrow('Query limit exceeded');
    });

    it('should handle missing geometry in response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          results: [
            {
              formatted_address: 'Some address'
              // Missing geometry
            }
          ],
          status: 'OK'
        })
      } as Response);

      await expect(geocodingService.zipCodeToCoordinates('12345'))
        .rejects.toThrow('Invalid response format');
    });

    it('should handle unknown errors', async () => {
      mockFetch.mockRejectedValueOnce('unknown error type');

      await expect(geocodingService.zipCodeToCoordinates('12345'))
        .rejects.toThrow('Unknown error during geocoding');
    });
  });

  describe('constructor error handling', () => {
    it('should throw ConfigurationError when no API key provided', () => {
      const originalKey = process.env.GOOGLE_PLACES_API_KEY;
      delete process.env.GOOGLE_PLACES_API_KEY;

      expect(() => new GeocodingService()).toThrow('Google Places API key is required');

      // Restore API key
      if (originalKey) {
        process.env.GOOGLE_PLACES_API_KEY = originalKey;
      }
    });

    it('should accept API key from constructor parameter', () => {
      expect(() => new GeocodingService('test-api-key')).not.toThrow();
    });
  });

  describe('coordinatesToZipCode edge cases', () => {
    it('should handle multiple address components', async () => {
      const mockReverseApiResponse = {
        results: [
          {
            address_components: [
              {
                long_name: 'New York',
                short_name: 'NY',
                types: ['locality']
              },
              {
                long_name: '12345',
                short_name: '12345',
                types: ['postal_code']
              }
            ]
          }
        ],
        status: 'OK'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockReverseApiResponse
      } as Response);

      const result = await geocodingService.coordinatesToZipCode({
        latitude: 40.7128,
        longitude: -74.0060
      });

      expect(result).toBe('12345');
    });

    it('should handle API errors in reverse geocoding', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      } as Response);

      await expect(geocodingService.coordinatesToZipCode({
        latitude: 40.7128,
        longitude: -74.0060
      })).rejects.toThrow('Reverse geocoding API request failed');
    });

    it('should handle network errors in reverse geocoding', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(geocodingService.coordinatesToZipCode({
        latitude: 40.7128,
        longitude: -74.0060
      })).rejects.toThrow('Network error during reverse geocoding');
    });

    it('should cache reverse geocoding results', async () => {
      const mockReverseApiResponse = {
        results: [
          {
            address_components: [
              {
                long_name: '12345',
                short_name: '12345',
                types: ['postal_code']
              }
            ]
          }
        ],
        status: 'OK'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockReverseApiResponse
      } as Response);

      const coordinates = { latitude: 40.7128, longitude: -74.0060 };

      // First call
      const result1 = await geocodingService.coordinatesToZipCode(coordinates);

      // Second call should use cache
      const result2 = await geocodingService.coordinatesToZipCode(coordinates);

      expect(result1).toBe(result2);
      expect(mockFetch).toHaveBeenCalledTimes(1);
    });
  });
});
