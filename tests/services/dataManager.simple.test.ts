import { DataManager } from '../../src/services/dataManager';
import { SearchResult, Business } from '../../src/models/Business';

describe('DataManager - Core Functionality', () => {
  let dataManager: DataManager;

  const mockSearchResult: SearchResult = {
    searchParams: {
      zipCode: '10001',
      radius: 10,
      businessType: 'restaurant',
      timestamp: new Date('2023-01-01T00:00:00Z'),
    },
    results: {
      withWebsites: [
        {
          id: '1',
          name: 'Restaurant A',
          address: '123 Main St',
          websiteStatus: 'verified',
          category: ['restaurant'],
          distance: 1.5,
          metadata: {
            lastUpdated: new Date('2023-01-01T00:00:00Z'),
            dataSource: 'google_places',
            confidence: 0.9,
          },
        } as Business,
      ],
      withoutWebsites: [
        {
          id: '2',
          name: 'Restaurant B',
          address: '456 Oak Ave',
          websiteStatus: 'none',
          category: ['restaurant'],
          distance: 2.1,
          metadata: {
            lastUpdated: new Date('2023-01-01T00:00:00Z'),
            dataSource: 'google_places',
            confidence: 0.8,
          },
        } as Business,
      ],
    },
    statistics: {
      totalFound: 2,
      withWebsiteCount: 1,
      withoutWebsiteCount: 1,
      websiteAdoptionRate: 0.5,
      searchDuration: 1500,
    },
  };

  beforeEach(() => {
    dataManager = new DataManager();
  });

  describe('key generation', () => {
    it('should generate unique keys for different searches', () => {
      const key1 = dataManager.saveSearchResult(mockSearchResult);
      const key2 = dataManager.saveSearchResult({
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '10002',
        },
      });

      expect(key1).toBeTruthy();
      expect(key2).toBeTruthy();
      expect(key1).not.toBe(key2);
    });
  });

  describe('data serialization', () => {
    it('should properly serialize and deserialize search results', () => {
      const key = dataManager.saveSearchResult(mockSearchResult);
      const retrieved = dataManager.getSearchResult(key);

      expect(retrieved).toBeTruthy();
      if (retrieved) {
        expect(retrieved.searchParams.zipCode).toBe('10001');
        expect(retrieved.results.withWebsites).toHaveLength(1);
        expect(retrieved.results.withoutWebsites).toHaveLength(1);
        expect(retrieved.searchParams.timestamp).toBeInstanceOf(Date);
        expect(retrieved.results.withWebsites[0].metadata.lastUpdated).toBeInstanceOf(Date);
      }
    });
  });

  describe('export and import', () => {
    it('should export data in correct format', () => {
      dataManager.saveSearchResult(mockSearchResult);
      
      const exported = dataManager.exportData();
      const parsed = JSON.parse(exported);

      expect(parsed).toHaveProperty('version');
      expect(parsed).toHaveProperty('exportDate');
      expect(parsed).toHaveProperty('searchResults');
      expect(parsed.version).toBe('1.0');
      expect(Array.isArray(parsed.searchResults)).toBe(true);
    });

    it('should import data successfully', () => {
      const exportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        searchResults: [mockSearchResult],
      };

      const importedCount = dataManager.importData(JSON.stringify(exportData));
      expect(importedCount).toBe(1);
    });
  });

  describe('storage info', () => {
    it('should return storage information', () => {
      const info = dataManager.getStorageInfo();

      expect(info).toHaveProperty('totalEntries');
      expect(info).toHaveProperty('totalSizeBytes');
      expect(info).toHaveProperty('oldestEntry');
      expect(info).toHaveProperty('newestEntry');
      expect(typeof info.totalEntries).toBe('number');
      expect(typeof info.totalSizeBytes).toBe('number');
    });
  });

  describe('error handling', () => {
    it('should handle non-existent keys gracefully', () => {
      const result = dataManager.getSearchResult('non-existent-key');
      expect(result).toBeNull();
    });

    it('should handle invalid JSON in import gracefully', () => {
      expect(() => dataManager.importData('invalid-json')).toThrow();
    });

    it('should handle invalid import data structure', () => {
      const invalidData = {
        version: '1.0',
        // Missing searchResults
      };

      expect(() => dataManager.importData(JSON.stringify(invalidData))).toThrow();
    });

    it('should handle corrupted localStorage data', () => {
      // Simulate corrupted data by directly setting invalid JSON
      const key = dataManager.saveSearchResult(mockSearchResult);
      localStorage.setItem(key, 'invalid-json');

      const result = dataManager.getSearchResult(key);
      expect(result).toBeNull();
    });

    it('should handle localStorage quota exceeded', () => {
      // Mock the entire localStorage object
      const originalLocalStorage = global.localStorage;
      const mockLocalStorage = {
        ...originalLocalStorage,
        setItem: jest.fn().mockImplementation(() => {
          const error = new Error('QuotaExceededError');
          error.name = 'QuotaExceededError';
          throw error;
        }),
      };

      // Replace localStorage with the mock
      Object.defineProperty(global, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      });

      expect(() => dataManager.saveSearchResult(mockSearchResult)).toThrow('Failed to save search result: QuotaExceededError');

      // Restore original localStorage
      Object.defineProperty(global, 'localStorage', {
        value: originalLocalStorage,
        writable: true,
      });
    });
  });

  describe('data expiration', () => {
    it('should handle expired data correctly', () => {
      const key = dataManager.saveSearchResult(mockSearchResult);

      // Manually set expired data
      const expiredData = {
        data: mockSearchResult,
        timestamp: Date.now() - (25 * 60 * 60 * 1000), // 25 hours ago
        expiresAt: Date.now() - (1 * 60 * 60 * 1000), // 1 hour ago
      };
      localStorage.setItem(key, JSON.stringify(expiredData));

      const result = dataManager.getSearchResult(key);
      expect(result).toBeNull();
    });

    it('should cleanup expired data', () => {
      // Add valid data
      dataManager.saveSearchResult(mockSearchResult);

      // Add expired data
      const expiredKey = 'business_search_expired';
      const expiredData = {
        data: mockSearchResult,
        timestamp: Date.now() - (25 * 60 * 60 * 1000),
        expiresAt: Date.now() - (1 * 60 * 60 * 1000),
      };
      localStorage.setItem(expiredKey, JSON.stringify(expiredData));

      const removedCount = dataManager.cleanupExpiredData();
      expect(removedCount).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getAllSearchResults', () => {
    it('should return all valid search results', () => {
      dataManager.saveSearchResult(mockSearchResult);
      dataManager.saveSearchResult({
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '10002',
        },
      });

      const allResults = dataManager.getAllSearchResults();
      expect(allResults.length).toBeGreaterThanOrEqual(2);
    });

    it('should exclude expired results from getAllSearchResults', () => {
      // Add valid result
      dataManager.saveSearchResult(mockSearchResult);

      // Add expired result
      const expiredKey = 'business_search_expired';
      const expiredData = {
        data: mockSearchResult,
        timestamp: Date.now() - (25 * 60 * 60 * 1000),
        expiresAt: Date.now() - (1 * 60 * 60 * 1000),
      };
      localStorage.setItem(expiredKey, JSON.stringify(expiredData));

      const allResults = dataManager.getAllSearchResults();
      // Should only return valid results
      expect(allResults.every(result =>
        result.searchParams.timestamp instanceof Date
      )).toBe(true);
    });
  });

  describe('deleteSearchResult', () => {
    it('should delete specific search result', () => {
      const key = dataManager.saveSearchResult(mockSearchResult);

      dataManager.deleteSearchResult(key);

      const result = dataManager.getSearchResult(key);
      expect(result).toBeNull();
    });

    it('should handle deletion of non-existent key', () => {
      expect(() => dataManager.deleteSearchResult('non-existent')).not.toThrow();
    });
  });

  describe('clearAllData', () => {
    it('should clear all business search data', () => {
      dataManager.saveSearchResult(mockSearchResult);
      dataManager.saveSearchResult({
        ...mockSearchResult,
        searchParams: {
          ...mockSearchResult.searchParams,
          zipCode: '10002',
        },
      });

      dataManager.clearAllData();

      const allResults = dataManager.getAllSearchResults();
      expect(allResults).toHaveLength(0);
    });

    it('should only clear business search data, not other localStorage data', () => {
      // Add non-business search data
      localStorage.setItem('other_app_data', 'should_remain');

      dataManager.saveSearchResult(mockSearchResult);
      dataManager.clearAllData();

      expect(localStorage.getItem('other_app_data')).toBe('should_remain');
      expect(dataManager.getAllSearchResults()).toHaveLength(0);
    });
  });

  describe('getCacheSize', () => {
    it('should return current cache size', () => {
      const size = dataManager.getCacheSize();
      expect(typeof size).toBe('number');
      expect(size).toBeGreaterThanOrEqual(0);
    });
  });
});
