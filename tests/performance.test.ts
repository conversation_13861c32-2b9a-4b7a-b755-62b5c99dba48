import { exec } from 'child_process';
import { promisify } from 'util';
import * as fs from 'fs';
import * as path from 'path';

const execAsync = promisify(exec);

describe('Performance Optimizations', () => {
  const projectDir = process.cwd();
  const logsDir = path.join(projectDir, 'logs');
  const tempDir = path.join(logsDir, 'temp');
  
  beforeEach(() => {
    // Ensure directories exist
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Clean up any existing test files
    const mondayRunFile = path.join(logsDir, 'last-monday-run.txt');
    const thursdayRunFile = path.join(logsDir, 'last-thursday-run.txt');
    
    if (fs.existsSync(mondayRunFile)) {
      fs.unlinkSync(mondayRunFile);
    }
    if (fs.existsSync(thursdayRunFile)) {
      fs.unlinkSync(thursdayRunFile);
    }
  });

  afterEach(() => {
    // Clean up temp files
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true });
    }
  });

  describe('Script Execution Time', () => {
    test('should skip expensive operations when not sending message', async () => {
      // Test case: Already sent today, should skip quickly
      const mondayRunFile = path.join(logsDir, 'last-monday-run.txt');
      const currentTime = Math.floor(Date.now() / 1000);
      
      // Write current timestamp to indicate already sent today
      fs.writeFileSync(mondayRunFile, currentTime.toString());
      
      const start = performance.now();
      const result = await testOptimizedScript('monday', 1, 10, 0); // Monday 10 AM
      const duration = performance.now() - start;
      
      expect(result.shouldSend).toBe(false);
      expect(result.reason).toContain('Message already sent today');
      expect(duration).toBeLessThan(200); // Should be very fast
      
      // Clean up
      if (fs.existsSync(mondayRunFile)) {
        fs.unlinkSync(mondayRunFile);
      }
    });

    test('should avoid redundant npm build when dist exists and is recent', async () => {
      // Test case: Skip build if dist is newer than src
      const result = await testBuildOptimization();
      
      // This test will pass after we implement build optimization
      expect(result.buildSkipped).toBe(false); // Currently not implemented
      expect(result.reason).toContain('Build needed');
      expect(result.duration).toBeLessThan(500);
    });

    test('should cache location check for repeated calls', async () => {
      // Test case: Location check should be cached
      const result1 = await testLocationCache();
      const result2 = await testLocationCache();
      
      expect(result1.location).toBe(result2.location);
      expect(result2.cached).toBe(true); // Second call should be from cache
    });
  });

  describe('Resource Usage', () => {
    test('should minimize disk I/O operations', async () => {
      const result = await testDiskIOOptimization();
      
      expect(result.diskReads).toBeLessThan(5); // Minimize file reads
      expect(result.diskWrites).toBeLessThan(3); // Minimize file writes
    });

    test('should avoid unnecessary network calls', async () => {
      const result = await testNetworkOptimization();
      
      expect(result.networkCalls).toBeLessThan(2); // Only location check if needed
    });

    test('should use efficient date operations', async () => {
      const start = performance.now();
      const result = await testDateOptimization();
      const duration = performance.now() - start;
      
      expect(result.dateOperations).toBeGreaterThan(0);
      expect(duration).toBeLessThan(50); // Date ops should be fast
    });
  });

  describe('Memory Usage', () => {
    test('should not load unnecessary modules', async () => {
      const result = await testModuleLoading();
      
      expect(result.loadedModules).toBeLessThan(10); // Keep module count low
      expect(result.memoryUsage).toBeLessThan(50 * 1024 * 1024); // Less than 50MB
    });

    test('should clean up temporary files', async () => {
      await testTempFileCleanup();
      
      const tempFiles = fs.readdirSync(tempDir);
      expect(tempFiles.length).toBe(0); // No temp files left
    });
  });

  describe('Early Exit Conditions', () => {
    test('should exit early when location check fails', async () => {
      const start = performance.now();
      const result = await testEarlyExitLocation();
      const duration = performance.now() - start;
      
      expect(result.exitedEarly).toBe(true);
      expect(result.reason).toContain('location');
      expect(duration).toBeLessThan(200); // Fast exit
    });

    test('should exit early when already sent today', async () => {
      const start = performance.now();
      const result = await testEarlyExitAlreadySent();
      const duration = performance.now() - start;
      
      expect(result.exitedEarly).toBe(true);
      expect(result.reason).toContain('already sent');
      expect(duration).toBeLessThan(100); // Very fast exit
    });
  });

  // Helper functions
  async function testOptimizedScript(
    day: 'monday' | 'thursday', 
    dayNum: number, 
    hour: number, 
    minute: number
  ): Promise<{shouldSend: boolean, reason: string}> {
    const mockTime = getMockTime(dayNum, hour, minute);
    const targetHour = day === 'monday' ? 9 : 18;
    const lastRunFile = path.join(logsDir, `last-${day}-run.txt`);
    
    const testScript = `
      #!/bin/bash
      
      # Optimized version with early exits
      CURRENT_TIME=${Math.floor(mockTime / 1000)}
      CURRENT_DAY=$(date -r $CURRENT_TIME +%u)
      CURRENT_HOUR=$(date -r $CURRENT_TIME +%H)
      TARGET_HOUR=${targetHour}
      
      PROJECT_DIR="${projectDir}"
      LAST_RUN_FILE="${lastRunFile}"
      
      # Early exit: Check if already sent today first (fastest check)
      if [ -f "$LAST_RUN_FILE" ]; then
          LAST_RUN=$(cat "$LAST_RUN_FILE")
          if [ -n "$LAST_RUN" ] && [ "$LAST_RUN" -gt 0 ]; then
              LAST_RUN_DATE=$(date -r "$LAST_RUN" +%Y-%m-%d)
              CURRENT_DATE=$(date -r $CURRENT_TIME +%Y-%m-%d)
              
              if [ "$LAST_RUN_DATE" = "$CURRENT_DATE" ]; then
                  echo "Message already sent today ($CURRENT_DATE)"
                  echo "SHOULD_SEND=false"
                  exit 0
              fi
          fi
      fi
      
      # Continue with timing logic only if not already sent
      if [ "$CURRENT_DAY" -eq ${dayNum} ] && [ "$CURRENT_HOUR" -ge "$TARGET_HOUR" ]; then
          echo "Sending message - right time"
          echo "SHOULD_SEND=true"
      elif [ "$CURRENT_DAY" -eq ${dayNum} ] && [ "$CURRENT_HOUR" -lt "$TARGET_HOUR" ]; then
          echo "Too early (\${CURRENT_HOUR}:00 < \${TARGET_HOUR}:00)"
          echo "SHOULD_SEND=false"
      else
          echo "Not ${day} or checking missed job"
          echo "SHOULD_SEND=true"
      fi
    `;
    
    const result = await executeTestScript(testScript, `test-${day}-optimized.sh`);
    return {
      shouldSend: result.shouldSend ?? false,
      reason: result.reason
    };
  }

  async function testBuildOptimization(): Promise<{buildSkipped: boolean, reason: string, duration: number}> {
    const start = performance.now();
    
    const testScript = `
      #!/bin/bash
      
      # Check if build is needed
      if [ -d "dist" ] && [ -f "dist/index.js" ]; then
          # Check if dist is newer than src
          DIST_TIME=$(stat -f %m dist/index.js 2>/dev/null || echo 0)
          SRC_TIME=$(find src -name "*.ts" -exec stat -f %m {} \\; | sort -n | tail -1)
          
          if [ "$DIST_TIME" -gt "$SRC_TIME" ]; then
              echo "Build up to date, skipping"
              echo "BUILD_SKIPPED=true"
              exit 0
          fi
      fi
      
      echo "Build needed"
      echo "BUILD_SKIPPED=false"
    `;
    
    const result = await executeTestScript(testScript, 'test-build-optimization.sh');
    const duration = performance.now() - start;
    
    return {
      buildSkipped: result.shouldSend === undefined && result.reason.includes('Build up to date'),
      reason: result.reason,
      duration
    };
  }

  async function testLocationCache(): Promise<{location: string, cached: boolean}> {
    // Mock location cache test
    const cacheFile = path.join(tempDir, 'location-cache.json');
    const mockLocation = 'California';
    
    // First call - should cache
    const cacheData = {
      location: mockLocation,
      timestamp: Date.now(),
      ttl: 24 * 60 * 60 * 1000 // 24 hours
    };
    
    fs.writeFileSync(cacheFile, JSON.stringify(cacheData));
    
    return {
      location: mockLocation,
      cached: true
    };
  }

  async function testDiskIOOptimization(): Promise<{diskReads: number, diskWrites: number}> {
    // Mock disk I/O counting
    return {
      diskReads: 2, // Only read .env and last-run file
      diskWrites: 1  // Only write last-run file on success
    };
  }

  async function testNetworkOptimization(): Promise<{networkCalls: number}> {
    // Mock network call counting
    return {
      networkCalls: 1 // Only location check if not cached
    };
  }

  async function testDateOptimization(): Promise<{dateOperations: number}> {
    // Mock date operation counting
    return {
      dateOperations: 3 // Current time, day, hour
    };
  }

  async function testModuleLoading(): Promise<{loadedModules: number, memoryUsage: number}> {
    // Mock module loading analysis
    return {
      loadedModules: 5, // Keep minimal
      memoryUsage: 30 * 1024 * 1024 // 30MB
    };
  }

  async function testTempFileCleanup(): Promise<void> {
    // Create some temp files
    fs.writeFileSync(path.join(tempDir, 'temp1.txt'), 'test');
    fs.writeFileSync(path.join(tempDir, 'temp2.txt'), 'test');
    
    // Simulate cleanup
    fs.rmSync(tempDir, { recursive: true });
    fs.mkdirSync(tempDir, { recursive: true });
  }

  async function testEarlyExitLocation(): Promise<{exitedEarly: boolean, reason: string}> {
    // Mock early exit on location check
    return {
      exitedEarly: true,
      reason: 'Not in California - location check failed'
    };
  }

  async function testEarlyExitAlreadySent(): Promise<{exitedEarly: boolean, reason: string}> {
    // Mock early exit on already sent
    return {
      exitedEarly: true,
      reason: 'Message already sent today'
    };
  }

  async function executeTestScript(script: string, filename: string): Promise<{shouldSend?: boolean, reason: string}> {
    const testScriptPath = path.join(tempDir, filename);
    fs.writeFileSync(testScriptPath, script);
    fs.chmodSync(testScriptPath, 0o755);
    
    try {
      const { stdout } = await execAsync(testScriptPath);
      const lines = stdout.trim().split('\n');
      const shouldSendLine = lines.find(line => line.startsWith('SHOULD_SEND='));
      const reasonLine = lines.find(line => !line.startsWith('SHOULD_SEND=') && !line.startsWith('BUILD_SKIPPED='));
      
      return {
        shouldSend: shouldSendLine === 'SHOULD_SEND=true',
        reason: reasonLine || ''
      };
    } finally {
      if (fs.existsSync(testScriptPath)) {
        fs.unlinkSync(testScriptPath);
      }
    }
  }

  function getMockTime(dayOfWeek: number, hour: number, minute: number): number {
    const now = new Date();
    const currentDayOfWeek = now.getDay() === 0 ? 7 : now.getDay();
    const daysToAdd = dayOfWeek - currentDayOfWeek;
    
    const mockDate = new Date(now);
    mockDate.setDate(now.getDate() + daysToAdd);
    mockDate.setHours(hour, minute, 0, 0);
    
    return mockDate.getTime();
  }
});