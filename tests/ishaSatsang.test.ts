import {
  getSecondSaturdayOfMonth,
  isSecondSaturday,
  getAllSecondSaturdays,
  getSecondSaturdaysForYears,
  formatSecondSaturdays,
} from '../src/utils/ishaSatsangUtils';
import { WeekendEventService } from '../src/services/WeekendEventService';
import { EventType } from '../src/parser/types';

describe('Isha Satsang - Second Saturday Calculations', () => {
  describe('getSecondSaturdayOfMonth', () => {
    test('should calculate second Saturday correctly for July 2025', () => {
      const secondSaturday = getSecondSaturdayOfMonth(2025, 6); // July = month 6
      expect(secondSaturday.getDate()).toBe(12);
      expect(secondSaturday.getMonth()).toBe(6); // July
      expect(secondSaturday.getFullYear()).toBe(2025);
      expect(secondSaturday.getDay()).toBe(6); // Saturday
    });

    test('should calculate second Saturday correctly for August 2025', () => {
      const secondSaturday = getSecondSaturdayOfMonth(2025, 7); // August = month 7
      expect(secondSaturday.getDate()).toBe(9);
      expect(secondSaturday.getMonth()).toBe(7); // August
      expect(secondSaturday.getFullYear()).toBe(2025);
      expect(secondSaturday.getDay()).toBe(6); // Saturday
    });

    test('should calculate second Saturday for edge cases', () => {
      // January 2025 - first day is Wednesday
      const jan2025 = getSecondSaturdayOfMonth(2025, 0);
      expect(jan2025.getDate()).toBe(11);
      expect(jan2025.getDay()).toBe(6);

      // February 2025 - first day is Saturday
      const feb2025 = getSecondSaturdayOfMonth(2025, 1);
      expect(feb2025.getDate()).toBe(8);
      expect(feb2025.getDay()).toBe(6);
    });
  });

  describe('isSecondSaturday', () => {
    test('should correctly identify July 12, 2025 as second Saturday', () => {
      const july12 = new Date('2025-07-12T08:00:00.000Z');
      expect(isSecondSaturday(july12)).toBe(true);
    });

    test('should correctly identify that July 5, 2025 is NOT second Saturday', () => {
      const july5 = new Date('2025-07-05T08:00:00.000Z');
      expect(isSecondSaturday(july5)).toBe(false);
    });

    test('should correctly identify that July 19, 2025 is NOT second Saturday', () => {
      const july19 = new Date('2025-07-19T08:00:00.000Z');
      expect(isSecondSaturday(july19)).toBe(false);
    });

    test('should correctly identify August 9, 2025 as second Saturday', () => {
      const aug9 = new Date('2025-08-09T08:00:00.000Z');
      expect(isSecondSaturday(aug9)).toBe(true);
    });
  });

  describe('getAllSecondSaturdays for 2025', () => {
    test('should return all 12 second Saturdays for 2025', () => {
      const secondSaturdays = getAllSecondSaturdays(2025);
      expect(secondSaturdays).toHaveLength(12);

      // Verify each is a Saturday
      secondSaturdays.forEach(date => {
        expect(date.getDay()).toBe(6); // All should be Saturdays
        expect(date.getFullYear()).toBe(2025);
      });
    });

    test('should have correct dates for 2025 second Saturdays', () => {
      const secondSaturdays = getAllSecondSaturdays(2025);
      const expectedDates = [
        { month: 0, date: 11 }, // January 11, 2025
        { month: 1, date: 8 }, // February 8, 2025
        { month: 2, date: 8 }, // March 8, 2025
        { month: 3, date: 12 }, // April 12, 2025
        { month: 4, date: 10 }, // May 10, 2025
        { month: 5, date: 14 }, // June 14, 2025
        { month: 6, date: 12 }, // July 12, 2025
        { month: 7, date: 9 }, // August 9, 2025
        { month: 8, date: 13 }, // September 13, 2025
        { month: 9, date: 11 }, // October 11, 2025
        { month: 10, date: 8 }, // November 8, 2025
        { month: 11, date: 13 }, // December 13, 2025
      ];

      expectedDates.forEach((expected, index) => {
        expect(secondSaturdays[index].getMonth()).toBe(expected.month);
        expect(secondSaturdays[index].getDate()).toBe(expected.date);
      });
    });
  });

  describe('getSecondSaturdaysForYears - Next 5 Years', () => {
    test('should return second Saturdays for 2025-2029 (5 years)', () => {
      const allSecondSaturdays = getSecondSaturdaysForYears(2025, 5);
      expect(allSecondSaturdays).toHaveLength(60); // 12 months × 5 years

      // Verify years are distributed correctly
      const years = new Set(allSecondSaturdays.map(date => date.getFullYear()));
      expect(years).toEqual(new Set([2025, 2026, 2027, 2028, 2029]));
    });

    test('should maintain Saturday constraint across all years', () => {
      const allSecondSaturdays = getSecondSaturdaysForYears(2025, 5);

      // All should be Saturdays
      allSecondSaturdays.forEach(date => {
        expect(date.getDay()).toBe(6);
      });
    });
  });

  describe('formatSecondSaturdays', () => {
    test('should format 2025 second Saturdays correctly', () => {
      const formatted = formatSecondSaturdays(2025);
      expect(formatted).toContain('January 11, 2025');
      expect(formatted).toContain('July 12, 2025');
      expect(formatted).toContain('December 13, 2025');
    });
  });

  describe('Integration with WeekendEventService', () => {
    let service: WeekendEventService;

    beforeEach(() => {
      service = new WeekendEventService();
    });

    test('should include Isha Satsang on July 12, 2025', async () => {
      const friday = new Date('2025-07-11T08:00:00.000Z'); // July 12-13 weekend
      const events = await service.getWeekendEvents(friday);

      const ishaSatsangEvents = events.saturday.filter(e => e.type === EventType.ISHA_SATSANG);
      expect(ishaSatsangEvents).toHaveLength(1);

      const ishaSatsang = ishaSatsangEvents[0];
      expect(ishaSatsang.startTime).toBe('7:00 PM');
      expect(ishaSatsang.endTime).toBe('9:00 PM');
      expect(ishaSatsang.description).toBe('Isha Satsang - Monthly spiritual gathering');
    }, 10000);

    test('should NOT include Isha Satsang on July 19, 2025 (third Saturday)', async () => {
      const friday = new Date('2025-07-18T08:00:00.000Z'); // July 19-20 weekend
      const events = await service.getWeekendEvents(friday);

      const ishaSatsangEvents = events.saturday.filter(e => e.type === EventType.ISHA_SATSANG);
      expect(ishaSatsangEvents).toHaveLength(0);
    }, 10000);

    test('should include Isha Satsang in formatted report for July 12, 2025', async () => {
      const friday = new Date('2025-07-11T08:00:00.000Z');
      const report = await service.formatWeekendReport(friday);

      expect(report).toContain('Isha Satsang');
      expect(report).toContain('7:00 PM – 9:00 PM');
      expect(report).toContain('South Bay Vipassana Hall');
    }, 10000);

    test('should include Isha Satsang for August 9, 2025', async () => {
      const friday = new Date('2025-08-08T08:00:00.000Z'); // August 9-10 weekend
      const events = await service.getWeekendEvents(friday);

      const ishaSatsangEvents = events.saturday.filter(e => e.type === EventType.ISHA_SATSANG);
      expect(ishaSatsangEvents).toHaveLength(1);
    }, 10000);
  });

  describe('Manual Verification of 2025 Second Saturdays', () => {
    test('should manually verify key dates for 2025', () => {
      // These are the actual second Saturdays that should occur in 2025
      const manuallyVerifiedDates = [
        new Date('2025-01-11T08:00:00.000Z'), // January
        new Date('2025-02-08T08:00:00.000Z'), // February
        new Date('2025-03-08T08:00:00.000Z'), // March
        new Date('2025-04-12T08:00:00.000Z'), // April
        new Date('2025-05-10T08:00:00.000Z'), // May
        new Date('2025-06-14T08:00:00.000Z'), // June
        new Date('2025-07-12T08:00:00.000Z'), // July
        new Date('2025-08-09T08:00:00.000Z'), // August
        new Date('2025-09-13T08:00:00.000Z'), // September
        new Date('2025-10-11T08:00:00.000Z'), // October
        new Date('2025-11-08T08:00:00.000Z'), // November
        new Date('2025-12-13T08:00:00.000Z'), // December
      ];

      manuallyVerifiedDates.forEach((date, index) => {
        expect(isSecondSaturday(date)).toBe(true);
        expect(date.getDay()).toBe(6); // Saturday

        const calculated = getSecondSaturdayOfMonth(2025, index);
        expect(calculated.getDate()).toBe(date.getDate());
        expect(calculated.getMonth()).toBe(date.getMonth());
      });
    });

    test('should verify July 12, 2025 is actually the second Saturday', () => {
      // July 2025 calendar verification:
      // July 1 (Tuesday), 2, 3, 4, 5 (Saturday - first), 6 (Sunday)
      // July 7, 8, 9, 10, 11, 12 (Saturday - second), 13 (Sunday)

      const july1 = new Date('2025-07-01T08:00:00.000Z');
      expect(july1.getDay()).toBe(2); // Tuesday

      const july5 = new Date('2025-07-05T08:00:00.000Z');
      expect(july5.getDay()).toBe(6); // First Saturday

      const july12 = new Date('2025-07-12T08:00:00.000Z');
      expect(july12.getDay()).toBe(6); // Second Saturday
      expect(isSecondSaturday(july12)).toBe(true);

      const july19 = new Date('2025-07-19T08:00:00.000Z');
      expect(july19.getDay()).toBe(6); // Third Saturday
      expect(isSecondSaturday(july19)).toBe(false);
    });
  });
});
