describe('Date Validation - Sundays and Saturdays', () => {
  describe('2025 Sundays', () => {
    const sundays2025 = [
      // July 2025
      { month: 6, days: [6, 13, 20, 27] },
      // August 2025
      { month: 7, days: [3, 10, 17, 24, 31] },
      // September 2025
      { month: 8, days: [7, 14, 21, 28] },
      // October 2025
      { month: 9, days: [5, 12, 19, 26] },
      // November 2025
      { month: 10, days: [2, 9, 16, 23, 30] },
      // December 2025
      { month: 11, days: [7, 14, 21, 28] },
    ];

    sundays2025.forEach(({ month, days }) => {
      const monthName = new Date(2025, month, 1).toLocaleString('en-US', { month: 'long' });

      days.forEach(day => {
        test(`${monthName} ${day}, 2025 should be Sunday`, () => {
          const date = new Date(2025, month, day);
          expect(date.getDay()).toBe(0); // 0 = Sunday
          expect(date.toDateString()).toContain('Sun');
        });
      });
    });
  });

  describe('2026 January Sundays', () => {
    const januarySundays = [4, 11, 18, 25];

    januarySundays.forEach(day => {
      test(`January ${day}, 2026 should be Sunday`, () => {
        const date = new Date(2026, 0, day); // 0 = January
        expect(date.getDay()).toBe(0); // 0 = Sunday
        expect(date.toDateString()).toContain('Sun');
      });
    });
  });

  describe('2025 Saturdays (day before each Sunday)', () => {
    const saturdays2025 = [
      // July 2025
      { month: 6, days: [5, 12, 19, 26] },
      // August 2025
      { month: 7, days: [2, 9, 16, 23, 30] },
      // September 2025
      { month: 8, days: [6, 13, 20, 27] },
      // October 2025
      { month: 9, days: [4, 11, 18, 25] },
      // November 2025
      { month: 10, days: [1, 8, 15, 22, 29] },
      // December 2025
      { month: 11, days: [6, 13, 20, 27] },
    ];

    saturdays2025.forEach(({ month, days }) => {
      const monthName = new Date(2025, month, 1).toLocaleString('en-US', { month: 'long' });

      days.forEach(day => {
        test(`${monthName} ${day}, 2025 should be Saturday`, () => {
          const date = new Date(2025, month, day);
          expect(date.getDay()).toBe(6); // 6 = Saturday
          expect(date.toDateString()).toContain('Sat');
        });
      });
    });
  });

  describe('2026 January Saturdays', () => {
    const januarySaturdays = [3, 10, 17, 24, 31];

    januarySaturdays.forEach(day => {
      test(`January ${day}, 2026 should be Saturday`, () => {
        const date = new Date(2026, 0, day); // 0 = January
        expect(date.getDay()).toBe(6); // 6 = Saturday
        expect(date.toDateString()).toContain('Sat');
      });
    });
  });

  describe('Weekend date calculations', () => {
    test('each Sunday should be exactly one day after Saturday', () => {
      // Test a few examples
      const testCases = [
        { year: 2025, month: 6, saturday: 5, sunday: 6 }, // July 5-6
        { year: 2025, month: 6, saturday: 12, sunday: 13 }, // July 12-13
        { year: 2025, month: 7, saturday: 2, sunday: 3 }, // August 2-3
        { year: 2025, month: 11, saturday: 27, sunday: 28 }, // December 27-28
        { year: 2026, month: 0, saturday: 3, sunday: 4 }, // January 3-4
      ];

      testCases.forEach(({ year, month, saturday, sunday }) => {
        const satDate = new Date(year, month, saturday);
        const sunDate = new Date(year, month, sunday);

        expect(satDate.getDay()).toBe(6); // Saturday
        expect(sunDate.getDay()).toBe(0); // Sunday

        // Sunday should be exactly one day after Saturday
        const nextDay = new Date(satDate);
        nextDay.setDate(satDate.getDate() + 1);
        expect(nextDay.toDateString()).toBe(sunDate.toDateString());
      });
    });

    test('verify second Saturdays for Isha Satsang', () => {
      const secondSaturdays = [
        { year: 2025, month: 6, day: 12 }, // July 12
        { year: 2025, month: 7, day: 9 }, // August 9
        { year: 2025, month: 8, day: 13 }, // September 13
        { year: 2025, month: 9, day: 11 }, // October 11
        { year: 2025, month: 10, day: 8 }, // November 8
        { year: 2025, month: 11, day: 13 }, // December 13
        { year: 2026, month: 0, day: 10 }, // January 10
      ];

      secondSaturdays.forEach(({ year, month, day }) => {
        const date = new Date(year, month, day);
        const monthName = date.toLocaleString('en-US', { month: 'long' });

        expect(date.getDay()).toBe(6); // Should be Saturday

        // Verify it's the second Saturday
        const dayOfMonth = date.getDate();
        const weekNumber = Math.ceil(dayOfMonth / 7);
        expect(weekNumber).toBe(2);

        // Should be between 8th and 14th
        expect(dayOfMonth).toBeGreaterThanOrEqual(8);
        expect(dayOfMonth).toBeLessThanOrEqual(14);

        console.log(`✓ ${monthName} ${day}, ${year} is the 2nd Saturday`);
      });
    });
  });

  describe('Date edge cases', () => {
    test('should handle month boundaries correctly', () => {
      // August 31, 2025 is Sunday
      const aug31 = new Date(2025, 7, 31);
      expect(aug31.getDay()).toBe(0);

      // Next day should be September 1 (Monday)
      const sep1 = new Date(2025, 8, 1);
      expect(sep1.getDay()).toBe(1);

      // November 30, 2025 is Sunday
      const nov30 = new Date(2025, 10, 30);
      expect(nov30.getDay()).toBe(0);

      // Next day should be December 1 (Monday)
      const dec1 = new Date(2025, 11, 1);
      expect(dec1.getDay()).toBe(1);
    });

    test('should handle year boundary correctly', () => {
      // December 28, 2025 is Sunday
      const dec28 = new Date(2025, 11, 28);
      expect(dec28.getDay()).toBe(0);

      // January 4, 2026 is Sunday
      const jan4 = new Date(2026, 0, 4);
      expect(jan4.getDay()).toBe(0);

      // Check the week between years
      const dec29 = new Date(2025, 11, 29); // Monday
      const dec30 = new Date(2025, 11, 30); // Tuesday
      const dec31 = new Date(2025, 11, 31); // Wednesday
      const jan1 = new Date(2026, 0, 1); // Thursday
      const jan2 = new Date(2026, 0, 2); // Friday
      const jan3 = new Date(2026, 0, 3); // Saturday

      expect(dec29.getDay()).toBe(1);
      expect(dec30.getDay()).toBe(2);
      expect(dec31.getDay()).toBe(3);
      expect(jan1.getDay()).toBe(4);
      expect(jan2.getDay()).toBe(5);
      expect(jan3.getDay()).toBe(6);
    });
  });

  describe('getWeekendDates utility validation', () => {
    // Import the actual utility function
    const { getWeekendDates } = require('../src/utils/dateUtils');

    test('should return correct weekend dates for reference dates', () => {
      // Test with a few Fridays as reference
      const testCases = [
        { reference: new Date(2025, 6, 4), expectedSat: 5, expectedSun: 6 }, // July 4 (Fri)
        { reference: new Date(2025, 6, 11), expectedSat: 12, expectedSun: 13 }, // July 11 (Fri)
        { reference: new Date(2025, 7, 1), expectedSat: 2, expectedSun: 3 }, // Aug 1 (Fri)
        { reference: new Date(2025, 11, 26), expectedSat: 27, expectedSun: 28 }, // Dec 26 (Fri)
      ];

      testCases.forEach(({ reference, expectedSat, expectedSun }) => {
        const { saturday, sunday } = getWeekendDates(reference);

        expect(saturday.getDate()).toBe(expectedSat);
        expect(sunday.getDate()).toBe(expectedSun);
        expect(saturday.getDay()).toBe(6);
        expect(sunday.getDay()).toBe(0);
      });
    });
  });
});
