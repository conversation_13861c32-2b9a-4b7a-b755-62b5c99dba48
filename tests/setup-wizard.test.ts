import { SetupWizard } from '../src/utils/setupWizard';
import * as fs from 'fs';
import * as readline from 'readline';
import { promisify } from 'util';

jest.mock('fs');
jest.mock('readline');

describe('SetupWizard', () => {
  let mockInterface: any;
  let consoleLogSpy: jest.SpyInstance;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    mockInterface = {
      question: jest.fn(),
      close: jest.fn()
    };
    
    (readline.createInterface as jest.Mock).mockReturnValue(mockInterface);
    consoleLogSpy = jest.spyOn(console, 'log').mockImplementation();
  });
  
  afterEach(() => {
    consoleLogSpy.mockRestore();
  });

  describe('checkAndSetup', () => {
    test('should not run setup if .env file exists', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      
      const wizard = new SetupWizard();
      await wizard.checkAndSetup();
      
      expect(fs.existsSync).toHaveBeenCalledWith('.env');
      expect(mockInterface.question).not.toHaveBeenCalled();
    });

    test('should run setup if .env file does not exist', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      (fs.writeFileSync as jest.Mock).mockReturnValue(undefined);
      
      // Mock user inputs
      mockInterface.question
        .mockImplementationOnce((q: string, cb: Function) => cb('ACxxxxx')) // Account SID
        .mockImplementationOnce((q: string, cb: Function) => cb('auth_token')) // Auth token
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+***********')) // From number
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+**********')) // To number
        .mockImplementationOnce((q: string, cb: Function) => cb('')) // Google Doc ID (use default)
        .mockImplementationOnce((q: string, cb: Function) => cb('n')); // Location bypass
      
      const wizard = new SetupWizard();
      await wizard.checkAndSetup();
      
      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('🚀 VIP Setup Wizard'));
      expect(mockInterface.question).toHaveBeenCalledTimes(6);
      expect(fs.writeFileSync).toHaveBeenCalled();
    });

    test('should validate Twilio Account SID format', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      const wizard = new SetupWizard();
      
      // Test invalid SID
      expect(wizard.validateAccountSid('invalid')).toBe(false);
      expect(wizard.validateAccountSid('')).toBe(false);
      
      // Test valid SID
      expect(wizard.validateAccountSid('ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx')).toBe(true);
      expect(wizard.validateAccountSid('AC**********abcdef**********abcdef')).toBe(true);
    });

    test('should validate WhatsApp number format', async () => {
      const wizard = new SetupWizard();
      
      // Test invalid numbers
      expect(wizard.validateWhatsAppNumber('+**********')).toBe(false);
      expect(wizard.validateWhatsAppNumber('**********')).toBe(false);
      expect(wizard.validateWhatsAppNumber('')).toBe(false);
      
      // Test valid numbers
      expect(wizard.validateWhatsAppNumber('whatsapp:+**********')).toBe(true);
      expect(wizard.validateWhatsAppNumber('whatsapp:+***********')).toBe(true);
    });

    test('should create .env file with correct content', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      let savedContent = '';
      (fs.writeFileSync as jest.Mock).mockImplementation((path: string, content: string) => {
        savedContent = content;
      });
      
      mockInterface.question
        .mockImplementationOnce((q: string, cb: Function) => cb('ACxxxxx'))
        .mockImplementationOnce((q: string, cb: Function) => cb('auth_token'))
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+***********'))
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+**********'))
        .mockImplementationOnce((q: string, cb: Function) => cb('custom_doc_id'))
        .mockImplementationOnce((q: string, cb: Function) => cb('y'));
      
      const wizard = new SetupWizard();
      await wizard.checkAndSetup();
      
      expect(savedContent).toContain('TWILIO_ACCOUNT_SID=ACxxxxx');
      expect(savedContent).toContain('TWILIO_AUTH_TOKEN=auth_token');
      expect(savedContent).toContain('TWILIO_WHATSAPP_FROM=whatsapp:+***********');
      expect(savedContent).toContain('WHATSAPP_TO_NUMBER=whatsapp:+**********');
      expect(savedContent).toContain('VIP_GOOGLE_DOC_ID=custom_doc_id');
      expect(savedContent).toContain('VIP_BYPASS_LOCATION_CHECK=true');
    });

    test('should add .env to .gitignore if not already present', async () => {
      (fs.existsSync as jest.Mock)
        .mockReturnValueOnce(false) // .env doesn't exist
        .mockReturnValueOnce(true); // .gitignore exists
      
      (fs.readFileSync as jest.Mock).mockReturnValue('node_modules\n');
      (fs.appendFileSync as jest.Mock).mockReturnValue(undefined);
      
      mockInterface.question
        .mockImplementationOnce((q: string, cb: Function) => cb('ACxxxxx'))
        .mockImplementationOnce((q: string, cb: Function) => cb('auth_token'))
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+***********'))
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+**********'))
        .mockImplementationOnce((q: string, cb: Function) => cb(''))
        .mockImplementationOnce((q: string, cb: Function) => cb('n'));
      
      const wizard = new SetupWizard();
      await wizard.checkAndSetup();
      
      expect(fs.appendFileSync).toHaveBeenCalledWith('.gitignore', '\n# Environment variables\n.env\n.env.local\n.env.*.local\n');
    });

    test('should not add .env to .gitignore if already present', async () => {
      (fs.existsSync as jest.Mock)
        .mockReturnValueOnce(false) // .env doesn't exist
        .mockReturnValueOnce(true); // .gitignore exists
      
      (fs.readFileSync as jest.Mock).mockReturnValue('node_modules\n.env\n');
      (fs.appendFileSync as jest.Mock).mockReturnValue(undefined);
      
      mockInterface.question
        .mockImplementationOnce((q: string, cb: Function) => cb('ACxxxxx'))
        .mockImplementationOnce((q: string, cb: Function) => cb('auth_token'))
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+***********'))
        .mockImplementationOnce((q: string, cb: Function) => cb('whatsapp:+**********'))
        .mockImplementationOnce((q: string, cb: Function) => cb(''))
        .mockImplementationOnce((q: string, cb: Function) => cb('n'));
      
      const wizard = new SetupWizard();
      await wizard.checkAndSetup();
      
      expect(fs.appendFileSync).not.toHaveBeenCalled();
    });

    test('should handle readline errors gracefully', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      mockInterface.question.mockImplementationOnce((q: string, cb: Function) => {
        throw new Error('Readline error');
      });
      
      const wizard = new SetupWizard();
      
      await expect(wizard.checkAndSetup()).rejects.toThrow('Setup failed');
      expect(mockInterface.close).toHaveBeenCalled();
    });
  });

  describe('isFirstTimeSetup', () => {
    test('should return true if no .env file exists', () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
      
      const wizard = new SetupWizard();
      expect(wizard.isFirstTimeSetup()).toBe(true);
    });

    test('should return false if .env file exists', () => {
      (fs.existsSync as jest.Mock).mockReturnValue(true);
      
      const wizard = new SetupWizard();
      expect(wizard.isFirstTimeSetup()).toBe(false);
    });
  });
});