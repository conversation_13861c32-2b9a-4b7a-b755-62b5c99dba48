import fc from 'fast-check';
import { CalendarParser } from '../../src/parser/CalendarParser';
import { EventType, Location } from '../../src/parser/types';
import { logger } from '../../src/utils/logger';

// Enable test logging
beforeAll(() => {
  logger.setTestMode(true);
});

describe('CalendarParser Property-Based Tests', () => {
  const parser = new CalendarParser();

  describe('Date Parsing Properties', () => {
    test('should parse any valid date format and preserve the date', () => {
      fc.assert(
        fc.property(
          fc.date({
            min: new Date(2025, 0, 1),
            max: new Date(2030, 11, 31),
          }),
          date => {
            const months = [
              'January',
              'February',
              'March',
              'April',
              'May',
              'June',
              'July',
              'August',
              'September',
              'October',
              'November',
              'December',
            ];
            const weekdays = [
              'Sunday',
              'Monday',
              'Tuesday',
              'Wednesday',
              'Thursday',
              'Friday',
              'Saturday',
            ];

            const monthName = months[date.getMonth()];
            const weekday = weekdays[date.getDay()];
            const day = date.getDate();
            const year = date.getFullYear();

            // Test various date formats
            const formats = [
              `${weekday} ${monthName} ${day}`,
              `${monthName} ${day}, ${year}`,
              `${weekday}, ${monthName} ${day}, ${year}`,
              `${monthName.substring(0, 3)} ${day}, ${year}`,
            ];

            for (const format of formats) {
              const calendarText = `
SOUTH BAY VIPASSANA HALL
3080 Olcott St #D232, Santa Clara, CA

${format} (9:00 AM - 12:00 PM) - 3 HOUR SIT
              `;

              const events = parser.parseCalendar(calendarText);

              // Should parse exactly one event
              expect(events).toHaveLength(1);

              // Date should match
              const parsedDate = events[0].date;
              expect(parsedDate.getFullYear()).toBe(date.getFullYear());
              expect(parsedDate.getMonth()).toBe(date.getMonth());
              expect(parsedDate.getDate()).toBe(date.getDate());
            }
          }
        ),
        { numRuns: 100 }
      );
    });

    test('should handle multiple dates on same line correctly', () => {
      fc.assert(
        fc.property(
          fc.array(fc.date({ min: new Date(2025, 0, 1), max: new Date(2025, 11, 31) }), {
            minLength: 2,
            maxLength: 5,
          }),
          dates => {
            // Sort dates to ensure chronological order
            dates.sort((a, b) => a.getTime() - b.getTime());

            const dateStrings = dates.map(
              d =>
                `${['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][d.getMonth()]} ${d.getDate()}`
            );

            const calendarText = `
SOUTH BAY VIPASSANA HALL
3080 Olcott St #D232, Santa Clara, CA

Saturday ${dateStrings.join(', ')}, 2025 (9:15 AM - 4:30 PM) - 1 Day SIT
            `;

            const events = parser.parseCalendar(calendarText);

            // Should parse correct number of events
            expect(events.length).toBe(dates.length);

            // All events should be 1-day sits
            events.forEach(event => {
              expect(event.type).toBe(EventType.ONE_DAY);
            });
          }
        )
      );
    });
  });

  describe('Time Parsing Properties', () => {
    test('should parse any valid time range', () => {
      fc.assert(
        fc.property(
          fc.integer({ min: 1, max: 12 }), // start hour
          fc.integer({ min: 0, max: 59 }), // start minute
          fc.integer({ min: 1, max: 12 }), // end hour
          fc.integer({ min: 0, max: 59 }), // end minute
          fc.boolean(), // AM/PM for start
          fc.boolean(), // AM/PM for end
          (startHour, startMin, endHour, endMin, startAM, endAM) => {
            const formatTime = (hour: number, min: number, isAM: boolean) =>
              `${hour}:${min.toString().padStart(2, '0')} ${isAM ? 'AM' : 'PM'}`;

            const startTime = formatTime(startHour, startMin, startAM);
            const endTime = formatTime(endHour, endMin, endAM);

            const calendarText = `
SOUTH BAY VIPASSANA HALL
3080 Olcott St #D232, Santa Clara, CA

Saturday July 12 (${startTime} - ${endTime}) - GROUP SIT
            `;

            const events = parser.parseCalendar(calendarText);

            if (events.length > 0) {
              expect(events[0].startTime).toBe(startTime);
              expect(events[0].endTime).toBe(endTime);
            }
          }
        )
      );
    });
  });

  describe('Location Parsing Properties', () => {
    test('should correctly associate events with locations', () => {
      const locations = [
        { key: Location.SOUTH_BAY, header: 'SOUTH BAY VIPASSANA HALL' },
        { key: Location.EAST_BAY, header: 'EAST BAY VIPASSANA HALL' },
        { key: Location.CENTER_LAND, header: 'CENTER LAND' },
      ];

      fc.assert(
        fc.property(
          fc.oneof(...locations.map(l => fc.constant(l))),
          fc.array(
            fc.record({
              date: fc.constant('July 12'),
              type: fc.oneof(
                fc.constant('3 HOUR SIT'),
                fc.constant('1 Day SIT'),
                fc.constant('GROUP SIT')
              ),
            }),
            { minLength: 1, maxLength: 5 }
          ),
          (location, eventSpecs) => {
            let calendarText = `${location.header}\n`;
            calendarText += parser.getLocationDetails(location.key)?.address || '' + '\n\n';

            eventSpecs.forEach(spec => {
              calendarText += `Saturday ${spec.date} (9:00 AM - 12:00 PM) - ${spec.type}\n`;
            });

            const events = parser.parseCalendar(calendarText);

            // All events should have the correct location
            events.forEach(event => {
              expect(event.location).toBe(location.key);
            });

            // Should parse all events
            expect(events.length).toBe(eventSpecs.length);
          }
        )
      );
    });
  });

  describe('Event Type Properties', () => {
    test('should correctly parse all event types', () => {
      const eventTypeMap = [
        { text: '3 HOUR SIT', type: EventType.THREE_HOUR },
        { text: '1 Day SIT', type: EventType.ONE_DAY },
        { text: 'GROUP SIT', type: EventType.GROUP_SIT },
        { text: 'SERVICE PERIOD', type: EventType.SERVICE },
      ];

      fc.assert(
        fc.property(fc.shuffledSubarray(eventTypeMap, { minLength: 1 }), selectedTypes => {
          let calendarText = `SOUTH BAY VIPASSANA HALL\n\n`;

          selectedTypes.forEach((typeInfo, index) => {
            calendarText += `Saturday July ${index + 1} (9:00 AM - 12:00 PM) - ${typeInfo.text}\n`;
          });

          const events = parser.parseCalendar(calendarText);

          expect(events.length).toBe(selectedTypes.length);

          events.forEach((event, index) => {
            expect(event.type).toBe(selectedTypes[index].type);
          });
        })
      );
    });
  });

  describe('Robustness Properties', () => {
    test('should never throw on any string input', () => {
      fc.assert(
        fc.property(fc.string(), input => {
          expect(() => parser.parseCalendar(input)).not.toThrow();
        }),
        { numRuns: 1000 }
      );
    });

    test('should handle arbitrary whitespace', () => {
      fc.assert(
        fc.property(
          fc.array(fc.oneof(fc.constant(' '), fc.constant('\t'), fc.constant('\n')), {
            minLength: 0,
            maxLength: 10,
          }),
          whitespaceArray => {
            const whitespace = whitespaceArray.join('');
            const calendarText = `${whitespace}SOUTH BAY VIPASSANA HALL${whitespace}
${whitespace}
${whitespace}Saturday July 12 (9:00 AM - 12:00 PM) - 3 HOUR SIT${whitespace}`;

            const events = parser.parseCalendar(calendarText);

            // Should still parse the event correctly
            if (events.length > 0) {
              expect(events[0].type).toBe(EventType.THREE_HOUR);
            }
          }
        )
      );
    });

    test('should preserve event order', () => {
      fc.assert(
        fc.property(
          fc.array(fc.integer({ min: 1, max: 28 }), { minLength: 2, maxLength: 10 }),
          days => {
            // Remove duplicates and sort
            const uniqueDays = [...new Set(days)].sort((a, b) => a - b);

            let calendarText = 'SOUTH BAY VIPASSANA HALL\n\n';
            uniqueDays.forEach(day => {
              calendarText += `Saturday July ${day} (9:00 AM - 12:00 PM) - 3 HOUR SIT\n`;
            });

            const events = parser.parseCalendar(calendarText);

            // Events should be in chronological order
            for (let i = 1; i < events.length; i++) {
              expect(events[i].date.getTime()).toBeGreaterThanOrEqual(events[i - 1].date.getTime());
            }
          }
        )
      );
    });
  });

  describe('Logging Properties', () => {
    test('should log performance metrics for various input sizes', () => {
      fc.assert(
        fc.property(fc.integer({ min: 1, max: 100 }), eventCount => {
          let calendarText = 'SOUTH BAY VIPASSANA HALL\n\n';

          for (let i = 1; i <= eventCount; i++) {
            calendarText += `Saturday July ${(i % 28) + 1} (9:00 AM - 12:00 PM) - 3 HOUR SIT\n`;
          }

          const startTime = performance.now();
          const events = parser.parseCalendar(calendarText);
          const duration = performance.now() - startTime;

          logger.debug(`Parsed ${eventCount} events in ${duration.toFixed(2)}ms`, {
            module: 'PropertyTest',
            eventCount,
            duration,
            eventsPerMs: eventCount / duration,
          });

          // Performance assertion: should parse at least 10 events per ms
          expect(eventCount / duration).toBeGreaterThan(10);
        })
      );
    });
  });
});
