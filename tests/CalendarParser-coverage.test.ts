import { CalendarParser } from '../src/parser/CalendarParser';
import { EventType, Location } from '../src/parser/types';

describe('CalendarParser - Full Coverage Tests', () => {
  let parser: CalendarParser;

  beforeEach(() => {
    parser = new CalendarParser();
  });

  describe('Edge case date parsing - uncovered lines 150-152', () => {
    test('should parse date with weekday, month, day, year format', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: Monday July 14 2025, Tuesday August 15 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBe(2);
      expect(events[0].date.getMonth()).toBe(6); // July
      expect(events[0].date.getDate()).toBe(14);
      expect(events[1].date.getMonth()).toBe(7); // August
      expect(events[1].date.getDate()).toBe(15);
    });

    test('should parse complex date patterns with extra words', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: On July 14 2025, Also August 15 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBeGreaterThan(0);
    });
  });

  describe('Service time parsing edge cases', () => {
    test('should handle service times with various formats', () => {
      const testCalendar = `
CENTER LAND (Gilroy)
Address: 9201 El Matador Drive, Gilroy, CA

SERVICE EVENTS:
Saturday July 26: Morning service only
Sunday July 27: Full day of service activities
Saturday August 2: Service from dawn to dusk
Sunday August 3: Service (flexible timing)
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBeGreaterThan(0);
      events.forEach(event => {
        expect(event.location).toBe(Location.CENTER_LAND);
      });
    });

    test('should handle malformed service descriptions', () => {
      const testCalendar = `
CENTER LAND (Gilroy)
Address: 9201 El Matador Drive, Gilroy, CA

SERVICE EVENTS:
Saturday July 26: Service
Sunday July 27:
Saturday August 2: (no description)
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBeGreaterThan(0);
    });
  });

  describe('Location detection edge cases', () => {
    test('should handle empty location arrays', () => {
      const events: any[] = [];
      const result = parser.getEventsForDate(events, new Date('2025-07-12'));
      expect(result).toEqual([]);
    });

    test('should handle location with no events', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
(No events scheduled)

ONE-DAY COURSES (9:00 AM – 4:00 PM):
(No events scheduled)
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events).toEqual([]);
    });
  });

  describe('Invalid input handling', () => {
    test('should handle null/undefined calendar data', () => {
      expect(() => parser.parseCalendar(null as any)).not.toThrow();
      expect(() => parser.parseCalendar(undefined as any)).not.toThrow();
      expect(parser.parseCalendar(null as any)).toEqual([]);
      expect(parser.parseCalendar(undefined as any)).toEqual([]);
    });

    test('should handle calendar with no recognizable content', () => {
      const testCalendar = `
This is just random text
With no dates or locations
Nothing to parse here
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events).toEqual([]);
    });

    test('should handle extremely long lines', () => {
      const longLine = 'Saturday July 12 ' + 'A'.repeat(5000) + ' 2025';
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
${longLine}
      `;

      expect(() => parser.parseCalendar(testCalendar)).not.toThrow();
    });
  });

  describe('Time parsing edge cases', () => {
    test('should handle times with seconds', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00:00 AM – 12:00:00 PM):
Saturday: July 12 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBe(1);
      expect(events[0].startTime).toBe('9:00 AM');
      expect(events[0].endTime).toBe('12:00 PM');
    });

    test('should handle 24-hour time format', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (09:00 – 12:00):
Saturday: July 12 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBe(1);
      expect(events[0].startTime).toBeDefined();
      expect(events[0].endTime).toBeDefined();
    });
  });

  describe('Multiple date formats on same line', () => {
    test('should parse mixed date formats', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: July 12, Aug 16 2025, September 20th, Oct 25
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBeGreaterThanOrEqual(2);
    });

    test('should handle dates with special characters', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: July-12-2025, Aug/16/2025, Sept.20.2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBeGreaterThan(0);
    });
  });

  describe('Year inference edge cases', () => {
    test('should infer current year when not specified', () => {
      const currentYear = new Date().getFullYear();
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: December 25, December 31
      `;

      const events = parser.parseCalendar(testCalendar);
      events.forEach(event => {
        expect(event.date.getFullYear()).toBe(currentYear);
      });
    });
  });

  describe('Special event types', () => {
    test('should handle events with no time specified', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

SPECIAL EVENTS:
Saturday July 12: All-day mindfulness retreat
Sunday July 13: Community potluck
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBeGreaterThan(0);
    });
  });

  describe('Address parsing edge cases', () => {
    test('should handle addresses with suite numbers and special characters', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St, Suite #D-232, Santa Clara, CA 95054

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: July 12 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBe(1);
      expect(events[0].address).toContain('3080 Olcott St');
    });

    test('should handle missing address', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: July 12 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      expect(events.length).toBe(1);
      expect(events[0].address).toBeDefined();
    });
  });

  describe('Deduplication edge cases', () => {
    test('should deduplicate exact duplicate events', () => {
      const testCalendar = `
SOUTH BAY VIPASSANA HALL (Santa Clara)
Address: 3080 Olcott St #D232, Santa Clara, CA

THREE-HOUR SITS (9:00 AM – 12:00 PM):
Saturday: July 12 2025, July 12 2025, July 12 2025

ONE-DAY COURSES (9:00 AM – 4:00 PM):
Saturday: July 12 2025
      `;

      const events = parser.parseCalendar(testCalendar);
      const july12Events = events.filter(e => e.date.getMonth() === 6 && e.date.getDate() === 12);
      // Should have at most 2 events (one 3-hour, one 1-day)
      expect(july12Events.length).toBeLessThanOrEqual(2);
    });
  });

  describe('GetLocationDetails coverage', () => {
    test('should handle all location types', () => {
      expect(parser.getLocationDetails(Location.SOUTH_BAY)).toMatchObject({
        name: 'South Bay Vipassana Hall',
      });
      expect(parser.getLocationDetails(Location.EAST_BAY)).toMatchObject({
        name: 'East Bay Vipassana Hall',
      });
      expect(parser.getLocationDetails(Location.CENTER_LAND)).toMatchObject({
        name: 'Center Land',
      });
      expect(parser.getLocationDetails('unknown' as Location)).toMatchObject({
        name: 'Unknown Location',
      });
    });
  });
});
