import {
  main,
  handleWeekendCommand,
  handleFetchCommand,
  handleIshaSatsangCommand,
  handleWhatsAppCommand,
  handleCustomMessage,
  handleSchedulerCommand,
  showHelp,
} from '../src/index';
import { WeekendEventService } from '../src/services/WeekendEventService';
import { WeeklyScheduler } from '../src/scheduler/WeeklyScheduler';
import { WebFetcher } from '../src/utils/webFetcher';
import * as ishaSatsangUtils from '../src/utils/ishaSatsangUtils';
import { WhatsAppService } from '../src/services/WhatsAppService';
import * as fs from 'fs';

// Mock all dependencies
jest.mock('dotenv', () => ({
  config: jest.fn(),
}));
jest.mock('../src/services/WeekendEventService');
jest.mock('../src/scheduler/WeeklyScheduler');
jest.mock('../src/utils/webFetcher');
jest.mock('../src/utils/ishaSatsangUtils');
jest.mock('../src/services/WhatsAppService');
jest.mock('fs');

describe('Index Functions Tests', () => {
  let mockConsoleLog: jest.SpyInstance;
  let mockConsoleError: jest.SpyInstance;
  let mockProcessExit: jest.SpyInstance;
  let originalArgv: string[];

  beforeEach(() => {
    jest.clearAllMocks();
    mockConsoleLog = jest.spyOn(console, 'log').mockImplementation();
    mockConsoleError = jest.spyOn(console, 'error').mockImplementation();
    mockProcessExit = jest.spyOn(process, 'exit').mockImplementation((code?: any) => {
      throw new Error(`Process.exit: ${code}`);
    });
    originalArgv = process.argv;
  });

  afterEach(() => {
    jest.restoreAllMocks();
    process.argv = originalArgv;
  });

  describe('main function', () => {
    test('should handle weekend command', async () => {
      process.argv = ['node', 'index.js', 'weekend'];
      const mockService = {
        formatWeekendReport: jest.fn().mockResolvedValue('Weekend report'),
      };
      (WeekendEventService as jest.Mock).mockImplementation(() => mockService);

      await main();

      expect(mockService.formatWeekendReport).toHaveBeenCalled();
      expect(mockConsoleLog).toHaveBeenCalledWith('Weekend report');
    });

    test('should handle w shorthand', async () => {
      process.argv = ['node', 'index.js', 'w'];
      const mockService = {
        formatWeekendReport: jest.fn().mockResolvedValue('Weekend report'),
      };
      (WeekendEventService as jest.Mock).mockImplementation(() => mockService);

      await main();

      expect(mockService.formatWeekendReport).toHaveBeenCalled();
    });

    test('should handle help command', async () => {
      process.argv = ['node', 'index.js', 'help'];
      await main();
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Vipassana Calendar Parser (VIP)')
      );
    });

    test('should handle h shorthand', async () => {
      process.argv = ['node', 'index.js', 'h'];
      await main();
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Vipassana Calendar Parser (VIP)')
      );
    });

    test('should show help with no args', async () => {
      process.argv = ['node', 'index.js'];
      await main();
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Vipassana Calendar Parser (VIP)')
      );
    });

    test('should handle unknown command', async () => {
      process.argv = ['node', 'index.js', 'unknown'];

      try {
        await main();
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith('Unknown command: unknown');
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Vipassana Calendar Parser (VIP)')
      );
    });

    test('should handle fetch command', async () => {
      process.argv = ['node', 'index.js', 'fetch', 'https://example.com'];
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(false);
      (WebFetcher.fetchUrl as jest.Mock).mockResolvedValue('Content');

      await main();

      expect(WebFetcher.fetchUrl).toHaveBeenCalledWith('https://example.com');
      expect(mockConsoleLog).toHaveBeenCalledWith('Content');
    });

    test('should handle f shorthand', async () => {
      process.argv = ['node', 'index.js', 'f', 'https://example.com'];
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(false);
      (WebFetcher.fetchUrl as jest.Mock).mockResolvedValue('Content');

      await main();

      expect(WebFetcher.fetchUrl).toHaveBeenCalled();
    });

    test('should handle isha command', async () => {
      process.argv = ['node', 'index.js', 'isha'];
      (ishaSatsangUtils.formatSecondSaturdays as jest.Mock).mockReturnValue('Satsang dates');

      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Isha Satsang Schedule'));
    });

    test('should handle satsang alias', async () => {
      process.argv = ['node', 'index.js', 'satsang'];
      (ishaSatsangUtils.formatSecondSaturdays as jest.Mock).mockReturnValue('Satsang dates');

      await main();

      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Isha Satsang Schedule'));
    });

    test('should handle whatsapp command', async () => {
      process.argv = ['node', 'index.js', 'whatsapp', 'test'];
      const mockScheduler = {
        sendTestMessage: jest.fn().mockResolvedValue(undefined),
      };
      (WeeklyScheduler as jest.Mock).mockImplementation(() => mockScheduler);

      await main();

      expect(mockScheduler.sendTestMessage).toHaveBeenCalled();
    });

    test('should handle wa shorthand', async () => {
      process.argv = ['node', 'index.js', 'wa', 'test'];
      const mockScheduler = {
        sendTestMessage: jest.fn().mockResolvedValue(undefined),
      };
      (WeeklyScheduler as jest.Mock).mockImplementation(() => mockScheduler);

      await main();

      expect(mockScheduler.sendTestMessage).toHaveBeenCalled();
    });

    test('should handle schedule command', async () => {
      process.argv = ['node', 'index.js', 'schedule', 'start'];
      const mockScheduler = {
        start: jest.fn(),
      };
      (WeeklyScheduler as jest.Mock).mockImplementation(() => mockScheduler);

      await main();

      expect(mockScheduler.start).toHaveBeenCalled();
    });

    test('should handle scheduler alias', async () => {
      process.argv = ['node', 'index.js', 'scheduler', 'start'];
      const mockScheduler = {
        start: jest.fn(),
      };
      (WeeklyScheduler as jest.Mock).mockImplementation(() => mockScheduler);

      await main();

      expect(mockScheduler.start).toHaveBeenCalled();
    });
  });

  describe('handleWeekendCommand', () => {
    test('should handle weekend with default date', async () => {
      const mockService = {
        formatWeekendReport: jest.fn().mockResolvedValue('Weekend report'),
      };

      await handleWeekendCommand(mockService as any, []);

      expect(mockService.formatWeekendReport).toHaveBeenCalledWith(expect.any(Date));
      expect(mockConsoleLog).toHaveBeenCalledWith('Weekend report');
    });

    test('should handle custom date', async () => {
      const mockService = {
        formatWeekendReport: jest.fn().mockResolvedValue('Weekend report'),
      };

      await handleWeekendCommand(mockService as any, ['2025-07-11']);

      const callArg = mockService.formatWeekendReport.mock.calls[0][0];
      expect(callArg).toBeInstanceOf(Date);
      expect(callArg.toISOString().substring(0, 10)).toBe('2025-07-11');
    });

    test('should handle invalid date', async () => {
      const mockService = {
        formatWeekendReport: jest.fn(),
      };

      try {
        await handleWeekendCommand(mockService as any, ['invalid-date']);
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith('Invalid date format: invalid-date');
      expect(mockConsoleError).toHaveBeenCalledWith(
        'Use format like: "2025-07-11" or "July 11, 2025"'
      );
    });

    test('should handle service error', async () => {
      const mockService = {
        formatWeekendReport: jest.fn().mockRejectedValue(new Error('Service error')),
      };

      try {
        await handleWeekendCommand(mockService as any, []);
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith(
        'Error generating weekend report:',
        expect.any(Error)
      );
    });

    test('should handle multi-word date', async () => {
      const mockService = {
        formatWeekendReport: jest.fn().mockResolvedValue('Weekend report'),
      };

      await handleWeekendCommand(mockService as any, ['July', '11,', '2025']);

      const callArg = mockService.formatWeekendReport.mock.calls[0][0];
      expect(callArg).toBeInstanceOf(Date);
      expect(callArg.getMonth()).toBe(6); // July is month 6 (0-indexed)
    });
  });

  describe('handleFetchCommand', () => {
    test('should require URL', async () => {
      try {
        await handleFetchCommand([]);
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith('Please provide a URL');
    });

    test('should fetch Google Doc', async () => {
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(true);
      (WebFetcher.extractDocumentId as jest.Mock).mockReturnValue('123');
      (WebFetcher.fetchGoogleDoc as jest.Mock).mockResolvedValue('Doc content');

      await handleFetchCommand(['https://docs.google.com/document/d/123/edit']);

      expect(mockConsoleLog).toHaveBeenCalledWith('Fetching Google Doc...');
      expect(WebFetcher.fetchGoogleDoc).toHaveBeenCalledWith('123');
      expect(mockConsoleLog).toHaveBeenCalledWith('Doc content');
    });

    test('should handle invalid Google Doc URL', async () => {
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(true);
      (WebFetcher.extractDocumentId as jest.Mock).mockReturnValue(null);

      try {
        await handleFetchCommand(['https://docs.google.com/invalid']);
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith('Invalid Google Doc URL');
    });

    test('should fetch regular URL', async () => {
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(false);
      (WebFetcher.fetchUrl as jest.Mock).mockResolvedValue('Page content');

      await handleFetchCommand(['https://example.com']);

      expect(mockConsoleLog).toHaveBeenCalledWith('Fetching URL...');
      expect(WebFetcher.fetchUrl).toHaveBeenCalledWith('https://example.com');
      expect(mockConsoleLog).toHaveBeenCalledWith('\n--- Fetched Content ---');
      expect(mockConsoleLog).toHaveBeenCalledWith('Page content');
      expect(mockConsoleLog).toHaveBeenCalledWith('\n--- End Content ---');
    });

    test('should save to file with --save flag', async () => {
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(false);
      (WebFetcher.fetchUrl as jest.Mock).mockResolvedValue('Content to save');
      (fs.writeFileSync as jest.Mock).mockImplementation();

      await handleFetchCommand(['https://example.com', '--save']);

      expect(fs.writeFileSync).toHaveBeenCalledWith(
        expect.stringContaining('fetched-'),
        'Content to save'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Content saved to:'));
    });

    test('should handle fetch error', async () => {
      (WebFetcher.isGoogleDocUrl as jest.Mock).mockReturnValue(false);
      (WebFetcher.fetchUrl as jest.Mock).mockRejectedValue(new Error('Network error'));

      try {
        await handleFetchCommand(['https://example.com']);
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith('Failed to fetch content:', expect.any(Error));
    });
  });

  describe('handleIshaSatsangCommand', () => {
    test('should show schedule for default year', () => {
      (ishaSatsangUtils.formatSecondSaturdays as jest.Mock).mockReturnValue('Satsang dates');

      handleIshaSatsangCommand([]);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Isha Satsang Schedule for 2025')
      );
      expect(ishaSatsangUtils.formatSecondSaturdays).toHaveBeenCalledWith(2025);
    });

    test('should handle custom year', () => {
      (ishaSatsangUtils.formatSecondSaturdays as jest.Mock).mockReturnValue('Satsang dates');

      handleIshaSatsangCommand(['2026']);

      expect(ishaSatsangUtils.formatSecondSaturdays).toHaveBeenCalledWith(2026);
    });

    test('should reject invalid year', () => {
      handleIshaSatsangCommand(['2050']);

      expect(mockConsoleError).toHaveBeenCalledWith(
        'Please provide a valid year between 2025 and 2030'
      );
      expect(ishaSatsangUtils.formatSecondSaturdays).not.toHaveBeenCalled();
    });

    test('should reject non-numeric year', () => {
      handleIshaSatsangCommand(['abc']);

      expect(mockConsoleError).toHaveBeenCalledWith(
        'Please provide a valid year between 2025 and 2030'
      );
    });

    test('should show location and time info', () => {
      (ishaSatsangUtils.formatSecondSaturdays as jest.Mock).mockReturnValue('Dates');

      handleIshaSatsangCommand([]);

      expect(mockConsoleLog).toHaveBeenCalledWith('\nTime: 7:00 PM – 9:00 PM');
      expect(mockConsoleLog).toHaveBeenCalledWith(
        'Location: South Bay Vipassana Hall, Santa Clara, CA'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith('Description: Monthly spiritual gathering\n');
    });
  });

  describe('handleWhatsAppCommand', () => {
    let mockScheduler: any;

    beforeEach(() => {
      mockScheduler = {
        sendTestMessage: jest.fn().mockResolvedValue(undefined),
        sendWeeklyUpdate: jest.fn().mockResolvedValue(undefined),
      };
    });

    test('should handle test subcommand', async () => {
      await handleWhatsAppCommand(mockScheduler, ['test']);

      expect(mockScheduler.sendTestMessage).toHaveBeenCalled();
    });

    test('should handle send subcommand', async () => {
      await handleWhatsAppCommand(mockScheduler, ['send']);

      expect(mockScheduler.sendWeeklyUpdate).toHaveBeenCalled();
    });

    test('should require subcommand', async () => {
      await handleWhatsAppCommand(mockScheduler, []);

      expect(mockConsoleError).toHaveBeenCalledWith(
        'WhatsApp subcommand required. Use: test, send, send-custom'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith('\nAvailable WhatsApp commands:');
    });

    test('should handle unknown subcommand', async () => {
      await handleWhatsAppCommand(mockScheduler, ['invalid']);

      expect(mockConsoleError).toHaveBeenCalledWith('Unknown WhatsApp command: invalid');
    });
  });

  describe('handleCustomMessage', () => {
    let mockStdin: any;
    let originalStdin: any;

    beforeEach(() => {
      originalStdin = process.stdin;
      mockStdin = {
        setEncoding: jest.fn(),
        on: jest.fn(),
        read: jest.fn(),
      };
      Object.defineProperty(process, 'stdin', {
        value: mockStdin,
        configurable: true,
      });

      const mockWhatsAppService = {
        sendWhatsAppMessage: jest.fn().mockResolvedValue(true),
      };
      (WhatsAppService as jest.Mock).mockImplementation(() => mockWhatsAppService);
    });

    afterEach(() => {
      Object.defineProperty(process, 'stdin', {
        value: originalStdin,
        configurable: true,
      });
    });

    test('should send custom message from stdin', async () => {
      const customMessage = 'Test message';
      const mockWhatsAppService = new WhatsAppService() as jest.Mocked<WhatsAppService>;

      // Setup stdin mock behavior
      mockStdin.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'readable') {
          mockStdin.read.mockReturnValueOnce(customMessage);
          mockStdin.read.mockReturnValueOnce(null);
          setTimeout(() => callback(), 0);
        } else if (event === 'end') {
          setTimeout(() => callback(), 10);
        }
      });

      await handleCustomMessage();

      expect(mockWhatsAppService.sendWhatsAppMessage).toHaveBeenCalledWith(customMessage);
      expect(mockConsoleLog).toHaveBeenCalledWith('✅ Custom message sent successfully');
    });

    test('should handle send failure', async () => {
      const mockWhatsAppService = new WhatsAppService() as jest.Mocked<WhatsAppService>;
      (mockWhatsAppService.sendWhatsAppMessage as jest.Mock).mockResolvedValue(false);

      mockStdin.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'readable') {
          mockStdin.read.mockReturnValueOnce('Test');
          mockStdin.read.mockReturnValueOnce(null);
          setTimeout(() => callback(), 0);
        } else if (event === 'end') {
          setTimeout(() => callback(), 10);
        }
      });

      try {
        await handleCustomMessage();
      } catch (error: any) {
        expect(error.message).toBe('Process.exit: 1');
      }

      expect(mockConsoleError).toHaveBeenCalledWith('❌ Failed to send custom message');
    });

    test('should handle empty message', async () => {
      mockStdin.on.mockImplementation((event: string, callback: Function) => {
        if (event === 'readable') {
          mockStdin.read.mockReturnValue(null);
          setTimeout(() => callback(), 0);
        } else if (event === 'end') {
          setTimeout(() => callback(), 10);
        }
      });

      await handleCustomMessage();

      const mockWhatsAppService = new WhatsAppService() as jest.Mocked<WhatsAppService>;
      expect(mockWhatsAppService.sendWhatsAppMessage).not.toHaveBeenCalled();
    });
  });

  describe('handleSchedulerCommand', () => {
    let mockScheduler: any;

    beforeEach(() => {
      mockScheduler = {
        start: jest.fn(),
        stop: jest.fn(),
        getNextRunTime: jest.fn().mockReturnValue('Monday 9 AM'),
      };
    });

    test('should handle start subcommand', async () => {
      await handleSchedulerCommand(mockScheduler, ['start']);

      expect(mockScheduler.start).toHaveBeenCalled();
    });

    test('should handle stop subcommand', async () => {
      await handleSchedulerCommand(mockScheduler, ['stop']);

      expect(mockScheduler.stop).toHaveBeenCalled();
    });

    test('should handle status subcommand', async () => {
      await handleSchedulerCommand(mockScheduler, ['status']);

      expect(mockScheduler.getNextRunTime).toHaveBeenCalled();
      expect(mockConsoleLog).toHaveBeenCalledWith('Next scheduled run: Monday 9 AM');
    });

    test('should require subcommand', async () => {
      await handleSchedulerCommand(mockScheduler, []);

      expect(mockConsoleError).toHaveBeenCalledWith(
        'Scheduler subcommand required. Use: start, stop, status'
      );
      expect(mockConsoleLog).toHaveBeenCalledWith('\nAvailable scheduler commands:');
    });

    test('should handle unknown subcommand', async () => {
      await handleSchedulerCommand(mockScheduler, ['invalid']);

      expect(mockConsoleError).toHaveBeenCalledWith('Unknown scheduler command: invalid');
    });
  });

  describe('showHelp', () => {
    test('should display help message', () => {
      showHelp();

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Vipassana Calendar Parser (VIP)')
      );
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Usage:'));
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Commands:'));
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Examples:'));
      expect(mockConsoleLog).toHaveBeenCalledWith(expect.stringContaining('Current date:'));
    });
  });
});
