{"name": "er", "version": "1.0.0", "description": "", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"readline": "^1.3.0"}, "devDependencies": {"typescript": "^5.5.3", "ts-node": "^10.9.0", "@types/node": "^20.0.0", "@types/jest": "^29.5.0", "jest": "^29.5.0", "ts-jest": "^29.1.0"}, "private": true}