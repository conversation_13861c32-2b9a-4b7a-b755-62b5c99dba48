{"name": "goo", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/validator": "^13.15.2", "axios": "^1.10.0", "jest-environment-jsdom": "^30.0.0", "libphonenumber-js": "^1.12.9", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "ts-node": "^10.9.2", "typescript": "^4.9.5", "validator": "^13.15.15", "web-vitals": "^2.1.4", "zipcodes": "^8.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}