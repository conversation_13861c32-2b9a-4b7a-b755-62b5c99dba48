{"permissions": {"allow": ["*", "Bash(npm install)", "Bash(*)", "Bash(npm *)", "Bash(npm*)", "Bash(npm run dev:*)", "Bash(npm uninstall:*)", "Bash(node:*)", "<PERSON><PERSON>(node*)", "Bash(rm:*)", "Bash(npm test:*)", "WebFetch(domain:www.twilio.com)", "WebFetch(domain:console.twilio.com)", "Bash(npm run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(VIP_LOCATION_OVERRIDE=CA npm run dev whatsapp test)", "<PERSON><PERSON>(crontab:*)", "Bash(grep:*)", "<PERSON><PERSON>(launchctl:*)", "Bash(./scripts/install-both-crons.sh:*)", "Bash(npx tsc:*)", "Bash(ls:*)", "Bash(./scripts/monday-whatsapp.sh:*)", "Bash(npm test)", "Bash(npm run test:coverage:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(sed:*)"], "deny": [], "auto_approve": true, "require_confirmation": false}}