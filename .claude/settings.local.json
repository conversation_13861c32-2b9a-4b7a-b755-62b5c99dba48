{"permissions": {"allow": ["WebFetch(domain:docs.google.com)", "Bash(npm install:*)", "Bash(npm test)", "WebFetch(domain:doc-08-2k-docstext.googleusercontent.com)", "Bash(npm run build:*)", "Bash(npm *:*)", "Bash(*)", "Bash(npm *)", "Bash(npm run dev:*)", "Bash(npm uninstall:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm test:*)", "WebFetch(domain:www.twilio.com)", "WebFetch(domain:console.twilio.com)", "Bash(npm run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(chmod:*)", "Bash(/Users/<USER>/WebstormProjects/vip/scripts/test-cron.sh:*)", "Bash(/Users/<USER>/WebstormProjects/vip/scripts/manage-cron.sh install:*)", "Bash(/Users/<USER>/WebstormProjects/vip/scripts/manage-cron.sh status:*)", "Bash(/Users/<USER>/WebstormProjects/vip/scripts/test-power-scenarios.sh:*)", "Bash(/Users/<USER>/WebstormProjects/vip/scripts/test-restart-persistence.sh:*)", "Bash(VIP_LOCATION_OVERRIDE=CA npm run dev whatsapp test)", "<PERSON><PERSON>(crontab:*)", "Bash(grep:*)", "<PERSON><PERSON>(launchctl:*)", "Bash(./scripts/install-both-crons.sh:*)", "Bash(npx tsc:*)", "Bash(ls:*)", "Bash(./scripts/monday-whatsapp.sh:*)"], "deny": []}}