# Claude Code Memory

## Project Context
- TypeScript project with Node.js
- Entry point: src/index.ts
- Standard npm project structure
- Git repository initialized

## Code Patterns
- Follow existing TypeScript conventions
- Use consistent naming and structure
- Prefer functional programming patterns where appropriate

## Development Workflow
1. Always run lint and typecheck before committing
2. Use existing code patterns and conventions
3. Keep code concise and well-structured
4. Follow TypeScript best practices

## Dependencies
- Check package.json for current dependencies
- Use existing libraries when possible
- Follow project's dependency management approach

## Testing
- Auto-detect testing framework from package.json
- Run tests before major changes
- Write tests for new functionality

## Recent Patterns
- (Will be populated as development continues)

## Preferences
- Concise code without unnecessary comments
- Follow existing code style
- Use TypeScript strict mode
- Prefer explicit types over inference where clarity helps