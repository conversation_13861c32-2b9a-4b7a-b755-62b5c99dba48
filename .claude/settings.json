{"project_name": "er", "language": "typescript", "framework": "node", "preferences": {"code_style": "typescript", "test_framework": "auto-detect", "linting": true, "type_checking": true}, "commands": {"build": "npm run build", "dev": "npm run dev", "test": "npm run test", "lint": "npm run lint", "typecheck": "npm run typecheck"}, "memory": {"remember_patterns": true, "remember_preferences": true, "context_size": "large"}}