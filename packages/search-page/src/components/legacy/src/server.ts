// @ts-nocheck
import serviceDiscovery, { initialize } from '@ecom-next/plp-ui/legacy/service-discovery';
import { create } from '../lib/config/akv';
import app from './app';
import logger from '../lib/logger';
import featureFlags from '@ecom-next/plp-ui/legacy/feature-flags';
import { initializeBroker } from '../lib/localization';
import 'newrelic';

if (process.env.USE_AKV === 'true') {
  initialize({ akvConfig: create() });
}

const ERROR_LOG_LEVEL = 4;

const featureFlagOptions = {
  logger,
  logLevel: ERROR_LOG_LEVEL,
  cronTime: '*/5 * * * *',
};

type OptimizelyConfig = {
  url: string;
};

const port = process.env.PORT || '3000';
const { url: optimizelyUrl } = serviceDiscovery('optimizely-onesite-config') as OptimizelyConfig;
featureFlags
  .initialize(optimizelyUrl, featureFlagOptions)
  .then(() => initializeBroker())
  .then(() => {
    const server = app.listen(port, () =>
      logger.info(`Search Page App listening on port ${port}!`),
    );

    process.on('SIGTERM', () => {
      server.close(() => process.exit()); // socket file is automatically removed here
    });
  });
