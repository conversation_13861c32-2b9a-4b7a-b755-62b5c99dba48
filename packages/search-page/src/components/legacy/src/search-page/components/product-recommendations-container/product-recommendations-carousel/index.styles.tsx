// @ts-nocheck
// @flow
import { styled, css, forBrands, Theme, SerializedStyles } from "@ecom-next/core/react-stitch";
import { Carousel as CoreUiCarousel } from '@ecom-next/core/legacy/carousel';

export const CarouselContainer = styled.section`
  margin-bottom: ${({ isDesktop }: { isDesktop: boolean }) => (isDesktop ? '40px' : '20px')};
`;

export const Carousel = styled(CoreUiCarousel)`
  .slick-list {
    overflow: hidden;
  }
  .product-card {
    margin: ${({ isBRRedesignEnabled }) => (isBRRedesignEnabled ? '0rem' : '0.5rem')};
    min-height: unset;
  }
`;
const baseArrowStyle = (theme: Theme): SerializedStyles => {
  const slickArrowWidth = forBrands(theme, {
    default: () => '13px',
    at: () => '20px',
  });

  const slickArrowHeight = forBrands(theme, {
    default: () => '20px',
    at: () => '32px',
  });

  const slickArrowBackground = forBrands(theme, {
    default: () =>
      'url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMTUgMjIiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+YXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLjAwMDAwMCwgLTEuMDAwMDAwKSIgZmlsbD0iIzAwMDAwMCIgZmlsbC1ydWxlPSJub256ZXJvIj4KICAgICAgICAgICAgPHBvbHlnb24gaWQ9IlNoYXBlIiBwb2ludHM9IjE0LjM5NCAxOS40MjM1IDYuOTY5IDEyIDE0LjM5NCA0LjU3NSAxMS4yMTI1IDEuMzkzNSAwLjYwNiAxMiAxMS4yMTI1IDIyLjYwNSI+PC9wb2x5Z29uPgogICAgICAgIDwvZz4KICAgIDwvZz4KPC9zdmc+) no-repeat',
    at: () =>
      'url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB2aWV3Qm94PSIwIDAgMjIgMzMiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8dGl0bGU+bGFyZ2UgYXJyb3c8L3RpdGxlPgogICAgPGcgaWQ9IlBhZ2UtMSIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPGcgaWQ9ImFycm93IiBmaWxsPSIjNjY2NjY2IiBmaWxsLXJ1bGU9Im5vbnplcm8iPgogICAgICAgICAgICA8cG9seWdvbiBpZD0iU2hhcGUiIHBvaW50cz0iMjEuNDA2NzkyIDI3LjU5MzgzOTQgMTAuMjA1MzIxMSAxNi4zOTQ2MzE1IDIxLjQwNjc5MiA1LjE5MzE2MDU2IDE2LjYwNzEzMTUgMC4zOTM1IDAuNjA2IDE2LjM5NDYzMTUgMTYuNjA3MTMxNSAzMi4zOTM1Ij48L3BvbHlnb24+CiAgICAgICAgPC9nPgogICAgPC9nPgo8L3N2Zz4=) no-repeat',
  });

  return css`
    position: absolute;
    display: block;
    line-height: 0;
    font-size: 0;
    cursor: pointer;
    background: transparent;
    color: transparent;
    top: 50%;
    padding: 0;
    border: none;
    outline: none;
    margin-top: 0;
    height: ${slickArrowHeight};

    &::before {
      font-family: slick;
      font-size: 20px;
      line-height: 1;
      color: transparent;
      opacity: 0.75;
      content: '';
      display: block;
      height: ${slickArrowHeight};
      width: ${slickArrowWidth};
      background: ${slickArrowBackground};
    }

    &:hover,
    &:focus {
      outline: none;
      background: transparent;
      color: transparent;

      &::before {
        opacity: 1;
      }
    }

    .slick-disabled {
      &:hover,
      &:focus {
        cursor: initial;
      }
    }
  `;
};

export const generateArrowCss = (arrow: 'prev' | 'next') => (theme: Theme) => {
  const baseCss = baseArrowStyle(theme);
  const isPreviousArrow = arrow === 'prev';
  return css`
    && {
      ${baseCss}
      transform: rotate(${isPreviousArrow ? '0deg' : '180deg'});
    }
  `;
};
