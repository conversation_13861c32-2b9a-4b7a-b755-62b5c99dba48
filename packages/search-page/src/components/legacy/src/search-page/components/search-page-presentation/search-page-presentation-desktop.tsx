// @ts-nocheck
import React, { Suspense } from 'react';
import { CSSObject } from '@ecom-next/core/react-stitch/types';
import { useBRRedesign } from '../../common/hooks/experiments/use-br-redesign';
import { useSearchResponse } from '../fetch-products/search-response-context';
import { hasNoProductsStatusResponse } from '../fetch-products/search-function-helpers';
import { NullSearchPage } from '../null-search-page';
import { Pagination } from '../pagination';
import { QuickFilterWrapper } from '../quick-filter-wrapper';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { STATUSES } from '../fetch-products/types';
import { RailContainerBrRedesign, RailContainerDesktop } from '../rail-container';
import { SearchHeaderResponse } from '../search-response-message';
import { useBrRedesignStyles } from './hooks/use-br-redesign-styles';
import {
  BRRedesign2023Styles,
  InlineFacetsAndSortWrapperStyles,
  SearchBRRedesignStyles,
  SearchPageGridContainer,
} from './search-page-presentation.styles';
import QuickFilterPlaceholder from '../quick-filter-placeholder';
import { RedesignedFacets } from '../redesign-grid-header';
import {
  TEST_WITH_BRAND_PRIORITIZED_QUICK_FACETS,
  TEST_WITH_DYNAMIC_QUICK_FACETS,
  useBrRedesign2023,
  useFiltersFacets,
} from '@ecom-next/plp-ui/legacy/plp-experiments';
import { BackToTop } from '@ecom-next/plp-ui/legacy/back-to-top';
import GridPlaceholder from '../search-page-grid/components/GridPlaceholder';

const InlineFacets = React.lazy(() => import('../inline-facets'));
const SortByDropdownContainer = React.lazy(() => import('../sort-by-dropdown'));
const SearchPageGrid = React.lazy(() => import('../search-page-grid/search-page-grid'));

const facetBarBaseStyles: CSSObject = {
  maxWidth: '256px',
  padding: '0 1rem',
};

// eslint-disable-next-line complexity
export const SearchPagePresentationDesktop = () => {
  const { searchResponse: response = {} } = useSearchResponse();
  const { brandName } = useAppState();
  const isBRRedesignEnabled = useBRRedesign();
  const isBRRedesign2023Enabled = useBrRedesign2023();
  const isFiltersFacets = useFiltersFacets();
  const isBananaRepublicBrand = brandName === 'br' || brandName === 'brfs';
  const isPLPRedesignEnabled =
    isFiltersFacets === TEST_WITH_DYNAMIC_QUICK_FACETS ||
    isFiltersFacets === TEST_WITH_BRAND_PRIORITIZED_QUICK_FACETS;
  const isLoading = response?.status === STATUSES.WAITING;

  const { BRRedesignBackground, brRedesignFacetBarStyles } = useBrRedesignStyles();

  const gridContainerClass = isBRRedesignEnabled
    ? 'search-page__grid_br_enabled'
    : 'search-page__grid';
  const searchPageContentClass =
    isPLPRedesignEnabled && !isBananaRepublicBrand
      ? 'search-page__content_plp_enabled'
      : isBRRedesignEnabled
      ? 'search-page__content_br_enabled'
      : 'search-page__content';

  // TODO: Refactor the Null Search rendering logic
  const hasNoProductsStatus = hasNoProductsStatusResponse(response);

  const gridBaseStyles = () =>
    (isBRRedesignEnabled && InlineFacetsAndSortWrapperStyles.BRRedesignBase2023) ||
    InlineFacetsAndSortWrapperStyles.Base;

  return (
    <div
      css={isBRRedesignEnabled ? BRRedesignBackground : {}}
      data-testid="search-wrapper"
    >
      <SearchPageGridContainer
        className={gridContainerClass}
        isBRRedesignEnabled={isBRRedesignEnabled}
        isDesktop={true}
        isFiltersFacets={isPLPRedesignEnabled}
        isNullSearch={hasNoProductsStatus}
      >
        {hasNoProductsStatus ? (
          <NullSearchPage />
        ) : (
          <>
            {isBRRedesignEnabled || isPLPRedesignEnabled ? null : (
              <div className="search-page__left-column" css={facetBarBaseStyles}>
                <Suspense fallback={<div style={{ minHeight: '0px', width: '272px' }} />}>
                  <RailContainerDesktop />
                </Suspense>
              </div>
            )}
            <div className={searchPageContentClass}>
              {isPLPRedesignEnabled && !isBananaRepublicBrand ? (
                <RedesignedFacets />
              ) : null}
              <SearchHeaderResponse />
              {isPLPRedesignEnabled ? null : (
                <Suspense fallback={<QuickFilterPlaceholder />}>
                  <QuickFilterWrapper />
                </Suspense>
              )}

              <div
                className="search-page__product-grid"
                data-testid="product-grid"
                css={
                  isPLPRedesignEnabled && !isBananaRepublicBrand && !isLoading
                    ? { marginLeft: '-1rem', marginRight: '-1rem' }
                    : isBRRedesign2023Enabled && BRRedesign2023Styles
                }
              >
                {isPLPRedesignEnabled && !isBananaRepublicBrand ? null : (
                  <div
                    css={[
                      gridBaseStyles(),
                      InlineFacetsAndSortWrapperStyles.WithInlineFacets,
                      isBRRedesignEnabled && SearchBRRedesignStyles,
                      facetBarBaseStyles,
                      isBRRedesignEnabled && brRedesignFacetBarStyles,
                    ]}
                    data-testid="inline-facets_sort-by-price-wrapper"
                  >
                    {isBRRedesignEnabled ? (
                      <div data-testid="br-redesign-left-rail">
                        <Suspense
                          fallback={<div style={{ minHeight: '0px', width: '272px' }} />}
                        >
                          <RailContainerBrRedesign />
                        </Suspense>
                      </div>
                    ) : (
                      <>
                        <div data-testid="search-inline-facet">
                          <Suspense fallback={<div />}>
                            <InlineFacets />
                          </Suspense>
                        </div>
                        <div data-testid="sort-by-price-desktop">
                          <Suspense fallback={<div />}>
                            <SortByDropdownContainer
                              css={[
                                InlineFacetsAndSortWrapperStyles.SortByPriceDropdown,
                                InlineFacetsAndSortWrapperStyles.SortByPriceDropdownFeaturedIndent,
                              ]}
                            />
                          </Suspense>
                        </div>
                      </>
                    )}
                  </div>
                )}

                <Suspense fallback={<GridPlaceholder />}>
                  <SearchPageGrid />
                </Suspense>

                <Suspense fallback={<div />}>
                  <Pagination />
                </Suspense>
              </div>
              {isPLPRedesignEnabled && <BackToTop />}
            </div>
          </>
        )}
      </SearchPageGridContainer>
    </div>
  );
};
