// @ts-nocheck
import { getLocationHref } from '@ecom-next/core/url-helper';
import buildProductLink from '.';

const mockedHref = 'https://www.gap.com/browse/category.do';
jest.mock('@ecom-next/core/url-helper');
getLocationHref.mockReturnValue(mockedHref);

describe('Product Card - build product link', () => {
  const grid = {
    productGridSource: 'productGridSource',
    cardIndex: 10,
    totalCount: 20,
    pageIndex: '10',
  };
  const constructedGridParam = `${grid.productGridSource}_${grid.cardIndex}_${
    grid.totalCount
  }_${Number.parseInt(grid.pageIndex, 10) + 1}`;
  const additionalQueryParams = 'foo=bar&fizz=buzz';
  const otherParams = { pid: 'pid', test: 'value', cheese: 'cheddar' };
  const otherParamsQueryString = new URLSearchParams(otherParams).toString();

  it('should include constructed grid param if productGridSource is defined', () => {
    expect(buildProductLink({ grid })).toEqual(
      expect.stringContaining(`grid=${constructedGridParam}`),
    );
  });

  it('should not include constructed grid param if productGridSource is undefiend', () => {
    const invalidGridParam = { productGridSource: undefined };
    expect(buildProductLink({ grid: invalidGridParam })).toEqual(
      expect.not.stringContaining('grid='),
    );
  });

  it('should include all additionalParams when provided', () => {
    expect(buildProductLink({ additionalQueryParams })).toEqual(
      expect.stringContaining(`${additionalQueryParams}`),
    );
  });

  it('should include any other parameters as query parameters in string', () => {
    expect(buildProductLink({ ...otherParams })).toEqual(
      expect.stringContaining(otherParamsQueryString),
    );
  });

  it('should start with domain followed immediately by path and a question mark to separate the query params', () => {
    const startsWithHref = /^https:\/\/www.gap.com\/browse\/product\.do?.*$/g;
    expect(buildProductLink({})).toEqual(expect.stringMatching(startsWithHref));
  });

  it('should separate all query parameters with an ampersand', () => {
    expect(buildProductLink({ grid, additionalQueryParams, ...otherParams })).toEqual(
      expect.stringContaining(
        `${otherParamsQueryString}&grid=${constructedGridParam}&${additionalQueryParams}`,
      ),
    );
  });

  it('should not include back to back & in query parameters when undefined values are included', () => {
    expect(buildProductLink({ ...otherParams })).toEqual(expect.not.stringContaining('&&'));
  });

  it('should filter out undefined parameters in otherParams', () => {
    const badQueryParams = { badParam: undefined, anotherBadParam: null };
    const result = buildProductLink({ ...badQueryParams });
    expect(result).toEqual(expect.not.stringContaining('badParam=undefined'));
    expect(result).toEqual(expect.not.stringContaining('anotherBadParam=null'));
  });

  it('should replace /browse/category.do in href', () => {
    expect(buildProductLink({})).toEqual(expect.not.stringContaining('/browse/category.do'));
  });

  it('should not alter encoded param', () => {
    const encodedValue = encodeURIComponent('<&:encode this stuff:&>');
    const encodedQueryParameter = { encoded: encodedValue };
    expect(buildProductLink(encodedQueryParameter)).toEqual(
      expect.stringContaining(`encoded=${encodedValue}`),
    );
  });

  it('should encode parameter that is not already url encoded', () => {
    const notEncodedValue = '<&: encode this stuff :&>';
    const notEncodedQueryParameter = { notEncoded: notEncodedValue };
    expect(buildProductLink(notEncodedQueryParameter)).toEqual(
      expect.stringContaining(`notEncoded=${encodeURIComponent(notEncodedValue)}`),
    );
  });

  it('should properly handle intended % symbol in category name', () => {
    const percentageValue = '40% off cool stuff';
    const properlyEncodedValue = '40%25%20off%20cool%20stuff';
    expect(buildProductLink({ percentageValue })).toEqual(
      expect.stringContaining(`percentageValue=${properlyEncodedValue}`),
    );
  });
});
