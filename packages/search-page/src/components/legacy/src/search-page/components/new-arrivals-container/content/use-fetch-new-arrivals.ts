// @ts-nocheck
import { BrandInfo, NewArrival } from '../types';
import {
  newArrivalsErrorPageAction,
  newArrivalsPageAction,
} from '../collaborators/new-relic-handler/index';

export async function fetchNewArrivals({
  brandName,
  market,
  locale,
}: BrandInfo): Promise<NewArrival[]> {
  const { NEW_ARRIVALS_DATA } = await require(`../content/${market}/${locale}/${brandName}.json`);
  NEW_ARRIVALS_DATA ? newArrivalsPageAction() : newArrivalsErrorPageAction();
  return NEW_ARRIVALS_DATA;
}
