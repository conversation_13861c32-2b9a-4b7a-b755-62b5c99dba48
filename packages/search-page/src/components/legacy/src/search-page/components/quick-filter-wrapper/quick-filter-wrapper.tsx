// @ts-nocheck
import React, { useContext } from 'react';
import { useFeature } from '@ecom-next/plp-ui/legacy/features';
import { useAppState } from '../../app-state/use-app-state';
import { useFacets } from '../facet-adapter/use-facets';
import { useSearchResponse } from '../fetch-products/search-response-context';
import QuickFilterPlaceholder from '../quick-filter-placeholder';
import onFacetChangeHandler from '../rail-container/facet-change-handler';
import { prepareAllowedFacetsAsPerBrand } from './collaborators/quick-filter-allowed-facets';
import { QuickFilterClearButton } from './components/quick-filter-clear-button';
import { getSearchQuickFilterStyles } from './index.styles';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { QuickFilterProps } from '@ecom-next/plp-ui/legacy/quick-filter/src/types';
import CatPageQuickFilter from '@ecom-next/plp-ui/legacy/quick-filter';
import { useNewPsApi } from '../../common/hooks/experiments/use-new-ps-api';

export const QuickFilterWrapper = () => {
  const { brandName } = useAppState();
  const { updateParameter } = useSearchResponse();
  const isNewPsApiEnabled = useNewPsApi();
  const isSearchBloomreachEnabled = useFeature('search-bloomreach');
  const isQuickFilterEnabled = useFeature('search-quick-filter');
  const {
    listOfAppliedFacets,
    facetsWithMappedName,
    hasInlineFacetTags,
    appliedFacetsPerFacetId,
  } = useFacets();
  const { minWidth } = useContext(BreakpointContext);
  const isMobile = !minWidth(XLARGE);

  const styles = getSearchQuickFilterStyles(isMobile);

  const quickFilterJsx = facetsWithMappedName?.length ? (
    <div className="search-quick-filter" css={styles} data-testid="quick-filter">
      <CatPageQuickFilter
        allowedFacets={prepareAllowedFacetsAsPerBrand(brandName, isNewPsApiEnabled)}
        appliedFacets={(appliedFacetsPerFacetId as unknown) as QuickFilterProps['appliedFacets']}
        appliedOptions={(listOfAppliedFacets as unknown) as QuickFilterProps['appliedOptions']}
        facets={facetsWithMappedName as QuickFilterProps['facets']}
        key="quick-filters"
        onFacetChange={
          onFacetChangeHandler(
            updateParameter,
            isNewPsApiEnabled,
          ) as QuickFilterProps['onFacetChange']
        }
        position="top"
      />
      {isMobile && hasInlineFacetTags && (
        <div data-testid="quick-filter-clear-button">
          <QuickFilterClearButton />
        </div>
      )}
    </div>
  ) : (
    <div>
      <QuickFilterPlaceholder key="quick-filter-placeholder" />
    </div>
  );

  return isQuickFilterEnabled && isSearchBloomreachEnabled ? quickFilterJsx : null;
};

export default QuickFilterWrapper;
