// @ts-nocheck
export type FacetType = 'simple' | 'complex' | 'range';

export type SearchFacetOption = {
  isActive?: 'true';
  isSelected?: 'false' | 'true';
  searchFacetOptionId?: string;
  searchFacetOptionName?: string;
  searchFacetOptionValue?: string;
};

export type SimpleFacetOption = {
  id?: string;
  name?: string;
  isActive?: 'true';
  value?: string;
  applied?: boolean;
};

export type Size = {
  description: string;
  dimId: string;
  id: string;
  name: string;
  searchFacetOptionId: string;
  selected: boolean;
  sfcId: string;
  sfcName: string;
  variantName: string;
};

export type StyleGroups = {
  name?: string;
  sizes?: Size[];
};

export type SizeVariants = {
  name?: string;
  selected?: boolean;
  styleGroups?: StyleGroups[];
};

export type MinMaxRange = {
  min?: number;
  max?: number;
};

export type Facet = {
  displayName?: string;
  facetDisplay?: string;
  facetLayout?: string;
  hidden?: boolean;
  id?: string;
  isActive?: 'true';
  name?: string;
  order?: number;
  searchFacetId?: string;
  searchFacetName?: string;
  searchFacetOptionGroupList?: {
    searchFacetOptionList?: SearchFacetOption[];
  };
  selectionType?: string;
  type?: FacetType;
};

export type SimpleFacet = Facet & {
  searchFacetId?: string;
  options?: SimpleFacetOption[];
};

export type RangeFacet = Facet & {
  appliedRange?: unknown;
  isOpen?: boolean;
  isSelected?: boolean;
  maxOptions?: string[];
  maxValue?: number;
  minOptions?: string[];
  minValue?: number;
  range?: MinMaxRange;
  searchFacetOptionId?: string;
  shouldShow?: boolean;
  value?: MinMaxRange;
};

export type SizeFacet = Facet & {
  sizeVariants?: SizeVariants[];
};

export type ComplexFacet = SizeFacet;

export type AllFacets = SimpleFacet | ComplexFacet | RangeFacet;
