// @ts-nocheck
import React, { useContext, useMemo } from 'react';
import { Grid, GridData } from '@ecom-next/plp-ui/legacy/grid';
import {
  LARGE,
  MEDIUM,
  SMALL,
  XLARGE,
  BreakpointContext,
} from '@ecom-next/core/breakpoint-provider';
import ProductGridPlaceholder from '@ecom-next/core/legacy/product-grid-placeholder';
import { useBRRedesign } from '../../common/hooks/experiments/use-br-redesign';
import { useSearchResponse } from '../fetch-products/search-response-context';
import { ChildProduct, STATUSES } from '../fetch-products/types';
import { SearchProductCard } from '../product-card';
import { productInfoMapper } from './product-info-mapper';
import { Brands, useTheme } from "@ecom-next/core/react-stitch";
import {
  GridColumnsOptions,
  GridToggle,
  useBrRedesign2023,
  useBrRedesignMobileGrid,
  useFiltersFacets,
  useSearchColorSwatches,
} from '@ecom-next/plp-ui/legacy/plp-experiments';
import { useGridButtons } from '@ecom-next/plp-ui/legacy/grid-buttons';
import ProductGridPlaceholderWrapper from './search-page-grid-placeholder';
import { useStarRatings } from '../../common/hooks/experiments/use-star-ratings';
import GridPlaceholder from './components/GridPlaceholder';

const getMobileGridRedesignColumns = (isPLPGridToggleEnabled: string) => {
  switch (isPLPGridToggleEnabled) {
    case GridToggle.One:
      return 1;
    case GridToggle.Two:
      return 2;
    default:
      return;
  }
};

const isBrBrand = (brand: string) =>
  brand === Brands.BananaRepublic || brand === Brands.BananaRepublicFactoryStore;

const getBRFSColumns = (isBRFSRedesign2023: boolean) => (isBRFSRedesign2023 ? 4 : 3);

const isBRFSRedesign2023 = (brand: string, isBRRedesign2023: boolean) =>
  brand === Brands.BananaRepublicFactoryStore && isBRRedesign2023;

const isPLPRedesign = (isFiltersFacets: string | null) =>
  isFiltersFacets === 'TEST_WITH_DYNAMIC_QUICK_FACETS' ||
  isFiltersFacets === 'TEST_WITH_BRAND_PRIORITIZED_QUICK_FACETS';

const getNumberOfPlaceholders = (isBRRedesignEnabled: boolean) =>
  isBRRedesignEnabled ? 9 : 8;

export const SearchPageGrid = () => {
  const isBRRedesignEnabled = useBRRedesign();
  const isBRRedesign2023 = useBrRedesign2023();
  const { searchResponse } = useSearchResponse();
  const theme = useTheme();
  const { isAlternateGrid, plpDropdownGridValue, plpGridValue } = useGridButtons();
  const { isColorSwatchesEnabled } = useSearchColorSwatches();
  const isStarRatingsEnabled = useStarRatings();
  const { childProducts, productCategoryPaginator } =
    searchResponse?.productCategoryFacetedSearch?.productCategory ?? {};
  const { totalItemCount } = searchResponse?.productCategoryFacetedSearch ?? {};
  const { pageNumberRequested } = productCategoryPaginator ?? {};
  const { greaterOrEqualTo, smallerThan, maxWidth } = useContext(BreakpointContext);
  const isFiltersFacets = useFiltersFacets();
  const isPLPRedesignEnabled = isPLPRedesign(isFiltersFacets);

  const isDesktop = greaterOrEqualTo(XLARGE);
  const isMobile = smallerThan(XLARGE);
  const isLoading = searchResponse?.status === STATUSES.WAITING;
  const isBRFSRedesign2023Enabled = isBRFSRedesign2023(theme.brand, isBRRedesign2023);
  const isBrMobile = maxWidth(MEDIUM);
  const brRedesignMobileGrid = useBrRedesignMobileGrid(isBrMobile);
  const mobileGridRedesignColumns = getMobileGridRedesignColumns(plpGridValue);

  const DEFAULT_COLUMNS = useMemo(
    () =>
      isBRRedesignEnabled
        ? {
          [XLARGE]: getBRFSColumns(isBRFSRedesign2023Enabled),
          [LARGE]: getBRFSColumns(isBRFSRedesign2023Enabled),
          [MEDIUM]: 3,
          [SMALL]: 2,
        }
        : {
          [XLARGE]: 4,
          [LARGE]: 3,
          [MEDIUM]: 2,
          [SMALL]: 2,
          1024: 3,
          1280: 4,
        },
    [isBRRedesignEnabled]
  );

  return isLoading ? (
    brRedesignMobileGrid !== GridColumnsOptions.GRID_TOGGLE_OFF ? (
      <GridPlaceholder />
    ) :
      (
        <ProductGridPlaceholderWrapper
          isBrRedesignEnabled={isBRRedesignEnabled}
          isPLPRedesignEnabled={isPLPRedesignEnabled}
          isDesktop={isDesktop}
          mobileGridRedesignColumns={mobileGridRedesignColumns}
          isBr={isBrBrand(theme.brand)}
        >
          <ProductGridPlaceholder
            showColorSwatchesPlaceholder={isColorSwatchesEnabled}
            showStarRatingsPlaceholder={!!isStarRatingsEnabled}
            numberOfPlaceholders={getNumberOfPlaceholders(isBRRedesignEnabled)}
          />
        </ProductGridPlaceholderWrapper >
      )

  ) : (
    <>
      <Grid
        isAlternateGrid={isAlternateGrid}
        gridSize={isMobile ? plpGridValue : plpDropdownGridValue}
        columns={DEFAULT_COLUMNS}
        contents={{
          default: (product: GridData & ChildProduct) => {
            const productInfo = productInfoMapper(product);
            const pageIndex = pageNumberRequested || '0';

            return (
              <SearchProductCard
                pageIndex={pageIndex}
                productInfo={productInfo}
                totalCount={totalItemCount}
                alternateGrid={isAlternateGrid}
              />
            );
          },
        }}
        data={childProducts ?? []}
        index={0}
        theme={theme}
      />
    </>
  );
};

export default SearchPageGrid;
