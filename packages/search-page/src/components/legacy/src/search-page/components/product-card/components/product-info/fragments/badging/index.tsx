// @ts-nocheck
import React from 'react';
import { Brands, styled } from "@ecom-next/core/react-stitch";
import { useBreakpoints, BreakPoints } from '../../../../hooks/use-breakpoints';

interface BadgeContainerProps {
  breakPoints: BreakPoints;
}

type BadgeProps = {
  abbrBrand: Brands;
  badgingMessage?: string;
};

const isBR = (abbrBrand: Brands) => abbrBrand === 'br' || abbrBrand === 'brfs';
const getPaddingForOtherViewPorts = ({ isLarge }: BreakPoints) =>
  isLarge ? '0.25rem' : '0.1875rem';
const BadgeContainer = styled.div<BadgeContainerProps>(({ theme, breakPoints }) => ({
  width: '171px',
  height: '13px',
  color: theme.color.wh,
  lineHeight: '0.5rem',
  paddingBottom: breakPoints.isXLarge ? '0.5rem' : getPaddingForOtherViewPorts(breakPoints),
  fontStyle: 'italic',
  fontFamily: 'Banana',
  fontWeight: 400,
  fontSize: breakPoints.isSmall ? '11px' : '15px',
}));
export const BadgingFragment: React.FC<BadgeProps> = ({
  abbrBrand,
  badgingMessage,
}: BadgeProps) => {
  const breakpoints = useBreakpoints();
  return isBR(abbrBrand) && badgingMessage ? (
    <BadgeContainer breakPoints={breakpoints} data-test-id="badge-message-container">
      {badgingMessage}
    </BadgeContainer>
  ) : null;
};
