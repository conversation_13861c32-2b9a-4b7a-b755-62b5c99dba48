// @ts-nocheck
import { getLocationHref } from '@ecom-next/core/url-helper';

type Grid = {
  productGridSource?: string;
  cardIndex?: number;
  totalCount?: string;
  pageIndex?: string;
};

type BuildProductLinkProps = {
  pid: string;
  additionalQueryParams?: string;
  cid?: string;
  pcid?: string;
  vid?: string;
  grid: Grid;
  nav: string | null;
  storeId?: string;
};

const buildGridQueryParam = ({ productGridSource, cardIndex, totalCount, pageIndex }: Grid) =>
  productGridSource && pageIndex
    ? `grid=${productGridSource}_${cardIndex}_${totalCount}_${Number.parseInt(pageIndex, 10) + 1}`
    : '';

const safelyEncodeURIParam = (param: string) => {
  const nonURICodePercentageSigns = new RegExp('%(?![0-9A-Fa-f]{2})', 'g');
  const sanitizedPercentageSigns = param.replace(nonURICodePercentageSigns, '%25');
  return encodeURIComponent(decodeURIComponent(sanitizedPercentageSigns));
};

const convertParamsToStrings = (
  queryParamObject: Omit<BuildProductLinkProps, 'grid' | 'additionalQueryParams'>,
) =>
  Object.entries(queryParamObject).map(([key, value]) =>
    value ? `${key}=${safelyEncodeURIParam(value)}` : '',
  );

const buildProductLink = ({
  grid = {},
  additionalQueryParams,
  ...otherParams
}: BuildProductLinkProps) => {
  const gridParamString = buildGridQueryParam(grid);
  const otherParamsAsStrings = convertParamsToStrings(otherParams);
  const fullQueryString = [...otherParamsAsStrings, gridParamString, additionalQueryParams?.replace('&','')]
    .filter(Boolean)
    .join('&');

  const productPageUrl = new URL('/browse/product.do', getLocationHref());

  return `${productPageUrl}?${fullQueryString}`;
};

export default buildProductLink;
