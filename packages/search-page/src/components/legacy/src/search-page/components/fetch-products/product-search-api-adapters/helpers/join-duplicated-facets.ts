// @ts-nocheck
import { SearchSizeFacet } from '../../types';

const joinDuplicatedFacets = (facets: SearchSizeFacet[]): SearchSizeFacet[] => {
  const mergeFacet = (
    facet: SearchSizeFacet,
    duplicatedFacet: SearchSizeFacet,
  ): SearchSizeFacet => {
    const mergedOptions = facet.searchFacetOptionGroupList?.concat(
      duplicatedFacet.searchFacetOptionGroupList || [],
    ) as SearchSizeFacet[];
    return {
      ...facet,
      ...duplicatedFacet,
      ...(mergedOptions && { searchFacetOptionGroupList: joinDuplicatedFacets(mergedOptions) }),
    };
  };

  const items: Record<string, unknown> = {};
  facets.forEach(facet => {
    const facetName = facet?.searchFacetOptionGroupName as string;
    const isDuplicate = Boolean(items[facetName]);
    if (isDuplicate) {
      const mappedFacet = items[facetName] as SearchSizeFacet;
      items[facetName] = mergeFacet(facet, mappedFacet);
    } else {
      items[facetName] = facet;
    }
  });

  return Object.values(items) as SearchSizeFacet[];
};

export default joinDuplicatedFacets;
