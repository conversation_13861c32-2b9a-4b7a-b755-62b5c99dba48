// @ts-nocheck
import React from 'react';
import { ProductCard } from '@ecom-next/plp-ui/legacy/product-card';
import { ColorSwatches } from './components/color-swatches';
import { ProductImage } from './components/product-image';
import { ProductInfo } from './components/product-info';
import { ProductCardProvider } from './product-card-context';
import { SearchProductCardProps } from './types';
import { useBrRedesign2023 } from '@ecom-next/plp-ui/legacy/plp-experiments';

export const SearchProductCard = ({
  cardIndex = 0,
  cid,
  groupIndex = 0,
  isDisplayingPriceAndMarketingFlag = true,
  isFromNullSearch,
  modalContainerId,
  pageIndex,
  pcid,
  productInfo,
  totalCount = '0',
  useRatio,
}: SearchProductCardProps) => {
  const gridInfo = {
    cardIndex,
    totalCount,
    pageIndex,
  };
  const isBRRedesign2023Enabled = useBrRedesign2023();
  return (
    <ProductCardProvider
      cid={cid}
      gridInfo={gridInfo}
      isFromNullSearch={isFromNullSearch}
      pcid={pcid}
      productInfo={productInfo}
    >
      <ProductCard productInfo={productInfo}>
        <ProductImage
          cardIndex={cardIndex}
          groupIndex={groupIndex}
          modalContainerId={modalContainerId}
          useRatio={useRatio}
        />
        {!isBRRedesign2023Enabled && <ColorSwatches swatchesProps={productInfo.swatchesProps} />}
        <ProductInfo isDisplayingPriceAndMarketingFlag={isDisplayingPriceAndMarketingFlag} />
      </ProductCard>
    </ProductCardProvider>
  );
};

export default SearchProductCard;
