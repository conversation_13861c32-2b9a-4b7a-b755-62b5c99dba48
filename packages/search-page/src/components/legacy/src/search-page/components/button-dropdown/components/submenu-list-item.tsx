// @ts-nocheck
import { CSSObject, styled, forBrands, Theme } from "@ecom-next/core/react-stitch";
import { SubmenuListItemProps } from '../types';

export const SubmenuListItem = styled.li<SubmenuListItemProps>(({ theme, breakpoint }) => {
  const isDesktop = breakpoint === 'desktop';

  const gapStyle = (theme: Theme): CSSObject => ({
    borderColor: theme.color.g3,
    letterSpacing: '1px',
    marginLeft: '0',
    textTransform: 'uppercase',
    padding: isDesktop ? '0' : '0 0.5rem',
  });

  const brandStyles = forBrands(theme, {
    gap: gapStyle,
    gapfs: gapStyle,
    on: { borderBottom: `1px solid ${theme.color.b1}` },
  }) as CSSObject;

  return {
    width: isDesktop ? '100%' : 'auto',
    borderBottom: `1px solid ${theme.color.bk}`,
    ':last-child': {
      border: 'none',
    },
    ...brandStyles,
  };
});
