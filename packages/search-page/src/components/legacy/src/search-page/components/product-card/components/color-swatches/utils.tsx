// @ts-nocheck
const MAX_SWATCHES_GAP_REDESIGN = 5;
const EXTRA_PADDING_AFTER_PLUS_N = 22;
const MOBILE_SWATCH_SIZE = 28;
const DESKTOP_SWATCH_SIZE = 30;
const MOBILE_GAP = 4;
const DESKTOP_GAP = 8;
const DESKTOP_PLUS_N_SIZE = 38;
const MOBILE_PLUS_N_SIZE = 28;

function calculateSwatchSize(isMobile: boolean): number {
  return isMobile ? MOBILE_SWATCH_SIZE + MOBILE_GAP : DESKTOP_SWATCH_SIZE + DESKTOP_GAP;
}

function calculatePlusNSize(isMobile: boolean): number {
  return isMobile ? MOBILE_PLUS_N_SIZE : DESKTOP_PLUS_N_SIZE;
}

export function getSwatchesForRedesign(
  plpDropdownGridValue: string,
  containerWidth: number
): number {
  const numberOfColumns = parseInt(plpDropdownGridValue, 10) || 4;
  const isMobile = window.matchMedia("(max-width: 480px)").matches || numberOfColumns <= 2;
  const sizeOfPlusNElement = calculatePlusNSize(isMobile)

  const adjustedContainerWidth = containerWidth - sizeOfPlusNElement - EXTRA_PADDING_AFTER_PLUS_N;

  const sizeOfSwatch = calculateSwatchSize(isMobile);

  const derivedSwatchesToShow = Math.floor(adjustedContainerWidth / sizeOfSwatch);


  return Math.min(MAX_SWATCHES_GAP_REDESIGN, derivedSwatchesToShow);
}

function getDefaultSwatches(swatchesDefaultBehavior: string): number {
  return swatchesDefaultBehavior === "link" ? 4 : 20;
}

export function getNumberOfSwatches(
  plpDropdownGridValue: string,
  enableGapRedesign2024: boolean,
  swatchesDefaultBehavior: string,
  containerWidth: number
): number {
  if (enableGapRedesign2024) {
    return getSwatchesForRedesign(plpDropdownGridValue, containerWidth);
  }

  return getDefaultSwatches(swatchesDefaultBehavior);
}
