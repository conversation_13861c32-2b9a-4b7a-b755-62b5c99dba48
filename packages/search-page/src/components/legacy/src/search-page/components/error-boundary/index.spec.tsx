// @ts-nocheck
import React from 'react';
import { LocalizationProvider, Translations } from '@ecom-next/sitewide/localization-provider';
import SearchPageErrorBoundary from './index';
import ErrorContent from './error-content';
import translationsJson from '../../../../translations.json';
import { render, screen } from '../../test-helpers';

const translations = (translationsJson as unknown) as Translations;

describe('ErrorContent', () => {
  it('renders error content message text correctly', () => {
    const { container } = render(
      <LocalizationProvider locale={'en-US'} translations={translations}>
        <ErrorContent abbrBrand={'gap'} />
      </LocalizationProvider>,
    );
    expect(container).toMatchSnapshot();
  });
});

describe('SearchPageErrorBoundary', () => {
  const BadComponent = () => {
    throw new Error('ka boom! this is an expected error');
  };

  describe('when inner component throws an error', () => {
    let mockErrorLogger: jest.Mock;
    beforeEach(() => {
      mockErrorLogger = jest.fn();
      render(
        <LocalizationProvider locale={'en-US'} translations={translations}>
          <SearchPageErrorBoundary abbrBrand="gap" errorLogger={mockErrorLogger}>
            <BadComponent />
          </SearchPageErrorBoundary>
        </LocalizationProvider>,
      );
    });

    it('should render the error content component', () => {
      expect(screen.queryByText(/we're sorry/i)).toBeInTheDocument();
    });

    it('should call the errorLogger function', () => {
      expect(mockErrorLogger).toHaveBeenCalled();
    });
  });

  describe('when inner component does not throw an error', () => {
    it('should render the children', () => {
      render(
        <LocalizationProvider locale={'en-US'} translations={translations}>
          <SearchPageErrorBoundary abbrBrand="gap" errorLogger={jest.fn()}>
            <span>Hello</span>
          </SearchPageErrorBoundary>
        </LocalizationProvider>,
      );
      expect(screen.queryByText(/hello/i)).toBeInTheDocument();
    });
  });
});
