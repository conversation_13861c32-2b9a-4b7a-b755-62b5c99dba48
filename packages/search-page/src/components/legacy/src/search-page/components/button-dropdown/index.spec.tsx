// @ts-nocheck
import React from 'react';
import { render, fireEvent, RenderResult, screen, RenderOptions } from '@ecom-next/core/legacy/test-helpers/rtl';
import { SMALL } from '@ecom-next/core/breakpoint-provider';
import { ButtonDropdownProps } from '.';
import * as dropDown from '.';

const handle = jest.spyOn(dropDown, 'handleLinkClick');
const { ButtonDropdown } = dropDown;

const ARIA_EXPANDED = 'aria-expanded';

const baseProps = {
  heading: {
    text: 'heading text',
  },
  submenu: [
    {
      href: 'girls',
      text: 'Girls',
      trackingId: '123',
      isAJumplink: true,
      jumplinkCSSSelector: '.faceted-grid',
      target: '_blank',
    },
    {
      href: 'boys',
      text: 'Boys',
      trackingId: '456',
    },
  ],
  buttonStyle: {
    desktopStyle: {
      color: 'red',
    },
    style: {
      color: 'blue',
    },
  },
  submenuItemStyles: {
    desktopStyle: {
      color: 'orange',
    },
    style: {
      color: 'green',
    },
  },
  submenuListStyles: {
    desktopStyle: {
      color: 'pink',
    },
    style: {
      color: 'purple',
    },
  },
  startsExpanded: false,
};

const getButtonDropdownContainer = (): HTMLElement =>
  screen.getByTestId(/button-dropdown-container/i);

const getButtonDropdown = (): HTMLElement => screen.getByRole('button', { name: /heading text/i });

const getSubmenuList = (hidden = false): HTMLElement => screen.getByRole('list', { hidden });

const getSubmenuListLink = (name?: string): HTMLElement | null =>
  screen.queryByRole('link', { name });

const getSubmenuListClickableLink = (): HTMLElement => screen.getByRole('link', { name: 'Girls' });

type RenderButtonDropdownProps = {
  props?: ButtonDropdownProps;
  options?: RenderOptions;
};

const renderButtonDropdown = ({ props, options }: RenderButtonDropdownProps): RenderResult =>
  render(<ButtonDropdown {...{ ...baseProps, ...props }} />, options);

describe('<ButtonDropdown />', () => {
  describe('snapshots', () => {
    test('closed dropdown', () => {
      renderButtonDropdown({});
      expect(getButtonDropdownContainer()).toMatchSnapshot();
    });

    test('open dropdown', async () => {
      renderButtonDropdown({});
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })
      expect(getButtonDropdownContainer()).toMatchSnapshot();
    });

    test('mobile closed dropdown', () => {
      renderButtonDropdown({
        options: {
          breakpoint: SMALL,
        },
      });

      expect(getButtonDropdownContainer()).toMatchSnapshot();
    });

    test('mobile open dropdown', async () => {
      renderButtonDropdown({
        options: {
          breakpoint: SMALL,
        },
      });
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })

      expect(getButtonDropdownContainer()).toMatchSnapshot();
    });
  });

  describe('closed state', () => {
    test(`${ARIA_EXPANDED} is false for aria users`, () => {
      renderButtonDropdown({});

      expect(getButtonDropdown()).toHaveAttribute(ARIA_EXPANDED, 'false');
    });

    test('list aria-hidden should be true', () => {
      renderButtonDropdown({});

      expect(getSubmenuList(true)).toBeInTheDocument();
    });
  });

  describe('open state', () => {
    test(`${ARIA_EXPANDED} is true for aria users`, async () => {
      renderButtonDropdown({});
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })

      expect(getButtonDropdown()).toHaveAttribute(ARIA_EXPANDED, 'true');
    });

    test('list aria-hidden should be false', async () => {
      renderButtonDropdown({});
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })

      expect(getSubmenuList()).toBeInTheDocument();
    });
  });

  describe('when startsExpanded is true', () => {
    beforeEach(() => renderButtonDropdown({ props: { startsExpanded: true } }));
    test(`${ARIA_EXPANDED} is true for aria users`, () => {
      expect(getButtonDropdown()).toHaveAttribute(ARIA_EXPANDED, 'true');
    });
    test('list aria-hidden should be false', () => {
      expect(getSubmenuList()).toBeInTheDocument();
    });
    test(`${ARIA_EXPANDED} is false for aria users`, async () => {
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })

      expect(getButtonDropdown()).toHaveAttribute(ARIA_EXPANDED, 'false');
    });
    test('list aria-hidden should be true', async () => {
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })
      expect(getSubmenuList(true)).toBeInTheDocument();
    });
  });

  describe('dropdown button items', () => {
    test('menu renders links with proper attributes', async () => {
      renderButtonDropdown({});
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })

      baseProps.submenu.forEach(({ text, href, target }) => {
        expect(getSubmenuListLink(text)).toHaveAttribute('href', href);
        expect(getSubmenuListLink(text)).toHaveAttribute('target', target);
      });
    });

    test('links should not be accessible when not expanded', () => {
      renderButtonDropdown({});

      baseProps.submenu.forEach(({ text }) => {
        expect(getSubmenuListLink(text)).not.toBeInTheDocument();
      });
    });

    test('links should be accessible when expanded', async () => {
      renderButtonDropdown({});
      await act(async () => { 

       fireEvent.click(getButtonDropdown()); 

       })
      baseProps.submenu.forEach(({ text }) => {
        expect(getSubmenuListLink(text)).toBeInTheDocument();
      });
    });
  });
});

describe('submenu list handle click', () => {
  test('click on submenu link', async () => {
    renderButtonDropdown({
      options: {
        breakpoint: SMALL,
      },
    });
    await act(async () => { 

     fireEvent.click(getButtonDropdown()); 

     })
    await act(async () => { 

     fireEvent.click(getSubmenuListClickableLink()); 

     })
    expect(handle).toHaveBeenCalled();
  });
});
