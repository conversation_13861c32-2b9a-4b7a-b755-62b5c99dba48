// @ts-nocheck
import { CSSObject, styled, forBrands, Theme } from "@ecom-next/core/react-stitch";
import { Link } from '@ecom-next/core/legacy/link';
import { SubmenuListLinkProps } from '../types';

const styledLink = styled(Link);

export const SubmenuListLink = styledLink<SubmenuListLinkProps>(({ theme, breakpoint }) => {
  const isDesktop = breakpoint === 'desktop';

  const gapStyle = (theme: Theme): CSSObject => ({
    backgroundColor: theme.color.wh,
    color: theme.color.b1,
    fontSize: isDesktop ? '12px' : '1rem',
    fontWeight: 700,
    textAlign: 'center',
    letterSpacing: '1px',
    ...theme.font.primary,
  });

  const bananaRepublicStyle = (theme: Theme): CSSObject => ({
    padding: '1rem',
    textAlign: 'center',
    textTransform: 'uppercase',
    transition: 'none',
    '&:hover, &:focus': {
      background: theme.color.bk,
      color: theme.color.wh,
    },
    ...theme.font.secondary,
  });

  const brandStyles = forBrands(theme, {
    gap: gapStyle,
    gapfs: gapStyle,
    on: () => ({
      textAlign: 'center',
      ...theme.font.secondary,
    }),
    br: bananaRepublicStyle,
    brfs: bananaRepublicStyle,
  }) as CSSObject;

  return {
    cursor: 'pointer',
    display: 'block',
    padding: isDesktop ? '0.5rem 0' : '0.75rem 0',
    textDecoration: 'none',
    transition: 'color 0.3s ease',
    whiteSpace: 'nowrap',
    fontSize: 'inherit',
    textAlign: 'left',
    color: theme.color.bk,
    ...brandStyles,
  };
});
