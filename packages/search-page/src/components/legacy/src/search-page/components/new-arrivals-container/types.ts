// @ts-nocheck
import { Locales as Locale } from '@ecom-next/sitewide/app-state-provider';
import { Brands as Brand } from '@ecom-next/core/legacy/utility';

export type Market = 'us' | 'ca';

export type BrandInfo = {
  brandName: Brand;
  market: Market;
  locale: Locale;
};

type NewArrivalsCategory = {
  name: string;
  link: string;
};

export type NewArrival = {
  id?: string;
  title?: string;
  categories: Array<NewArrivalsCategory>;
};

type EnabledFeatures = { [key: string]: string };

type ErrorLogger = (...arguments_: unknown[]) => void;

export type NewArrivalsContainerProps = {
  newArrivalsData: NewArrival[];
  enabledFeatures?: EnabledFeatures;
  errorLogger?: ErrorLogger;
  isDesktop: boolean;
};

export type NewArrivalsOptionsProps = {
  isOpened?: boolean;
  categories: Array<NewArrivalsCategory>;
  title?: string;
  id?: string;
  isDesktop?: boolean;
  brandName: Brand;
  startsExpanded?: boolean;
  isBRRedesignEnabled?: boolean;
};
