// @ts-nocheck
import React, { useContext } from 'react';
import ProductGridPlaceholder from '@ecom-next/core/legacy/product-grid-placeholder';
import { useStarRatings } from '../../../common/hooks/experiments/use-star-ratings';
import { useBRRedesign } from '../../../common/hooks/experiments/use-br-redesign';
import ProductGridPlaceholderWrapper from './styled/ProductGridPlaceholderWrapper';
import {
  GridToggle,
  useCatColorSwatches as useColorSwatches,
  usePlaceholderGridToggle,
  GridColumnsOptions,
  useBrRedesignMobileGrid,
} from '@ecom-next/plp-ui/legacy/plp-experiments';
import { useGridButtons } from '@ecom-next/plp-ui/legacy/grid-buttons';
import { BreakpointContext, XLARGE, MEDIUM } from '@ecom-next/core/breakpoint-provider';

const getNumberOfPlaceholders = (
  isBrRedesignEnabled: boolean,
  gridRedesignColumns: number,
  isMobile: boolean
) => {
  if (isBrRedesignEnabled && !isMobile) {
    return 9;
  }
  if (gridRedesignColumns > 3 && !isMobile) {
    return 12;
  }
  return 8;
};

const getGridRedesignColumns = (plpGridValue: string, isMobile: boolean) => {
  switch (plpGridValue) {
    case GridToggle.One:
      return 1;
    case GridToggle.Two:
      return 2;
    case GridToggle.Three:
      return 3;
    case GridToggle.Four:
      return 4;
    case GridToggle.Five:
      return 5;
    case GridToggle.Six:
      return 6;
    default:
      return isMobile ? 1 : 3;
  }
};

import { usePageContext } from "@sitewide/hooks/usePageContext";
const GridPlaceholder = (): JSX.Element => {
  const { isColorSwatchesEnabled } = useColorSwatches();
  const isStarRatingsEnabled = useStarRatings();
  const isBrRedesignEnabled = useBRRedesign();
  const gridPlaceholderRedesign = usePlaceholderGridToggle();
  const { smallerThan, maxWidth } = useContext(BreakpointContext);
  const isBrMobile = maxWidth(MEDIUM);
  const brRedesignMobileGrid = useBrRedesignMobileGrid(isBrMobile);
  const isMobile = smallerThan(XLARGE);
  const { plpGridValue, plpDropdownGridValue } = useGridButtons();
  const mobileGridValue = isBrRedesignEnabled ? brRedesignMobileGrid : plpGridValue;
  const plpGridSize = isMobile ? mobileGridValue : plpDropdownGridValue;
  const gridRedesignColumns = getGridRedesignColumns(plpGridSize, isMobile);
  const numberOfPlaceholders = getNumberOfPlaceholders(
    isBrRedesignEnabled,
    gridRedesignColumns,
    isMobile
  );

  return (
    <ProductGridPlaceholderWrapper
      isBrRedesignEnabled={isBrRedesignEnabled}
      gridPlaceholderRedesign={gridPlaceholderRedesign}
      gridRedesignColumns={gridRedesignColumns}
    >
      <ProductGridPlaceholder
        numberOfPlaceholders={numberOfPlaceholders}
        showColorSwatchesPlaceholder={isColorSwatchesEnabled}
        showStarRatingsPlaceholder={!!isStarRatingsEnabled}
        gridPlaceholderRedesign={plpGridSize as GridColumnsOptions}
      />
    </ProductGridPlaceholderWrapper>
  );
};

export default GridPlaceholder;
