// @ts-nocheck
import { Facet } from '../types'
import {ZodSchema, z} from 'zod';
import { SwatchesProps } from '@ecom-next/core/legacy/swatches';
import { SearchSwatchesProps, SearchSwatchesColorProps } from '../product-search-api-adapters/response-mappers/product-card/types';
import { SearchFacetInfo, SearchFacet } from '..';
import { ProductsPagination, SearchResponse as PSApiSearchResponse } from '@ecom-next/plp-ui/legacy/search-service/types/engines/productsearch-service/types/search';
import {
  AppliedRange,
  BrandsUpperCase,
  Colors,
  Images, 
  ProductCategoryFacetedSearch, 
  ProductCategory, 
  ChildProduct, 
  Image, 
  Metadata,
  ProductPrice, 
  ProductCategoryPaginator, 
  LegacyPDSSearchResponse, 
  Range,
  SearchFacetOption, 
  SearchedProduct,
  SortByDir,
  SortByField,
  STATUSES} from './search-response';

const ImageSchema: z.ZodType<Image> = z.object({
    catalogContentId: z.string().optional(),
    contentTypeId: z.string().optional(),
    height: z.string().optional(),
    mediaTypeId: z.string().optional(),
    path: z.string().optional(),
    prefixId: z.string().optional(),
    width: z.string().optional(),
});

const ProductPriceSchema: z.ZodType<ProductPrice> = z.object({
  currentMaxPrice: z.string().optional(),
  currentMinPrice: z.string().optional(),
  isClearanceItem: z.string().optional(),
  localizedCurrentMaxPrice: z.string().optional(),
  localizedCurrentMinPrice: z.string().optional(),
  localizedRegularMaxPrice: z.string().optional(),
  localizedRegularMinPrice: z.string().optional(),
  maxPercentageOff: z.string().optional(),
  minPercentageOff: z.string().optional(),
  priceType: z.string().optional(),
  regularMaxPrice: z.string().optional(),
  regularMinPrice: z.string().optional(),
});

export const SwatchesPropsSchema: z.ZodType<SearchSwatchesProps> = z.object({
  className: z.string().optional(),
  colorArray: z.array(
    z.object({
      id: z.string(),
    })
  ),
  onChange: z.function().optional(),
  onColorSwatchClick: z.function().optional(),
  onCarouselArrowClick: z.function().optional(),
  onFirstOverflowLinkClick: z.function().optional(),
  size: z.enum(['small', 'default']).optional(),
  overflowBehavior: z
    .enum(['carousel', 'link', 'default'])
    .optional(),
  productUrl: z.string().optional(),
  selected: z.string(),
  radioGroupName: z.string().optional(),
  shouldDisplayInStock: z.boolean().optional(),
});

const ChildProductSchema: z.ZodType<ChildProduct> = z.object({
  additionalQueryParams: z.string().optional(),
  altText: z.string().optional(),
  avImages: z.record(z.string().optional()).nullable().optional(),
  businessCatalogItemId: z.string().optional(),
  categoryLargeImage: ImageSchema.optional(),
  currentPrice: z.string().optional(),
  defaultSizeVariantId: z.string().optional(),
  inventoryStatusId: z.string().optional(),
  isDropship: z.boolean().optional(),
  marketingFlag: z.object({
    marketingFlagName: z.string().optional(),
    badgingName: z.string().optional(),
  }).optional(),
  name: z.string().optional(),
  numVariantsFiltered: z.number().optional(),
  originalPrice: z.string().optional(),
  parentBusinessCatalogItemId: z.string().optional(),
  price: ProductPriceSchema.optional(),
  pristineImages: z.object({
    pristine1ImagePath: z.string().optional(),
  }).optional(),
  reviewCount: z.number().optional(),
  reviewScore: z.number().optional(),
  sellerName: z.string().optional(),
  swatchesProps: SwatchesPropsSchema.optional(),
  url: z.string().optional(),
  zoomImages: z.object({
    p01ZoomImagePath: z.string().optional(),
  }).optional(),
  vendorId: z.string().optional(),
});

const ProductCategoryPaginatorSchema: z.ZodType<ProductCategoryPaginator> = z.object({
  pageNumberRequested: z.string().optional(),
  pageNumberTotal: z.string().optional(),
  productPerPageCount: z.string().optional(),
  productPerRowCount: z.string().optional(),
  isSearchPage: z.enum(['true', 'false']).optional(),
});

const SearchFacetOptionSchema: z.ZodType<SearchFacetOption> = z.object({
  isActive: z.enum(['true', 'false']).optional(),
  isSelected: z.enum(['true', 'false']).optional(),
  searchFacetOptionId: z.string().optional(),
  searchFacetOptionImagePath: z.string().optional(),
  searchFacetOptionName: z.string().optional(),
  searchFacetOptionValue: z.string().optional(),
});

const SearchFacetSchema: z.ZodType<SearchFacet> = z.object({
  isActive: z.enum(['true', 'false']).optional(),
  searchFacetId: z.string().optional(),
  searchFacetName: z.string().optional(),
  searchFacetOptionGroupList: z.object({
    searchFacetOptionList: z.array(SearchFacetOptionSchema).optional(),
  }).optional(),
});

const SearchFacetInfoSchema: z.ZodType<SearchFacetInfo> = z.object({
  searchFacetList: z.array(SearchFacetSchema).optional(),
});

const ProductCategorySchema: z.ZodType<ProductCategory> = z.object({
  childProducts: z.array(ChildProductSchema).optional(),
  isOutfitCategory: z.string().optional(),
  productCategoryPaginator: ProductCategoryPaginatorSchema.optional(),
});

const ProductCategoryFacetedSearchSchema : z.ZodType<ProductCategoryFacetedSearch> = z.object({
  areAllSubCatsOutfit: z.enum(['true', 'false']).optional(),
  isOutfitCategory: z.enum(['true', 'false']).optional(),
  isRedirect: z.enum(['true', 'false']).optional(),
  productCategory: ProductCategorySchema.optional(),
  searchDivName: z.string().optional(),
  searchFacetInfo: SearchFacetInfoSchema.optional(),
  searchText: z.string().optional(),
  totalItemCount: z.string().optional(),
  autoCorrectedText: z.string().optional(),
  spellingSuggestions: z.array(z.string()).optional(),
});

export const LegacyPDSSearchResponseSchema: z.ZodType<LegacyPDSSearchResponse> = z.object({
  campaign: z.unknown().optional(),
  error: z.unknown().optional(),
  productFacet: ProductCategoryFacetedSearchSchema.optional(),
  status: z.nativeEnum(STATUSES).optional(),
});

const AppliedRangeSchema: z.ZodType<AppliedRange> = z.object({
  max: z.number(),
  min: z.number(),
});

const RangeSchema: z.ZodType<Range>= z.object({
  count: z.number(),
  max: z.number(),
  min: z.number(),
});

const FacetSchema: z.ZodType<Facet> = z.lazy( () => z.object({
    applied: z.string().optional(),
    appliedRange: AppliedRangeSchema.optional(),
    localeName: z.string().optional(),
    name: z.string().optional(),
    options: z.array(FacetSchema),
    range: RangeSchema.optional(),
    type: z.string().optional(),
}));

const FacetOptionsSchema = z.enum(['color','department','price','size','style','reviewScore','new']);
const FlexFacetOptionsSchema = z.enum(['activity','collar','collection','fabricMaterial','features','fit','legShape','length','neckline','occasion','packageQuantity','panelType','performanceTechnology', 'pregnancyStage','rise','sleeveLength','supportType','sustainability','warmthRating','wash'])

const SortByDirSchema: z.ZodType<SortByDir> = z.enum(['asc', 'desc']);
const sortByFieldSchema: z.ZodType<SortByField> = z.union([FacetOptionsSchema, FlexFacetOptionsSchema]);

const LocalesSchema = z.enum([
  'en_US',
  'en_CA',
  'fr_CA',
  'en-US',
  'en-CA',
  'fr-CA',
]);

const ModificationSchema = z.object({
  mode: z.string().optional(),
  value: z.string().optional(),
});

const QuerySchema = z.object({
  didYouMean: z.array(z.string()).optional(),
  modification: ModificationSchema.optional(),
});

const MetadataSchema: z.ZodType<Metadata> = z.object({
  query: QuerySchema.optional(),
});

const ProductsPaginationSchema: z.ZodType<ProductsPagination> = z.object({
  currentPage: z.number(),
  pageNumberTotal: z.number(),
  pageSize: z.number(),
});

const MarketUpperCase = z.enum([
  'US',
  'CA',
])

const ImagesSchema: z.ZodType<Images> = z.object({
  path: z.string().optional(),
  type: z.string().optional(),
});

const ColorsSchema: z.ZodType<Colors> = z.object({
  backOrderInventoryCount: z.string().optional(),
  defaultSizeVariantId: z.string().optional(),
  effectivePrice: z.string().optional(),
  id: z.string().optional(),
  images: z.array(ImagesSchema),
  inventoryCount: z.string().optional(),
  marketingFlags: z.array(z.unknown()).or(z.undefined()).optional(),
  ccMarketingFlagsDetails: z.array(z.unknown()).or(z.undefined()).optional(),
  name: z.string().optional(),
  percentageOff: z.string().optional(),
  priceType: z.string().optional(),
  regularPrice: z.string().optional(),
  shortDescription: z.string().optional(),
  skuSizes: z.array(z.string()).optional(),
  subCategoryIds: z.array(z.string()).optional(),
  vendorName: z.string().optional(),
});

const SearchedProductSchema: z.ZodType<SearchedProduct> = z.object({
  brand: z.nativeEnum(BrandsUpperCase).optional(),
  colors: z.array(ColorsSchema).optional(),
  defaultThumbImage: z.string().optional(),
  description: z.string().optional(),
  excludedFromPromotion: z.string().optional(),
  id: z.string().optional(),
  market: MarketUpperCase.optional(),
  marketingFlags: z.array(z.unknown()).optional(),
  styleMarketingFlagsDetails: z.array(z.unknown()).optional(),
  name: z.string().optional(),
  priceRange: z.tuple([z.string(), z.string()]).optional(),
  reviewCount: z.string().optional(),
  reviewScore: z.unknown().optional(),
  salePriceRange: z.tuple([z.string(), z.string()]).optional(),
  showSellerName: z.string().optional(),
  webProductType: z.string().optional(),
  vendorId: z.string().optional(),
});

export const SearchResponseSchema: z.ZodType<PSApiSearchResponse> = z.object({
  categories: z.array(z.unknown()).optional(),
  facets: z.array(FacetSchema).optional(),
  locale: LocalesSchema.optional(),
  metadata: MetadataSchema.optional(),
  pagination: ProductsPaginationSchema.optional(),
  products: z.array(SearchedProductSchema).optional(),
  sortByDir: SortByDirSchema.optional(),
  sortByField: sortByFieldSchema.optional(),
  totalColors: z.string().optional(),
  totalProducts: z.string().optional(),
  campaign: z.unknown().optional(),
});
