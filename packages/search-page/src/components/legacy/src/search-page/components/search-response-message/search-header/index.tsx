// @ts-nocheck
import React, { useContext } from "react";
import {
  useTheme,
  css,
  forBrands,
  CSSObject,
  Theme,
  getFontWeight,
} from "@ecom-next/core/react-stitch";
import { useLocalize } from "@ecom-next/sitewide/localization-provider";
import { useFiltersFacets } from "@ecom-next/plp-ui/legacy/plp-experiments";
import { BreakpointContext, XLARGE } from "@ecom-next/core/breakpoint-provider";
import { useBRRedesign } from "../../../common/hooks/experiments/use-br-redesign";
import { useSearchResponse } from "../../fetch-products/search-response-context";

const SEARCH_RESULTS_KEY = "search_header.search_results_for";

const getMediaQuery = (breakPoint: string) =>
  `@media (min-width: ${breakPoint}px)`;

const SearchHeader = () => {
  const { font, color, spacing } = useTheme();
  const { localize } = useLocalize();
  const { minWidth } = useContext(BreakpointContext);
  const { searchText: searchTerm } = useSearchResponse();
  const isDesktop = minWidth(XLARGE);
  const isBRRedesignEnabled = useBRRedesign();
  const isFiltersFacets = useFiltersFacets();
  const isPLPRedesignEnabled =
    isFiltersFacets === "TEST_WITH_DYNAMIC_QUICK_FACETS" ||
    isFiltersFacets === "TEST_WITH_BRAND_PRIORITIZED_QUICK_FACETS";

  const HeaderStyles = (theme: Theme) => ({
    width: "100%",
    color: color.g1,
    lineHeight: isBRRedesignEnabled ? "26px" : "",
    ...font.primary,
    [getMediaQuery("768")]: {
      maxWidth: isBRRedesignEnabled ? 1024 : 768,
    },
    [getMediaQuery("1024")]: {
      maxWidth: isBRRedesignEnabled ? 1024 : 768,
    },
    [getMediaQuery("1280")]: {
      maxWidth: 1024,
    },
      ...(forBrands(theme, {
        at: {
          color: theme.color.bk,
          fontWeight: getFontWeight("regular").fontWeight,
          letterSpacing: "0px",
          lineHeight: "normal",
          h1: {
            fontSize: "22px",
          },
        },
      }) as CSSObject),
  });

  const RegularTitle = css`
    text-align: left;
    max-width: 512px;
    padding-top: ${isPLPRedesignEnabled ? 0 : spacing.medium};
    padding-bottom: ${isPLPRedesignEnabled ? 0 : spacing.medium};
    padding-left: ${isPLPRedesignEnabled
      ? isDesktop
        ? 0
        : spacing.medium
      : spacing.medium};
    font-size: 20px;
    ${getMediaQuery("768")}: {
      font-size: 22px;
      max-width: none;
      min-width: 0;
    }
  `;

  const BRRedesignTitle = css`
    text-align: left;
    max-width: 512px;
    padding: ${isDesktop ? "16px" : "16px 16px 12.5px 16px"};
    color: ${color.b1};
    font-size: 20px;
    font-weight: 400;
    ${getMediaQuery("1024")}: {
      font-size: 22px;
      max-width: none;
      min-width: 0;
    }
  `;

  return (
    <div css={HeaderStyles}>
      <h1
        css={isBRRedesignEnabled ? BRRedesignTitle : RegularTitle}
        data-testid="search-header"
      >
        {localize(SEARCH_RESULTS_KEY, { searchTerm })}
      </h1>
    </div>
  );
};

export default SearchHeader;
