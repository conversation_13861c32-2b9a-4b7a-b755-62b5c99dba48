// @ts-nocheck
export type CertonaSchemeItem = {
  CurrentPrice: string;
  DetailURL: string;
  explanation?: string; // Canada only prop
  ID: string;
  ImageURL: string;
  instock: string;
  item?: CertonaSchemeItem[]; // Canada only prop
  LightWeightImageURL: string;
  MarketingFlag: string;
  OriginalPrice: string;
  ProductName: string;
  PromotionDisplay: string;
  webProductType: string;
};

export type CertonaScheme = {
  display: string;
  explanation: string;
  items: CertonaSchemeItem[];
  scheme: string;
};

export type CertonaTransformedSearchScheme = {
  scheme: string;
  heading: string;
  recommendations: CertonaSchemeItem[];
};

type PageType =
  | 'ACCOUNT'
  | 'ADDTOCART'
  | 'CART'
  | 'CATEGORY'
  | 'CATEGORYLANDING'
  | 'CUSTOMERLANDING'
  | 'DIVISION'
  | 'HOME'
  | 'NOSEARCH'
  | 'ORDERHISTORY'
  | 'OUTOFSTOCK'
  | 'PRODUCT'
  | 'PURCHASE'
  | 'SALE'
  | 'SEARCH'
  | 'WISHLIST';

type GlobalCertonaConfig = {
  boutiquetags?: string;
  brand?: 'GAP' | 'BR' | 'AT' | 'ON';
  categoryexclusions?: string;
  categoryid?: string;
  containers?: string;
  customerid?: string;
  departmenttags?: string;
  device?: 'DESKTOP' | 'MOBILE' | 'TABLET';
  divisionid?: string;
  emailcustomerid?: string;
  gender?: string;
  itemid?: string;
  links?: string;
  pageid?: string;
  pagetype: PageType;
  price?: string;
  qty?: string;
  recommendationcategoryid?: string;
  recommendations?: boolean;
  site?: 'GAP' | 'BR' | 'AT' | 'ON';
  styletags?: string;
  subdivisionid?: string;
  total?: string;
  transactionid?: string;
};

export type ProductRecommendationsContainerProps = {
  globalCertonaConfig: GlobalCertonaConfig;
};
