// @ts-nocheck
import { Facet } from '@ecom-next/plp-ui/legacy/search-service/types/engines/productsearch-service/types/search/response';
import { SearchSizeFacet, SearchSizeFacetOption } from '../../../types';
import joinDuplicatedFacets from '../../helpers/join-duplicated-facets';
import { BuildSearchFacet, FacetsMapperReturn, NodeInformation, Node } from './types';

const buildSearchFacet = ({
  id,
  name,
  parentId,
  optionGroupList,
  optionList,
}: BuildSearchFacet): SearchSizeFacet => ({
  searchFacetOptionGroupId: id,
  searchFacetOptionGroupName: name?.toLowerCase(),
  ...(parentId && { parentSearchFacetOptionGroupId: parentId }),
  ...(optionGroupList && { searchFacetOptionGroupList: optionGroupList }),
  ...(optionList && { searchFacetOptionList: optionList }),
});

export const buildFacetValue = (
  facet: Facet,
  nodeInfo?: NodeInformation,
): SearchSizeFacetOption => {
  const { category, dimension, variant } = nodeInfo ?? {};
  const [displayName] = facet.name?.split(':') ?? [];
  const facetValue = facet.name;
  const optionValue = `_${variant}_${category}_${dimension}_${facetValue}`.toLowerCase();
  return {
    searchFacetOptionId: optionValue,
    searchFacetOptionValue: optionValue,
    searchFacetOptionName: displayName,
    isActive: 'true',
    isSelected: facet.applied ? 'true' : 'false',
    parentSearchFacetOptionGroupId: nodeInfo?.variant,
  };
};

const buildFacetValues = (options?: Facet[], nodeInfo?: NodeInformation): SearchSizeFacetOption[] =>
  options?.map(option => buildFacetValue(option, nodeInfo)) || [];

const handleVariantNode = (
  facetsMapper: CallableFunction,
  facet: Facet,
  nodeInfo?: NodeInformation,
) =>
  facet.options?.map(category =>
    facetsMapper(category, {
      ...nodeInfo,
      currentNode: Node.Category,
      variant: facet.name,
      category: category.name,
    }),
  ) as SearchSizeFacet[];

const handleCategoryNode = (
  facetsMapper: CallableFunction,
  facet: Facet,
  nodeInfo?: NodeInformation,
) => {
  const optionGroupList = facet.options?.map(dimension =>
    facetsMapper(dimension, {
      ...nodeInfo,
      currentNode: Node.Dimension,
      dimension: dimension.name,
    }),
  ) as SearchSizeFacet[];
  return buildSearchFacet({
    id: facet.name,
    name: facet.localeName,
    optionGroupList,
  });
};

const handleDimensionNode = (facet: Facet, nodeInfo?: NodeInformation) => {
  const optionList = buildFacetValues(facet.options, nodeInfo);
  const variantFacet = buildSearchFacet({
    id: nodeInfo?.variant,
    name: nodeInfo?.variant,
    parentId: facet.name,
    optionList,
  });
  return buildSearchFacet({
    id: facet.name,
    name: facet.localeName,
    parentId: nodeInfo?.category,
    optionGroupList: [variantFacet],
  });
};

const facetsMapper = (facet: Facet, nodeInfo?: NodeInformation): FacetsMapperReturn => {
  switch (nodeInfo?.currentNode) {
    case Node.Category:
      return handleCategoryNode(facetsMapper, facet, nodeInfo);
    case Node.Dimension:
      return handleDimensionNode(facet, nodeInfo);
    case Node.Variant:
    default: {
      return handleVariantNode(facetsMapper, facet, nodeInfo);
    }
  }
};

export const complexFacetMapper = (facet: Facet) => {
  const mappedFacets = facet.options?.flatMap(variant => facetsMapper(variant));
  const searchFacetOptionGroupList = joinDuplicatedFacets(mappedFacets ?? []);
  return {
    isActive: 'true',
    searchFacetId: 'size',
    searchFacetName: 'Size',
    searchFacetOptionGroupList,
  };
};
