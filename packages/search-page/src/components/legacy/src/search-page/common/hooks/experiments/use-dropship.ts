// @ts-nocheck
import { useFeature, features } from '@ecom-next/plp-ui/legacy/features';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';

export function useDropship(): boolean {
  const { brandName } = useAppState();
  const brandWithDropshipProducts = brandName === 'br' || brandName === 'gap';
  const isFeatureFlagEnabled = useFeature(features.DROPSHIP);

  return brandWithDropshipProducts && isFeatureFlagEnabled;
}
