// @ts-nocheck
import { useExperimentsForMultiVariants } from '@ecom-next/plp-ui/legacy/features';
import { useAppState } from '../../../app-state/use-app-state';
import { experiments } from '../../../experiments-config/constants';

export const useStarRatings = (): string | null => {
  const { brandName, experiments: abSeg } = useAppState();
  const { experimentStatus: starRatingExperiment } = useExperimentsForMultiVariants(
    `${brandName}${experiments.STAR_RATING}`,
    abSeg,
  );

  if (starRatingExperiment === 'a' || starRatingExperiment === 'b') {
    return 'STAR_RATING_SUPRESS_ENABLED';
  } else if (starRatingExperiment === 'c' || starRatingExperiment === 'd') {
    return 'STAR_RATING_SUPRESS_DISABLED';
  }
  return null;
};