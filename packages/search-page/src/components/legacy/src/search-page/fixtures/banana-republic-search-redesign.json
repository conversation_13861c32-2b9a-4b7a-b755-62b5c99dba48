{"resourceVersion": "1.1", "resourceUrl": "/resources/bloomreach/productSearch/v1/search?cid=undefined&isFacetsEnabled=true&globalShippingCountryCode=&globalShippingCurrencyCode=&locale=en_US&pageId=0", "productCategoryFacetedSearch": {"areAllSubCatsOutfit": "false", "isOutfitCategory": "false", "searchDivName": "", "isRedirect": "false", "searchText": "jeans", "totalItemCount": 5, "searchFacetInfo": {"searchFacetList": [{"isActive": "true", "searchFacetId": "department", "searchFacetName": "Department", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "true", "isActive": "true", "searchFacetOptionId": "Women", "searchFacetOptionName": "Women", "searchFacetOptionValue": "Women"}]}}, {"isActive": "true", "searchFacetId": "style", "searchFacetName": "Style", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Shirts", "searchFacetOptionName": "Shirts", "searchFacetOptionValue": "Shirts"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Sweaters", "searchFacetOptionName": "Sweaters", "searchFacetOptionValue": "Sweaters"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "T shirts", "searchFacetOptionName": "T shirts", "searchFacetOptionValue": "T shirts"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "Tank", "searchFacetOptionName": "Tank", "searchFacetOptionValue": "Tank"}]}}, {"isActive": "true", "searchFacetId": "size", "searchFacetName": "Size", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "bottoms", "searchFacetOptionGroupName": "bottoms", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "size", "searchFacetOptionGroupName": "size", "parentSearchFacetOptionGroupId": "bottoms", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "3", "searchFacetOptionGroupName": "petite", "parentSearchFacetOptionGroupId": "size", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_size_petite_xs", "searchFacetOptionValue": "_bottoms_size_petite_xs", "searchFacetOptionName": "xs", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite"}, {"searchFacetOptionId": "_bottoms_size_petite_00_xxs/xs", "searchFacetOptionValue": "_bottoms_size_petite_00_xxs/xs", "searchFacetOptionName": "00", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxs/xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_0_xs", "searchFacetOptionValue": "_bottoms_size_petite_0_xs", "searchFacetOptionName": "0", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_2_xs", "searchFacetOptionValue": "_bottoms_size_petite_2_xs", "searchFacetOptionName": "2", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_4_s", "searchFacetOptionValue": "_bottoms_size_petite_4_s", "searchFacetOptionName": "4", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_6_s", "searchFacetOptionValue": "_bottoms_size_petite_6_s", "searchFacetOptionName": "6", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_8_m", "searchFacetOptionValue": "_bottoms_size_petite_8_m", "searchFacetOptionName": "8", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_10_m", "searchFacetOptionValue": "_bottoms_size_petite_10_m", "searchFacetOptionName": "10", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_12_l", "searchFacetOptionValue": "_bottoms_size_petite_12_l", "searchFacetOptionName": "12", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_14_l", "searchFacetOptionValue": "_bottoms_size_petite_14_l", "searchFacetOptionName": "14", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_16_xl", "searchFacetOptionValue": "_bottoms_size_petite_16_xl", "searchFacetOptionName": "16", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_18_xl", "searchFacetOptionValue": "_bottoms_size_petite_18_xl", "searchFacetOptionName": "18", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_petite_20_xxl", "searchFacetOptionValue": "_bottoms_size_petite_20_xxl", "searchFacetOptionName": "20", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "petite", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxl", "variantId": 1, "dimensionId": 1}}]}, {"searchFacetOptionGroupId": "1", "searchFacetOptionGroupName": "regular", "parentSearchFacetOptionGroupId": "size", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_size_regular_s", "searchFacetOptionValue": "_bottoms_size_regular_s", "searchFacetOptionName": "s", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_m", "searchFacetOptionValue": "_bottoms_size_regular_m", "searchFacetOptionName": "m", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_l", "searchFacetOptionValue": "_bottoms_size_regular_l", "searchFacetOptionName": "l", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_xl", "searchFacetOptionValue": "_bottoms_size_regular_xl", "searchFacetOptionName": "xl", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_xxl", "searchFacetOptionValue": "_bottoms_size_regular_xxl", "searchFacetOptionName": "xxl", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular"}, {"searchFacetOptionId": "_bottoms_size_regular_00_xxs/xs", "searchFacetOptionValue": "_bottoms_size_regular_00_xxs/xs", "searchFacetOptionName": "00", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxs/xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_0_xs", "searchFacetOptionValue": "_bottoms_size_regular_0_xs", "searchFacetOptionName": "0", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_2_xs", "searchFacetOptionValue": "_bottoms_size_regular_2_xs", "searchFacetOptionName": "2", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_4_s", "searchFacetOptionValue": "_bottoms_size_regular_4_s", "searchFacetOptionName": "4", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_6_s", "searchFacetOptionValue": "_bottoms_size_regular_6_s", "searchFacetOptionName": "6", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_8_m", "searchFacetOptionValue": "_bottoms_size_regular_8_m", "searchFacetOptionName": "8", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_10_m", "searchFacetOptionValue": "_bottoms_size_regular_10_m", "searchFacetOptionName": "10", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_12_l", "searchFacetOptionValue": "_bottoms_size_regular_12_l", "searchFacetOptionName": "12", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_14_l", "searchFacetOptionValue": "_bottoms_size_regular_14_l", "searchFacetOptionName": "14", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_16_xl", "searchFacetOptionValue": "_bottoms_size_regular_16_xl", "searchFacetOptionName": "16", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_18_xl", "searchFacetOptionValue": "_bottoms_size_regular_18_xl", "searchFacetOptionName": "18", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_regular_20_xxl", "searchFacetOptionValue": "_bottoms_size_regular_20_xxl", "searchFacetOptionName": "20", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "regular", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxl", "variantId": 1, "dimensionId": 1}}]}, {"searchFacetOptionGroupId": "2", "searchFacetOptionGroupName": "tall", "parentSearchFacetOptionGroupId": "size", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_size_tall_s", "searchFacetOptionValue": "_bottoms_size_tall_s", "searchFacetOptionName": "s", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall"}, {"searchFacetOptionId": "_bottoms_size_tall_xl", "searchFacetOptionValue": "_bottoms_size_tall_xl", "searchFacetOptionName": "xl", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall"}, {"searchFacetOptionId": "_bottoms_size_tall_00_xxs/xs", "searchFacetOptionValue": "_bottoms_size_tall_00_xxs/xs", "searchFacetOptionName": "00", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxs/xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_0_xs", "searchFacetOptionValue": "_bottoms_size_tall_0_xs", "searchFacetOptionName": "0", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_2_xs", "searchFacetOptionValue": "_bottoms_size_tall_2_xs", "searchFacetOptionName": "2", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xs", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_4_s", "searchFacetOptionValue": "_bottoms_size_tall_4_s", "searchFacetOptionName": "4", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_6_s", "searchFacetOptionValue": "_bottoms_size_tall_6_s", "searchFacetOptionName": "6", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "s", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_8_m", "searchFacetOptionValue": "_bottoms_size_tall_8_m", "searchFacetOptionName": "8", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_10_m", "searchFacetOptionValue": "_bottoms_size_tall_10_m", "searchFacetOptionName": "10", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "m", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_12_l", "searchFacetOptionValue": "_bottoms_size_tall_12_l", "searchFacetOptionName": "12", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_14_l", "searchFacetOptionValue": "_bottoms_size_tall_14_l", "searchFacetOptionName": "14", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "l", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_16_xl", "searchFacetOptionValue": "_bottoms_size_tall_16_xl", "searchFacetOptionName": "16", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_18_xl", "searchFacetOptionValue": "_bottoms_size_tall_18_xl", "searchFacetOptionName": "18", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xl", "variantId": 1, "dimensionId": 1}}, {"searchFacetOptionId": "_bottoms_size_tall_20_xxl", "searchFacetOptionValue": "_bottoms_size_tall_20_xxl", "searchFacetOptionName": "20", "isActive": "true", "isSelected": "false", "parentSearchFacetOptionGroupId": "tall", "attributeMap": {"index": 0, "searchFacetOptionName2": "xxl", "variantId": 1, "dimensionId": 1}}]}]}, {"searchFacetOptionGroupId": "inseam", "searchFacetOptionGroupName": "inseam", "parentSearchFacetOptionGroupId": "bottoms", "searchFacetOptionGroupList": [{"searchFacetOptionGroupId": "1", "searchFacetOptionGroupName": "regular", "parentSearchFacetOptionGroupId": "inseam", "searchFacetOptionList": [{"searchFacetOptionId": "_bottoms_inseam_regular_regular", "searchFacetOptionValue": "_bottoms_inseam_regular_regular", "searchFacetOptionName": "regular", "isActive": "true", "isSelected": "true", "parentSearchFacetOptionGroupId": "regular"}]}]}]}]}, {"isActive": "true", "searchFacetId": "color", "searchFacetName": "Color", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isSelected": "false", "isActive": "true", "searchFacetOptionId": "yellow", "searchFacetOptionName": "yellow", "searchFacetOptionValue": "yellow"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "green", "searchFacetOptionName": "green", "searchFacetOptionValue": "green"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "blue", "searchFacetOptionName": "blue", "searchFacetOptionValue": "blue"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "purple", "searchFacetOptionName": "purple", "searchFacetOptionValue": "purple"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "pink", "searchFacetOptionName": "pink", "searchFacetOptionValue": "pink"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "beige", "searchFacetOptionName": "beige", "searchFacetOptionValue": "beige"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "gray", "searchFacetOptionName": "gray", "searchFacetOptionValue": "gray"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "silver", "searchFacetOptionName": "silver", "searchFacetOptionValue": "silver"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "black", "searchFacetOptionName": "black", "searchFacetOptionValue": "black"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "white", "searchFacetOptionName": "white", "searchFacetOptionValue": "white"}, {"isSelected": "false", "isActive": "true", "searchFacetOptionId": "multi", "searchFacetOptionName": "multi", "searchFacetOptionValue": "multi"}]}}, {"isActive": "true", "searchFacetId": "price", "searchFacetName": "Price", "searchFacetOptionGroupList": {"searchFacetOptionList": [{"isActive": "true", "isSelected": "false", "searchFacetOptionId": "MIN", "searchFacetOptionName": "37.97", "searchFacetOptionValue": "37.97"}, {"isActive": "true", "isSelected": "false", "searchFacetOptionId": "MAX", "searchFacetOptionName": "105", "searchFacetOptionValue": "105"}]}}]}, "productCategory": {"productCategoryPaginator": {"pageNumberTotal": "1", "pageNumberRequested": "0"}, "isOutfitCategory": "false", "childProducts": [{"parentBusinessCatalogItemId": "839919", "name": "Boxy Cropped Shirt", "categoryLargeImage": {"path": "https://www.gap.com/webcontent/0028/820/968/cn28820968.jpg"}, "pristineImages": {"pristine1ImagePath": "webcontent/0028/820/956/cn28820956.jpg"}, "zoomImages": {"p01ZoomImagePath": "https://www.gap.com/webcontent/0028/820/956/cn28820956.jpg"}, "currentPrice": "75.00", "originalPrice": "75.00", "price": {"currentMaxPrice": "75.00", "currentMinPrice": "75.00", "localizedCurrentMaxPrice": "$75.00", "localizedCurrentMinPrice": "$75.00", "localizedRegularMaxPrice": "$75.00", "localizedRegularMinPrice": "$75.00", "minPercentageOff": "0", "maxPercentageOff": "0", "priceType": "1", "regularMaxPrice": "75.00", "regularMinPrice": "75.00"}, "defaultSizeVariantId": "1", "marketingFlag": {"marketingFlagName": "New!"}, "numVariantsFiltered": 0, "swatchesProps": {"overflowBehavior": "carousel", "size": "small", "colorArray": [{"colorName": "Yellow Stripe", "inStock": true, "id": "839919042", "productImage": "https://www.gap.com/webcontent/0028/960/196/cn28960196.jpg", "productImageAltText": "Boxy Cropped Shirt Yellow Stripe", "url": "https://www.gap.com/webcontent/0028/960/098/cn28960098.jpg"}, {"colorName": "White & Navy Stripe", "inStock": true, "id": "839919032", "productImage": "https://www.gap.com/webcontent/0028/463/987/cn28463987.jpg", "productImageAltText": "Boxy Cropped Shirt White & Navy Stripe", "url": "https://www.gap.com/webcontent/0028/463/578/cn28463578.jpg"}, {"colorName": "White", "inStock": true, "id": "839919012", "productImage": "https://www.gap.com/webcontent/0028/820/968/cn28820968.jpg", "productImageAltText": "Boxy Cropped <PERSON><PERSON>", "url": "https://www.gap.com/webcontent/0028/820/723/cn28820723.jpg"}, {"colorName": "<PERSON> Green", "inStock": true, "id": "839919052", "productImage": "https://www.gap.com/webcontent/0029/191/351/cn29191351.jpg", "productImageAltText": "Boxy Cropped Shirt <PERSON>", "url": "https://www.gap.com/webcontent/0028/860/989/cn28860989.jpg"}]}, "businessCatalogItemId": "839919012", "isDropship": false, "additionalQueryParams": "&searchText=jeans"}, {"parentBusinessCatalogItemId": "836088", "name": "TENCEL&#153-<PERSON><PERSON>", "categoryLargeImage": {"path": "https://www.gap.com/webcontent/0028/754/782/cn28754782.jpg"}, "pristineImages": {"pristine1ImagePath": "webcontent/0028/754/806/cn28754806.jpg"}, "zoomImages": {"p01ZoomImagePath": "https://www.gap.com/webcontent/0028/754/806/cn28754806.jpg"}, "currentPrice": "105.00", "originalPrice": "150.00", "price": {"currentMaxPrice": "105.00", "currentMinPrice": "105.00", "localizedCurrentMaxPrice": "$105.00", "localizedCurrentMinPrice": "$105.00", "localizedRegularMaxPrice": "$150.00", "localizedRegularMinPrice": "$150.00", "minPercentageOff": "0", "maxPercentageOff": "0", "priceType": "3", "regularMaxPrice": "150.00", "regularMinPrice": "150.00"}, "defaultSizeVariantId": "1", "marketingFlag": {"marketingFlagName": "Final sale. No returns or exchanges."}, "numVariantsFiltered": 0, "swatchesProps": {"overflowBehavior": "carousel", "size": "small", "colorArray": [{"colorName": "Oatmeal", "inStock": false, "id": "836088002", "productImage": "https://www.gap.com/webcontent/0028/754/782/cn28754782.jpg", "productImageAltText": "TENCEL&#153-Linen <PERSON>igan Oatmeal", "url": "https://www.gap.com/webcontent/0028/754/561/cn28754561.jpg"}, {"colorName": "Black", "inStock": true, "id": "836088022", "productImage": "https://www.gap.com/webcontent/0028/718/578/cn28718578.jpg", "productImageAltText": "TENCEL&#153-<PERSON><PERSON>", "url": "https://www.gap.com/webcontent/0028/718/463/cn28718463.jpg"}]}, "businessCatalogItemId": "836088002", "isDropship": false, "additionalQueryParams": "&searchText=jeans"}, {"parentBusinessCatalogItemId": "860355", "name": "Cross-Front Sweater Tank", "categoryLargeImage": {"path": "https://www.gap.com/webcontent/0029/112/474/cn29112474.jpg"}, "pristineImages": {"pristine1ImagePath": "webcontent/0029/112/478/cn29112478.jpg"}, "zoomImages": {"p01ZoomImagePath": "https://www.gap.com/webcontent/0029/112/478/cn29112478.jpg"}, "currentPrice": "70.00", "originalPrice": "70.00", "price": {"currentMaxPrice": "70.00", "currentMinPrice": "70.00", "localizedCurrentMaxPrice": "$70.00", "localizedCurrentMinPrice": "$70.00", "localizedRegularMaxPrice": "$70.00", "localizedRegularMinPrice": "$70.00", "minPercentageOff": "0", "maxPercentageOff": "0", "priceType": "1", "regularMaxPrice": "70.00", "regularMinPrice": "70.00"}, "defaultSizeVariantId": "1", "numVariantsFiltered": 0, "swatchesProps": {"overflowBehavior": "carousel", "size": "small", "colorArray": [{"colorName": "Teal Ocean", "inStock": true, "id": "860355002", "productImage": "https://www.gap.com/webcontent/0029/112/474/cn29112474.jpg", "productImageAltText": "Cross-Front Sweater Tank Teal Ocean", "url": "https://www.gap.com/webcontent/0029/112/374/cn29112374.jpg"}, {"colorName": "Black", "inStock": true, "id": "860355012", "productImage": "https://www.gap.com/webcontent/0029/322/675/cn29322675.jpg", "productImageAltText": "Cross-Front Sweater Tank Black", "url": "https://www.gap.com/webcontent/0029/045/143/cn29045143.jpg"}, {"colorName": "Pink Dragonfruit", "inStock": true, "id": "860355022", "productImage": "https://www.gap.com/webcontent/0029/097/844/cn29097844.jpg", "productImageAltText": "Cross-Front Sweater Tank Pink Dragonfruit", "url": "https://www.gap.com/webcontent/0029/097/644/cn29097644.jpg"}]}, "businessCatalogItemId": "860355002", "isDropship": false, "additionalQueryParams": "&searchText=jeans"}]}}, "status": "normal"}