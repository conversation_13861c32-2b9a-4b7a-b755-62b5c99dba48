// @ts-nocheck
import { LegacyPDSSearchResponse, STATUSES } from '../components/fetch-products';

export const searchReponseWaitingStatus: LegacyPDSSearchResponse = {
  productCategoryFacetedSearch: {
    areAllSubCatsOutfit: 'false',
    isOutfitCategory: 'false',
    searchDivName: '',
    isRedirect: 'false',
    searchText: 'jeans',
    totalItemCount: '8',
    searchFacetInfo: {
      searchFacetList: [],
    },
    productCategory: {
      productCategoryPaginator: { pageNumberTotal: '0', pageNumberRequested: '0' },
      isOutfitCategory: 'false',
      childProducts: [],
    },
  },
  status: STATUSES.WAITING,
};
