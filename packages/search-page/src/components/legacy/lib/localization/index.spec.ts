// @ts-nocheck
import { <PERSON>ronJob } from 'cron';
import localizationBroker from 'localization-broker';
import { mocked } from 'ts-jest/utils';
import {
  fetchTranslations,
  getTranslations,
  SEARCH_PAGE_KEYS,
  startPollingLocalizationBroker,
} from '.';
import logger from '../logger';
import { TRO } from './types';

jest.mock('cron', () => ({
  CronJob: jest.fn(() => ({ start: jest.fn() })),
}));

jest.mock('localization-broker', () => ({
  initTranslations: jest.fn(() => Promise.resolve('done')),
  getTranslations: jest.fn(),
}));

jest.mock('../logger', () => ({
  error: jest.fn(),
}));

const localizationBrokerStub = mocked(localizationBroker);

describe('localization', () => {
  const mfeKey = 'search-page';
  const locale = 'en_US';

  describe('when getTranslations is called', () => {
    describe('and localization-broker has been initialized', () => {
      let result: TRO;
      const errors = ['this is a false alarm'];
      const localeTranslations = {
        translations: {
          'en-US': { translation: { hello: 'hello' } },
          'en-CA': { translation: { hello: 'hello' } },
          'fr-CA': { translation: { hello: 'hello' } },
        },
        errors: ['this is a false alarm'],
      };
      beforeAll(async () => {
        localizationBrokerStub.getTranslations.mockImplementation(() => localeTranslations);
        localizationBrokerStub.localeTranslations = localeTranslations;
        result = await getTranslations(locale);
      });

      afterAll(() => {
        jest.clearAllMocks();
        localizationBrokerStub.localeTranslations = undefined;
      });

      it('should only return translations for requested locale', () => {
        expect(result).toBe(localeTranslations.translations);
      });

      it('should call not reinitialize with TRO', () => {
        expect(localizationBrokerStub.initTranslations).not.toHaveBeenCalled();
      });
      it('should call getTranslations with provided normalized locale and required keys', () => {
        expect(localizationBrokerStub.getTranslations).toHaveBeenCalledWith(
          'en-US',
          SEARCH_PAGE_KEYS,
        );
      });
      it('the logger should be called with an error message', () => {
        expect(logger.error).toHaveBeenCalledWith(errors);
        expect(logger.error).toHaveBeenCalledTimes(1);
      });
    });

    describe('and localization-broker has not been initialized', () => {
      beforeAll(async () => {
        localizationBrokerStub.getTranslations.mockImplementation(() => ({}));
        await getTranslations(locale);
      });

      afterAll(() => {
        jest.clearAllMocks();
      });
      it('should call initialize with mfeKey only', () => {
        expect(localizationBrokerStub.initTranslations).toHaveBeenCalledWith(mfeKey);
      });
      it('should call brokerGetTranslations with provided locale and keys', () => {
        expect(localizationBrokerStub.getTranslations).toHaveBeenCalledWith(
          'en-US',
          SEARCH_PAGE_KEYS,
        );
      });
      it('should not call the logger with an error', () => {
        expect(logger.error).not.toHaveBeenCalled();
      });
    });
  });

  describe('when startPollingLocalizationBroker is called', () => {
    describe('and no environment variable is set for TRO_FILE_DOWNLOAD_FREQUENCY_IN_MINUTES', () => {
      let mockJob: CronJob;
      beforeAll(() => {
        mockJob = startPollingLocalizationBroker();
      });

      afterAll(() => {
        jest.clearAllMocks();
      });

      it('should call CronJob constructor with default time and initialisation function', () => {
        expect(CronJob).toHaveBeenCalledWith('*/5 * * * *', fetchTranslations);
      });

      it('should call the start function on the job', () => {
        expect(mockJob.start).toHaveBeenCalled();
      });
    });

    describe('and TRO_FILE_DOWNLOAD_FREQUENCY_IN_MINUTES is set at 15', () => {
      beforeAll(() => {
        process.env.TRO_FILE_DOWNLOAD_FREQUENCY_IN_MINUTES = '15';
        startPollingLocalizationBroker();
      });

      afterAll(() => {
        delete process.env.TRO_FILE_DOWNLOAD_FREQUENCY_IN_MINUTES;
        jest.clearAllMocks();
      });

      it('should call CronJob constructor with 15 min time and initialisation function', () => {
        expect(CronJob).toHaveBeenCalledWith('*/15 * * * *', fetchTranslations);
      });
    });
  });
});
