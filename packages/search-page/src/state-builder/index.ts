import { getBrandInformation } from '@mfe/brand-info';
import { Brand, Market, Locale } from '@ecom-next/utils/server';
import type { EnabledFeatures } from '@ecom-next/plp-ui/legacy/features';
import { getAppConfig } from '@ecom-next/sitewide/state-builder-configs';
import getEngineEndpointConfiguration from '../components/legacy/lib/state-builder/engine-endpoint-configuration';
import { getBrandSecureUrl } from './utils';

const ecomBaseUS = process.env.ECOM_CLIENT_API_BASE_URL_US || '';
const ecomBaseCA = process.env.ECOM_CLIENT_API_BASE_URL_CA || '';

const ecomFactoryBaseUS = process.env.ECOM_CLIENT_API_BASE_URL_FACTORY_US || '';
const ecomFactoryBaseCA = process.env.ECOM_CLIENT_API_BASE_URL_FACTORY_CA || '';

type SearchPageStateProps = {
  autosuggest: string | string[];
  brand: Brand;
  commerceProductsSearchUrl: string;
  contentType: 'wip' | 'app' | 'ecom';
  country: string;
  enabledFeatures: EnabledFeatures;
  experiments: Record<string, string>;
  featureVariables: unknown;
  locale: Locale;
  market: Market;
  pid?: string;
  recentSearch: string | string[];
  searchParams: { [key: string]: string | string[] | undefined };
  searchText: string;
  selectedNodes: SelectedNode[];
  targetEnv: string;
};

const locationServiceDomains = {
  'us-ecom': ecomBaseUS,
  'ca-ecom': ecomBaseCA,

  'factory-ecom': ecomFactoryBaseUS,
  'ca-factory-ecom': ecomFactoryBaseCA,
};

const getLocationServiceDomain = (brand: Brand, market: Market) => {
  const marketSelector = brand.includes('fs') ? (market === 'ca' ? `ca-factory` : 'factory') : market;
  return locationServiceDomains[`${marketSelector}-ecom`];
};

export const buildState = async ({
  autosuggest,
  recentSearch,
  brand,
  market,
  locale,
  contentType,
  country,
  searchText,
  targetEnv,
  experiments,
  enabledFeatures,
  featureVariables,
  searchParams,
  commerceProductsSearchUrl,
  selectedNodes,
}: SearchPageStateProps) => {
  const brandInformation = getBrandInformation(brand, market);
  const encodedSearchText = encodeURIComponent(searchText || '');

  const brandSecureUrl = getBrandSecureUrl()[targetEnv][brandInformation.marketAwareBrandCode];
  const engineEndpointConfiguration = getEngineEndpointConfiguration(enabledFeatures, market, brand, targetEnv);

  return {
    abbrBrand: brand as Brand,
    brandInformation,
    brandSecureUrl,
    contentType,
    country,
    enabledFeatures,
    featureVariables,
    engineEndpointConfiguration,
    env: targetEnv || 'prod',
    experiments,
    locale,
    locationConfig: {
      lasAttributes: {
        domain: getLocationServiceDomain(brand, market),
        appName: 'PLP',
      },
    },
    market,
    pageType: 'search',
    appConfig: getAppConfig(contentType),
    searchInformation: {
      searchText: encodedSearchText,
      autosuggest: autosuggest as string,
      recentSearch: recentSearch as string,
    },
    searchParams,
    commerceProductsSearchUrl,
    selectedNodes,
  };
};
