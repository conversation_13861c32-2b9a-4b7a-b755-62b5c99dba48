import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { withPersonalizationData } from '@ecom-next/sitewide/personalization-provider';
import { PsDataProvider, PLPStateProvider, ProductList, queryClientOptions, Paginator, PLPDataLayerProvider, translations, ViewMore } from '@ecom-next/plp';
import LocalizationProvider, { Locale } from '@sitewide/providers/localization';
import { BopisProvider } from '../../../core/src/components/legacy/bopis';
import SearchPageTrackingClient from '../utils/SearchPageTrackingClient';
import { SearchResponseProvider } from '../components/legacy/src/search-page/components/fetch-products/search-response-context';
import { SearchProps } from './Search';

function HuiSearchPage(props: SearchProps) {
  const client = new QueryClient({
    ...queryClientOptions,
  });

  const {
    brand,
    locale,
    market,
    searchText,
    commerceProductsSearchUrl,
    abSeg,
    isPreviewEnabled,
    enabledFeatures,
    featureVariables,
    searchInformation,
    businessUnitId,
    abbrNameForTealium,
    locationConfig,
    personalizationData,
    pageType,
    errorLogger,
    marketAwareBrandCode,
    selectedNodes,
  } = props;
  const translation = translations.translationObjects[locale as Locale]?.translation;

  const isPaginationRewriteEnabled = (enabledFeatures?.['plp-hui-q2-pagination-rewrite'] && abSeg?.[`${brand}241`] === 'a') || false;

  return (
    <>
      <SearchResponseProvider searchText={searchText}>
        <SearchPageTrackingClient />
      </SearchResponseProvider>
      <LocalizationProvider locale={locale as Locale} translations={translation} market={market}>
        <QueryClientProvider client={client}>
          <PLPStateProvider
            abSeg={abSeg}
            enabledFeatures={enabledFeatures}
            featureVariables={featureVariables}
            brand={brand}
            isPreviewEnabled={isPreviewEnabled}
            locale={locale}
            market={market}
            pageType={'search'}
            searchText={searchText}
            searchInformation={searchInformation}
            businessUnitId={businessUnitId}
            abbrNameForTealium={abbrNameForTealium}
            productsUrl={commerceProductsSearchUrl}
            marketAwareBrandCode={marketAwareBrandCode}
            selectedNodes={selectedNodes}
          >
            <PLPDataLayerProvider>
              <PsDataProvider url={commerceProductsSearchUrl} searchText={searchText} pageType={'search'}>
                <BopisProvider
                  brand={brand}
                  locationAttributeServiceConfig={locationConfig.lasAttributes}
                  personalizationData={personalizationData}
                  market={market}
                  pageType={pageType}
                  errorLogger={errorLogger as never}
                >
                  <ProductList />
                </BopisProvider>
                {isPaginationRewriteEnabled ? <ViewMore /> : <Paginator />}
              </PsDataProvider>
            </PLPDataLayerProvider>
          </PLPStateProvider>
        </QueryClientProvider>
      </LocalizationProvider>
    </>
  );
}

export default withPersonalizationData(HuiSearchPage);
