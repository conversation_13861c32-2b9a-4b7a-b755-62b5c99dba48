import { findObjProperty } from '../findObjProperty';

describe('findObjProperty', () => {
    it('should return undefined if the property does not exist', () => {
        const obj = { a: 1, b: { c: 2 } };
        const result = findObjProperty(obj, 'd');
        expect(result).toBeUndefined();
    });

    it('should return undefined if the object is null', () => {
        const result = findObjProperty(null, 'a');
        expect(result).toBeUndefined();
    });

    it('should return undefined if the object is not an object', () => {
        const result = findObjProperty(42, 'a');
        expect(result).toBeUndefined();
    });

    it('should return the value of the property if it exists at the top level', () => {
        const obj = { a: 1, b: 2 };
        const result = findObjProperty(obj, 'b');
        expect(result).toBe(2);
    });

    it('should return the value of the property if it exists in a nested object', () => {
        const obj = { a: 1, b: { c: 2 } };
        const result = findObjProperty(obj, 'c');
        expect(result).toBe(2);
    });

    it('should return the value of the property if it exists in a deeply nested object', () => {
        const obj = { a: 1, b: { c: { d: { e: 3 } } } };
        const result = findObjProperty(obj, 'e');
        expect(result).toBe(3);
    });

    it('should return the value of the property if it exists in an array within the object', () => {
        const obj = { a: 1, b: [{ c: 2 }] };
        const result = findObjProperty(obj, 'c');
        expect(result).toBe(2);
    });
});
