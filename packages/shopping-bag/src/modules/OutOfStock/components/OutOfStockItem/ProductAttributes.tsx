import React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { ProductListItemPrice } from './ProductListItemPrice';

type ProductAttributesProps = {
  color: string;
  markdownPrice: number;
  regularPrice: number;
  size: string;
};

export const ProductAttributes = ({
  color,
  markdownPrice,
  regularPrice,
  size,
}: ProductAttributesProps) => {
  const { localize } = useLocalize();

  return (
    <div className='flex flex-col' id='oos-product-attributes'>
      <span className='cb-base-compact capitalize pb-1'>
        {size} | {color}
      </span>
      <span className='text-cb-textColor-error py-1 pb-2 cb-base-compact-emphasis'>
        {localize('oos.outofStock')}
      </span>
      {
        <ProductListItemPrice
          markdownPrice={markdownPrice}
          regularPrice={regularPrice}
        />
      }
    </div>
  );
};
