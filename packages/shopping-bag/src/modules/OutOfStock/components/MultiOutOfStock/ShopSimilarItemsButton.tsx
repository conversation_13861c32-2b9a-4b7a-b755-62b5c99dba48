import React from 'react';
import { Button } from '@ecom-next/core/migration/button';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useDatalayer } from '../../../../hooks/useDataLayer';
import { useActiveBagContext } from '../../../../contexts/ActiveBagProvider';
import { UserStatus } from '../../../../types/bag-types';

type ShopSimilarItemsButtonProps = {
  itemId: string;
  setOosMultiCarousel: (value: boolean) => void;
  title: string;
};

export const ShopSimilarItemsButton = ({ title, itemId, setOosMultiCarousel }: ShopSimilarItemsButtonProps) => {
  const { publishDataLayerEvent, getRecognitionStatus } = useDatalayer();
  const { localize } = useLocalize();
  const shopSimilarItemsText = localize('oos.shopSimilarItems');
  const {
    bagState: { data },
  } = useActiveBagContext();
  const recognition_status = getRecognitionStatus(data?.bagAttributes?.userStatus as UserStatus);

  const handleClick = () => {
    setOosMultiCarousel(true);
    publishDataLayerEvent({
      type: 'link',
      name: 'shop_similar_items_click',
      source: 'ShoppingBag',
      extraAttrs: { recognition_status },
    });
  };

  return (
    <div
      className='cursor-pointer px-1 py-2.5'
      onClick={handleClick}
      role='button'
      tabIndex={0}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleClick();
        }
      }}
    >
      <Button
        aria-label={`Shop Similar Items ${title}`}
        id={`oos-see-similar-button-${itemId}`}
        kind='text-underline-small'
        className='cb-base-compact flex font-normal'
      >
        {shopSimilarItemsText}
      </Button>
    </div>
  );
};
