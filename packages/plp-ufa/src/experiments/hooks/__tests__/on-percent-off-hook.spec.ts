import { renderHook } from 'test-utils';
import { usePLPState } from '../../../data';
import { useOldNavyPercentOff } from '../on-percent-off-hook';

jest.mock('../../../data');

describe('useOldNavyPercentOff', () => {
  it('should return true if correct values are informed', () => {
    (usePLPState as jest.Mock).mockReturnValue({ brand: 'on', abSeg: { on225: 'a' }, enabledFeatures: { 'plp-percentage-off': true } });
    const { result } = renderHook(() => useOldNavyPercentOff());
    expect(result.current).toBe(true);
  });
});
