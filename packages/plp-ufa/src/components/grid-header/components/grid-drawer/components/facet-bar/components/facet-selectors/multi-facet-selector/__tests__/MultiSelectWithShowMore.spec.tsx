import { fireEvent, render, screen } from 'test-utils';
import { NewFacet, translations } from '@ecom-next/plp-ufa';
import { MultiSelectWithShowMore } from '../MultiSelectWithShowMore';

const mockFacet: NewFacet = {
  name: 'mockName',
  localeName: 'Mock Name',
  selectionType: 'multi-select',
  options: [],
  type: 'simple',
};

const fewFacets = [
  { id: '1', name: 'mockName1', applied: false },
  { id: '2', name: 'mockName2', applied: false },
  { id: '3', name: 'mockName3', applied: false },
];

const moreFacets = [
  { id: '4', name: 'mockName4', applied: false },
  { id: '5', name: 'mockName5', applied: false },
  { id: '6', name: 'mockName6', applied: false },
  { id: '7', name: 'mockName7', applied: false },
  { id: '8', name: 'mockName8', applied: false },
];

const customRenderProps = {
  localization: {
    translations: {
      'en-US': translations.translationObjects.en_US
    }, 
    formatCurrency: jest.fn(),
    localize: jest.fn((key: string, replace?: Record<string, string | number>) => ({
      'facet_reference.show_more_toggle': `+ Show ${replace?.count} More`,
      'facet_reference.show_less_toggle': '- Show Less',
    }[key] || '')) 
  },
}

describe('<MultiSelectWithShowMore />', () => {
  it('should render the multi select check box component with styles', () => {
    const styleClasses = 'text-color-font-default mt-4 flex w-full flex-col gap-4';
    mockFacet.options = fewFacets;
    render(<MultiSelectWithShowMore facet={mockFacet} />, { customRenderProps });

    const wrapper = screen.getByTestId('plp__grid-drawer__check-box');
    expect(wrapper).toHaveClass(styleClasses);
  });

  it('should not display the show more button when the options are not more than 6', () => {
    mockFacet.options = fewFacets;
    render(<MultiSelectWithShowMore facet={mockFacet} />, { customRenderProps });

    const wrapper = screen.getByTestId('plp__grid-drawer__check-box');
    expect(wrapper).not.toHaveClass('plp_grid-drawer-show-more__toggle');
  });

  it('should display show more toggle with correct count when options > 6', () => {
    mockFacet.options = [...fewFacets, ...moreFacets];
    render(<MultiSelectWithShowMore facet={mockFacet} />, { customRenderProps });

    const showMoreToggle = screen.queryByRole('link', { name: '+ Show 2 More' });
    expect(showMoreToggle).toBeInTheDocument();
    expect(showMoreToggle).toHaveClass('plp_grid-drawer-show-more__toggle');

    const showLessToggle = screen.queryByRole('link', { name: '- Show Less' });
    expect(showLessToggle).not.toBeInTheDocument();
  });

  it('should display show less toggle when clicking show more', () => {
    mockFacet.options = [...fewFacets, ...moreFacets];
    render(<MultiSelectWithShowMore facet={mockFacet} />, { customRenderProps });
    const showMoreToggle = screen.getByRole('link', { name: '+ Show 2 More' });
    fireEvent.click(showMoreToggle);

    const showLessToggle = screen.queryByRole('link', { name: '- Show Less' });
    expect(showLessToggle).toBeInTheDocument();
    expect(showLessToggle).toHaveClass('plp_grid-drawer-show-more__toggle');
    expect(showMoreToggle).not.toBeInTheDocument();
  });

  it('should display show more toggle after closing using show less', () => {
    mockFacet.options = [...fewFacets, ...moreFacets];
    render(<MultiSelectWithShowMore facet={mockFacet} />, { customRenderProps });
    const showMoreToggle = screen.getByRole('link', { name: '+ Show 2 More' });
    fireEvent.click(showMoreToggle);

    const showLessToggle = screen.getByRole('link', { name: '- Show Less' });
    fireEvent.click(showLessToggle);
    expect(showLessToggle).not.toBeInTheDocument();

    const nextShowMoreToggle = screen.getByRole('link', { name: '+ Show 2 More' });
    expect(nextShowMoreToggle).toBeInTheDocument();
    expect(nextShowMoreToggle).toHaveClass('plp_grid-drawer-show-more__toggle');
  });
});
