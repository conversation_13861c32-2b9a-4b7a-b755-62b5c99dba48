import { UpdateFacetOption, OnFacetHandlerProps } from './types';

export const colorIdMap: Record<string, string> = {
  '1002': 'clear',
  '1005': 'silver',
  '1017': 'black',
  '1018': 'gray',
  '1019': 'red',
  '1020': 'pink',
  '1021': 'blue',
  '1022': 'green',
  '1023': 'brown',
  '1026': 'gold',
  '1028': 'purple',
  '1029': 'white',
  '1031': 'yellow',
  '1034': 'orange',
  '1040': 'beige',
  '1025': 'multi',
};

export const updateFacetByType = {
  simple(updateParameter: UpdateFacetOption, props: OnFacetHandlerProps) {
    const simpleFacetOption = {
      applied: props?.applied,
      displayName: props?.name as string,
      name: props?.facetName as string,
      value: props?.id as string,
      isActive: true,
    };
    const facetOption = { ...props, ...simpleFacetOption };
    updateParameter(facetOption);
  },
  range(updateParameter: UpdateFacetOption, props: OnFacetHandlerProps) {
    const { facetName, range, isSelected } = props;
    const value = `${range?.min}-${range?.max}`;
    const rangeFacetOption = {
      applied: isSelected,
      isActive: true,
      name: facetName as string,
      value: value,
      id: value,
      displayName: value,
    };
    const facetOption = { ...props, ...rangeFacetOption };
    updateParameter(facetOption);
  },
  complex(updateParameter: UpdateFacetOption, props: OnFacetHandlerProps) {
    const complexFacetOption = {
      applied: props?.isSelected,
      isActive: true,
      name: props?.facetName as string,
      value: props?.id,
      displayName: props?.facetOption?.name as string,
    };
    const facetOption = { ...props, ...complexFacetOption };
    updateParameter(facetOption);
  },
};
