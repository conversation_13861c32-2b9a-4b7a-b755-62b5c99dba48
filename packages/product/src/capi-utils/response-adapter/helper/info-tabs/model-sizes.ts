import type { Locale } from '../../../types';

export type ModelSizes = {
  height: string;
  size: string;
};

const annotationRegex = (annotation: string): RegExp => new RegExp(`${annotation}\\s*([\\s\\S]+)\\s*${annotation}`, 'm');
const modelSizes = '@modelsizes';

export const removeModelSizes = (text: string): string => {
  if (text.length > 0) {
    return text.replace(annotationRegex(modelSizes), '');
  }
  return '';
};

const extractModelSizesAnnotation = (text: string): string => {
  if (text.length > 0) {
    const match = text.match(annotationRegex(modelSizes));
    return match && match[1] ? match[1] : '';
  }
  return '';
};

const parseModelSizes = (modelSizesText: string, locale: Locale): ModelSizes[] =>
  modelSizesText
    .split('|')
    .map(modelSizeText => modelSizeText.trim())
    .map(modelSize => {
      if (locale === 'fr_CA') {
        const [height, size] = modelSize.split('m');
        const everythingAfterTailleRegex = /(?<=taille).*$/;
        const sizeValue = everythingAfterTailleRegex.exec(size);
        return { height: height.trim(), size: Array.isArray(sizeValue) ? sizeValue[0].trim() : '' };
      }

      const [height, size] = modelSize.split(':');
      return { height, size };
    });

const extractModelSizes = (description: string, locale: Locale): ModelSizes[] => {
  const annotation = extractModelSizesAnnotation(description);
  if (annotation.length > 0) {
    return parseModelSizes(annotation, locale);
  }
  return [];
};

const containsModelSizes = (text: string): boolean => {
  return text.length > 0 ? !!text.match(modelSizes) : false;
};

export const createModelSizes = (bullets: string[], locale: Locale): ModelSizes[][] => {
  const modelSizes: ModelSizes[][] = [];
  bullets.forEach(attribute => {
    if (containsModelSizes(attribute)) {
      modelSizes.push(extractModelSizes(attribute, locale));
    }
  });
  return modelSizes;
};
