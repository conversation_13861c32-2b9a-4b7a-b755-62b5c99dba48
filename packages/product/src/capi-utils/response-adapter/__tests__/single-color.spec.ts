// @ts-nocheck
import type { Locale, ProductData } from '../../types';
import ChasBaseResponse from '../mocks/chas-cc-response.mock.json';
import { singleColorResult, singleColorResultWithDropship } from '../mocks/chas-single-color-result.mock';
import { singleColorResponseAdapter } from '../single-color';

const styleMarketingFlag = [
  {
    effectiveEndDate: '4000-01-02T04:59:59.999Z',
    effectiveStartDate: '2022-03-21T04:00:00.000Z',
    flagContent: 'Best Price',
    positionNumber: 1,
  },
];

const colorMarketingFlag = [
  {
    flagContent: 'Hot Deal',
    positionNumber: 1,
  },
];

const singleColorAdapterTestHelper = (res, locale = 'en_US') => {
  return singleColorResponseAdapter(res as ProductData, locale as Locale);
};

const getResponseWithCustomData = (styleMarketingFlag, colorMarketingFlag) => {
  return {
    ...ChasBaseResponse,
    styles: [
      {
        ...ChasBaseResponse.styles[0],
        variants: [
          {
            ...ChasBaseResponse.styles[0].variants[0],
            customerChoices: [
              {
                ...ChasBaseResponse.styles[0].variants[0].customerChoices[0],
                marketingFlags: colorMarketingFlag || null,
              },
            ],
            marketingFlags: styleMarketingFlag || null,
          },
        ],
      },
    ],
  };
};

describe('single color adapter', () => {
  const baseResponse = singleColorAdapterTestHelper(ChasBaseResponse);

  test('is an object', () => {
    expect(baseResponse).toBeInstanceOf(Object);
  });

  test('returns correct object without dropship', () => {
    expect(baseResponse).toStrictEqual(singleColorResult);
  });

  test('returns correct object with dropship', () => {
    const mockChasDataWithDropDhip = {
      ...ChasBaseResponse,
      styles: [
        {
          ...ChasBaseResponse.styles[0],
          isExcludedFromPromotion: true,
          webProductType: 'dropship',
        },
      ],
    };

    const adaptedResponse = singleColorAdapterTestHelper(mockChasDataWithDropDhip);
    expect(adaptedResponse).toStrictEqual(singleColorResultWithDropship);
  });

  describe('marketingFlag', () => {
    describe('GIVEN color level marketing flag is present', () => {
      test('WHEN style level flag is NOT present, THEN colorlevel marketing flag should be returned', () => {
        const styleFlag = null;
        const colorFlag = [...colorMarketingFlag];
        const chasResponseWithMarketingFlag = getResponseWithCustomData(styleFlag, colorFlag);
        const adaptedResponse = singleColorAdapterTestHelper(chasResponseWithMarketingFlag);

        expect(adaptedResponse.marketingFlag).toStrictEqual(colorFlag[0].flagContent);
      });

      test('WHEN style level flag is also present, THEN color marketing flag should be returned', () => {
        const colorFlag = [...colorMarketingFlag];
        const styleFlag = [...styleMarketingFlag];
        const chasResponseWithMarketingFlag = getResponseWithCustomData(styleFlag, colorFlag);
        const adaptedResponse = singleColorAdapterTestHelper(chasResponseWithMarketingFlag);

        expect(adaptedResponse.marketingFlag).toStrictEqual(colorFlag[0].flagContent);
      });
    });

    describe('GIVEN color level marketing flag is NOT present', () => {
      test('WHEN style level flag is present, THEN styleLevel marketing flag should be returned', () => {
        const colorFlag = null;
        const styleFlag = [...styleMarketingFlag];
        const chasResponseWithMarketingFlag = getResponseWithCustomData(styleFlag, colorFlag);
        const adaptedResponse = singleColorAdapterTestHelper(chasResponseWithMarketingFlag);

        expect(adaptedResponse.marketingFlag).toStrictEqual(styleFlag[0].flagContent);
      });

      test('WHEN style level flag is also NOT present, THEN colorlevel marketing flag should be null', () => {
        const colorFlag = null;
        const styleFlag = null;
        const chasResponseWithMarketingFlag = getResponseWithCustomData(styleFlag, colorFlag);
        const adaptedResponse = singleColorAdapterTestHelper(chasResponseWithMarketingFlag);

        expect(adaptedResponse.marketingFlag).toBeNull();
      });
    });
  });
  describe('when given a valid response', () => {
    test('should return adapted single color response', () => {
      const adaptedResponse = singleColorAdapterTestHelper(ChasBaseResponse);
      expect(adaptedResponse).toStrictEqual(singleColorResult);
    });
  });
  describe('when given a valid response containing super pdp phase 2 props', () => {
    const vetoAttributes = {
      '000123456': {
        copyHeaders: [
          {
            bullets: ['rib-knit collar', 'long sleeves', 'three-button placket', 'vented sides at hem', 'online exclusive', '#123456'],
            description: 'Mega soft & no itchy tags.',
            id: 'overview',
            label: 'Product Details',
            links: [],
            name: 'Overview',
          },
          {
            bullets: ['relaxed fit', 'hits below waist'],
            description: null,
            id: 'fitAndSizing',
            label: 'Fit & Sizing',
            links: [],
            name: 'Fit',
          },
          {
            bullets: ['soft pique-knit cotton', '97% cotton', '3% spandex', 'Imported.'],
            description: null,
            id: 'fabric',
            label: 'Materials & Care',
            links: [],
            name: 'Fabric',
          },
        ],
        marketingFlags: [
          {
            effectiveEndDate: '2400-01-02T07:59:59.999Z',
            effectiveStartDate: '2023-07-20T07:00:00.000Z',
            flagContent: 'Final Sale',
            positionNumber: 1,
          },
        ],
      },
      '000581569': {
        copyHeaders: [
          {
            bullets: ['rib-knit collar', 'long sleeves', 'three-button placket', 'vented sides at hem', 'online exclusive', '#581569'],
            description: 'Mega soft & no itchy tags.',
            id: 'overview',
            label: 'Product Details',
            links: [],
            name: 'Overview',
          },
          {
            bullets: ['relaxed fit', 'hits below waist'],
            description: null,
            id: 'fitAndSizing',
            label: 'Fit & Sizing',
            links: [],
            name: 'Fit',
          },
          {
            bullets: ['soft pique-knit cotton', '97% cotton', '3% spandex', 'Imported.'],
            description: null,
            id: 'fabric',
            label: 'Materials & Care',
            links: [],
            name: 'Fabric',
          },
        ],
        marketingFlags: null,
      },
    };
    beforeEach(() => {
      jest.clearAllMocks();
    });
    describe('when we have both style & color level marketing flags', () => {
      test('should return adapted single color response', () => {
        const responseWithSuperPdpPhase2Props = {
          ...ChasBaseResponse,
          styles: [
            {
              ...ChasBaseResponse.styles[0],
              variants: [
                {
                  ...ChasBaseResponse.styles[0].variants[0],
                  customerChoices: [
                    {
                      ...ChasBaseResponse.styles[0].variants[0].customerChoices[0],
                      manufacturingStyleDescription: 'This is a super pdp phase 2 style',
                      manufacturingStyleNumber: '000123456',
                      marketingFlag: [
                        {
                          effectiveEndDate: '2400-01-02T07:59:59.999Z',
                          effectiveStartDate: '2023-07-20T07:00:00.000Z',
                          flagContent: 'Color Level Flag',
                          positionNumber: 1,
                        },
                      ],
                    },
                  ],
                },
              ],
              vetoAttributes,
            },
          ],
        };
        const adaptedResponse = singleColorAdapterTestHelper(responseWithSuperPdpPhase2Props);
        const expectedResponse = {
          ...singleColorResult,
          marketingFlag: 'Final Sale',
          name: 'This is a super pdp phase 2 style',
        };
        expect(adaptedResponse).toStrictEqual(expectedResponse);
      });
    });
    describe('when we only have style level marketing flags', () => {
      test('should return adapted single color response', () => {
        const responseWithSuperPdpPhase2Props = {
          ...ChasBaseResponse,
          styles: [
            {
              ...ChasBaseResponse.styles[0],
              variants: [
                {
                  ...ChasBaseResponse.styles[0].variants[0],
                  customerChoices: [
                    {
                      ...ChasBaseResponse.styles[0].variants[0].customerChoices[0],
                      manufacturingStyleDescription: 'This is a super pdp phase 2 style',
                      manufacturingStyleNumber: '000123456',
                    },
                  ],
                },
              ],
              vetoAttributes,
            },
          ],
        };
        const adaptedResponse = singleColorAdapterTestHelper(responseWithSuperPdpPhase2Props);
        const expectedResponse = {
          ...singleColorResult,
          marketingFlag: 'Final Sale',
          name: 'This is a super pdp phase 2 style',
        };
        expect(adaptedResponse).toStrictEqual(expectedResponse);
      });
    });
    describe('when we do not have any marketing flags', () => {
      test('should return adapted single color response', () => {
        const responseWithSuperPdpPhase2Props = {
          ...ChasBaseResponse,
          styles: [
            {
              ...ChasBaseResponse.styles[0],
              variants: [
                {
                  ...ChasBaseResponse.styles[0].variants[0],
                  customerChoices: [
                    {
                      ...ChasBaseResponse.styles[0].variants[0].customerChoices[0],
                      manufacturingStyleDescription: 'This is a super pdp phase 2 style',
                      manufacturingStyleNumber: '000581569',
                    },
                  ],
                  marketingFlags: null,
                },
              ],
              vetoAttributes,
            },
          ],
        };
        const adaptedResponse = singleColorAdapterTestHelper(responseWithSuperPdpPhase2Props);
        const expectedResponse = {
          ...singleColorResult,
          marketingFlag: null,
          name: 'This is a super pdp phase 2 style',
        };
        expect(adaptedResponse).toStrictEqual(expectedResponse);
      });
    });
  });
});
