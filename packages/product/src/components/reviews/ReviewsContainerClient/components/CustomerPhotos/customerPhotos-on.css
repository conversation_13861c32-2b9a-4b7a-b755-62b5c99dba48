.onImageStyles {
  .p-w-r {
    margin: 15px 0;
    padding: 0 0.75rem;
    .pr-ggl {
      display: grid;
      grid-template-columns: repeat(4, 1fr);

      @media (min-width: 569px) {
        grid-template-columns: repeat(8, 1fr);
      }
    }

    .pr-rid-btn-container {
      background: transparent;
      padding: 5px 5px;
      position: relative;
      bottom: -23px;

      .pr-ellipsis-icon {
        display: none;
      }

      .pr-rid-btn-text {
        color: rgb(var(--pdp-color-black-1400)) !important;
        font-family: theme('fontFamily.brand');
        font-weight: 400;
        text-decoration: underline;
        font-size: 1.15em;
      }
    }

    .pr-media-modal .modal__body {
      .button__close,
      .button__close--dark {
        z-index: 10004;
      }
    }

    .pr-media-carousel--light {
      .pr-media-card-content {
        .pr-snippet-rating-decimal,
        .pr-media-card-content-text-headline,
        .pr-media-card-content-text-readreview,
        .pr-rd-author-submission-date,
        .pr-media-card-content-text-date {
          font-family: theme('fontFamily.brand');
          font-weight: 400;
          font-size: 1rem;
          line-height: 1.38;
          color: theme('colors.g2');
          margin: 0;
        }

        .pr-media-card-content-text-readreview {
          color: rgb(var(--pdp-color-black-1400)) !important;
          font-family: theme('fontFamily.brand');
          font-weight: 400;
          text-decoration: underline;
        }

        .pr-snippet-rating-decimal {
          border: 0;
        }

        .pr-media-card-footer-sect1 {
          display: none;
        }

        .pr-media-card-footer-flagging a {
          color: rgb(var(--pdp-color-black-1400)) !important;
          font-family: theme('fontFamily.brand');
          font-weight: 400;
          text-decoration: underline;
          font-size: 1.35em;
        }
      }

      @media (min-width: 1024px) {
        .button__prev {
          left: 50px;
        }

        .button__next {
          right: 50px;
        }
      }

      @media (min-width: 768px) and (max-width: 1024px) {
        .button__prev {
          left: 40px;
        }

        .button__next {
          right: 40px;
        }
      }
    }
    /* Modal opens from Read review link from large customer photos modal */
    .pr-read-review.pr-read-review-in {
      overflow: auto;
      font-family: theme('fontFamily.brand');
      font-weight: 400;
      font-size: 1rem;
      line-height: 1.38;
      color: theme('colors.g2');

      .pr-rd-content-block {
        .pr-rd-author-submission-date time,
        span,
        .pr-read-review dd {
          font-family: theme('fontFamily.brand');
          font-weight: 400;
          font-size: 1rem;
          line-height: 1.38;
          color: theme('colors.g2');
        }
      }

      .pr-star-v4 {
        background-size: 1.2rem 1.2rem;
      }

      .pr-snippet-rating-decimal {
        border: 0;
      }

      .pr-back-to-media {
        color: rgb(var(--pdp-color-black-1400)) !important;
        font-family: theme('fontFamily.brand');
        font-weight: 400;
        text-decoration: underline;
      }

      .pr-rd-helpful-action {
        .pr-rd-helpful-action-legend,
        .pr-rd-helpful-action-btn-group {
          display: none;
        }
      }

      .pr-accordion-btn,
      .pr-accordion-btn span,
      .pr-rd-flag-review-container .pr-underline {
        font-family: theme('fontFamily.brand');
        font-weight: 400;
        color: theme('colors.b1');
        font-size: 1rem;
        line-height: 1.38;
      }
      .pr-accordion-btn {
        .pr-caret-icon__line {
          stroke: theme('colors.b1');
        }
      }
    }

    /* Flag image/review form modal */
    .pr-modal-flagging-form {
      .pr-inner-flagging-form-container {
        font-family: theme('fontFamily.brand');
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.38;
        color: theme('colors.g2');
      }
    }
  }
}
