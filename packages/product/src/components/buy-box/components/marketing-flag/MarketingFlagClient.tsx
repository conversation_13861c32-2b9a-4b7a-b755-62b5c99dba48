'use client';
import React from 'react';
import { useShallow } from 'zustand/shallow';
import { MarketingFlagInTitleProps } from '../../../../../src/legacy/src/app/components/buy-box/components/marketing-flag/types';
import { useBuyBoxStore } from '../../../../providers/buybox-provider/index';

type MarketingFlagClientProps = {
  isStyleLevelFlag: boolean;
  isSuperPdpPh2?: boolean;
};

export const MarketingFlagClient = ({ isStyleLevelFlag, isSuperPdpPh2 = false }: MarketingFlagClientProps) => {
  const { colorPromoFlag, marketingFlagFromSelectedColor, marketingFlagFromProductData } = useBuyBoxStore(
    useShallow(state => ({
      colorPromoFlag: state.selectedColor?.marketingFlag,
      marketingFlagFromSelectedColor: state.selectedColor?.styleMarketingFlags,
      marketingFlagFromProductData: state.marketingFlag,
    }))
  );
  const flagContent = isSuperPdpPh2 && marketingFlagFromSelectedColor ? marketingFlagFromSelectedColor : marketingFlagFromProductData ?? '';

  if (isStyleLevelFlag) {
    return <StyleMarketingFlag marketingFlag={flagContent} />;
  }

  const hasCustomerChoiceMarketingFlag = colorPromoFlag && !(flagContent && colorPromoFlag.toUpperCase() === flagContent.toUpperCase());
  return hasCustomerChoiceMarketingFlag ? <CustomerChoiceMarketingFlag colorPromoFlag={colorPromoFlag} /> : <></>;
};

const StyleMarketingFlag = ({ marketingFlag }: MarketingFlagInTitleProps): JSX.Element => (
  <div className='style-level-marketing-flag-wrapper' data-testid='marketing-flag' data-origin='style-level'>
    {marketingFlag && <p className='marketing-flag'>{marketingFlag}</p>}
  </div>
);

const CustomerChoiceMarketingFlag = ({ colorPromoFlag }: MarketingFlagInTitleProps): JSX.Element => {
  return (
    <p className='color-level-marketing-flag-wrapper' data-testid='marketing-flag' data-origin='cc-level'>
      {colorPromoFlag}
    </p>
  );
};
