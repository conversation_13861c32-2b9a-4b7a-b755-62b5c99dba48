import { DimensionsList } from '@product-page/pages/services/capi-aggregation-service/v3/types';
import { filterSelectedDimensions } from '../mvgSizeDimensions';

describe('filterSelectedDimensions', () => {
  it('should return an array of dimensions with selectedDimension and stock information', () => {
    const dimensions: DimensionsList[] = [
      {
        selectedDimension: 'M',
        dimensions: [
          {
            dimension: 'S',
            isInstock: false,
            bopis_in_stock: false,
            sort_order: 0,
          },
          {
            dimension: 'M',
            isInstock: true,
            bopis_in_stock: true,
            sort_order: 0,
          },
        ],
        dimensionGroupId: '',
        label: '',
      },
      {
        selectedDimension: 'L',
        dimensions: [
          {
            dimension: 'L',
            isInstock: true,
            bopis_in_stock: false,
            sort_order: 0,
          },
          {
            dimension: 'XL',
            isInstock: false,
            bopis_in_stock: false,
            sort_order: 0,
          },
        ],
        dimensionGroupId: '',
        label: '',
      },
    ];

    const result = filterSelectedDimensions(dimensions);

    expect(result).toEqual([
      {
        bopis_in_stock: true,
        isInstock: true,
        selectedDimension: 'M',
        size_dimension: 1,
      },
      {
        bopis_in_stock: false,
        isInstock: true,
        selectedDimension: 'L',
        size_dimension: 2,
      },
    ]);
  });

  it('should filter out dimensions without a selectedDimension', () => {
    const dimensions: DimensionsList[] = [
      {
        selectedDimension: '',
        dimensions: [
          {
            dimension: 'S',
            isInstock: false,
            bopis_in_stock: false,
            sort_order: 0,
          },
          {
            dimension: 'M',
            isInstock: true,
            bopis_in_stock: true,
            sort_order: 0,
          },
        ],
        dimensionGroupId: '',
        label: '',
      },
      {
        selectedDimension: '',
        dimensions: [
          {
            dimension: 'L',
            isInstock: true,
            bopis_in_stock: false,
            sort_order: 0,
          },
          {
            dimension: 'XL',
            isInstock: false,
            bopis_in_stock: false,
            sort_order: 0,
          },
        ],
        dimensionGroupId: '',
        label: '',
      },
    ];

    const result = filterSelectedDimensions(dimensions);

    expect(result).toEqual([]);
  });

  it('should return an empty array if no dimensions are provided', () => {
    const result = filterSelectedDimensions([]);
    expect(result).toEqual([]);
  });

  it('should return an empty array if all dimensions are missing selectedDimension', () => {
    const dimensions: DimensionsList[] = [
      {
        selectedDimension: '',
        dimensions: [
          {
            dimension: 'S',
            isInstock: false,
            bopis_in_stock: false,
            sort_order: 0,
          },
          {
            dimension: 'M',
            isInstock: true,
            bopis_in_stock: true,
            sort_order: 0,
          },
        ],
        dimensionGroupId: '',
        label: '',
      },
      {
        selectedDimension: '',
        dimensions: [
          {
            dimension: 'L',
            isInstock: true,
            bopis_in_stock: false,
            sort_order: 0,
          },
          {
            dimension: 'XL',
            isInstock: false,
            bopis_in_stock: false,
            sort_order: 0,
          },
        ],
        dimensionGroupId: '',
        label: '',
      },
    ];

    const result = filterSelectedDimensions(dimensions);

    expect(result).toEqual([]);
  });
});
