import PinchZoom from 'pinch-zoom-js';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { AdaptedImage } from '../../pages/helpers/mediaSequencer';
import { Image } from './Image';
import { getPhotoBricksSrcSet, getPhotoBricksSizes } from './helpers/util';

type ImageZoomWrapperProps = {
  altText: string;
  imgRef?: React.Ref<HTMLImageElement>;
  isLoadOnPriority: boolean;
  item: AdaptedImage;
  onLoad?: () => void;
};

export const ImageZoomWrapper = forwardRef<JSX.Element, ImageZoomWrapperProps>((props, ref) => {
  const { altText, item, isLoadOnPriority, onLoad, imgRef } = props;
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [isZoomed, setIsZoomed] = useState(false);
  const pinchZoomRef = useRef<PinchZoom | null>(null);

  const normalImageSrc = item?.['VIEW_LARGE_IMAGE'] || item?.['PRIMARY'] || '';
  const zoomImageSrc = item?.['ZOOM'] || item?.['VIEW_LARGE_IMAGE'] || item?.['PRIMARY'] || '';
  const normalImageSrcSet = getPhotoBricksSrcSet(item);
  const zoomCrop = item?.crops?.find(crop => crop.type === 'ZOOM');

  useEffect(() => {
    if (containerRef.current) {
      pinchZoomRef.current = new PinchZoom(containerRef.current, {
        draggableUnzoomed: false,
        maxZoom: 2.5,
        minZoom: 0.99,
        tapZoomFactor: 2.5,
        setOffsetsOnce: true,
        onDoubleTap: () => {
          setIsZoomed(prev => !prev);
        },
      });
    }
    return () => {
      if (pinchZoomRef.current) {
        pinchZoomRef.current.destroy();
      }
    };
  }, []);

  return (
    <div ref={containerRef} style={{ width: '100%', height: '100%', overflow: 'hidden', position: 'relative' }}>
      {/* Normal resolution image - visible when not zoomed */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          opacity: isZoomed ? 0 : 1,
          pointerEvents: isZoomed ? 'none' : 'auto',
        }}
      >
        <Image
          alt={altText}
          fill
          image={item}
          priority={isLoadOnPriority}
          src={normalImageSrc}
          customSrcSet={normalImageSrcSet}
          sizes={getPhotoBricksSizes()}
          onLoad={onLoad}
          style={{ imageRendering: 'auto' }}
          ref={imgRef || (ref as React.RefObject<HTMLImageElement>)}
        />
      </div>

      {/* High resolution image - visible when zoomed */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          opacity: isZoomed ? 1 : 0,
          pointerEvents: isZoomed ? 'auto' : 'none',
        }}
      >
        <Image
          alt={altText}
          fill
          image={item}
          priority={isLoadOnPriority}
          src={zoomImageSrc}
          width={zoomCrop?.width}
          height={zoomCrop?.height}
          style={{ imageRendering: 'auto' }}
          onLoad={onLoad}
        />
      </div>
    </div>
  );
});

ImageZoomWrapper.displayName = 'ImageZoomWrapper';
