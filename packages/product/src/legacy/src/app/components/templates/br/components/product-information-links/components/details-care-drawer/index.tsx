// @ts-nocheck
'use client'

/* eslint-disable react/no-danger */
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { DrawerWrapper } from '@product-page/legacy/drawer-wrapper';
import React, { Fragment, memo } from 'react';

import type { UseDetailsDataType } from './hooks/use-details-data';
import { UseDetailsData } from './hooks/use-details-data';
import { detailsAndCareDetailsStyles, detailsAndCareFabricStyles, detailsAndCareItemStyles } from './index.styles';
import type { DetailsCareDrawerMemoizedProps } from './types';

const Details = ({ description, info }: Partial<UseDetailsDataType>): JSX.Element => (
  <div className="details" css={detailsAndCareDetailsStyles}>
    <p dangerouslySetInnerHTML={{ __html: description }} />
    <ul className="details-list">
      {Object.entries(info).map(([key, value]) => {
        return (
          <li key={key} className="details-list__item">
            <span dangerouslySetInnerHTML={{ __html: value as string }} />
          </li>
        );
      })}
    </ul>
  </div>
);

const FabricAndCare = ({ fabricLocalizedTitle, info }: Partial<UseDetailsDataType>): JSX.Element => (
  <div className="fabric-and-care" css={detailsAndCareFabricStyles}>
    <h2 dangerouslySetInnerHTML={{ __html: fabricLocalizedTitle }} />
    <ul className="fabric-and-care-list">
      {Object.entries(info).map(([key, value]) => {
        return (
          <li key={key} className="fabric-and-care-list__item">
            <span dangerouslySetInnerHTML={{ __html: value as string }} />
          </li>
        );
      })}
    </ul>
  </div>
);

const ItemNumber = ({ styleId }: Partial<UseDetailsDataType>): JSX.Element => {
  const { localize } = useLocalize();
  const itemLabel = localize('pdp.productInfoLinks.details.itemLabel');

  return (
    <div className="item-number" css={detailsAndCareItemStyles}>
      <p>
        {itemLabel} &#x23;{styleId}
      </p>
    </div>
  );
};

const DetailsCareDrawerMemoized = ({
  detailsCareDrawerView,
  setDetailsCareDrawerView,
  detailsCareText,
}: DetailsCareDrawerMemoizedProps) => {
  const RenderDetailsCareDrawer = () => {
    const { description, fabricLocalizedInformation, fabricLocalizedTitle, info, styleId } = UseDetailsData();

    return (
      <DrawerWrapper
        isOpen={detailsCareDrawerView}
        setInformationView={setDetailsCareDrawerView}
        title={detailsCareText}
      >
        <Fragment>
          <Details description={description} info={info} />
          <FabricAndCare fabricLocalizedTitle={fabricLocalizedTitle} info={fabricLocalizedInformation} />
          <ItemNumber styleId={styleId} />
        </Fragment>
      </DrawerWrapper>
    );
  };

  return detailsCareDrawerView ? <RenderDetailsCareDrawer /> : <Fragment />;
};

export const DetailsCareDrawer = memo(DetailsCareDrawerMemoized);
