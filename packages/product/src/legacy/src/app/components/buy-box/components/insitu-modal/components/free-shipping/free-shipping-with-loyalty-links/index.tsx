// @ts-nocheck
'use client'

import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Meter } from '@ecom-next/core/legacy/meter';
import { useTheme } from '@emotion/react';
import { linkStyles } from '@pdp/packages/styles/brand-styles/utils/util'; // NOSONAR
import type { ReactNode } from 'react';
import React from 'react';

import { useAppState } from '../../../../../../../hooks/use-app-state';
import { usePDPReporter } from '../../../../../../../hooks/use-pdp-reporter';
import { usePersonalization } from '../../../../../../../hooks/use-personalization';
import type { ReportEasyEnrollLinkClickType } from '../../../../../../product-page/collaborators/pdp-reporter';
import { centerTextStyles } from '../../../styles/index.styles';
import { freeShippingStyles, notSignedInBelowMessageStyles } from '../index.styles';

type ShippingBarMessagesType = {
  above: string;
  below?: string | ReactNode;
};

type SignedInStates = 'signedIn' | 'notSignedIn';
type ThresholdBasedPosition = 'underThreshold' | 'overThreshold';

type FreeShippingBarWithLoyaltyLinksProps = {
  percentage: number;
  positionBasedOnThreshold: ThresholdBasedPosition;
  translationParameters: { [key: string]: any };
};

export const insituSignInEasyEnrollType = { easy_enroll_type: 'Product : Insitu : SignIn' };
export const insituJoinEasyEnrollType = { easy_enroll_type: 'Product : Insitu : Join' };

export const FreeShippingBarWithLoyaltyLinks = (props: FreeShippingBarWithLoyaltyLinksProps): JSX.Element => {
  const { translationParameters, positionBasedOnThreshold, percentage } = props;
  const personalizationContextData = usePersonalization();
  const { localize } = useLocalize();
  const reporter = usePDPReporter();
  const { url: targetUrl } = useAppState();
  const theme = useTheme();
  const signedInKey = personalizationContextData?.userContext?.isLoggedInUser === 'true' ? 'signedIn' : 'notSignedIn';
  const targetUrlWithScapedCharacter = targetUrl.replace('?', '\\?');

  const linkSignIn = (text: string, reportClickCustomParam: ReportEasyEnrollLinkClickType) => (
    <a
      css={linkStyles}
      href={`/my-account/sign-in?targetURL=${targetUrlWithScapedCharacter}`}
      onClick={() => reporter?.reportEasyEnrollLinkClick(reportClickCustomParam)}
      rel="noopener noreferrer"
      target="_blank"
    >
      {localize(text)}
    </a>
  );

  const useFreeShippingBarMessage = (
    signedInState: SignedInStates,
    underOrOverThreshold: ThresholdBasedPosition
  ): ShippingBarMessagesType => {
    const minToGetFreeShippingMessage = localize('pdp.insitu.minToGetFreeShipping', translationParameters);
    const signInMessage = localize('pdp.insitu.signIn');
    const orMessage = localize('pdp.conjunction.or');
    const joinNowMessage = localize('pdp.insitu.joinNow');

    const notSignedInBelowMessage = (!personalizationContextData?.userContext?.isLoggedInUser ||
      personalizationContextData?.userContext?.isLoggedInUser === 'false') && (
      <div data-testid="freeShippingSingInOrJoinNow">
        {minToGetFreeShippingMessage}, {linkSignIn(signInMessage, insituSignInEasyEnrollType)} {orMessage}{' '}
        {linkSignIn(joinNowMessage, insituJoinEasyEnrollType)}
      </div>
    );

    const messages = {
      notSignedIn: {
        overThreshold: {
          above: localize('pdp.insitu.notSignedOverThreshold.aboveFreeShippingBar', translationParameters),
          below: notSignedInBelowMessage,
        },
        underThreshold: {
          above: localize('pdp.insitu.notSignedUnderThreshold.aboveFreeShippingBar', translationParameters),
          below: notSignedInBelowMessage,
        },
      },
      signedIn: {
        overThreshold: {
          above: localize('pdp.insitu.signedInOverThreshold'),
        },
        underThreshold: {
          above: localize('pdp.insitu.signedInUnderThreshold.aboveFreeShippingBar', translationParameters),
          below: localize('pdp.insitu.signedInUnderThreshold.belowFreeShippingBar', translationParameters),
        },
      },
    };

    return messages[signedInState][underOrOverThreshold];
  };

  const shippingStateMessages = useFreeShippingBarMessage(signedInKey, positionBasedOnThreshold);

  return (
    <div className="free-shipping-progress" css={freeShippingStyles}>
      <p
        className="free-shipping-text"
        css={centerTextStyles}
        dangerouslySetInnerHTML={{ __html: shippingStateMessages.above }}
      />
      <Meter
        animation
        backgroundColor={theme.color.g5}
        borderColor={theme.color.g3}
        percentage={percentage}
        valueColor={theme.crossBrand.color.b2}
      />
      <div className="free-shipping-progress" css={notSignedInBelowMessageStyles}>
        {typeof shippingStateMessages.below === 'string' ? (
          <p className="free-shipping-text" dangerouslySetInnerHTML={{ __html: shippingStateMessages.below }} />
        ) : (
          <div className="free-shipping-text">{shippingStateMessages.below}</div>
        )}
      </div>
    </div>
  );
};
