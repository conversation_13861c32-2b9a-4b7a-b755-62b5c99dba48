// @ts-nocheck
'use client'

import { useSegment } from '../../abseg-provider';
import { useDrapr } from './use-drapr';

export const useDraprWidget = (): {
  is3D: boolean;
  isAutoSelectEnabled: boolean;
  isFitOnly: boolean;
  isOOSErrorEnabled: boolean;
  isSizeLabelEnabled: boolean;
  isSliderFeatureEnabled: boolean;
} => {
  const { draprEnabled, isAutoSelectEnabled, isOOSErrorEnabled, isSizeLabelEnabled, isSliderFeatureEnabled } =
    useDrapr();
  const segment = useSegment('pdpDraprFitVs3d');

  const isFitOnly = draprEnabled && segment === 'a';
  const is3D = draprEnabled && segment === 'x';

  return {
    is3D,
    isAutoSelectEnabled: isFitOnly && isAutoSelectEnabled,
    isFitOnly,
    isOOSErrorEnabled,
    isSizeLabelEnabled: isFitOnly && isSizeLabelEnabled,
    isSliderFeatureEnabled: is3D && isSliderFeatureEnabled,
  };
};
