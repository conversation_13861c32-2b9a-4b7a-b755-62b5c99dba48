// @ts-nocheck
'use client'

import React from 'react';

import { fulfillmentLabelStyle } from '../fulfillment-method/brand-styles/index.styles';
import { getPickUpLocationContent } from '../fulfillment-method/collaborators/get-pickup-location-content';

type PickupLocationProps = {
  bopisAvailable: boolean;
  classes: string;
  displayNotification: boolean;
  isStoreSelected: boolean;
  isValidSizeSelection: boolean;
  noSelectedSkuId: string;
  openModal: () => void;
  selectAStore: string;
  selectedStore: ProductStore;
};

const PickupLocation = (props: PickupLocationProps): JSX.Element => {
  const {
    classes,
    bopisAvailable,
    displayNotification,
    isStoreSelected,
    noSelectedSkuId,
    openModal,
    selectAStore,
    selectedStore,
    isValidSizeSelection,
  } = props;

  return (
    <div className={classes} css={theme => fulfillmentLabelStyle(theme)}>
      <div className="fulfillment-method-pickup__location">
        {getPickUpLocationContent({
          bopisAvailable,
          displayNotification,
          isStoreSelected,
          isValidSizeSelection,
          noSelectedSkuId,
          openModal,
          selectAStore,
          selectedStore,
        })}
      </div>
    </div>
  );
};

export default PickupLocation;
