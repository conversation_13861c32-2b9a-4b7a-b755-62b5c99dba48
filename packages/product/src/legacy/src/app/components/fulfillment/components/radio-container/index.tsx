// @ts-nocheck
'use client'

/***/
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { Radio } from '@ecom-next/core/legacy/radio';
import { jsx } from "@ecom-next/core/react-stitch";
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { isBananaRepublic } from '../../../../helpers/util/evaluate-brand';
import useGapBuyboxRedesign2024 from '../../../../hooks/use-gap-buybox-redesign-2024';
import { useFulfillmentV2 } from '../fulfillment-method/hooks/use-fulfillment-v2';
import { radioContainerStyles } from './brand-styles/index.styles';

type RadioContainerProps = {
  bopisActive: boolean;
  bopisAvailable: boolean;
  bopisExclusionColor?: boolean;
  children?: JSX.Element;
  isShippingAvailable: boolean;
  isValidSizeSelection: boolean;
  onChange: (e: React.ChangeEvent) => void;
  onDisabled?: (e: React.ChangeEvent) => void;
  pickup: boolean;
};

const renderRadio = (
  isChecked: boolean,
  getRadioValue: 'bopis-active' | 'bopis-inactive',
  changeHandler: (e: React.ChangeEvent) => void,
  isFulfillmentV2Enabled: boolean,
  customProp: any,
  isBR: boolean
) => {
  const commonProps = {
    checked: isChecked,
    'data-testid': 'bopis-ship-radio',
    id: `bopis-radio-${getRadioValue}`,
    name: 'bopis-ship-radio',
    onChange: changeHandler,
    value: getRadioValue,
  };
  if (isFulfillmentV2Enabled) {
    return (
      <Radio {...commonProps} {...customProp} className="bopis-core-ui-radio" isBRRedesignEnabled={isBR} isRadioSmall />
    );
  }
  return <input {...commonProps} className="bopis-ship-radio" type="radio" />;
};

const isCheckedCallback = (
  isDisabled: boolean,
  isFulfillmentV2Checked: boolean,
  pickup: boolean,
  isPickupChecked: boolean,
  isShipToChecked: boolean,
  isComponentMounted: boolean
): boolean => {
  const pickupOrShip = pickup ? !!isPickupChecked : !!isShipToChecked;
  return isComponentMounted && !isDisabled && (isFulfillmentV2Checked || pickupOrShip);
};

const RadioContainer = ({
  bopisAvailable,
  bopisActive,
  bopisExclusionColor = false,
  children,
  onChange,
  onDisabled = () => {},
  pickup,
  isShippingAvailable,
  isValidSizeSelection,
}: RadioContainerProps): JSX.Element => {
  const getRadioValue = pickup ? 'bopis-active' : 'bopis-inactive';
  const isPickupChecked = bopisActive && bopisAvailable;
  const isPickupDisabled = bopisExclusionColor || !bopisAvailable || (bopisActive && !bopisAvailable);
  const isShipToChecked = !bopisActive && isShippingAvailable;
  const isDisabled = pickup ? isPickupDisabled : !isShippingAvailable;
  const { brandName } = useAppState();
  const { isFulfillmentV2Enabled } = useFulfillmentV2();
  const [isComponentMounted, setIsComponetMounted] = useState<boolean>(false);

  const isFulfillmentV2Checked = useMemo(
    () => isFulfillmentV2Enabled && isPickupChecked && pickup && isValidSizeSelection,
    [isFulfillmentV2Enabled, isPickupChecked, pickup, isValidSizeSelection]
  );

  useEffect(() => {
    setIsComponetMounted(true);
  }, []);

  const isChecked = useMemo(
    (): boolean =>
      isCheckedCallback(
        isDisabled,
        isFulfillmentV2Checked,
        pickup,
        isPickupChecked,
        isShipToChecked,
        isComponentMounted
      ),
    [isDisabled, pickup, isPickupChecked, isShipToChecked, isFulfillmentV2Checked, isComponentMounted]
  );

  const changeHandler = useCallback(
    (e: React.ChangeEvent): void => {
      e.persist();
      if (!isDisabled) {
        onChange(e);
      } else {
        onDisabled(e);
      }
    },
    [onChange, onDisabled, isDisabled]
  );

  const isGapBuyBoxRedesign2024 = useGapBuyboxRedesign2024(brandName);

  const isBR = useMemo(() => isBananaRepublic(brandName), [brandName]);
  const customProp = useMemo(() => (isBR ? { customBackgroundColor: '#000000' } : {}), [isBR]);

  const radioContainerClasses = `radio-container ${isChecked ? 'checked' : ''}
  ${isDisabled && isGapBuyBoxRedesign2024 ? ' disabled' : ''}`;

  return (
    <div className={radioContainerClasses} css={theme => radioContainerStyles(theme)}>
      {renderRadio(isChecked, getRadioValue, changeHandler, isFulfillmentV2Enabled, customProp, isBR)}
      <label
        className={isDisabled ? 'bopis-ship-radio-circle disabled' : 'bopis-ship-radio-circle'}
        data-testid="radio-label"
        htmlFor={`bopis-radio-${getRadioValue}`}
      >
        <div className="bopis-label-wrapper">{children}</div>
      </label>
    </div>
  );
};

export default RadioContainer;
