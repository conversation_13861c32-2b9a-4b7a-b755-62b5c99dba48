// @ts-nocheck
import { cleanup } from '@testing-library/react-hooks';

import { defaultBoundingData, getBoundingClientReactDataById } from './get-bounding-client-react-data';

describe('#getBoundingClientReactDataById', () => {
  let div: any;
  beforeEach(() => {
    div = document.createElement('div');
    div.setAttribute('id', 'testId');
    div.style.height = 100;
    div.style.width = 20;
    global.document.body.appendChild(div);

    jest.spyOn(div, 'getBoundingClientRect').mockImplementation(() => ({
      bottom: 10,
      height: 10,
      left: 0,
      right: 10,
      top: 0,
      width: 10,
      x: 20,
      y: 40,
    }));
  });
  afterEach(cleanup);

  test('should get the bounding data if the element is there', async () => {
    const data = getBoundingClientReactDataById('testId');
    expect(data).not.toEqual(defaultBoundingData);
    expect(data.x).toEqual(20);
    expect(data.height).toEqual(10);
    expect(data.width).toEqual(10);
  });

  test('should get the default bounding data if the element is not there', async () => {
    expect(getBoundingClientReactDataById('testId1')).toEqual(defaultBoundingData);
  });
});
