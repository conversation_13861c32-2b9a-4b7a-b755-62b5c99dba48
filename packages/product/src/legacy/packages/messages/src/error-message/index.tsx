// @ts-nocheck
'use client'

/***/
/***/
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { jsx } from "@ecom-next/core/react-stitch";
import { Notification } from '@ecom-next/core/legacy/notification';

import { errorMessageBorderStyles, errorMessageStyles } from './index.styles';
import type { ErrorMessageProps } from './types';

export const ErrorMessage = ({ isNotificationComponentRequired, message }: ErrorMessageProps): JSX.Element => {
  const { brandName } = useAppState();

  return isNotificationComponentRequired ? (
    <Notification className="messages__error-messaging" css={errorMessageBorderStyles} kind="error">
      {message}
    </Notification>
  ) : (
    <div
      aria-live="assertive"
      className="messages__error-messaging"
      css={[errorMessageStyles(brandName), errorMessageBorderStyles]}
      role="alert"
    >
      {message}
    </div>
  );
};
