// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { messageForPickupStylesForAT } from './at.styles';
import { messageForPickupStylesForBR } from './br.styles';
import { messageForPickupStylesForGAP } from './gap.styles';
import { messageForPickupStylesForON } from './on.styles';

export function messageForPickupStyles(theme: Theme): InterpolationPrimitive {
  return forBrands(theme, {
    at: messageForPickupStylesForAT,
    br: messageForPickupStylesForBR,
    brfs: messageForPickupStylesForBR,
    gap: messageForPickupStylesForGAP,
    gapfs: messageForPickupStylesForGAP,
    on: messageForPickupStylesForON,
  });
}
