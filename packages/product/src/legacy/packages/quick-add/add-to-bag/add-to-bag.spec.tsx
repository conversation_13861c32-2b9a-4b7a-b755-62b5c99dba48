import type { Brands } from "@ecom-next/core/react-stitch";
import { act, fireEvent, screen } from '@testing-library/react';
import React from 'react';

import { addToBagRequest } from '../../helpers/service/add-to-bag-request';
import { setupSnapshots } from '../../helpers/util/setup-snapshots';
import { QuickAddTestContext } from '../spec/test-context';
import { AddToBag } from './add-to-bag';
import { staticProps } from './specs/add-to-bag-static-props-mock';
import { renderAddToBag } from './specs/render-add-to-bag';

jest.mock('../../helpers/service/add-to-bag-request');

const matchSnapshotsAllBrands = setupSnapshots();

const leapFrogSuccessFullResponse = {
  brand: 'GP',
  channel: 'WEB',
  charges: {
    baseTotal: 229.84,
    currencyCode: 'USD',
    estimatedTax: 0.0,
    estimatedTotal: 232.34,
    merchandiseSubtotal: 225.34,
    retailDeliveryFee: 0,
    shippingCost: 7.0,
    subTotal: 229.84,
  },
  currencyCode: 'USD',
  items: [
    {
      addedQuantity: 1,
      brand: 'ON',
      fulfillment: {
        type: 'SHIP',
      },
      itemLinks: {
        self: '/shopping-bags/31E993E13A2C4BB1AD80C5E63EDD09B8/item/null',
      },
      maxAllowedQuantity: 99,
      priceOverride: null,
      productPrice: {
        finalItemPrice: 25.49,
        regularPrice: 29.99,
        salePrice: 29.99,
      },
      sku: '*********0006',
    },
  ],
  lineOfBusiness: 'SPECIALTY',
  locale: 'en_US',
  market: 'US',
  productData: [
    {
      autoAdded: false,
      backOrderDate: null,
      categoryNumber: null,
      color: 'Bandolier Brown',
      colorStyleNumber: '*********',
      eligibleReturnLocationCode: null,
      inventoryStatus: 'IN_STOCK',
      inventoryStatusId: 0,
      isShippable: true,
      marketingFlag: '0',
      merchandiseSubClassId: '3363610106',
      merchandiseType: '7',
      noReturnItem: false,
      onSale: false,
      priceTypeId: 1,
      primaryCategoryName: null,
      productAttributes: [
        {
          productType: 'Baby Girl Bottoms',
        },
      ],
      productImage: {
        mainImagePath: 'webcontent/0013/593/261/cn13593261.jpg',
        summaryImagePath: 'webcontent/0013/593/253/cn13593253.jpg',
      },
      productName: 'Slim 360° Stretch Five-Pocket Pants for Boys',
      productStyleDescription: 'Slim 360° Stretch Five-Pocket Pants for Boys',
      productTypeId: 0,
      productURL: 'browse/product.do?pid=*********0006',
      promoMessage: null,
      returnByMailItem: false,
      size: '6',
      sizeCode: '0006',
      sizeVariantId: 1,
      sku: '*********0006',
      skuCatalogItemId: 0,
      styleNumber: '822251',
      variantDescription: 'Regular',
      webVendorName: 'ARMANA APPARELS LTD',
    },
  ],
  shippingOptionAwards: [
    {
      promotionId: null,
      shippingOptionDetailId: 38702,
      shippingTypeId: null,
      thresholdAmount: 50,
    },
  ],
  shoppingBagId: '31E993E13A2C4BB1AD80C5E63EDD09B8',
  totalBagQuantity: 4,
};

const getAddToBag = (selectedSku = {}) => {
  const addToBagComponent = (brandName: Brands) => (
    <QuickAddTestContext brandName={brandName}>
      <AddToBag {...staticProps} {...selectedSku} />
    </QuickAddTestContext>
  );
  return addToBagComponent;
};
describe('<AddToBag />', () => {
  matchSnapshotsAllBrands(getAddToBag(), 'renders correctly product info version with regular price');

  const selectedSku = {
    price: {
      localizedCurrentPrice: '$8.00',
      localizedRegularPrice: '$12.34',
      rawCurrentPrice: 8,
      rawRegularPrice: 12.34,
    },
    skuId: '123456001',
  };

  matchSnapshotsAllBrands(getAddToBag(selectedSku), 'renders correctly product info version with promotional price');

  test('Should have the button', () => {
    const selectedSku = {};
    const { container } = renderAddToBag({ selectedSku });
    const button = screen.getByText('Add To Bag');
    expect(container).toContainElement(button);
  });

  test('Should have button with the expected class names', () => {
    const selectedSku = {};
    renderAddToBag({ selectedSku });
    expect(screen.getByRole('button')).toHaveClass('quick-add', 'add-to-bag');
  });

  test('Should use atbOidUrl once button is clicked', () => {
    const addToBagRequestMock = jest.fn().mockReturnValue(Promise.resolve(leapFrogSuccessFullResponse));
    (addToBagRequest as jest.Mock).mockImplementation(addToBagRequestMock);

    const addToBagUrls = getAtbUrls(staticProps.brandName as Brands, 'us');
    const addToBagUrlsTest = { ...addToBagUrls, atbOidcUrl: 'atb oid url tests' };
    renderAddToBag({ addToBagUrls: addToBagUrlsTest, selectedSku });

    act(async () => {
      const button = screen.getByText('Add To Bag');
      await act(async () => { 

       fireEvent.click(button); 

       })
    });

    expect(addToBagRequestMock).toHaveBeenCalledTimes(1);
    expect(addToBagRequestMock.mock.calls[0][0]?.oidcUrl).toEqual(addToBagUrlsTest.atbOidcUrl);
  });
});
