// @ts-nocheck
'use client'

import type { ProductColor, ProductSku } from '@pdp/types/product-data/style-level'; // NOSONAR

import type { ProductDimension, ProductDimensionItem } from '../../types/dimensions';

type SelectedDimensionsHelper = Record<string, string>;

const filterDimensions = (dimensions: ProductDimension[], color: ProductColor): ProductDimension[] => {
  if (dimensions.length <= 1) {
    return dimensions;
  }

  const selectedDimensions: SelectedDimensionsHelper = {};
  dimensions.forEach(d => {
    selectedDimensions[d.dimensionGroupId] = d.selectedDimension;
  });

  const filteredSizesValue = dimensions.map(dim => {
    const key = dim.dimensionGroupId;
    const otherGroupId = dimensions.map(d => d.dimensionGroupId).filter(groupId => groupId !== key);
    let value: ProductSku[] = [];
    otherGroupId.forEach(groupId => {
      value = Array.isArray(color.sizes)
        ? value.concat(
            color.sizes.filter(
              size =>
                size[groupId as keyof ProductSku] === selectedDimensions[groupId] || selectedDimensions[groupId] === ''
            )
          )
        : [];
    });

    return value;
  });

  const displayedDimensions: ProductDimension[] = dimensions.map(d => ({ ...d, dimensions: [] }));
  dimensions.forEach((dim, index) => {
    dim.dimensions.forEach(item => {
      if (filteredSizesValue[index].some(size => item.name === size[`sizeDimension${index + 1}` as keyof ProductSku])) {
        displayedDimensions[index].dimensions.push(item as ProductDimensionItem);
      }
    });
  });

  return displayedDimensions;
};

export { filterDimensions };
