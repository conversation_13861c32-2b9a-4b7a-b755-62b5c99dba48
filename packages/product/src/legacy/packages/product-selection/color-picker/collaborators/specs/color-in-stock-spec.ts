// @ts-nocheck
import type { ProductColor } from '@pdp/types/product-data/style-level';

import colorInStock, { getInStockByFulfillmentMethod } from '../color-in-stock';
import { colorOneDimension, colorThreeDimensions, colorTwoDimensions } from './data/color-in-stock';
import {
  noSelectedDimensions,
  oneSizeDimensionBopisInStock,
  oneSizeDimensionBopisOOS,
  oneSizeDimensionInStock,
  oneSizeDimensionOutOfStock,
  threeSizeDimensionsBopisInStock,
  threeSizeDimensionsBopisOOS,
  threeSizeDimensionsInStock,
  threeSizeDimensionsOutOfStock,
  twoSizeDimensionsBopisInStock,
  twoSizeDimensionsBopisOOS,
  twoSizeDimensionsInStock,
  twoSizeDimensionsOutOfStock,
} from './data/dimensions';

describe('colorInStock', () => {
  describe('no selected size dimensions', () => {
    test('returns in stock', () => {
      const stockData = colorInStock(colorTwoDimensions({}), noSelectedDimensions);
      expect(stockData).toBe(true);
    });
  });

  describe('one selected size dimension', () => {
    test('returns out of stock if color not available in selected size', () => {
      const stockData = colorInStock(colorOneDimension({}), oneSizeDimensionOutOfStock);
      expect(stockData).toBe(false);
    });

    test('returns in stock if color available in selected size', () => {
      const stockData = colorInStock(colorOneDimension({ inStock: true }), oneSizeDimensionInStock);
      expect(stockData).toBe(true);
    });

    test('returns in stock if color is bopisInStock for the selected size', () => {
      const stockData = colorInStock(colorOneDimension({ bopisInStock: true }), oneSizeDimensionBopisInStock);
      expect(stockData).toBe(true);
    });

    test('returns in stock if color is BOPIS OOS but is inStock', () => {
      const stockData = colorInStock(colorOneDimension({ inStock: true }), oneSizeDimensionBopisOOS);
      expect(stockData).toBe(true);
    });
  });

  describe('one selected dimension for multi-dimension style', () => {
    test('returns true given the selected dimension is available and is in stock', () => {
      const stockData = colorInStock(colorTwoDimensions({ inStock: true }), oneSizeDimensionInStock);
      expect(stockData).toBe(true);
    });

    test('returns false given the selected dimension is not available and is in stock', () => {
      const stockData = colorInStock(colorTwoDimensions({}), oneSizeDimensionOutOfStock);
      expect(stockData).toBe(false);
    });

    test('returns true given the selected dimension is bopisInStock but not inStock', () => {
      const stockData = colorInStock(colorTwoDimensions({ bopisInStock: true }), oneSizeDimensionBopisInStock);
      expect(stockData).toBe(true);
    });

    test('returns true given the selected dimension is inStock but not bopisInStock', () => {
      const stockData = colorInStock(colorTwoDimensions({ inStock: true }), oneSizeDimensionBopisOOS);
      expect(stockData).toBe(true);
    });
  });

  describe('two selected size dimensions', () => {
    test('returns out of stock if color not available in selected size combination', () => {
      const stockData = colorInStock(colorTwoDimensions({}), twoSizeDimensionsOutOfStock);
      expect(stockData).toBe(false);
    });

    test('returns in stock if color available in selected size combination', () => {
      const stockData = colorInStock(colorTwoDimensions({ inStock: true }), twoSizeDimensionsInStock);
      expect(stockData).toBe(true);
    });

    test('returns in stock if color is BOPIS available but not inStock in selected size combination', () => {
      const stockData = colorInStock(colorTwoDimensions({ bopisInStock: true }), twoSizeDimensionsBopisInStock);
      expect(stockData).toBe(true);
    });

    test('returns in stock if color available but BOPIS OOS in selected size combination', () => {
      const stockData = colorInStock(colorTwoDimensions({ inStock: true }), twoSizeDimensionsBopisOOS);
      expect(stockData).toBe(true);
    });
  });

  describe('three selected size dimensions', () => {
    test('returns out of stock if color not available in selected size', () => {
      const stockData = colorInStock(colorThreeDimensions({}), threeSizeDimensionsOutOfStock);
      expect(stockData).toBe(false);
    });

    test('returns in stock if color available in selected size', () => {
      const stockData = colorInStock(colorThreeDimensions({ inStock: true }), threeSizeDimensionsInStock);
      expect(stockData).toBe(true);
    });

    test('returns in stock if color is bopisInStock but not inStock', () => {
      const stockData = colorInStock(colorThreeDimensions({ bopisInStock: true }), threeSizeDimensionsBopisInStock);
      expect(stockData).toBe(true);
    });

    test('returns in stock if color inStock but not bopisInStock', () => {
      const stockData = colorInStock(colorThreeDimensions({ inStock: true }), threeSizeDimensionsBopisOOS);
      expect(stockData).toBe(true);
    });
  });

  describe('online instock mode', () => {
    const ONLINE_INVENTORY = 1;
    test('only consider online inventory', () => {
      const stockData = colorInStock(
        colorThreeDimensions({ bopisInStock: true, inStock: false }),
        threeSizeDimensionsBopisInStock,
        ONLINE_INVENTORY
      );
      expect(stockData).toBe(false);
    });
  });

  describe('bopis instock mode', () => {
    const BOPIS_INVENTORY = 2;
    test('only consider bopis inventory', () => {
      const stockData = colorInStock(
        colorThreeDimensions({ bopisInStock: false, inStock: true }),
        threeSizeDimensionsInStock,
        BOPIS_INVENTORY
      );
      expect(stockData).toBe(false);
    });
  });

  describe('error conditions', () => {
    test('returns false if there are no sizes', () => {
      const colorWithoutSizes: ProductColor = {
        businessCatalogItemId: '',
        colorName: 'Blue',
        currencySymbol: '',
        findInStore: {
          enabled: false,
          exclusions: [],
        },
        inStock: false,
        localizedCurrentPrice: '',
        localizedRegularPrice: '',
        marketingFlag: null,
        percentageOff: '',
        priceType: 0,
        productStyleColorImages: [],
        rawCurrentPrice: 0,
        rawRegularPrice: 0,
        sizes: [],
        url: '',
      };
      const stockData = colorInStock(colorWithoutSizes, threeSizeDimensionsInStock);
      expect(stockData).toBe(false);
    });
  });
});

describe('getInStockByFulfillmentMethod', () => {
  describe('no selected size dimensions', () => {
    test('returns both in stock', () => {
      const stockData = getInStockByFulfillmentMethod(colorTwoDimensions({}), noSelectedDimensions);
      expect(stockData.isShippingAvailable).toBe(true);
      expect(stockData.bopisAvailable).toBe(true);
    });
  });

  describe('one selected size dimension', () => {
    test('returns both out of stock if color not available in selected size', () => {
      const stockData = getInStockByFulfillmentMethod(colorOneDimension({}), oneSizeDimensionOutOfStock);
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns in global stock if color available in selected size', () => {
      const stockData = getInStockByFulfillmentMethod(colorOneDimension({ inStock: true }), oneSizeDimensionInStock);
      expect(stockData.isShippingAvailable).toBe(true);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns in bopis stock if color available in selected size', () => {
      const stockData = getInStockByFulfillmentMethod(
        colorOneDimension({ bopisInStock: true }),
        oneSizeDimensionInStock
      );
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(true);
    });
  });

  describe('one selected dimension for multi-dimension style', () => {
    test('returns true in global stock given the selected dimension is available and is in stock', () => {
      const stockData = getInStockByFulfillmentMethod(colorTwoDimensions({ inStock: true }), oneSizeDimensionInStock);
      expect(stockData.isShippingAvailable).toBe(true);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns true in bopis stock given the selected dimension is available and is in bopis stock', () => {
      const stockData = getInStockByFulfillmentMethod(
        colorTwoDimensions({ bopisInStock: true }),
        oneSizeDimensionInStock
      );
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(true);
    });

    test('returns false for both given the selected dimension is not available and is in stock', () => {
      const stockData = getInStockByFulfillmentMethod(colorTwoDimensions({}), oneSizeDimensionOutOfStock);
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(false);
    });
  });

  describe('two selected size dimensions', () => {
    test('returns both out of stock if color not available in selected size combination', () => {
      const stockData = getInStockByFulfillmentMethod(
        colorTwoDimensions({ inStock: true }),
        twoSizeDimensionsOutOfStock
      );
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns in global stock if color available in selected size combination', () => {
      const stockData = getInStockByFulfillmentMethod(colorTwoDimensions({ inStock: true }), twoSizeDimensionsInStock);
      expect(stockData.isShippingAvailable).toBe(true);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns in bopis stock if color available in selected size combination', () => {
      const stockData = getInStockByFulfillmentMethod(
        colorTwoDimensions({ bopisInStock: true }),
        twoSizeDimensionsInStock
      );
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(true);
    });
  });

  describe('three selected size dimensions', () => {
    test('returns false for both if color not available in selected size', () => {
      const stockData = getInStockByFulfillmentMethod(colorThreeDimensions({}), threeSizeDimensionsOutOfStock);
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns in global stock if color available in selected size', () => {
      const stockData = getInStockByFulfillmentMethod(
        colorThreeDimensions({ inStock: true }),
        threeSizeDimensionsInStock
      );
      expect(stockData.isShippingAvailable).toBe(true);
      expect(stockData.bopisAvailable).toBe(false);
    });

    test('returns in bopis stock if color available in selected size', () => {
      const stockData = getInStockByFulfillmentMethod(
        colorThreeDimensions({ bopisInStock: true }),
        threeSizeDimensionsInStock
      );
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(true);
    });
  });

  describe('error conditions', () => {
    test('returns false if there are no sizes', () => {
      const colorWithoutSizes: ProductColor = {
        businessCatalogItemId: '',
        colorName: 'Blue',
        currencySymbol: '',
        findInStore: {
          enabled: false,
          exclusions: [],
        },
        inStock: false,
        localizedCurrentPrice: '',
        localizedRegularPrice: '',
        marketingFlag: null,
        percentageOff: '',
        priceType: 0,
        productStyleColorImages: [],
        rawCurrentPrice: 0,
        rawRegularPrice: 0,
        sizes: [],
        url: '',
      };
      const stockData = getInStockByFulfillmentMethod(colorWithoutSizes, threeSizeDimensionsInStock);
      expect(stockData.isShippingAvailable).toBe(false);
      expect(stockData.bopisAvailable).toBe(false);
    });
  });
});
