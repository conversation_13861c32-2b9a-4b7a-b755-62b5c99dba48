// @ts-nocheck
import { css } from '@emotion/react';

import { setupRedesignStyles } from './setupRedesignStyles';

describe('setupRedesignStyles', () => {
  describe('should apply styles based on flag method returned', () => {
    test('for BR', () => {
      const { isBrRedesign2024, isBrRedesign2024Ph2 } = setupRedesignStyles({
        brand: 'br',
        isBrRedesign2024: false,
        isBrRedesign2024Ph2: true,
      });

      expect(isBrRedesign2024('new styles', 'old styles')).toBe('old styles');
      expect(isBrRedesign2024Ph2('new styles', 'old styles')).toBe('new styles');
    });
    test('for BRfs', () => {
      const { isBrfsRedesign2024, isBrfsRedesign2024Ph2 } = setupRedesignStyles({
        brand: 'brfs',
        isBrfsRedesign2024: false,
        isBrfsRedesign2024Ph2: true,
      });

      expect(isBrfsRedesign2024('new styles', 'old styles')).toBe('old styles');
      expect(isBrfsRedesign2024Ph2('new styles', 'old styles')).toBe('new styles');
    });
  });

  test('for AT', () => {
    const setRedesignStyles = setupRedesignStyles({
      brand: 'at',
      isAtRedesign: true,
    }).isAtRedesign;

    expect(setRedesignStyles('new styles', 'old styles')).toBe('new styles');
  });

  test('should return SerializedStyles when passed a function', () => {
    const setRedesignStyles = setupRedesignStyles({
      brand: 'at',
      isAtRedesign: false,
    }).isAtRedesign;

    expect(
      setRedesignStyles(
        () =>
          css`
            margin: 1px;
          `,
        () =>
          css`
            margin: 2px;
          `
      ).styles
    ).toBe('margin:2px');
  });
});
