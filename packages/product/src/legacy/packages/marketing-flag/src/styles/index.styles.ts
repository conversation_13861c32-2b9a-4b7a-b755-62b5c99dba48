// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { setupRedesignStyles } from '../../../styles';
import { ATMarketingFlagStyles } from './at.styles';
import { BRMarketingFlagStyles } from './br.styles';
import { BRFSMarketingFlagStyles } from './brfs.styles';
import { GAPMarketingFlagStyles } from './gap.styles';
import { ONMarketingFlagStyles } from './on.styles';

export const marketingflagStyles = (theme: Theme): InterpolationPrimitive => {
  const setRedesignStyles = setupRedesignStyles(theme).isBrBrfsRedesign;

  return forBrands(theme, {
    at: ATMarketingFlagStyles,
    br: BRMarketingFlagStyles,
    brfs: () => setRedesignStyles(BRMarketingFlagStyles, BRFSMarketingFlagStyles),
    gap: GAPMarketingFlagStyles,
    gapfs: GAPMarketingFlagStyles,
    on: ONMarketingFlagStyles,
  });
};
