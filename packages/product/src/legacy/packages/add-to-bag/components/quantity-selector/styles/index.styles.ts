// @ts-nocheck
'use client'

import type { Theme } from "@ecom-next/core/react-stitch";
import { forBrands } from "@ecom-next/core/react-stitch";
import type { InterpolationPrimitive } from '@emotion/serialize';

import { setupRedesignStyles } from '../../../../styles';
import { ATQuantitySelectorStyles } from './brands-styles/at.styles';
import { BRQuantitySelectorStyles, BRQuantitySelectorStylesPh2 } from './brands-styles/br.styles';
import { GAPQuantitySelectorStyles } from './brands-styles/gap.styles';
import { ONQuantitySelectorStyles } from './brands-styles/on.styles';

export const quantitySelectorStyles = (isSticky?: boolean) => {
  return (theme: Theme): InterpolationPrimitive => {
    const setStyles = setupRedesignStyles(theme).isBrBrfsRedesign;
    return forBrands(theme, {
      at: ATQuantitySelectorStyles,
      br: setStyles(BRQuantitySelectorStylesPh2(isSticky), BRQuantitySelectorStyles),
      brfs: setStyles(BRQuantitySelectorStylesPh2(isSticky), BRQuantitySelectorStyles),
      gap: GAPQuantitySelectorStyles,
      gapfs: GAPQuantitySelectorStyles,
      on: ONQuantitySelectorStyles,
    });
  };
};
