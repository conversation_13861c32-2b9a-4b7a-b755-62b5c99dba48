// @ts-nocheck
'use client'

/***/
/***/
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { jsx } from "@ecom-next/core/react-stitch";
import { productPriceScenario } from '@ecom-next/core/legacy/product-price-scenario';
import type { ProductPriceStyleLevel } from '@pdp/types/product-data/style-level';
import { useMemo } from 'react';

import { isOldNavy, isOnlyGap, isOnlyGapFactory } from '../helpers/util/evaluate-brand';
import { priceAdapter as productPriceAdapter } from './adapters/price-adapter';
import {
  getCurrentPriceRangeDisplay,
  getCurrentPriceRangeHighlightDisplay,
  getCurrentPriceRangeOneHighlightDisplay,
} from './collaborators/price-displays';
import { getPriceText } from './collaborators/price-text';
import { BasePriceDisplay } from './components/base-price-display';
import { pricingStyles } from './components/base-price-display/styles/index.styles';
import { MarkdownPriceDisplay } from './components/markdown-price-display';
import { SimpleRangeDisplay } from './components/simple-range-display';
import type { PriceProps } from './types';

const Price = ({
  price,
  hoverPrice = '',
  selectedPrice = '',
  percentageOffConfig = { show: false, useFromCapi: false },
  isGapBuyBoxRedesign2024 = false,
  isATBuyBoxRedesign2024 = false,
  selectedColorPriceInfo = {} as ProductPriceStyleLevel,
  hoverColorPriceInfo = {} as ProductPriceStyleLevel,
}: PriceProps): JSX.Element => {
  const { show: displayPercentageOff, useFromCapi: usePercentageOffFromCapi } = percentageOffConfig;
  const adaptedPriceObject = productPriceAdapter(price, usePercentageOffFromCapi);
  const priceScenario = productPriceScenario(adaptedPriceObject);

  const { brandName: brand, locale } = useAppState();
  const { localize, formatCurrency } = useLocalize();

  const { priceType, minPercentageOff, maxPercentageOff, regularMinPrice } = adaptedPriceObject;

  const priceText = getPriceText({
    adaptedPriceObject,
    brand,
    formatCurrency,
    locale,
    localize,
  });

  const { currentPriceRangeAriaLabel, formattedRegularMinPrice, percentageOffText, regularPriceAriaLabel } = priceText;

  const currentPriceRangeDisplay = getCurrentPriceRangeDisplay(priceText);
  const currentPriceRangeHighlightDisplay = getCurrentPriceRangeHighlightDisplay(priceText);
  const currentPriceRangeOneHighlightDisplay = getCurrentPriceRangeOneHighlightDisplay(priceText);

  const hoverPriceOnsale = !!hoverPrice && !!hoverColorPriceInfo.priceType && hoverColorPriceInfo.priceType !== 1;
  const selectedPriceOnsale =
    !hoverPrice && !!selectedColorPriceInfo.priceType && selectedColorPriceInfo.priceType !== 1;

  const conditionalStrikethrough = hoverPriceOnsale || selectedPriceOnsale;

  const shouldHidePriceRange =
    (isOldNavy(brand) && displayPercentageOff && priceScenario !== 'regularPrice') || isATBuyBoxRedesign2024;
  const shouldHidePercentageOff =
    !displayPercentageOff ||
    isOnlyGap(brand) ||
    (isOnlyGapFactory(brand) && !isGapBuyBoxRedesign2024) ||
    (priceScenario !== 'oldPriceNewPrice' && priceScenario !== 'oldRangeNewPrice' && !isGapBuyBoxRedesign2024);

  const isRedesign2024SalePrice = useMemo(() => {
    const isGapOrAtRedesignWithStrikethrough =
      (isGapBuyBoxRedesign2024 && conditionalStrikethrough) || (isATBuyBoxRedesign2024 && conditionalStrikethrough);

    return (
      isGapOrAtRedesignWithStrikethrough &&
      (priceScenario === 'oldPriceNewPrice' || priceScenario === 'oldRangeNewPrice' || shouldHidePriceRange)
    );
  }, [isGapBuyBoxRedesign2024, isATBuyBoxRedesign2024, conditionalStrikethrough, priceScenario, shouldHidePriceRange]);

  const getSecondaryPriceDisplay = () => {
    const simpleRangeDisplayParams = { ariaLabel: currentPriceRangeAriaLabel };

    if (priceScenario === 'oldPriceNewPrice' || priceScenario === 'oldRangeNewPrice' || shouldHidePriceRange) {
      const conditionalParameters = isOldNavy(brand)
        ? { conditionalStrikethrough: displayPercentageOff ? conditionalStrikethrough : true }
        : {};
      return (
        <MarkdownPriceDisplay
          ariaLabel={regularPriceAriaLabel}
          brand={brand}
          classes="product-price--pdp__markdown"
          {...conditionalParameters}
          maxPercentageOff={maxPercentageOff}
          minPercentageOff={minPercentageOff}
          percentageOffText={shouldHidePercentageOff ? undefined : percentageOffText}
          priceDisplay={formattedRegularMinPrice}
          priceType={conditionalStrikethrough ? 2 : priceType}
        />
      );
    }

    if (priceScenario === 'range') {
      return <SimpleRangeDisplay {...simpleRangeDisplayParams}>{currentPriceRangeDisplay}</SimpleRangeDisplay>;
    }

    if (priceScenario === 'newRangeSameMax') {
      return (
        <SimpleRangeDisplay {...simpleRangeDisplayParams}>{currentPriceRangeOneHighlightDisplay}</SimpleRangeDisplay>
      );
    }

    if (priceScenario === 'oldRangeNewRange' || priceScenario === 'oldPriceNewRange') {
      return <SimpleRangeDisplay {...simpleRangeDisplayParams}>{currentPriceRangeHighlightDisplay}</SimpleRangeDisplay>;
    }

    return null;
  };

  const secondaryPriceDisplay = getSecondaryPriceDisplay();
  return (
    <div className={`pdp-pricing ${isRedesign2024SalePrice ? 'redesign-saleprice' : ''}`} css={pricingStyles}>
      <BasePriceDisplay
        hoverPrice={hoverPrice}
        isRedesign2024SalePrice={isRedesign2024SalePrice}
        regularMinPrice={regularMinPrice}
        selectedPrice={selectedPrice}
      />
      {conditionalStrikethrough && secondaryPriceDisplay}
    </div>
  );
};

export default Price;

export * from './adapters/price-adapter';
export * from './collaborators/price-displays';
export * from './collaborators/price-text';
export * from './components/base-price-display';
export * from './components/conditional-strike-through-price';
export * from './components/markdown-price-display';
export * from './components/percentage-off-text';
export * from './components/simple-price-display';
export * from './components/simple-range-display';
export * from './helpers/format-price-and-remove-decimal';
export * from './styles/index.styles';
export * from './types';
