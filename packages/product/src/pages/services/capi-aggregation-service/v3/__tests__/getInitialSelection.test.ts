import { atMock } from '../__fixtures__/atMock';
import { getInitialSelection } from '../product-data-transformer/getInitialSelection';

const capiData = {
  ...atMock,
  input_id: '222222',
  input_type: 'STYLE',
  display_variant_key: 'PETITE|7_8|NO_POCKET',
  input_alternate_ids: {
    legacy_style_number: '222222',
    online_legacy_customer_choice_number: null,
    online_legacy_sku_number: null,
  },
  styles: {
    '222222': {},
    '333333': {},
  },
  variant_definition: [
    {
      name: 'Fit',
      values: [
        { id: 'REGULAR', name: 'Regular', variant_number: '1' },
        { id: 'PETITE', name: 'Petit<PERSON>', variant_number: '30' },
      ],
    },
    {
      name: 'Length',
      values: [
        { id: 'FULL', name: 'Full' },
        { id: '7_8', name: '7/8' },
      ],
    },
    {
      name: 'Pockets',
      values: [
        { id: 'POCKET', name: 'Pocket' },
        { id: 'NO_POCKET', name: 'No Pocket' },
      ],
    },
  ],
  variants: {
    'REGULAR|FULL|POCKET': {
      style_id: '222222',
      variantKey: 'REGULAR|FULL|POCKET',
      default_customer_choice_id: '2222220002',
      customer_choices: {
        '2222220001': {
          customer_choice_id: '2222220001',
          style_id: '222222',
          skus: [
            {
              sku_id: '22222200011234',
              size: '2',
            },
            {
              sku_id: '22222200012345',
              size: '4',
            },
          ],
        },
        '2222220002': {
          customer_choice_id: '2222220002',
          style_id: '222222',
        },
      },
      dimensions: [
        {
          dimensionGroupId: 'sizeDimension1',
          dimensions: [
            {
              dimension: 'S',
              sort_order: 10,
              isInstock: true,
              bopis_in_stock: false,
              availableSkus: [],
            },
            {
              dimension: 'M',
              sort_order: 10,
              isInstock: false,
              bopis_in_stock: false,
              availableSkus: [],
            },
          ],
          label: 'Size',
          selectedDimension: '',
        },
      ],
    },
    'REGULAR|7_8|NO_POCKET': {
      style_id: '333333',
      variantKey: 'REGULAR|7_8|NO_POCKET',
      default_customer_choice_id: '3333330002',
      customer_choices: {
        '3333330001': {
          style_id: '333333',
          customer_choice_id: '3333330001',
        },
        '3333330002': {
          style_id: '333333',
          customer_choice_id: '3333330002',
        },
      },
      dimensions: [
        {
          dimensionGroupId: 'sizeDimension1',
          dimensions: [
            {
              dimension: 'L',
              sort_order: 10,
              isInstock: false,
              bopis_in_stock: false,
              availableSkus: [],
            },
            {
              dimension: 'M',
              sort_order: 10,
              isInstock: true,
              bopis_in_stock: false,
              availableSkus: [],
            },
          ],
          label: 'Size',
          selectedDimension: '',
        },
      ],
    },
    'PETITE|7_8|NO_POCKET': {
      style_id: '333333',
      variantKey: 'PETITE|7_8|NO_POCKET',
      default_customer_choice_id: '3333330004',
      customer_choices: {
        '3333330003': {
          customer_choice_id: '3333330003',
          style_id: '333333',
        },
        '3333330004': {
          customer_choice_id: '3333330004',
          style_id: '333333',
        },
      },
      dimensions: [
        {
          dimensionGroupId: 'sizeDimension1',
          dimensions: [
            {
              dimension: 'S',
              sort_order: 10,
              isInstock: true,
              bopis_in_stock: false,
              availableSkus: [],
            },
            {
              dimension: 'M',
              sort_order: 10,
              isInstock: false,
              bopis_in_stock: false,
              availableSkus: [],
            },
          ],
          label: 'Size',
          selectedDimension: '',
        },
      ],
    },
    'PETITE|FULL|POCKET': {
      style_id: '222222',
      variantKey: 'PETITE|FULL|POCKET',
      default_customer_choice_id: '2222220004',
      customer_choices: {
        '2222220003': {
          style_id: '222222',
          customer_choice_id: '2222220003',
          skus: [
            {
              sku_id: '22222200031234',
              size: 'XS',
            },
          ],
        },
        '2222220004': {
          style_id: '222222',
          customer_choice_id: '2222220004',
          skus: [
            {
              sku_id: '22222200041234',
              size: 'XS',
            },
            {
              sku_id: '22222200042345',
              size: 'S',
            },
            {
              sku_id: '22222200043456',
              size: 'M',
            },
          ],
        },
      },
      dimensions: [
        {
          dimensionGroupId: 'sizeDimension1',
          dimensions: [
            {
              dimension: 'S',
              sort_order: 10,
              isInstock: true,
              bopis_in_stock: false,
              availableSkus: [],
            },
            {
              dimension: 'M',
              sort_order: 10,
              isInstock: false,
              bopis_in_stock: false,
              availableSkus: [],
            },
          ],
          label: 'Size',
          selectedDimension: '',
        },
      ],
    },
  },
};

describe('getInitialSelection', () => {
  describe('STYLE request', () => {
    test('WHEN display_variant_key is present, style is picked from the input alternate cc id', () => {
      const result = getInitialSelection({
        capiData,
        requestedCC: '3333330004',
        selectedMultiVariantKey: 'PETITE|7_8|NO_POCKET',
      });
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['PETITE|7_8|NO_POCKET']);
      expect(result.selectedCustomerChoice).toEqual(capiData.variants['PETITE|7_8|NO_POCKET'].customer_choices['3333330004']);
      expect(result.selectedStyle).toEqual(capiData.styles['333333']);
      expect(result.selectedSku).toEqual(null);
    });

    test('WHEN display_variant_key is NOT present, style is picked from the default cc id and default variant key', () => {
      const result = getInitialSelection({
        capiData,
        requestedCC: '2222220002',
        selectedMultiVariantKey: 'REGULAR|FULL|POCKET',
      });
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['REGULAR|FULL|POCKET']);
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['REGULAR|FULL|POCKET']);
      expect(result.selectedCustomerChoice).toEqual(capiData.variants['REGULAR|FULL|POCKET'].customer_choices['2222220002']);
      expect(result.selectedStyle).toEqual(capiData.styles['222222']);
      expect(result.selectedSku).toEqual(null);
    });
  });

  describe('CC request', () => {
    test('WHEN display_variant_key is present, style is picked from the input alternate cc id', () => {
      const result = getInitialSelection({
        capiData,
        requestedCC: '2222220004',
        selectedMultiVariantKey: 'PETITE|FULL|POCKET',
      });
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['PETITE|FULL|POCKET']);
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['PETITE|FULL|POCKET']);
      expect(result.selectedCustomerChoice).toEqual(capiData.variants['PETITE|FULL|POCKET'].customer_choices['2222220004']);
      expect(result.selectedStyle).toEqual(capiData.styles['222222']);
      expect(result.selectedSku).toEqual(null);
    });
  });

  describe('SKU request', () => {
    test('WHEN input type is SKU - reads the sku id from input id', () => {
      const result = getInitialSelection({
        capiData,
        requestedCC: '2222220001',
        selectedMultiVariantKey: 'REGULAR|FULL|POCKET',
      });
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['REGULAR|FULL|POCKET']);
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['REGULAR|FULL|POCKET']);
      expect(result.selectedStyle).toEqual(capiData.styles['222222']);
    });

    test('WHEN alternate ids has online_legacy_sku_number - reads the sku id from the same', () => {
      const result = getInitialSelection({
        capiData,
        requestedCC: '2222220001',
        selectedMultiVariantKey: 'REGULAR|FULL|POCKET',
      });
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['REGULAR|FULL|POCKET']);
      expect(result.selectedMultiVariantData).toEqual(capiData.variants['REGULAR|FULL|POCKET']);
      expect(result.selectedStyle).toEqual(capiData.styles['222222']);
    });
  });
});
