import { isAT, isBRandBRFS, isBR, isBRFS, isGap, isGapFs, isGapAndGapFs, isON } from '../evaluate-brand';

describe('Evaluate Brand', () => {
  describe('isAT:', () => {
    test('should return true when we pass "at" as input', () => {
      const result = isAT('at');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass empty brand as input', () => {
      const result = isAT('');
      expect(result).toBeFalsy();
    });
    test('should return false when we pass "br" as input', () => {
      const result = isAT('br');
      expect(result).toBeFalsy();
    });
  });

  describe('isBRandBRFS:', () => {
    test('should return true when we pass "br" as input', () => {
      const result = isBRandBRFS('br');
      expect(result).toBeTruthy();
    });
    test('should return true when we pass "brfs" as input', () => {
      const result = isBRandBRFS('brfs');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "gap" as input', () => {
      const result = isBRandBRFS('gap');
      expect(result).toBeFalsy();
    });
  });

  describe('isBR:', () => {
    test('should return true when we pass "br" as input', () => {
      const result = isBR('br');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "brfs" as input', () => {
      const result = isBR('brfs');
      expect(result).toBeFalsy();
    });
  });

  describe('isBRFS:', () => {
    test('should return true when we pass "brfs" as input', () => {
      const result = isBRFS('brfs');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "br" as input', () => {
      const result = isBRFS('br');
      expect(result).toBeFalsy();
    });
  });

  describe('isGap:', () => {
    test('should return true when we pass "gap" as input', () => {
      const result = isGap('gap');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "gapfs" as input', () => {
      const result = isGap('gapfs');
      expect(result).toBeFalsy();
    });
  });

  describe('isGapFs:', () => {
    test('should return true when we pass "gapfs" as input', () => {
      const result = isGapFs('gapfs');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "gap" as input', () => {
      const result = isGapFs('gap');
      expect(result).toBeFalsy();
    });
  });

  describe('isGapAndGapFs:', () => {
    test('should return true when we pass "gap" as input', () => {
      const result = isGapAndGapFs('gap');
      expect(result).toBeTruthy();
    });
    test('should return true when we pass "gapfs" as input', () => {
      const result = isGapAndGapFs('gapfs');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "br" as input', () => {
      const result = isGapAndGapFs('br');
      expect(result).toBeFalsy();
    });
  });

  describe('isON:', () => {
    test('should return true when we pass "on" as input', () => {
      const result = isON('on');
      expect(result).toBeTruthy();
    });
    test('should return false when we pass "gap" as input', () => {
      const result = isON('gap');
      expect(result).toBeFalsy();
    });
  });
});
