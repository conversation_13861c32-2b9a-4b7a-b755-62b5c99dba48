// @ts-nocheck
import { mockFacetApiResponse } from '../../fixtures/mockFacetApiResponse'
import { expectedMappedData } from '../../fixtures/expectedMappedData'
import { mapFacetsDataFromApiRes, transformFacetsData, transformFacetData } from './transform-facets-data'
import { ApiResponse } from '../types'

describe('transformFacetsData', () => {
  const transformedColorFacetData = {
    black: { id: '1017', name: 'Black' },
    gray: { id: '1018', name: '<PERSON>' },
    red: { id: '1019', name: 'Red' },
    pink: { id: '1020', name: 'Pink' },
    blue: { id: '1021', name: '<PERSON>' },
    green: { id: '1022', name: '<PERSON>' },
    brown: { id: '1023', name: '<PERSON>' },
    multi: { id: '1025', name: 'Multi' },
    purple: { id: '1028', name: '<PERSON>' },
    white: { id: '1029', name: '<PERSON>' },
    yellow: { id: '1031', name: 'Yellow' },
    beige: { id: '1040', name: 'Be<PERSON>' }
  };
  const transformedSleeveLengthFacetData = {
    'short-sleeve': {
      id: '1928',
      name: 'Short Sleeve'
    },
    'long-sleeve': {
      id: '1929',
      name: 'Long Sleeve'
    }
  };

  it('should transform facets required from the argument', () => {
    const expectedTransformedFacetsData1 = { sleeveLength: transformedSleeveLengthFacetData };
    const expectedTransformedFacetsData2 = {
      sleeveLength: transformedSleeveLengthFacetData,
      color: transformedColorFacetData
    };

    const transformedFacetsData1 = transformFacetsData(<ApiResponse>mockFacetApiResponse['category'], ['sleeveLength']);
    const transformedFacetsData2 = transformFacetsData(<ApiResponse>mockFacetApiResponse['category'], ['sleeveLength', 'color']);

    expect(transformedFacetsData1).toEqual(expectedTransformedFacetsData1);
    expect(transformedFacetsData2).toEqual(expectedTransformedFacetsData2);
  })

  it('should handle undefined facet data', () =>{
    const transformedFacetsData = transformFacetsData({
      totalColors: '0',
      locale: 'en_US',
      facets: []
    }, ['sleeveLength'])
    expect(transformedFacetsData).toEqual({sleeveLength: {}})
  })
  describe('mapFacetsDataFromApiRes', () => {
    it('should return facetsDataMap', () => {
      const mappedData = mapFacetsDataFromApiRes(<ApiResponse>mockFacetApiResponse['category'])
      expect(mappedData).toEqual(expectedMappedData)
    })

    it('should handle undefined facets data', () => {
      const mappedData = mapFacetsDataFromApiRes({
        totalColors: '0',
        locale: 'en_US',
        facets: []
      })
      expect(mappedData).toEqual({})
    })
  })
  describe('transformFacetData', () => {
    it('should transform facet data', () => {
      const expectedTransformedSleeveLengthFacetData = {
        'short-sleeve': { id: '1928', name: 'Short Sleeve' },
        'long-sleeve': { id: '1929', name: 'Long Sleeve' }
      }

      const sleeveLengthFacetData = {
        name: 'sleeveLength',
        localeName: 'Sleeve Length',
        type: 'multi-select',
        options: [
          {
            name: '1928',
            localeName: 'Short Sleeve'
          },
          {
            name: '1929',
            localeName: 'Long Sleeve'
          }
        ]
      }

      const transformedSleeveLengthFacetData = transformFacetData(sleeveLengthFacetData)

      expect(transformedSleeveLengthFacetData).toEqual(expectedTransformedSleeveLengthFacetData)
    })

    it('should handle any number of spaces in localeName', () => {
      const expectedTransformedSleeveLengthFacetData = {
        'short-sleeve': {
          id: '1928',
          name: 'Short   Sleeve'
        },
        'long-sleeve': {
          id: '1929',
          name: 'Long    Sleeve'
        }
      }

      const sleeveLengthFacetDataWithMoreSpaces = {
        name: 'sleeveLength',
        localeName: 'Sleeve Length',
        type: 'multi-select',
        options: [
          {
            name: '1928',
            localeName: 'Short   Sleeve'
          },
          {
            name: '1929',
            localeName: 'Long    Sleeve'
          }
        ]
      }

      const transformedSleeveLengthFacetData = transformFacetData(sleeveLengthFacetDataWithMoreSpaces)

      expect(transformedSleeveLengthFacetData).toEqual(expectedTransformedSleeveLengthFacetData)
    })
  })
})
