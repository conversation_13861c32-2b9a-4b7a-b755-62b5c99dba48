// @ts-nocheck
import { getFacetsDataFromRedis } from './get-facets-data-from-redis';
import { ErrorLog, FacetsNames } from '../types';
import * as storageConfig from '../storage-config';
import { mockFacetApiResponse } from '../../fixtures/mockFacetApiResponse';
import { mockTransformedCachedFacets } from '../../fixtures/mockTransformedCachedFacets';

jest.mock('../storage-config', () => {
  return {
    redisCache: {
      read: jest.fn(() => Promise.resolve({
        fileContent: {
          data: mockFacetApiResponse.category
        }
      })),
      write: jest.fn(() => Promise.resolve())
    },
  };
})

const { redisCache } = jest.mocked(storageConfig);

describe('getFacetsDataFromRedis', () => {
  const brand = 'gap'
  const market = 'ca'
  const locale = 'en_CA'
  const cid = '15043'
  const fileName = 'facets/ca/gap/category/cid=15043&locale=en_CA'
  const styleId = ''
  const facetsToTransform: FacetsNames[] = ['sleeveLength']

  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should read from redisCache', async() => {
    const transformedData = await getFacetsDataFromRedis(
      { brand, market, cid, styleId, locale, fileName}
      , facetsToTransform
    )
    
    expect(transformedData).toEqual({
      fileContent: { data: mockTransformedCachedFacets.data } 
    })
    expect(redisCache.read).toHaveBeenCalledWith({fileName})
    expect(redisCache.read).toHaveBeenCalledTimes(1)
  })
  
  it('should catch error and return error object', () => {
    const expectedErrorObj: ErrorLog= {
      log: 'Error getting facets data from Redis.',
      type: 'Facets Redis Request',
      brand,
      market,
      locale,
      cid,
      styleId,
      error: 'stack'
    }
  
    storageConfig.redisCache.read.mockRejectedValueOnce({
      stack: 'stack'
    });
    expect(getFacetsDataFromRedis({ brand, market, cid, styleId, locale, fileName}, facetsToTransform))
      .rejects.toEqual(expectedErrorObj)
    expect(redisCache.read).toHaveBeenCalledWith({fileName})
    expect(redisCache.read).toHaveBeenCalledTimes(1)
  })
})
