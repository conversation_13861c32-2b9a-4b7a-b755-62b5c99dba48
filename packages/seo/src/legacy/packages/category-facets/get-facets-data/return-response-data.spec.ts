// @ts-nocheck
import { mockTransformedCachedFacets } from '../../fixtures/mockTransformedCachedFacets';
import { mockTransformedFacetsData } from '../../fixtures/mockTransformedFacetsData';
import { returnResponseData } from './return-response-data';
import { ONE_WEEK } from '../helpers/utilities';
describe('returnResponseData', () => {
  let expectedReturnResponseDate;
  let ttl;

  beforeEach(() => {
    jest.useFakeTimers().setSystemTime(new Date('2024-05-08T16:52:58.014Z'));
    ttl = ONE_WEEK;
    expectedReturnResponseDate = {
      sleeveLength: {
        'short-sleeve': {
          id: '1928',
          name: 'Short Sleeve'
        },
        'long-sleeve': {
          id: '1929',
          name: 'Long Sleeve'
        }
      },
      timestamp: '5/8/2024, 9:52:58 AM Pacific Daylight Time',
      expireTime: '5/15/2024, 9:52:58 AM Pacific Daylight Time'
    }
  })

  it('should return expected transformed facets data, source, timestamp and expireTime for the response facets data from redis', () => {
    expectedReturnResponseDate.source = 'redis';
    const responseData = returnResponseData(mockTransformedCachedFacets, 'redis', ttl);

    expect(responseData).toEqual(expectedReturnResponseDate);
  })

  it('should return expected transformed facets data, source, timestamp and expireTime from api response', () => {
    expectedReturnResponseDate.source = 'facetsApi';
    const responseData = returnResponseData(mockTransformedFacetsData.category, 'facetsApi', ttl);
    expect(responseData).toEqual(expectedReturnResponseDate);
  })
})
