// @ts-nocheck
import { logError, logWarn } from '@ecom-next/seo-helpers/logger';
import { saveToCache } from './save-to-cache';
import { returnResponseData } from './return-response-data';
import { ONE_WEEK, TWENTY_FOUR_HOURS, getFileName } from '../helpers/utilities';
import { getFacetsDataFromRedis } from './get-facets-data-from-redis';
import { getFacetsDataFromApi } from './get-facets-data-from-api';
import { GetFacetsData } from '../types';

const getFacetsData: GetFacetsData = ({
  brand,
  market,
  cid,
  locale,
  styleId = '',
  host,
  mode,
  previewDate,
  facetsRefreshTime,
  facetsTTL,
  facetsToTransform = ['sleeveLength', 'color'],
}) => {
  const isRedisDisabled = process.env?.DISABLE_REDIS === 'true';

  const fileName = getFileName({ brand, market, cid, locale, styleId });
  const ttl = facetsTTL || ONE_WEEK;
  let cacheResponse = null;

  if (isRedisDisabled || (mode && previewDate && host)) {
    return getFacetsDataFromApi({ brand, market, cid, locale, styleId, host, mode, previewDate }, facetsToTransform)
      .then(({ transformedFacetsData }) => returnResponseData(transformedFacetsData, 'facetsApi'))
      .catch(err => Promise.reject(err));
  }

  return getFacetsDataFromRedis({ brand, market, cid, styleId, locale, fileName }, facetsToTransform)
    .then(({ fileContent, expireTime }) => {
      cacheResponse = { ...fileContent, expireTime };

      const now: Date = new Date();
      const cacheTimestamp: Date = new Date(cacheResponse.headers.UTCdate);
      const refreshTime = facetsRefreshTime || TWENTY_FOUR_HOURS;
      const staleData = now.getTime() - cacheTimestamp.getTime() > refreshTime;

      if (staleData) {
        return Promise.reject();
      }

      return returnResponseData(cacheResponse, 'redis', ttl);
    })
    .catch(() => {
      return getFacetsDataFromApi({ brand, market, cid, locale, styleId, host }, facetsToTransform)
        .then(({ transformedFacetsData, apiRes }) =>
          Promise.any([saveToCache({ fileName, facetsData: apiRes, ttl }), returnResponseData(transformedFacetsData, 'facetsApi', ttl)])
        )
        .catch(err => Promise.reject(err));
    })
    .catch(error => {
      if (cacheResponse) {
        const warnObj = {
          type: 'Facets Request',
          log: 'Failed to refresh cache. Returning stale data.',
          fileName,
          brand,
          market,
          cid,
          locale,
          styleId,
        };

        logWarn(warnObj);
        return Promise.resolve(returnResponseData(cacheResponse, 'redis', ttl));
      }

      const errObj = {
        type: 'Facets Request',
        log: 'Error returning facets data',
        fileName,
        brand,
        market,
        cid,
        locale,
        styleId,
        error: error.stack || JSON.stringify(error),
      };

      logError(errObj);
      return Promise.reject(errObj);
    });
};

export { getFacetsData };
