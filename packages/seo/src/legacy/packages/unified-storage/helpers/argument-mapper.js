// @ts-nocheck
const argMapper = (args) => {
  let providedSpaceName;
  if (args?.storageSpace) providedSpaceName = args.storageSpace;
  if (args?.bucketName) providedSpaceName = args.bucketName;
  if (args?.containerName) providedSpaceName = args.containerName;

  return Object.assign({
    storageSpace: providedSpaceName,
    bucketName: providedSpaceName,
    containerName: providedSpaceName
  }, args);
};

module.exports = argMapper;
