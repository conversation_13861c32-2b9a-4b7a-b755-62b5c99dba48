# Unified Storage

In order to make use of attached storage across multiple applications, we created a class that can leverage multiple storage configurations at the same time. This class acts as a boilerplate that handles different storage configurations and can perform various read, write, and delete operations, while returning information on files and storage spaces to the user in a predictable and standard pattern. This pattern should limit duplication and maintenance of code and behaviors across distinct applications.

## Outline

* * *

-   [Architecture](#architecture)
-   [Editing](#editing)
-   [Set Up](#set-up)
-   [Methods](#methods)

## Architecture

* * *

![Architecture](images/storage-composition.png)

## Editing

* * *

We edit the files in the `/src` directory. The `/lib` directory is generated automatically when we publish our package and is the version of the package that consumer applications load in and use.

## Monitoring & Reporting

* * *

We record performance metrics for all storage transactions using custom newrelic background transactions. Storage
interactions will appear in newrelic under `non-web` transactions as `unifiedStorage:<storageType>:<storageOperation>`.
For example, a read operation on Azure Redis Cache will appear as `unifiedStorage:redis:read`. Capturing metrics
requires a newrelic instance being set up in the consuming application.
```
const { getNewRelicInstance, setNewRelicInstance } = require('@mfe/new-relic-methods');

if (process.env.TARGET_ENV) {
  if (!getNewRelicInstance()) {
    const newrelic = require('newrelic');
    setNewRelicInstance(newrelic);
  }
}
```

## Set Up

* * *

### Install
```js
npm i --save @ecom-next/seo-helpers/unified-storage
```

### Basic Usage
1.  Import the `@ecom-next/seo-helpers/unified-storage` library into your project.
2.  Create a new `UnifiedStorage` object in your application and construct the `UnifiedStorage` class using the [required & desired options and configurations](#new_UnifiedStorage_new).
3.  You're ready to use your `UnifiedStorage` class instance to perform operations on your storage spaces and the objects within those spaces.

```js
const UnifiedStorage = require('@ecom-next/seo-helpers/unified-storage');

const cache = new UnifiedStorage({
  storageType: 'redis',
  port: <redis-port>,
  url: <redis-host-url>,
  accessKey: <redis-secret-access-key>
});

cache.read({ fileName: 'some-file' })
  .then((res) => {...})
  .catch((error) => {...});
```

## Methods

* * *

-   [Unified Storage](#classes)
-   [Azure Blob Storage](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure/README.md)
-   [Azure Redis Cache](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis/README.md)
-   [S3 Storage](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3/README.md)
-   [Helper Methods](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/helpers/README.md)

### Unified Storage

## Classes

<table>
  <thead>
    <tr>
      <th>Global</th><th>Description</th>
    </tr>
  </thead>
  <tbody>
<tr>
    <td><a href="#UnifiedStorage">UnifiedStorage</a></td>
    <td><p>Creates a new UnifiedStorage object.</p>
</td>
    </tr>
</tbody>
</table>

## Functions

<table>
  <thead>
    <tr>
      <th>Global</th><th>Description</th>
    </tr>
  </thead>
  <tbody>
<tr>
    <td><a href="#configureStorage">configureStorage(options)</a> ⇒ <code>Object</code></td>
    <td><p>This function returns a storage configuration based on the options passed into it.</p>
</td>
    </tr>
</tbody>
</table>

<a name="UnifiedStorage"></a>

## UnifiedStorage
Creates a new UnifiedStorage object.

**Kind**: global class  
**See**: [https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/index.js](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/index.js)  

* [UnifiedStorage](#UnifiedStorage)
    * [new UnifiedStorage(options)](#new_UnifiedStorage_new)
    * [.mapTo](#UnifiedStorage+mapTo) ⇒ <code>Promise</code>
    * [.listClusterInfo](#UnifiedStorage+listClusterInfo) ⇒ <code>Promise</code>
    * [.createStorageSpace](#UnifiedStorage+createStorageSpace) ⇒ <code>Promise</code>
    * [.deleteStorageSpace](#UnifiedStorage+deleteStorageSpace) ⇒ <code>Promise</code>
    * [.storageSpaceExists](#UnifiedStorage+storageSpaceExists) ⇒ <code>Promise</code>
    * [.list](#UnifiedStorage+list) ⇒ <code>Promise</code>
    * [.objectExistsInSpace](#UnifiedStorage+objectExistsInSpace) ⇒ <code>Promise</code>
    * [.write](#UnifiedStorage+write) ⇒ <code>Promise</code>
    * [.writeMultiple](#UnifiedStorage+writeMultiple) ⇒ <code>Promise</code>
    * [.read](#UnifiedStorage+read) ⇒ <code>Promise</code>
    * [.readMultiple](#UnifiedStorage+readMultiple) ⇒ <code>Promise</code>
    * [.delete](#UnifiedStorage+delete) ⇒ <code>Promise</code>
    * [.deleteMultiple](#UnifiedStorage+deleteMultiple) ⇒ <code>Promise</code>
    * [.deleteAll](#UnifiedStorage+deleteAll) ⇒ <code>Promise</code>
    * [.ping](#UnifiedStorage+ping) ⇒ <code>Promise</code>

<a name="new_UnifiedStorage_new"></a>

### new UnifiedStorage(options)

| Param | Type | Description |
| --- | --- | --- |
| options | <code>Object</code> | Configuration options vary according to the selected storageType: <br>&nbsp;&nbsp;- [Azure options](https://azuresdkdocs.blob.core.windows.net/$web/javascript/azure-storage-blob/12.0.0/classes/blobserviceclient.html) <br>&nbsp;&nbsp;- [Redis options](https://github.com/luin/ioredis/blob/master/API.md#new-redisport-host-options) |
| options.storageType | <code>String</code> | The storage type to be configured. Valid storage types include: 'azure', 'redis' or 's3'. |
| options.recordTransactionFallback | <code>Boolean</code> | When storage interactions are not captured by New Relic automatically, this toggle can be enabled as a fallback to record the interaction as a background transaction. By defualt, all storage interactions should automatically be captured by New Relic within the web transaction that called the storage operation. |

<a name="UnifiedStorage+mapTo"></a>

### unifiedStorage.mapTo ⇒ <code>Promise</code>
Maps class storage methods to the particular method based on the storage type.

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>any</code>  

| Param | Type | Description |
| --- | --- | --- |
| functionName | <code>string</code> | This is the function that is mapped to by the mapper. |
| args | <code>Object</code> | These are the arguments for a given request. |

<a name="UnifiedStorage+listClusterInfo"></a>

### unifiedStorage.listClusterInfo ⇒ <code>Promise</code>
This method lists the buckets or containers in a given storage space. Maps to:
- [listContainers](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#listContainers) for Azure
- `null` for Redis
- [listBucketsInRepo](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#listBucketsInRepo) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  
<a name="UnifiedStorage+createStorageSpace"></a>

### unifiedStorage.createStorageSpace ⇒ <code>Promise</code>
This method creates a storage container or bucket in a given space. Maps to:
- `null` for Azure
- `null` for Redis
- [createBucketInRepo](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#createBucketInRepo) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

<a name="UnifiedStorage+deleteStorageSpace"></a>

### unifiedStorage.deleteStorageSpace ⇒ <code>Promise</code>
This method deletes a storage container or bucket in a given space. Maps to:
- `null` for Azure
- `null` for Redis
- [deleteBucketInRepo](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#deleteBucketInRepo) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

<a name="UnifiedStorage+storageSpaceExists"></a>

### unifiedStorage.storageSpaceExists ⇒ <code>Promise</code>
This method returns an object to show whether a container or bucket exists in a given storage space. Maps to:
- [containerExistsInAccount](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#containerExistsInAccount) for Azure
- `null` for Redis
- [bucketExists](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#bucketExists) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

<a name="UnifiedStorage+list"></a>

### unifiedStorage.list ⇒ <code>Promise</code>
This method lists the objects, files or blobs in a given bucket, container or storage space. Maps to:
- [listBlobsInContainer](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#listBlobsInContainer) for Azure
- [listFiles](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#listFiles) for Redis
- [listObjectsInBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#listObjectsInBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });

cache.list({ matcher: '*' })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+objectExistsInSpace"></a>

### unifiedStorage.objectExistsInSpace ⇒ <code>Promise</code>
This method returns an object to show whether a blob or object exists in a given storage space. Maps to:
- [blobExistsInContainer](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#blobExistsInContainer) for Azure
- `null` for Redis
- [objectExistsInBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#objectExistsInBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

<a name="UnifiedStorage+write"></a>

### unifiedStorage.write ⇒ <code>Promise</code>
This method returns an object that lets us know the status of a write request (i.e. to put an object in a given storage space). Maps to:
- [writeBlob](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#writeBlob) for Azure
- [writeFile](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#writeFile) for Redis
- [putObjectIntoBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#putObjectIntoBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });

cache.write({ fileName: 'some-file', fileContent: { data: 'some-data' } })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+writeMultiple"></a>

### unifiedStorage.writeMultiple ⇒ <code>Promise</code>
This method returns an object that lets us know the status of a batch write request (i.e. to put multiple objects in a given storage space). Maps to:
- `null` for Azure
- [writeMultipleFiles](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#writeMultipleFiles) for Redis
- `null` for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });
const fileArray = [
  { fileName: 'file-1', fileContent: 'some-data' },
  { fileName: 'file-2', fileContent: 'some-data' }
]

cache.writeMultiple({ fileArray })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+read"></a>

### unifiedStorage.read ⇒ <code>Promise</code>
This method returns an object with data for a given file in a given storage space. Maps to:
- [readBlob](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#readBlob) for Azure
- [readFile](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#readFile) for Redis
- [getObjectFromBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#getObjectFromBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });

cache.read({ fileName: 'some-file' })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+readMultiple"></a>

### unifiedStorage.readMultiple ⇒ <code>Promise</code>
This method returns multiple objects with data for given files in a given storage space. Maps to:
- `null` for Azure
- [readMultipleFiles](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#readMultipleFiles) for Redis
- `null` for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Array&lt;Object&gt;</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });
const fileNames = ['file1', 'file2', 'file3']

cache.readMultiple({ fileNames })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+delete"></a>

### unifiedStorage.delete ⇒ <code>Promise</code>
This method returns an object to let us know the deletion status of a given file in a given storage space. Maps to:
- [deleteBlob](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#deleteBlob) for Azure
- [deleteFile](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#deleteFile) for Redis
- [deleteObjectInBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#deleteObjectInBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });

cache.delete({ fileName: 'some-file' })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+deleteMultiple"></a>

### unifiedStorage.deleteMultiple ⇒ <code>Promise</code>
This method returns an object to let us know the deletion status of multiple files in a given storage space. Maps to:
- [deleteMultipleBlobs](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#deleteMultipleBlobs) for Azure
- [deleteMultipleFiles](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#deleteMultipleFiles) for Redis
- [deleteMultipleObjectsInBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#deleteMultipleObjectsInBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });
const fileNames = ['file1', 'file2', 'file3'];
const matcher = '*product.do*';

cache.deleteMultiple({ fileNames })
  .then((res) => {...})
  .catch((error) => {...});

// Delete all data related to product pages (assuming page urls were used for the keys/filenames)
cache.deleteMultiple({ matcher })
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+deleteAll"></a>

### unifiedStorage.deleteAll ⇒ <code>Promise</code>
This method returns an object to let us know the deletion status of all files in a given storage space. Maps to:
- [emptyContainer](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/azure#emptyContainer) for Azure
- [deleteAllFiles](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#deleteAllFiles) for Redis
- [deleteAllObjectsInBucket](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/s3#deleteAllObjectsInBucket) for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>Object</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });

cache.deleteAll()
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="UnifiedStorage+ping"></a>

### unifiedStorage.ping ⇒ <code>Promise</code>
This method serves as a health check for a given storage space. Maps to:
- `null` for Azure
- [ping](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis#ping) for Redis
- `null` for S3

**Kind**: instance property of [<code>UnifiedStorage</code>](#UnifiedStorage)  
**Fulfil**: <code>any</code>  

| Param | Type | Default |
| --- | --- | --- |
| args | <code>Object</code> | <code>{}</code> | 

**Example** *(Azure Redis Cache)*  
```js
const cache = new UnifiedStorage({ storageType: 'redis' });

cache.ping()
  .then((res) => {...})
  .catch((error) => {...});
```
<a name="configureStorage"></a>

## configureStorage(options) ⇒ <code>Object</code>
This function returns a storage configuration based on the options passed into it.

**Kind**: global function  
**See**: [https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/configure-storage.js](https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/configure-storage.js)  

| Param | Type |
| --- | --- |
| options | <code>Object</code> | 
| options.storageType | <code>String</code> | 
| options.isRedisDisabled | <code>boolean</code> | 

