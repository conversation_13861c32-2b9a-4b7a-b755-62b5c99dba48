// @ts-nocheck
/**
 * This method check the connection status to the Redis server, and re-establishes connection if disconnected.
 * @see {@link https://github.gapinc.com/onlineplatform/seo-app-helpers/tree/main/packages/unified-storage/src/redis/redis-config.js|redis-config.js}
 * @param {RedisClient} redisClient Instance of the Redis client used to connect to remote server.
 * @returns {Promise}
 */
const verifyClientConnection = (redisClient) => {
  if (redisClient.status !== 'ready' && redisClient.status !== 'wait' && !redisClient.connected) return redisClient.connect();
  return Promise.resolve();
};

module.exports = verifyClientConnection;
