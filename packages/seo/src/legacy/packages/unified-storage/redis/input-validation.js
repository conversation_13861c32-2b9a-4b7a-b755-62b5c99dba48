// @ts-nocheck
const { isEmpty } = require('../helpers/helper-methods');

/**
 * Checks required fields in the redis configuration and throws an error is any is missing
 * @param {{port: Number, url: String, accessKey: String}} redisConfigs
 */
const inputValidation = (redisConfigs) => {
  if (isEmpty(redisConfigs.port)) throw new Error('Invalid Port');
  if (isEmpty(redisConfigs.url)) throw new Error('Invalid Redis Endpoint');
  if (isEmpty(redisConfigs.accessKey)) throw new Error('Invalid Credentials');
};

module.exports = inputValidation;
