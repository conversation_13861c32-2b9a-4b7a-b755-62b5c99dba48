// @ts-nocheck
const { chunkArray, respondWithSuccess, respondWithS3Failure } = require('../helpers/helper-methods');

/**
 * This method lists all the buckets in an S3 cluster.
 * @param {Object<{}>} providedValues this is an empty object
 * @param {string} s3Repo the S3 instance to check against
 * @returns {Promise}
 * @fulfil {{data: {Owner: {DisplayName: string, ID: string}, Buckets: {CreationDate: string, Name: string}[], success: boolean}}
 * @reject {{success: boolean, error: *}}
 */
const listBucketsInRepo = (providedValues, s3Repo) => s3Repo.listBuckets().promise()
  .then(resp => respondWithSuccess(resp))
  .catch(err => Promise.reject(respondWithS3Failure(err, '')));

/**
 * This method creates an S3 bucket in a given cluster.
 * @param {Object<{storageSpace: string}>} providedValues
 * @param {string} s3Repo the S3 instance to check against
 * @returns {Promise<{success: boolean, response: string} | {success: boolean, error: any}>}
 */
const createBucketInRepo = (providedValues, s3Repo) => s3Repo.createBucket({
  Bucket: providedValues.storageSpace
}).promise()
  .then(resp => respondWithSuccess(resp))
  .catch(err => Promise.reject(respondWithS3Failure(err, '')));

/**
 * This method deletes a bucket in a given cluster.
 * @param {Object<{storageSpace: string}>} providedValues
 * @param {string} s3Repo the S3 instance to check against
 * @returns {Promise<{success: boolean, response: string, message: string} | {success: boolean, response: string, error: any}>|Promise<{success: boolean, response: string}>}
 */
const deleteBucketInRepo = (providedValues, s3Repo) => {
  if (!providedValues.storageSpace) {
    return Promise.reject(
      respondWithS3Failure('', 'No bucket was specified to get deleted. Please specify a bucket name.')
    );
  }

  return s3Repo.deleteBucket({
    Bucket: providedValues.storageSpace
  }).promise()
    .then(resp => respondWithSuccess(resp))
    .catch(err => Promise.reject(respondWithS3Failure(err, `Error deleting the bucket ${providedValues.storageSpace}`))
    );
};

/**
 * This method returns whether or not a given bucket exists in a cluster.
 * @param {Object<{storageSpace: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise}
 * @fulfil {{data: {Owner: {DisplayName: string, ID: string}, Buckets: {CreationDate: string, Name: string}[]}, success: boolean}}
 * @reject {{success: boolean, error: *}}
 */
const bucketExists = (providedValues, s3Repo) => listBucketsInRepo(providedValues, s3Repo)
  .then((bucketList) => {
    if (bucketList.success) {
      return bucketList.response.Buckets.some(bucket => bucket.Name === providedValues.storageSpace);
    }
    return Promise.reject(respondWithS3Failure({}, 'Error retrieving the bucket list'));
  })
  .catch(err => Promise.reject(
    respondWithS3Failure(
      err,
      `There was an checking if the bucket-${providedValues.storageSpace} exists\nerror:\n${err}`)
  ));

/**
 * This method lists the S3 objects in a given bucket.
 * @param {Object<{storageSpace: string, maxKeys: number, prefix: string, continuationMarker: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise}
 * @fulfil {{success: boolean, response: {CommonPrefixes: [], Contents: {LastModified: string, Owner: {DisplayName: string, ID: string}, Size: number, StorageClass: string, Key: string}[], IsTruncated: boolean, Prefix: string, MaxKeys: number, Name: string}, objectsInBucket: number}}
 * @reject {{success: boolean, error: any, message: string}}
 */
const listObjectsInBucket = (providedValues, s3Repo) => s3Repo.listObjectsV2({
  Bucket: providedValues.storageSpace,
  MaxKeys: providedValues.maxKeys || 2000,
  Prefix: providedValues.prefix || '',
  ContinuationToken: providedValues.continuationMarker || '',
}).promise()
  .then(resp => Object.assign(respondWithSuccess(resp), { objectsInBucket: resp.Contents.length })
  )
  .catch(err => Promise.resolve(
    respondWithS3Failure(err, `There was an error retrieving the objects from the bucket ${providedValues.storageSpace}`)
  ));

/**
 * This method returns whether or not an object exists in a given bucket.
 * @param {Object<{storageSpace: string, objectName: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise}
 * @fulfil {{success: boolean, response: {CommonPrefixes: *[], Contents: {LastModified: string, Owner: {DisplayName: string, ID: string}, Size: number, StorageClass: string, Key: string}[], IsTruncated: boolean, Prefix: string, MaxKeys: number, Name: string}, objectsInBucket: number}}
 * @reject {{success: boolean, error: any, message: string}}
 */
const objectExistsInBucket = (providedValues, s3Repo) => {
  const updatedObjectWithKeys = Object.assign(providedValues, { maxKeys: 7500 });

  return listObjectsInBucket(updatedObjectWithKeys, s3Repo)
    .then((objectsList) => {
      if (objectsList.success) {
        return objectsList.response.Contents.some(
          bucketObject => bucketObject.Key === providedValues.objectName
        );
      }
      return Promise.reject(respondWithS3Failure({}, 'Error retrieving the object list'));
    })
    .catch(err => Promise.reject(
      respondWithS3Failure(
        err,
        `Error checking if object-${providedValues.objectName} exists in bucket-${providedValues.storageSpace}`)
    ));
};

/**
 * This method puts in object into a given S3 bucket and returns the status of that transaction.
 * @param {Object<{storageSpace: string, fileName: string, fileBody: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise<{success: boolean, response: *, message: string} | {success: boolean, response: string, error: *}>}
 */
const putObjectIntoBucket = (providedValues, s3Repo) => s3Repo.putObject({
  Bucket: providedValues.storageSpace,
  Key: providedValues.fileName,
  ContentType: 'text/html',
  Body: providedValues.fileBody
}).promise()
  .then((resp) => {
    const msg = `The file ${providedValues.fileName} was successfully written to the bucket ${providedValues.storageSpace}`;
    return respondWithSuccess(resp, msg);
  })
  .catch(err => Promise.reject(
    respondWithS3Failure(
      err,
      `There was an error writing ${providedValues.fileName} to the bucket ${providedValues.storageSpace}`)
  ));

/**
 * This method returns an object from an S3 bucket and returns the status of that transaction.
 * @param {Object<{storageSpace: string, fileName: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise<{success: boolean, response: string, message: string} | {success: boolean, message: string, error: any}>}
 */
const getObjectFromBucket = (providedValues, s3Repo) => s3Repo.getObject({
  Bucket: providedValues.storageSpace,
  Key: providedValues.fileName
}).promise()
  .then(resp => respondWithSuccess(resp))
  .catch(err => Promise.reject(
    respondWithS3Failure(
      err,
      `There was an error retrieving ${providedValues.fileName} from the bucket ${providedValues.storageSpace}`)
  ));


/**
 * This method deletes an object in an S3 bucket and returns the status of that transaction.
 * @param {Object<{fileName: string, storageSpace: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise<{success: boolean, response: string, message: string} | {success: boolean, message: string, error: any}>|Promise<{success: boolean, response: string}>}
 */
const deleteObjectInBucket = (providedValues, s3Repo) => {
  if (!providedValues.fileName) {
    return Promise.reject(respondWithS3Failure({}, 'No file was specified to get deleted. Please specify a file name.'));
  }

  return s3Repo.deleteObject({
    Bucket: providedValues.storageSpace,
    Key: providedValues.fileName
  }).promise()
    .then(resp => respondWithSuccess(resp))
    .catch(err => Promise.reject(
      respondWithS3Failure(
        err,
        `There was an error deleting ${providedValues.fileName} from the bucket ${providedValues.storageSpace}`)
    ));
};

/**
 * This method deletes multiple objects in a given S3 bucket and returns the status of that transaction.
 * @param {Object<{storageSpace: string, keysArray: Array<{Keys: string}>}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise<{success: boolean, response: string, message: string} | {success: boolean, message: string, error: any}>}
 */
const deleteMultipleObjectsInBucket = (providedValues, s3Repo) => s3Repo.deleteObjects({
  Bucket: providedValues.storageSpace,
  Delete: {
    Objects: providedValues.keysArray,
    Quiet: false,
  }
}).promise()
  .then(resp => respondWithSuccess(resp))
  .catch(err => Promise.reject(
    respondWithS3Failure(err, `The bucket ${providedValues.storageSpace} failed to delete multiple objects`)
  ));

/**
 * This method attempts to delete up to 10,000 objects within an S3 bucket and returns the status of that transaction.
 * @param {Object<{storageSpace: string}>} providedValues
 * @param {string} s3Repo
 * @returns {Promise}
 * @fulfil {Array<Object<{success: boolean, response: string, message: string}>>}
 * @reject {Array<Object<{success: boolean, message: string, error: any}>>}
 */
const deleteAllObjectsInBucket = (providedValues, s3Repo) => {
  const { storageSpace } = providedValues;
  const requestParameters = { storageSpace, maxKeys: 10000 };

  return listObjectsInBucket(requestParameters, s3Repo)
    .then((bucketObjects) => {
      const formattedFileArray = bucketObjects.response.Contents.map(fileObject => ({ Key: fileObject.Key }));
      const chunkedArray = chunkArray(formattedFileArray, 1000);
      const arrOfFormattedRequests = chunkedArray.map(chunk => Object.assign({ keysArray: chunk }, requestParameters));

      return Promise.all(arrOfFormattedRequests.map(request => deleteMultipleObjectsInBucket(request, s3Repo)));
    })
    .catch(err => Promise.reject(
      respondWithS3Failure(err, `The bucket ${storageSpace} failed to delete all its objects`)
    ));
};

module.exports = {
  listBucketsInRepo,
  bucketExists,
  createBucketInRepo,
  listObjectsInBucket,
  putObjectIntoBucket,
  getObjectFromBucket,
  objectExistsInBucket,
  deleteBucketInRepo,
  deleteObjectInBucket,
  deleteMultipleObjectsInBucket,
  deleteAllObjectsInBucket
};
