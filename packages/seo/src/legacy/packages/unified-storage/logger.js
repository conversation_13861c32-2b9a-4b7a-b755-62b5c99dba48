// @ts-nocheck
const winston = require('winston');

const LOG_LEVEL = process.env.LOG_LEVEL || 'info';
const silent = process.env.SILENCE_LOGS === 'true';

const customFormat = winston.format.printf(info => `${info.level}: ${JSON.stringify(info.message, null, 2)}`);

const setLogger = (isLocal = !process.env.ARTIFACT_VERSION) => {
  if (isLocal) {
    return winston.createLogger({
      level: 'verbose',
      transports: new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          customFormat,
        )
      })
    });
  }

  return winston.createLogger({
    level: LOG_LEVEL,
    silent,
    transports: [
      new winston.transports.Console({
        json: true
      })
    ]
  });
};

const logger = setLogger();

const logInfo = infoObj => logger.info(infoObj);

const logWarn = warnObj => logger.warn(warnObj);

const logError = errorObj => logger.error(errorObj);

module.exports = {
  logInfo,
  logError,
  logWarn,
};
