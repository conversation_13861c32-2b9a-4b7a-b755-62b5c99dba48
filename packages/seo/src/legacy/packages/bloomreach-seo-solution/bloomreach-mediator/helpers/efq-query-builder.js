// @ts-nocheck
const { EFQ_FILTER_QUERIES } = require('../../constants');
/**
 * Creates the filter param for bloomreach to consume
 * @param {string} type
 * @param {string} name
 * @returns {string} -filterType:("FilterName")
 */
const efqFilter = ({ type, query, operator }) => {
  const valueWithOperator = query
    .split(',')
    .map((fv) => `"${fv}"`)
    .join(` ${operator} `);
  return `${type}:(${valueWithOperator})`;
};

/**
 * @description Create params for filtering content with bloomreach
* @param {string[]} efq
 * @returns efq param expected by bloomreach
 */
const efqQueryBuilder = ({ efq }) => {
  if (!efq) return;

  return efq
    .map((id) => EFQ_FILTER_QUERIES[id])
    .filter((query) => !!query)
    .map(({ type, query, operator }) => efqFilter({ type, query, operator }))
    .map((query) => decodeURIComponent(query));
};

module.exports = { efqQueryBuilder };
