// @ts-nocheck
const chai = require('chai');
const sinon = require('sinon');
const proxyquire = require('proxyquire').noCallThru();
const logger = require('../../logger');

const { expect } = chai;
chai.use(require('sinon-chai'));

describe('Widget API Client', () => {
  const url = 'https://bananarepublic.gap.com/browse/product.do?pid=550382022&cid=10894';
  const brTestUrl = 'http://bsapi-test.brsrvr.com/v3/fetch_widget';
  const userAgent = 'some-user-agent';
  const ref = 'some-ref';
  const pageType = 'category';
  const brand = 'gap';
  const widgetApiParams = {
    url,
    ref,
    userAgent,
    pageType,
    brand
  };

  const logInfoSpy = sinon.spy(logger, 'logInfo');
  const logErrorStub = sinon.stub();
  const requestStub = sinon.stub().resolves({ data: { 'related-category': ['some category'] } });

  const getRequestOptionsModule = proxyquire('../helpers/get-request-options.js', {
    '../../authentication': { get: () => ({ gap: { accountId: 'test', apikey: 'test' } }) },
    '../../logger': { logInfo: logInfoSpy }
  });

  const widgetClient = proxyquire('../widget-client.js', {
    'axios': { default: requestStub },
    './helpers/get-request-options': getRequestOptionsModule,
    '../logger': { logError: logErrorStub }
  });

  afterEach(() => {
    logInfoSpy.resetHistory();
    logErrorStub.resetHistory();
    requestStub.resetHistory();
  });

  after(() => {
    logInfoSpy.restore();
    logErrorStub.reset();
    requestStub.reset();
  });

  it('should request the bloomreach widget api with proper parameters', async() => {
    const expectedQueryParams = {
      acct_id: 'test',
      acct_auth: 'test',
      ptype: pageType,
      ref,
      url,
      user_agent: userAgent,
      output_format: 'br_json'
    };
    const expectedRetryOptions = {
      retry: 3,
      noResponseRetries: 3,
      backoffType: 'exponential',
      onRetryAttempt: sinon.match.func,
    };
    const expectedRequestArgs = {
      method: 'GET',
      url: brTestUrl,
      params: expectedQueryParams,
      timeout: 60000,
      raxConfig: expectedRetryOptions
    };

    await widgetClient.requestWidgetApi(widgetApiParams);

    expect(requestStub).to.have.been.calledWith(sinon.match(expectedRequestArgs));
  });

  describe('On request failure', () => {    
    const widgetClientRetryModule = proxyquire('../widget-client.js', {
      './helpers/get-request-options': getRequestOptionsModule,
      '../logger': { logError: logErrorStub }
    });

    it('should retry request 3 times', async() => {
      try {
        await widgetClientRetryModule.requestWidgetApi(widgetApiParams);
      } catch (e) {
        expect(logInfoSpy).to.have.callCount(3);
        expect(logInfoSpy.firstCall.args[0].log).to.match(/.*Retry attempt #1.*/gi);
        expect(logInfoSpy.secondCall.args[0].log).to.match(/.*Retry attempt #2.*/gi);
        expect(logInfoSpy.thirdCall.args[0].log).to.match(/.*Retry attempt #3.*/gi);
        logInfoSpy.restore();
      }
    });

    it('should log an error if request to bloomreach api fails after max retries', async() => {
      try {
        await widgetClientRetryModule.requestWidgetApi(widgetApiParams);
      } catch (e) {
        expect(logErrorStub).to.have.been.calledOnce;
      }
    });
  });
});
