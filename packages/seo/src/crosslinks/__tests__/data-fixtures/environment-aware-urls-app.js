const environmentAwareUrlsApp = [
  {
    brandToTest: 'br',
    marketToTest: 'us',
    environment: 'test',
    expectedUrl: 'https://brol.app.test.gaptecholapps.com/',
  },
  {
    brandToTest: 'on',
    marketToTest: 'us',
    environment: 'test',
    expectedUrl: 'https://onol.app.test.gaptecholapps.com/',
  },
  {
    brandToTest: 'gap',
    marketToTest: 'us',
    environment: 'test',
    expectedUrl: 'https://www.app.test.gaptecholapps.com/',
  },
  {
    brandToTest: 'at',
    marketToTest: 'us',
    environment: 'test',
    expectedUrl: 'https://atol.app.test.gaptecholapps.com/',
  },
  {
    brandToTest: 'brfs',
    marketToTest: 'us',
    environment: 'test',
    expectedUrl: 'https://brfol.app.test.factory-gaptecholapps.com/',
  },
  {
    brandToTest: 'gapfs',
    marketToTest: 'us',
    environment: 'test',
    expectedUrl: 'https://www.app.test.factory-gaptecholapps.com/',
  },
  {
    brandToTest: 'br',
    marketToTest: 'ca',
    environment: 'test',
    expectedUrl: 'https://brol.app.test.gaptecholapps.ca/',
  },
  {
    brandToTest: 'on',
    marketToTest: 'ca',
    environment: 'test',
    expectedUrl: 'https://onol.app.test.gaptecholapps.ca/',
  },
  {
    brandToTest: 'gap',
    marketToTest: 'ca',
    environment: 'test',
    expectedUrl: 'https://www.app.test.gaptecholapps.ca/',
  },
  {
    brandToTest: 'at',
    marketToTest: 'ca',
    environment: 'test',
    expectedUrl: 'https://atol.app.test.gaptecholapps.ca/',
  },
  {
    brandToTest: 'brfs',
    marketToTest: 'ca',
    environment: 'test',
    expectedUrl: 'https://brfol.app.test.factory-gaptecholapps.ca/',
  },
  {
    brandToTest: 'br',
    marketToTest: 'us',
    environment: 'stage',
    expectedUrl: 'https://brol.app.stage.gaptecholapps.com/',
  },
  {
    brandToTest: 'on',
    marketToTest: 'us',
    environment: 'stage',
    expectedUrl: 'https://onol.app.stage.gaptecholapps.com/',
  },
  {
    brandToTest: 'gap',
    marketToTest: 'us',
    environment: 'stage',
    expectedUrl: 'https://www.app.stage.gaptecholapps.com/',
  },
  {
    brandToTest: 'at',
    marketToTest: 'us',
    environment: 'stage',
    expectedUrl: 'https://atol.app.stage.gaptecholapps.com/',
  },
  {
    brandToTest: 'brfs',
    marketToTest: 'us',
    environment: 'stage',
    expectedUrl: 'https://brfol.app.stage.factory-gaptecholapps.com/',
  },
  {
    brandToTest: 'gapfs',
    marketToTest: 'us',
    environment: 'stage',
    expectedUrl: 'https://www.app.stage.factory-gaptecholapps.com/',
  },
  {
    brandToTest: 'br',
    marketToTest: 'ca',
    environment: 'stage',
    expectedUrl: 'https://brol.app.stage.gaptecholapps.ca/',
  },
  {
    brandToTest: 'on',
    marketToTest: 'ca',
    environment: 'stage',
    expectedUrl: 'https://onol.app.stage.gaptecholapps.ca/',
  },
  {
    brandToTest: 'gap',
    marketToTest: 'ca',
    environment: 'stage',
    expectedUrl: 'https://www.app.stage.gaptecholapps.ca/',
  },
  {
    brandToTest: 'at',
    marketToTest: 'ca',
    environment: 'stage',
    expectedUrl: 'https://atol.app.stage.gaptecholapps.ca/',
  },
  {
    brandToTest: 'brfs',
    marketToTest: 'ca',
    environment: 'stage',
    expectedUrl: 'https://brfol.app.stage.factory-gaptecholapps.ca/',
  },
];

module.exports = {
  environmentAwareUrlsApp,
};
