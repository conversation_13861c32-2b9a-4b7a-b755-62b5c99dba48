import { render, screen } from '@testing-library/react';
import FilterIcon from '../FilterIcon';

describe('FilterIcon Component', () => {
  it('should render the SVG element with default dimensions', () => {
    render(<FilterIcon />);
    const svgElement = screen.getByTestId('plp-icon-filter');
    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute('width', '18px');
    expect(svgElement).toHaveAttribute('height', '15px');
  });

  it('should render the SVG element with custom dimensions', () => {
    render(<FilterIcon width='24px' height='20px' />);
    const svgElement = screen.getByTestId('plp-icon-filter');

    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveAttribute('width', '24px');
    expect(svgElement).toHaveAttribute('height', '20px');
  });

  it('should have the correct class names', () => {
    render(<FilterIcon />);
    const svgElement = screen.getByTestId('plp-icon-filter');

    expect(svgElement).toHaveClass('plp_custom-icons plp_filter-icon');
  });

  it('should have the correct title', () => {
    render(<FilterIcon />);
    const titleElement = screen.getByText('Filter icon');

    expect(titleElement).toBeInTheDocument();
  });
});
