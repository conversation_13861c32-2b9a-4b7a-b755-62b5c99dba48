import { Fragment, ReactNode } from 'react';
import { Link } from '@ecom-next/core/fabric/link';

export interface ProductBreadcrumbDataProps {
  name?: string;
  order?: number;
  url?: string;
}

export type MountBreadcrumbsType = {
  breadcrumbs: ProductBreadcrumbDataProps[];
  lastClickable?: boolean;
};

const isLastElement = (index: number, elements: ProductBreadcrumbDataProps[]): boolean => index === elements.length - 1;

export const MountBreadcrumbsRewrite = ({ breadcrumbs, lastClickable }: MountBreadcrumbsType): ReactNode[] => {
  return breadcrumbs
    .filter(({ name }) => !!name)
    .map(({ name, url }, index, elements) => {
      const isLast = isLastElement(index, elements);
      const isClickableLink = !!(url && (!isLast || lastClickable));

      return (
        <Fragment key={`${name}-${index}`}>
          {isClickableLink ? (
            <Link href={url} kind='inline'>
              {name}
            </Link>
          ) : (
            <span className='plp__breadcrumb--last-element'>{name}</span>
          )}
          {!isLast && (
            <span className='plp_breadcrumb-separator' aria-hidden='true'>
              &nbsp;/&nbsp;
            </span>
          )}
        </Fragment>
      );
    });
};
