import { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>rid<PERSON>eader, usePLPState, CategoryBannerContainer, SearchTextHeader, CategoryPageErrorMessage } from '@ecom-next/plp';
// @ts-ignore
import { useEBBContainerMargins } from '@ecom-next/plp';
import { customAttribute } from '../../../../core/src/components/reporting';
import { ProductListProps } from './types';

const ProductList = ({ categoryName, subcategoryName }: ProductListProps) => {
  const { searchText, pageType, selectedNodes } = usePLPState();

  useEffect(() => {
    customAttribute('isHUIRedesign', 'true');
  }, []);

  const isEBBContainerMarginsEnabled = useEBBContainerMargins();

  return (
    <>
      {pageType === 'category' && (
        <div
          className={`${isEBBContainerMarginsEnabled ? 'plp_category-banner--container--ff' : 'plp_category-banner--container'}`}
          data-testid='plp-category-banner-container'
        >
          <CategoryBannerContainer categoryName={categoryName} subcategoryName={subcategoryName} selectedNodes={selectedNodes} />
        </div>
      )}
      {/* product-grid class name is needed for EBB. don't remove it */}
      <div className='plp_product-list product-grid' data-testid='plp-product-list'>
        <div className='plp_product-list--container'>
          <GridHeader />
          {pageType === 'category' && <CategoryPageErrorMessage />}
          {pageType === 'search' && <SearchTextHeader searchText={searchText} />}
          <Grid />
        </div>
      </div>
    </>
  );
};

export { ProductList };
