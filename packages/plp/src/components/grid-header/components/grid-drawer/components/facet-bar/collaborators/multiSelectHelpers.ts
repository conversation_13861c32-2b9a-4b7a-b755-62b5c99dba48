import { SHOW_MORE_TOGGLE, SHOW_LESS_TOGGLE } from '../translations';
import { CheckBoxOption } from '../types';

export const getLocalizedText = (showMoreCount: number = 0, localize: (key: string, values?: Record<string, string | number>) => string) => {
  return {
    showMoreText: localize(SHOW_MORE_TOGGLE, { count: showMoreCount }),
    showLessText: localize(SHOW_LESS_TOGGLE),
  };
};

export const getCheckBoxOptions = (mainOptionsCount: number, options: CheckBoxOption[]) => {
  const main = options?.slice(0, Math.min(options.length, mainOptionsCount));
  const showMore = options?.slice(mainOptionsCount);
  return {
    mainOptions: main,
    showMoreOptions: showMore,
    showMoreCount: showMore.length,
  };
};
