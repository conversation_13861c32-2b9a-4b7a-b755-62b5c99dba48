import { ChangeEvent } from 'react';
import { Checkbox } from '@ecom-next/core/components/fabric/checkbox';
import { useFacets } from '../../../../../../../../../api/ps-data-provider/hooks';
import { FacetBarData } from '../../../../../../../../../api/ps-data-provider/hooks/helpers/types';
import { Option } from '../../../types';
import isOptionApplied from '../../../collaborators/isOptionApplied';
import { CheckBoxSelectorProps, CheckBoxChangeEvent } from './types';

export function CheckboxSelector({ options, facetName }: CheckBoxSelectorProps & { facetName: string }) {
  const facetBarData = useFacets();
  const { onFacetChangeHandler, appliedFacetsQuery } = facetBarData;
  const handlerFn = onFacetChangeHandler(facetBarData as FacetBarData);

  const handleCheckboxChange = ({ facetName, id, name, applied }: CheckBoxChangeEvent) => {
    handlerFn({ facetName, id, name, applied });
  };

  const getAppliedValue = (option: Option) => (appliedFacetsQuery ? isOptionApplied(option, appliedFacetsQuery, facetName) : false);

  return (
    <>
      {options?.map((option, index) => {
        return (
          <Checkbox
            key={index}
            label={option.label}
            onChange={e => {
              handleCheckboxChange({ facetName, id: option.id, name: option.label, applied: (e as ChangeEvent<HTMLInputElement>).target.checked });
            }}
            disabled={option.disabled}
            checked={getAppliedValue(option)}
          />
        );
      })}
    </>
  );
}
