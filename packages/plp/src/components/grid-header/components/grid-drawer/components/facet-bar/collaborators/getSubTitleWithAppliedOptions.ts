import { FacetsAdapterResult } from '../../../../../../../types';
import {
  RATING_FACET_ALL_REVIEWED,
  RATING_FACET_STARS_SUBTITLE,
  RATING_FACET_AND_UP,
} from '../components/facet-selectors/star-rating-selector/localization-tokens';

type Localize = (key: string) => string;

export const getSubTitleWithAppliedOptions = (facetName: string, facetsObj: FacetsAdapterResult, localize: Localize) => {
  const appliedFacetsListPerFacetName = getDisplayNamesFromAppliedFacet(facetsObj, facetName);

  if (facetName === 'price') {
    const priceLabel = facetsObj?.appliedFacets?.[facetName]?.[0]?.tagDisplayLabel;
    return priceLabel || '';
  }

  if (facetName === 'reviewScore' && appliedFacetsListPerFacetName && localize) {
    return processReviewScore(appliedFacetsListPerFacetName, localize);
  }

  return appliedFacetsListPerFacetName;
};

const extractRatingFromString = (facets: string) => facets.match(/(\d+)/);

const getDisplayNamesFromAppliedFacet = (facetsObj: FacetsAdapterResult, facetName: string) => {
  return facetsObj?.appliedFacets?.[facetName]?.map(appliedFacet => appliedFacet.localeName || appliedFacet.tagDisplayLabel).join(', ') || '';
};

const processReviewScore = (appliedFacetsListPerFacetName: string, localize: Localize) => {
  const allRatedProducts = localize(RATING_FACET_ALL_REVIEWED);
  const andUpText = localize(RATING_FACET_AND_UP);

  if (appliedFacetsListPerFacetName === allRatedProducts || appliedFacetsListPerFacetName === `1 ${andUpText}` || appliedFacetsListPerFacetName === '1 & up') {
    return allRatedProducts;
  }
  const ratingMatch = extractRatingFromString(appliedFacetsListPerFacetName);
  if (ratingMatch && ratingMatch[1] !== '1') {
    return `${ratingMatch[1]}+ ${localize(RATING_FACET_STARS_SUBTITLE)}`;
  }

  return appliedFacetsListPerFacetName;
};
