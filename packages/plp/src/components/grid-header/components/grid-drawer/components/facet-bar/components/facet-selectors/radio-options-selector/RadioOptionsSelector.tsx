import { Radio } from '@ecom-next/core/components/fabric/radio';
import { useFacets } from '../../../../../../../../../api/ps-data-provider/hooks';
import { Option, SimpleFacet } from '../../../types';
import { FacetBarData } from '../../../../../../../../../api/ps-data-provider/hooks/helpers/types';
import isOptionApplied from '../../../collaborators/isOptionApplied';
import { SORT_FACET } from '../../../constants';
import { useSortBy } from '../../../../../../../../../api/ps-data-provider/hooks/use-sort-by';
import { Direction, Field, SortByValue } from '../../../../../../../sort-by/types';
import { RadioOptionsChangeEvent } from './types';

export function RadioOptionsSelector({ options, facetName }: SimpleFacet & { facetName: string }) {
  const facetBarData = useFacets();
  const { sortByOnChange } = useSortBy();
  const { appliedFacetsQuery, onFacetChangeHandler } = facetBarData;
  const handlerFn = onFacetChangeHandler(facetBarData as unknown as FacetBarData);
  const isSortFacet = facetName === SORT_FACET;

  const facetRadioOptions = options?.map(option => ({
    label: option.name,
    direction: option.direction,
    value: option.id,
    checked: !!option.applied,
    disabled: false,
    inputProps: { value: option.name },
    id: option.id,
    name: option.name,
    isActive: option.isActive,
  }));

  const getSortField = (id: string): Field => {
    switch (id) {
      case 'bestsellers':
        return Field.BestSellers;
      case 'new':
        return Field.New;
      case 'reviewscore':
        return Field.ReviewScore;
      case 'featured':
        return Field.Featured;
      default:
        return Field.Price;
    }
  };

  const handleRadioChange = ({ facetName, id, name, applied, direction }: RadioOptionsChangeEvent) => {
    if (facetName === SORT_FACET) {
      const optionSelected = id;
      const sortField = getSortField(optionSelected.trim().toLowerCase());
      const sortDirection = direction as Direction;
      const sortValue: SortByValue = {
        field: sortField,
        direction: sortDirection,
        label: name,
        value: id,
        isActive: true,
      };
      sortByOnChange(sortValue);
    } else {
      handlerFn({ facetName, id, name, applied });
    }
  };

  const getAppliedSortValue = (option: Option) => option.checked === true;

  const getAppliedValue = (option: Option) => (appliedFacetsQuery ? isOptionApplied(option, appliedFacetsQuery, facetName) : false);

  return (
    <div className='plp_grid-drawer__radio-options'>
      {facetRadioOptions.map(option => (
        <Radio
          emphasisLabel={option.label}
          id={option.id}
          value={option.label}
          checked={isSortFacet ? getAppliedSortValue(option) : getAppliedValue(option)}
          className='plp_grid_drawer_radio-option'
          key={option.id}
          onChange={e => handleRadioChange({ facetName, id: option.id, name: option.label as string, applied: e.target.checked, direction: option.direction })}
          isLarge
          name={`${facetName}-facet`}
        />
      ))}
    </div>
  );
}
