//@ts-nocheck
import { renderHook } from '@testing-library/react-hooks';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useSortByFacet } from '../useSortByFacet';
import { useSortByBestSellers, useSortByNew } from '../../../../../../../../experiments/hooks';
import { Direction } from '../../types';
import { SORT_BY, SORT_BY_NEW, SORT_BY_BEST_SELLERS } from '../../translations';
import { usePLPState } from '../../../../../../../../data';

jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: jest.fn(),
}));
jest.mock('../../../../../../../../experiments/hooks', () => ({
  useSortByBestSellers: jest.fn(),
  useSortByNew: jest.fn(),
}));
jest.mock('../../../../../../../../data');
const mockUseAppState = usePLPState as jest.Mock;
mockUseAppState.mockReturnValue({ brand: 'br' });

describe('useSortByFacet', () => {
  it('should return all options when drawer is open', () => {
    useLocalize.mockReturnValue({ localize: jest.fn().mockImplementation(key => key) });
    useSortByBestSellers.mockReturnValue(true);
    useSortByNew.mockReturnValue(true);
    const { result } = renderHook(() => useSortByFacet({ sortByField: '', sortByDir: '' }, true));
    expect(result.current).toHaveLength(1);
    expect(result.current[0].options).toHaveLength(6);

    expect(result.current[0].options[0].applied).toBe(true);
  });

  it('should return all options when brand is gap', () => {
    useLocalize.mockReturnValue({ localize: jest.fn().mockImplementation(key => key) });
    useSortByBestSellers.mockReturnValue(true);
    useSortByNew.mockReturnValue(true);
    mockUseAppState.mockReturnValue({ brand: 'gap' });
    const { result } = renderHook(() => useSortByFacet({ sortByField: '', sortByDir: '' }, true));
    expect(result.current).toHaveLength(1);
    expect(result.current[0].options).toHaveLength(6);
    expect(result.current[0].options[0].applied).toBe(true);
  });

  it('should apply the correct sortValue for price ascending', () => {
    useLocalize.mockReturnValue({ localize: jest.fn().mockImplementation(key => key) });
    useSortByBestSellers.mockReturnValue(true);
    useSortByNew.mockReturnValue(true);
    const { result } = renderHook(() => useSortByFacet({ sortByField: 'price', sortByDir: Direction.Ascending }));
    expect(result.current[0].options[3].applied).toBe(true);
  });

  it('should not return as an option if isActive is false', () => {
    useLocalize.mockReturnValue({ localize: jest.fn().mockImplementation(key => key) });
    useSortByBestSellers.mockReturnValue(false);
    useSortByNew.mockReturnValue(false);
    const { result } = renderHook(() => useSortByFacet());
    const hasSortByNewOption = result.current[0].options.some(option => option.id === 'new');
    const hasBestsellersOption = result.current[0].options.some(option => option.id === 'bestsellers');

    expect(hasSortByNewOption).toBe(false);
    expect(hasBestsellersOption).toBe(false);
  });

  it('should return as an option if isActive is true', () => {
    useLocalize.mockReturnValue({ localize: jest.fn().mockImplementation(key => key) });
    useSortByBestSellers.mockReturnValue(true);
    useSortByNew.mockReturnValue(true);
    const { result } = renderHook(() => useSortByFacet());
    const hasSortByNewOption = result.current[0].options.some(option => option.id === 'new');
    const hasBestsellersOption = result.current[0].options.some(option => option.id === 'bestsellers');

    expect(hasSortByNewOption).toBe(true);
    expect(hasBestsellersOption).toBe(true);
  });

  it('should localize sort options correctly', () => {
    const localizeMock = jest.fn().mockImplementation(key => key);
    mockUseAppState.mockReturnValue({ brand: 'br' });

    useLocalize.mockReturnValue({ localize: localizeMock });

    renderHook(() => useSortByFacet());

    expect(localizeMock).toHaveBeenCalledWith(SORT_BY);
    expect(localizeMock).toHaveBeenCalledWith(SORT_BY_NEW);
    expect(localizeMock).toHaveBeenCalledWith(SORT_BY_BEST_SELLERS);
  });
});
