import { Facets as LegacyFacets } from '@ecom-next/plp-ui/legacy/facet-bar';
import { SortByValue } from '../../sort-by/types';
import { SortByAdapterResult } from '../../../../types/plp-data';
import { Facets } from './components/facet-bar/types';

export interface GridDrawerProps {
  appliedFacetsCount: number;
  appliedSortOptions: SortByAdapterResult;
  clearAll: () => void;
  drawerContent?: React.ReactNode;
  drawerState?: 'open' | 'close';
  facets: Facets[] | LegacyFacets[];
  isOnlyOneDepartment?: boolean;
  itemCount?: number;
  selectedFilters: string[];
  sortFacetValue: SortByValue;
  toggleDrawerState: () => void;
}
