@import '../components/facet-bar/components/facet-selectors/radio-options-selector/styles.css';
@import '../components/facet-bar/components/facet-selectors/multi-facet-selector/styles.css';
@import '../components/facet-bar/components/facet-selectors/star-rating-selector/styles.css';
@import '../components/facet-bar/components/facet-selectors/bopis-selector/styles.css';
@import '../components/facet-bar/components/facet-selectors/color-selector/styles.css';
@import '../components/facet-bar/components/facet-selectors/range-slider-selector/styles.css';

.plp_grid-drawer {
  .fds_drawer {
    overflow: visible;
    .fds_drawer__container {
      height: 100%;
      overflow: scroll;
      scrollbar-width: none;

      .fds_drawer__header-container {
        top: 0;
        z-index: 15;
        position: sticky;
        background-color: theme('colors.color-background-default--white');
      }
    }
    .fds_drawer__button-content {
      bottom: 0;
      position: sticky;
      height: fit-content;
      background-color: theme('colors.color-background-default--white');
    }
  }
  .fds_switch__span {
    z-index: 1;
  }
  .plp_grid-drawer__drawer {
    height: 100dvh;
    width: min-content;
    position: fixed;
    bottom: 0;
    z-index: 900;
    scrollbar-width: none;
    -ms-overflow-style: none;
    justify-content: space-between;
    max-height: calc(100dvh - theme('spacing.global-spacing-600'));
    top: theme('spacing.global-spacing-600');
    border-radius: theme('borderRadius.component-drawer-mobile-border-radius') theme('borderRadius.component-drawer-mobile-border-radius') 0 0;

    @media (min-width: theme('screens.lg')) {
      max-height: unset;
      top: unset;
      border-radius: unset;
    }
  }

  .plp_grid-drawer__footer {
    width: 100%;
    border-top: theme('borderWidth.global-border-width-1') solid theme('colors.color-border-subtle');
    padding: theme('spacing.global-spacing-200');
    gap: theme('padding.padding-stack-medium');

    .plp_grid-drawer-clear-filters__cta,
    .plp_grid-drawer-results__cta {
      font-family: theme('fontFamily.font-family-base');
      font-size: theme('fontSize.font-size-b-m-font-size');
      font-weight: theme('fontWeight.font-weight-regular-font-weight');
      white-space: nowrap;
      display: flex;
      justify-content: center;
      align-items: center;
      bottom: 0;
      position: sticky;
      z-index: 10;
    }
  }

  .plp_grid-drawer__content {
    width: 100vw;
    padding-left: theme('padding.padding-stack-medium');
    padding-right: theme('padding.padding-stack-medium');

    @media (min-width: theme('screens.lg')) {
      width: 25rem;
    }
  }
}

.plp_grid-drawer {
  overflow: none;

  .fds_panel__toggle {
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    background-color: rgba(var(--color-black-and-white-white));
    z-index: 20;
  }

  .fds_drawer__content {
    overflow-y: auto;
    scrollbar-width: none;
  }
}
