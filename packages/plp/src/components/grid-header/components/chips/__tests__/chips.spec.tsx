import { render, fireEvent, screen, cleanup } from '@testing-library/react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useBopis, usePLPState, usePsDataContext } from '@ecom-next/plp';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { ChipsAndClearAll } from '../chips';
import { BOPIS_CHIP_NAME, RATING_FACET_AND_UP, RATING_FACET_STARS_SUBTITLE, RATING_FACET_ALL_REVIEWED } from '../constants';
import { customFacets, ratingCustomFacets } from '../fixtures/mockFacets';

jest.mock('@ecom-next/sitewide/localization-provider');
jest.mock('@ecom-next/plp');

const mockClearAll = jest.fn();
const mockOnClearOneOption = jest.fn();
const mockSelectedStoreOnChange = jest.fn();
const setPageIndex = jest.fn();
const mockTranslation = (key: string) => {
  switch (key) {
    case 'facet_bar.clear_all_button':
      return 'Clear all';
    case BOPIS_CHIP_NAME:
      return 'Available In-Store';
    case RATING_FACET_AND_UP:
      return '& Up';
    case RATING_FACET_STARS_SUBTITLE:
      return 'Stars';
    case RATING_FACET_ALL_REVIEWED:
      return 'All Rated Products';
    default:
      return key;
  }
};

const renderWithBreakpoint = (ui: React.ReactElement, isMobile = false) => {
  return render(
    <BreakpointContext.Provider
      value={{
        smallerThan: _bp => !!isMobile,
        greaterOrEqualTo: bp => typeof bp === 'number' && !isMobile && bp === XLARGE,
        minWidth: bp => typeof bp === 'number' && bp >= 1024,
        maxWidth: bp => typeof bp === 'number' && bp <= 1920,
        size: isMobile ? 'small' : 'large',
        orientation: 'landscape',
        media: '(min-width: 1024px)',
      }}
    >
      {ui}
    </BreakpointContext.Provider>
  );
};

describe('ChipsAndClearAll', () => {
  const mockUseLocalize = useLocalize as jest.Mock;
  const mockUseBopis = useBopis as jest.Mock;
  const mockUsePsDataContext = usePsDataContext as jest.Mock;
  const mockuUsePLPState = usePLPState as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    mockUseBopis.mockReturnValue({
      appliedStoreSelected: null,
      selectedStoreOnChange: mockSelectedStoreOnChange,
    });
    mockUsePsDataContext.mockReturnValue({
      state: {
        isLoading: false,
        data: {},
      },
      setPageIndex,
    });
    mockuUsePLPState.mockReturnValue({
      hasPreselectedFacets: true,
    });
    window.location.hash = '#param';
  });

  afterEach(cleanup);

  describe('Rendering', () => {
    it('renders loading placeholder when isLoading is true', () => {
      mockUsePsDataContext.mockReturnValue({
        state: {
          isLoading: true,
        },
      });
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      expect(screen.getByTestId('plp-chips-and-clear-all-placeholder')).toBeInTheDocument();
    });
    it('returns null if no facetOptions and no store selected', () => {
      const { container } = renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={[]} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      expect(container).toBeEmptyDOMElement();
    });

    it('renders chips for each facet option', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      expect(screen.getByText('Tops: XS')).toBeInTheDocument();
      expect(screen.getByText('$10–$120')).toBeInTheDocument();
    });

    it('renders nothing if facetOptions is undefined and no store selected', () => {
      const { container } = renderWithBreakpoint(<ChipsAndClearAll clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />);
      expect(container).toBeEmptyDOMElement();
    });

    it('renders store chip if appliedStoreSelected is set', () => {
      mockUseBopis.mockReturnValue({
        appliedStoreSelected: '1234',
        selectedStoreOnChange: mockSelectedStoreOnChange,
      });
      renderWithBreakpoint(<ChipsAndClearAll facetOptions={[]} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />);
      expect(screen.getByText('Available In-Store')).toBeInTheDocument();
    });

    it('renders store chip first if both store and facets are present', () => {
      mockUseBopis.mockReturnValue({
        appliedStoreSelected: '1234',
        selectedStoreOnChange: mockSelectedStoreOnChange,
      });
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      const chips = screen.getAllByRole('button');
      expect(chips[0]).toHaveTextContent('Available In-Store');
    });

    it('renders chips in correct order: store chip first, then facets', () => {
      mockUseBopis.mockReturnValue({
        appliedStoreSelected: '1234',
        selectedStoreOnChange: mockSelectedStoreOnChange,
      });
      const facetOptions = [
        { id: '1', name: 'Color: Red', facetName: 'color', tagDisplayLabel: 'Color: Red', applied: true },
        { id: '2', name: 'Size: M', facetName: 'size', tagDisplayLabel: 'Size: M', applied: true },
        { id: '3', name: 'Fit: Slim', facetName: 'fit', tagDisplayLabel: 'Fit: Slim', applied: true },
      ];
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={facetOptions} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      const chips = screen.getAllByRole('button');
      expect(chips[0]).toHaveTextContent('Available In-Store');
      expect(chips[1]).toHaveTextContent('Color: Red');
      expect(chips[2]).toHaveTextContent('Size: M');
      expect(chips[3]).toHaveTextContent('Fit: Slim');
    });

    it('renders only the store chip if facetOptions is empty and store is selected', () => {
      mockUseBopis.mockReturnValue({
        appliedStoreSelected: '1234',
        selectedStoreOnChange: mockSelectedStoreOnChange,
      });
      renderWithBreakpoint(<ChipsAndClearAll facetOptions={[]} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />);
      const chips = screen.getAllByRole('button');
      expect(chips).toHaveLength(1);
      expect(chips[0]).toHaveTextContent('Available In-Store');
    });

    it('displays the rating chip for "4 Stars & Up" when the corresponding rating option is selected', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll
          facetOptions={customFacets}
          clearAll={mockClearAll}
          onClearOneOption={mockOnClearOneOption}
          drawerState="close"
        />
      );
      expect(screen.getByText('4 Stars & Up')).toBeInTheDocument();
    });

    it('displays the rating chip for "All Rated Products" when that option is selected', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll
          facetOptions={ratingCustomFacets}
          clearAll={mockClearAll}
          onClearOneOption={mockOnClearOneOption}
          drawerState="close"
        />
      );
      expect(screen.getByText('All Rated Products')).toBeInTheDocument();
    });
  });

  describe('Button visibility', () => {
    it('renders "Clear all" button when more than one facet is selected', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      expect(screen.getByLabelText('Clear all selected filters')).toBeInTheDocument();
    });

    it('does not render "Clear all" button when less than two facets', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={[customFacets[0]]} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      expect(screen.queryByLabelText('Clear all selected filters')).not.toBeInTheDocument();
    });

    it('has correct aria-label on the "Clear all" button', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      const clearAllBtn = screen.getByLabelText('Clear all selected filters');
      expect(clearAllBtn).toBeInTheDocument();
      expect(clearAllBtn).toHaveAttribute('aria-label', 'Clear all selected filters');
    });
    // it('removes store chip when clicked', () => {
    //   mockUseBopis.mockReturnValue({
    //     appliedStoreSelected: '1234',
    //     selectedStoreOnChange: mockSelectedStoreOnChange,
    //   });
    //   renderWithBreakpoint(<ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} />);
    //   fireEvent.click(screen.getByText('Available In-Store'));
    //   expect(mockSelectedStoreOnChange).toHaveBeenCalledWith(null);
    //   expect(mockOnClearOneOption).not.toHaveBeenCalled();
    // });

    // it('does not call selectedStoreOnChange if not present', () => {
    //   mockUseBopis.mockReturnValue({
    //     appliedStoreSelected: '1234',
    //     selectedStoreOnChange: undefined,
    //   });
    //   renderWithBreakpoint(<ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} />);
    //   fireEvent.click(screen.getByText('Available In-Store'));
    //   expect(mockOnClearOneOption).not.toHaveBeenCalled();
    // });
  });

  describe('Interactions', () => {
    mockUsePsDataContext.mockReturnValue({
      state: {
        isLoading: true,
      },
      setPageIndex,
    });
    it('calls clearAll when "Clear all" is clicked', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      fireEvent.click(screen.getByLabelText('Clear all selected filters'));
      expect(mockClearAll).toHaveBeenCalled();
    });

    it('calls onClearOneOption when a non-store chip is clicked', () => {
      renderWithBreakpoint(
        <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />
      );
      fireEvent.click(screen.getByText('Tops: XS'));
      expect(mockOnClearOneOption).toHaveBeenCalledWith({
        id: '1',
        name: 'Size',
        facetName: 'size',
      });
      expect(setPageIndex).toHaveBeenCalledWith(0);
    });
  });

  describe('GIVEN: the user has selected size and price filter options,', () => {
    describe('WHEN: the component renders,', () => {
      beforeEach(() => {
        renderWithBreakpoint(
          <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />,
          true
        );
      });

      test('THEN: displays a `Clear all` link', () => {
        const clearAllLink = screen.queryByText('Clear all');
        expect(clearAllLink).toBeInTheDocument();
      });

      it('THEN: renders a chip for each facet options selected.', () => {
        const chips = document.querySelectorAll('.plp_chip');
        expect(chips).toHaveLength(customFacets.length);
        expect(chips[0]).toHaveTextContent('Tops: XS');
        expect(chips[1]).toHaveTextContent('$10–$120');
      });
    });
    describe('WHEN: there are 2 chips, and the user clicks the X button', () => {
      beforeEach(() => {
        renderWithBreakpoint(
          <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />,
          false
        );
      });

      test('THEN: the callback that closes the chip is invoked.', () => {
        const chips = document.querySelectorAll('.fds_chips__remove-icon');
        fireEvent.click(chips[1]);
        expect(mockOnClearOneOption).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('GIVEN: Department is selected', () => {
    describe('WHEN: the component renders,', () => {
      beforeEach(() => {
        renderWithBreakpoint(
          <ChipsAndClearAll facetOptions={customFacets} clearAll={mockClearAll} onClearOneOption={mockOnClearOneOption} drawerState={'close'} />,
          true
        );
      });

      it('THEN: renders a chip for each facet options selected.', () => {
        const chips = document.querySelectorAll('.plp_chip');
        screen.debug();
        expect(chips).toHaveLength(customFacets.length);
        expect(chips[0]).toHaveTextContent('Tops: XS');
        expect(chips[1]).toHaveTextContent('$10–$120');
        expect(chips[2]).toHaveTextContent('Men');
      });

      it('THEN: the callback to remove all options when click on department should be called', () => {
        const chips = document.querySelectorAll('.fds_chips__remove-icon');
        fireEvent.click(chips[2]);
        expect(mockClearAll).toHaveBeenCalled();
      });
    });
  });
});
