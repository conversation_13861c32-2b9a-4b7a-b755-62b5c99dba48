import { CSSObject } from '@ecom-next/core/react-stitch';

export enum Direction {
  Ascending = 'asc',
  Descending = 'desc',
  Unset = 'unset',
}

export enum Field {
  BestSellers = 'bestsellers',
  Featured = 'featured',
  New = 'new',
  Price = 'price',
  ReviewScore = 'reviewScore',
}

export interface SortByValue {
  css?: CSSObject;
  direction: Direction;
  field: Field;
  isActive?: boolean;
  label?: string;
  selectWidth?: number;
  sortByDir?: string;
  sortByField?: string;
  value: string;
}

export type SortByChange = (option: SortByValue) => void;

export interface SortByProps {
  brand?: string;
  customPlaceholder?: string;
  customValues?: SortByValue[];
  hasSeparateLabel?: boolean;
  htmlFor?: string;
  isSortByBestSellersEnabled?: boolean;
  isSortByNewEnabled?: boolean;
  isSortByRatingsEnabled: boolean;
  onChange?: SortByChange;
  position?: string;
  title?: string;
  value?: SortByValue;
}

export interface GetValueByFieldAndDirection {
  defaultDirection?: string;
  defaultField?: string;
  values: SortByValue[];
}
