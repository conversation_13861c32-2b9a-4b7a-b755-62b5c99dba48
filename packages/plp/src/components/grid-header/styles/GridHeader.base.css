@import '../components/grid-drawer/styles/GridDrawer.base.css';
@import '../components/model-size/ModelSize.base.css';
@import '../components/chips/styles/ChipsAndClearAll.base.css';

.plp_grid-header {
  width: 100%;
  max-width: var(--plp-max-content-width);
  white-space: nowrap;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: theme('spacing.utk-spacing-m');
  padding: theme('spacing.global-spacing-100') theme('spacing.global-spacing-0') theme('spacing.global-spacing-100') theme('spacing.global-spacing-0');
  font-family: theme('fontFamily.brand-base');
  font-size: theme('fontSize.font-size--1');
  background-color: theme('colors.global-color-black-and-white-white');
  top: 0;

  @media (max-width: 1038px) {
    z-index: 400;
  }

  @media (min-width: theme('screens.lg')) {
    padding: theme('spacing.global-spacing-200') theme('spacing.global-spacing-0') theme('spacing.global-spacing-200') theme('spacing.global-spacing-0');
  }
}

.plp_grid-header__sticky {
  position: sticky;
}

.plp_grid-header .plp_grid-header__filters-container,
.plp_grid-header .plp_grid-header__items-count,
.plp_grid-header .plp_chips-and-clear-all,
.plp_grid-header .plp_chips-and-clear-all-placeholder {
  padding-left: theme('spacing.global-spacing-200');
  @media (min-width: theme('screens.lg')) {
    padding-left: theme('spacing.global-spacing-0');
  }
}

.plp_grid-header__filters {
  width: 100%;
  display: flex;
  gap: theme('spacing.global-spacing-100');
}

.plp_grid-header__filters-container {
  width: auto;
  display: flex;
  align-items: center;
  gap: theme('spacing.global-spacing-100');

  @media (max-width: theme('screens.md')) {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    /* Hide scrollbar in Firefox */
    padding: theme('borderWidth.style-selector-size-border-border-width');
    padding-left: theme('spacing.global-spacing-0');
    padding-right: theme('spacing.global-spacing-100');
  }
}

.plp_grid-header__filters-container::-webkit-scrollbar {
  display: none;
}

.plp_grid-header__filters-button {
  width: fit-content;
  max-height: 2.375rem;
  padding: theme('spacing.utk-spacing-m');
  line-height: var(--font-size-14);
  white-space: nowrap;
  flex-shrink: 0;
}

.plp_grid-header__filters-button-text {
  display: flex;
  align-items: flex-end;
  gap: 0 theme('spacing.utk-spacing-s');
  line-height: inherit;
  font-family: theme('fontFamily.brand-base');
  font-weight: theme('fontWeight.font-weight-base-default');
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
  text-transform: capitalize;
}

.plp_grid-header__items-count {
  display: flex;
  align-items: center;
  justify-self: flex-start;
  width: fit-content;
  white-space: nowrap;
  color: theme('colors.color-type-subtle');
  letter-spacing: theme('letterSpacing.font-letter-spacing-base', 0);
  font-size: theme('fontSize.font-size--2');
  font-weight: theme('fontWeight.font-weight-base-heavier');
}

.plp_grid-header__on-scroll-down {
  padding: theme('spacing.global-spacing-100') 0;

  @media (min-width: theme('screens.lg')) {
    padding: theme('spacing.global-spacing-200') 0;
  }
}

.plp_filter-drawer-panel__subtitle {
  text-transform: none;
}

/* Placeholder */
.plp_grid-header__item-count-placeholder {
  width: 4rem;
  max-height: calc(theme('fontSize.font-size-2') * 1.1); /* 1.1 is to account for line-height: normal */
  overflow: hidden;
}

.plp_grid-header__filters-placeholder {
  overflow: hidden;
  min-width: calc(4rem + (theme('spacing.utk-spacing-m') * 2));
  height: calc(var(--font-size-14) + (theme('spacing.utk-spacing-m') * 2));
}

.plp_grid-header__filters-placeholder:nth-of-type(2) {
  min-width: calc(3.5rem + (theme('spacing.utk-spacing-m') * 2));
}

.plp_grid-header__filters-placeholder:nth-of-type(3),
.plp_grid-header__filters-placeholder:nth-of-type(4) {
  min-width: calc(3rem + (theme('spacing.utk-spacing-m') * 2));
}
