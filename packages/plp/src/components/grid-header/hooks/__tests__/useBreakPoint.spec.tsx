import { ReactNode } from 'react';
import { renderHook } from '@testing-library/react-hooks';
import { BreakpointContext, BreakpointProviderState } from '@ecom-next/core/breakpoint-provider';
import { useBreakpoint } from '../useBreakPoint';

jest.mock('@ecom-next/core/breakpoint-provider');

describe('useBreakpoint', () => {
  const TestWrapper = ({ children, value }: { children: ReactNode; value: BreakpointProviderState }) => (
    <BreakpointContext.Provider value={value}>{children}</BreakpointContext.Provider>
  );
  TestWrapper.displayName = 'TestWrapper';
  it('should return true when the breakpoint is small', () => {
    const providerValue: BreakpointProviderState = {
      smallerThan: () => true,
      greaterOrEqualTo: () => false,
      minWidth: () => true,
      maxWidth: () => true,
      size: 'small',
      orientation: 'landscape',
      media: '(max-width: 600px)',
    };
    global.matchMedia = jest.fn().mockImplementation(query => ({
      matches: query === '(max-width: 1023px)',
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));
    const { result } = renderHook(() => useBreakpoint(), {
      wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
    });

    expect(result.current.isMobile).toBe(true);
  });
  it('should return false when the breakpoint is not small', () => {
    const providerValue: BreakpointProviderState = {
      smallerThan: () => false,
      greaterOrEqualTo: () => false,
      minWidth: () => true,
      maxWidth: () => true,
      size: 'large',
      orientation: 'landscape',
      media: '(max-width: 600px)',
    };
    global.matchMedia = jest.fn().mockImplementation(query => ({
      matches: query === '(max-width: 1024px)',
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));
    const { result } = renderHook(() => useBreakpoint(), {
      wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
    });

    expect(result.current.isMobile).toBe(false);
  });
  describe('WHEN: the isTiny() method returned by this hook is executed on the client,', () => {
    it('THEN: should return false for isTiny(), because the `window` object is part of a browser API.', () => {
      const originalWindow = global.window;
      delete global.window;

      const providerValue: BreakpointProviderState = {
        smallerThan: () => false,
        greaterOrEqualTo: () => true,
        minWidth: () => true,
        maxWidth: () => true,
        size: 'medium',
        orientation: 'portrait',
        media: '(max-width: 768px)',
      };

      const { result } = renderHook(() => useBreakpoint(), {
        wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
      });

      expect(result.current.isTiny).toBe(false);

      global.window = originalWindow;
    });
  });
  describe('GIVEN: the isTiny() method returned by this hook is executed in the browser,', () => {
    describe('WHEN: window width is NOT tiny, i.e. it is >= 440px,', () => {
      it('THEN: returns `false`.', () => {
        global.matchMedia = jest.fn().mockImplementation(query => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        }));

        const providerValue: BreakpointProviderState = {
          smallerThan: () => false,
          greaterOrEqualTo: () => true,
          minWidth: () => true,
          maxWidth: () => true,
          size: 'medium',
          orientation: 'portrait',
          media: '(max-width: 768px)',
        };

        const { result } = renderHook(() => useBreakpoint(), {
          wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
        });

        expect(result.current.isTiny).toBe(false);
      });
    });
    describe('WHEN: window width is tiny, i.e. it is < 440px,', () => {
      it('should return `true`.', () => {
        global.matchMedia = jest.fn().mockImplementation(query => ({
          matches: query === '(max-width: 439px)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        }));

        const providerValue: BreakpointProviderState = {
          smallerThan: () => false,
          greaterOrEqualTo: () => true,
          minWidth: () => true,
          maxWidth: () => true,
          size: 'medium',
          orientation: 'portrait',
          media: '(max-width: 768px)',
        };

        const { result } = renderHook(() => useBreakpoint(), {
          wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
        });

        expect(result.current.isTiny).toBe(true);
      });
    });
  });
  describe('WHEN: sitewide header is mobile', () => {
    it('should return `true`.', () => {
      global.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 1038px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      const providerValue: BreakpointProviderState = {
        smallerThan: () => false,
        greaterOrEqualTo: () => true,
        minWidth: () => true,
        maxWidth: () => true,
        size: 'medium',
        orientation: 'portrait',
        media: '(max-width: 768px)',
      };

      const { result } = renderHook(() => useBreakpoint(), {
        wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
      });

      expect(result.current.isSitewideHeaderMobile).toBe(true);
    });
    it('should return `false`.', () => {
      global.matchMedia = jest.fn().mockImplementation(query => ({
        matches: query === '(max-width: 1039px)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      }));

      const providerValue: BreakpointProviderState = {
        smallerThan: () => false,
        greaterOrEqualTo: () => true,
        minWidth: () => true,
        maxWidth: () => true,
        size: 'medium',
        orientation: 'portrait',
        media: '(max-width: 768px)',
      };

      const { result } = renderHook(() => useBreakpoint(), {
        wrapper: (props: { children: ReactNode }) => <TestWrapper value={providerValue}>{props.children}</TestWrapper>,
      });

      expect(result.current.isSitewideHeaderMobile).toBe(false);
    });
  });
});
