import { useContext, useEffect, useState } from 'react';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';

export const useBreakpoint = () => {
  const { size } = useContext(BreakpointContext);
  const [breakpoints, setBreakpoints] = useState({ isMobile: false, isTiny: false, isSitewideHeaderMobile: false });

  const screenLessThan1024px = () => (typeof window !== 'undefined' ? window.matchMedia('(max-width: 1023px)').matches : false);
  const screenLessThan440Px = () => (typeof window !== 'undefined' ? window.matchMedia('(max-width: 439px)').matches : false);
  const screenLessThan1039Px = () => (typeof window !== 'undefined' ? window.matchMedia('(max-width: 1038px)').matches : false);

  useEffect(() => {
    setBreakpoints({
      isMobile: screenLessThan1024px(),
      isTiny: screenLessThan440Px(),
      isSitewideHeaderMobile: screenLessThan1039Px(),
    });
  }, [size]);

  return breakpoints;
};
