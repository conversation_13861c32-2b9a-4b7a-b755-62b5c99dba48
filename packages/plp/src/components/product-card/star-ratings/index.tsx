import { useMemo } from 'react';
import { StarRatings as StarRatingsFabricComponent } from '@ecom-next/core/fabric/star-ratings';
import { useProductCardConfig } from '@ecom-next/plp';
import { useProductInfo, useProductReview } from '../../../api';

const PDP_REVIEWS_WIDGET_HEADER = '#pdp-reviews-widget-header';

export type StarRatingsProps = {
  productId: string;
};

const formatReviewCount = (reviewCount: number): string => {
  if (reviewCount > 100000) {
    const thousands = Math.floor(reviewCount / 1000);
    return `${thousands}k`;
  } else if (reviewCount >= 1000) {
    return `${Math.round(reviewCount / 100) / 10}k`;
  }
  return reviewCount.toString();
};

export function StarRatings({ productId }: StarRatingsProps) {
  const { reviewScore, reviewCount } = useProductReview(productId);
  const { displayStarRatings } = useProductCardConfig();

  const {
    productName: { link },
  } = useProductInfo(productId);
  const formattedReviewCount = useMemo(() => formatReviewCount(reviewCount), [reviewCount]);
  if (displayStarRatings && reviewCount >= 10) {
    return (
      <a
        className='plp_product-card-reviews'
        data-testid='plp_product-card-reviews'
        href={`${link}${PDP_REVIEWS_WIDGET_HEADER}`}
        aria-label={`${reviewScore} stars out of 5, ${formattedReviewCount} ratings`}
      >
        <StarRatingsFabricComponent ratingValue={reviewScore} postText={`${formattedReviewCount}`} ratingSize='small' showRatingValue={false} />
      </a>
    );
  }
  return null;
}
