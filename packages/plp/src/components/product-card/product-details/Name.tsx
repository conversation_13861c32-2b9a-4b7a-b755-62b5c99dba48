import { usePLPState } from '../../../data/index';
import { brandIsBr, decodeHtmlEntities } from '../../../utils';
import { useProductInfo } from '../../../api';

type NameProps = {
  productId: string;
};

export const Name = ({ productId }: NameProps) => {
  const { brand } = usePLPState();
  const {
    productName: { name },
  } = useProductInfo(productId);
  const productName = brandIsBr(brand) ? name.toUpperCase() : name;
  const decodedProductName = decodeHtmlEntities(productName);

  return (
    <h3 data-testid='plp_product-name' className='plp_product-card-name'>
      {decodedProductName}
    </h3>
  );
};
