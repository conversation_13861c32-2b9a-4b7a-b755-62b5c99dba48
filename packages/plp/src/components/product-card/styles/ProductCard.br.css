@import './ProductCard.base.css';

.plp_placeholder-grid {
  display: grid;
  width: 100%;
  max-width: 1560px;
  grid-template-columns: repeat(2, 1fr);
  gap: theme('spacing.global-spacing-200');

  @media (min-width: theme('screens.lg')) {
    grid-template-columns: repeat(3, 1fr);
    gap: theme('spacing.global-spacing-250') theme('spacing.global-spacing-200');
  }

  @media (min-width: theme('screens.xl')) {
    grid-template-columns: repeat(3, 1fr);
    gap: theme('spacing.global-spacing-300') theme('spacing.global-spacing-200');
  }
}
