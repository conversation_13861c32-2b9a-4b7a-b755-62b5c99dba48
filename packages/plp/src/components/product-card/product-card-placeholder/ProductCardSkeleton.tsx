import { LoadingPlaceholder } from '@ecom-next/core/fabric/loading-placeholder';

type ProductCardSkeletonProps = {
  numberOfSwatches?: number;
  showColorSwatchesPlaceholder?: boolean;
  showStarRatingsPlaceholder?: boolean;
};

const ProductCardSkeletonSwatches = ({ numberOfSwatches }: { numberOfSwatches: number }) => {
  return (
    <div className='product-card-skeleton__swatches'>
      {[...Array(numberOfSwatches)].map((_, index) => (
        <div key={index} className='product-card-skeleton__swatch'>
          <LoadingPlaceholder />
        </div>
      ))}
    </div>
  );
};

const ProductCardSkeletonProductInfo = ({ showStarRatings = true }: { showStarRatings?: boolean }) => {
  return (
    <div className='product-card-skeleton__info'>
      <div className='product-card-skeleton__name'>
        <LoadingPlaceholder />
      </div>
      <div className='product-card-skeleton__price'>
        <LoadingPlaceholder />
      </div>
      <div className='product-card-skeleton__marketing-flag'>
        <LoadingPlaceholder />
      </div>
      {showStarRatings && (
        <div className='product-card-skeleton__ratings'>
          <LoadingPlaceholder />
        </div>
      )}
    </div>
  );
};
export const ProductCardSkeleton = ({
  showColorSwatchesPlaceholder = true,
  showStarRatingsPlaceholder = true,
  numberOfSwatches = 4,
}: ProductCardSkeletonProps) => {
  return (
    <div className='product-card-skeleton'>
      <div className='product-card-skeleton__image'>
        <LoadingPlaceholder />
      </div>
      {showColorSwatchesPlaceholder && <ProductCardSkeletonSwatches numberOfSwatches={numberOfSwatches} />}
      <ProductCardSkeletonProductInfo showStarRatings={showStarRatingsPlaceholder} />
    </div>
  );
};
