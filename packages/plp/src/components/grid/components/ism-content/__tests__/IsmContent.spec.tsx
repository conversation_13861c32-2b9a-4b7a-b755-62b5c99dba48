import { render, screen } from '@testing-library/react';
import { useIsm, useGridColumns, usePLPState } from '@ecom-next/plp';
import { MarketingContext } from '@ecom-next/marketing-ui/legacy-marketing-provider';
import { IsmContent } from '../IsmContent';
import { MarketingContextData } from '../types';

jest.mock('@ecom-next/plp', () => ({
  usePLPState: jest.fn(),
  useIsm: jest.fn(),
  useGridColumns: jest.fn(),
}));

const mockUseIsm = useIsm as jest.Mock;
const mockUseGridColumns = useGridColumns as jest.Mock;
const mockUsePLPState = usePLPState as jest.Mock;
const mockPageMarketing = jest.fn();

describe('IsmContent', () => {
  const mockProductsGridIds = [{ productId: '1' }, { productId: '2' }, { productId: '3' }, { productId: '4' }];
  const mockMarketingContextValue: MarketingContextData = {
    PageMarketing: mockPageMarketing,
    someOtherProperty: {},
  } as unknown as MarketingContextData;

  beforeEach(() => {
    mockUseIsm.mockReturnValue([
      { rowPosition: { desktop: 1, mobile: 1 }, content: 'Content 1' },
      { rowPosition: { desktop: 2, mobile: 2 }, content: 'Content 2' },
    ]);
    mockUseGridColumns.mockReturnValue(3);
    mockUsePLPState.mockReturnValue({
      cid: '5664',
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders multiple parsed content items correctly', () => {
    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    const ismContentElements = screen.getAllByTestId('ism-content');
    expect(ismContentElements).toHaveLength(2);
    expect(ismContentElements[0]).toHaveStyle({ gridRow: '1', gridColumnStart: '1' });
    expect(ismContentElements[1]).toHaveStyle({ gridRow: '2', gridColumnStart: '1' });
  });

  it('renders single parsed content correctly', () => {
    mockUseIsm.mockReturnValue([{ rowPosition: { desktop: 1 }, content: 'Content 1' }]);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    const ismContentElements = screen.getAllByTestId('ism-content');
    expect(ismContentElements).toHaveLength(1);
    expect(ismContentElements[0]).toHaveStyle({ gridRow: '1', gridColumnStart: '1' });
  });

  it('renders content correctly for mobile layout with gridColumnsCount of 2', () => {
    const mockIsmContent = [
      { rowPosition: { mobile: 1 }, content: 'Content 1' },
      { rowPosition: { mobile: 2 }, content: 'Content 2' },
    ];
    mockUseIsm.mockReturnValue(mockIsmContent);
    mockUseGridColumns.mockReturnValue(2);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    const ismContentElements = screen.getAllByTestId('ism-content');
    expect(ismContentElements).toHaveLength(2);
    expect(ismContentElements[0]).toHaveStyle({ gridRow: '1', gridColumnStart: '1' });
    expect(ismContentElements[1]).toHaveStyle({ gridRow: '2', gridColumnStart: '1' });
  });

  it('does not render content if rowPosition is greater than the total number of rows in the grid', () => {
    const mockIsmContent = [{ rowPosition: { desktop: 5 }, content: 'Content 1' }];
    const mockProductsGridIds = [{ productId: '1' }, { productId: '2' }];
    mockUseIsm.mockReturnValue(mockIsmContent);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    expect(screen.queryByTestId('ism-content')).not.toBeInTheDocument();
  });

  it('does not render ism content when useIsm returns null', () => {
    mockUseIsm.mockReturnValue(null);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent />
      </MarketingContext.Provider>
    );

    expect(screen.queryByTestId('ism-content')).not.toBeInTheDocument();
  });

  it('does not render ism content when parsedContent is empty', () => {
    mockUseIsm.mockReturnValue([]);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={[]} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    expect(screen.queryByTestId('ism-content')).not.toBeInTheDocument();
  });

  it('does not render ism content when there is no subcategoryId or cid', () => {
    mockUsePLPState.mockReturnValue({
      cid: undefined,
    });

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent />
      </MarketingContext.Provider>
    );

    expect(screen.queryByTestId('ism-content')).not.toBeInTheDocument();
  });

  it('renders doubled content correctly', () => {
    mockUseIsm.mockReturnValue([{ _meta: { schema: 'ism-double' }, rowPosition: { desktop: 1 }, content: 'Content 1' }]);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    const ismContentElements = screen.getAllByTestId('ism-content');
    expect(ismContentElements).toHaveLength(1);
    expect(ismContentElements[0]).toHaveStyle({ gridRow: '1', gridColumnStart: '1', gridColumnEnd: '3' });
  });

  it('renders no content when productsGridIds is undefined', () => {
    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent subcategoryId='123' />
      </MarketingContext.Provider>
    );

    expect(screen.queryByTestId('ism-content')).not.toBeInTheDocument();
  });

  it('renders no content when ismContent is empty', () => {
    mockUseIsm.mockReturnValue([]);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    expect(screen.queryByTestId('ism-content')).not.toBeInTheDocument();
  });

  it('renders content with default gridColumnEnd when gridColumnEnd is not specified', () => {
    mockUseIsm.mockReturnValue([{ rowPosition: { desktop: 1 }, content: 'Content 1' }]);

    render(
      <MarketingContext.Provider value={mockMarketingContextValue}>
        <IsmContent productsGridIds={mockProductsGridIds} subcategoryId='123' />
      </MarketingContext.Provider>
    );

    const ismContentElements = screen.getAllByTestId('ism-content');
    expect(ismContentElements).toHaveLength(1);
    expect(ismContentElements[0]).toHaveStyle({ gridRow: '1', gridColumnStart: '1', gridColumnEnd: '1' });
  });
});
