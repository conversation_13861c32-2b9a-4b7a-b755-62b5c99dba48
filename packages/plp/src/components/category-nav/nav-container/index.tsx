import { useContext } from 'react';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import classNames from 'classnames';


export const NavContainer = ({ children }: { children?: React.ReactNode }): JSX.Element => {
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const isDesktop = greaterOrEqualTo(LARGE);
  return (
    <ul
      className={classNames('plp__category-nav-container', {
        'plp__nav-container-mobile': !isDesktop,
      })}
      role='navigation'
    >
      {children}
    </ul>
  );
};
