// @ts-nocheck
import { useEffect, useRef, useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Transition, transitions } from '@ecom-next/core/legacy/motion';
import { CategoryNavProps, CategoryNavSectionProps as SectionData } from './types';
import { CategoryNavSection } from './category-nav-section';
import { getToggleButtonText, getShowToggleButton, getSectionLength, getCollapsedSecondSection, getTransitionCSS, getSections, getCollapsedFirstSection, transitionTimeout } from './collaborators/Collaborators';
import { NavContainer } from './nav-container';

export const CategoryNav = ({
  categoryNavData
}: CategoryNavProps): JSX.Element => {
  const { localize } = useLocalize();
  const contentRef = useRef<HTMLDivElement>(null);
  const [transitionStyles, setTransitionStyles] = useState({
    entering: {},
    entered: {},
    exiting: {},
    exited: {},
    unmounted: {},
  });
  const [expanded, setExpanded] = useState(false);
  const maxLength = 7;
  const allSections = categoryNavData;
  const [firstSection, secondSection] = allSections;
  const firstSectionLength = getSectionLength(firstSection);
  const secondSectionLength = getSectionLength(secondSection);
  const collapsedFirstSection = getCollapsedFirstSection(firstSectionLength, maxLength, firstSection);
  const collapsedSecondSection = getCollapsedSecondSection(firstSectionLength, maxLength, secondSectionLength, secondSection);
  const collapsedSections = [collapsedFirstSection, collapsedSecondSection].filter((section: Partial<SectionData>) => {
    if (section && section.children) return section.children.length > 0;
  });
  const showToggleButton = getShowToggleButton(allSections, firstSectionLength, secondSectionLength, maxLength);
  const toggleExpanded = () => setExpanded(!expanded);
  const sections = getSections(expanded, allSections, collapsedSections);
  const toggleButtonText = getToggleButtonText(expanded, localize);
  const transitionCSS = getTransitionCSS(expanded, transitions);
  const toggleButtonFragment = showToggleButton ?
    (<button className={'plp__toggle-button'} style={{ marginTop: expanded ? '1.125rem' : '0' }} onClick={toggleExpanded}>
      {toggleButtonText}
    </button>
    ) : null;

  const contentWrapperStyle = ({ transitionState }: { transitionState: string }) => ({
    overflow: 'hidden',
    transition: `height ${transitionCSS}, opacity ${transitionCSS}`,
    ...transitionStyles[transitionState as keyof typeof transitionStyles],
  });
  const isCollapsedAtFirstSection = Object.keys(collapsedSecondSection).length === 0;

  useEffect(() => {
    const contentHeight = contentRef?.current?.offsetHeight ?? 0;
    setTransitionStyles({
      entering: { height: contentHeight, opacity: 1 },
      entered: { height: contentHeight, opacity: 1 },
      exiting: { height: 0, opacity: 0 },
      exited: { height: 0, opacity: 0 },
      unmounted: {},
    });
  }, [expanded]);

  const getCategoryNav = (): JSX.Element | null => {
    return allSections.length === 0 ? null : (
      <Transition in={true} timeout={transitionTimeout}>
        {(transitionState: string) => (
          <div style={contentWrapperStyle({ transitionState })}>
            <div ref={contentRef} className='plp__content-style'>
              {sections.map((item: SectionData, index: number) => {
                let isFirstSection = false, isSecondSection = false, isLastSection = false;
                if (index === 0) isFirstSection = true;
                if (index === 1) isSecondSection = true;
                if (index + 1 === sections.length) isLastSection = true;
                return <CategoryNavSection key={item.header} isFirstSection={isFirstSection}
                  isSecondSection={isSecondSection} isLastSection={isLastSection}
                  toggleButtonFragment={toggleButtonFragment} expanded={expanded}
                  isCollapsedAtFirstSection={isCollapsedAtFirstSection} {...item} />
              })}
            </div>
          </div>
        )}
      </Transition>
    )
  }

  return (
    <NavContainer>{getCategoryNav()}</NavContainer>
  );
};
