.gap .plp__category-nav-header,
.at .plp__category-nav-header,
.br .plp__category-nav-header,
.on .plp__category-nav-header {
    padding-top: 0;
    padding-bottom: 0.375rem;
    padding-right: 1rem;
    text-transform: uppercase;
    font-size: 0.875rem;
}

.gap .plp__category-nav-header {
    color: theme('colors.bk');
    font-weight: 700;
    font-family: theme('fontFamily.font-family-base');
    line-height: 1.063rem;
}

.at .plp__category-nav-header {
    color: theme('colors.bk');
    font-weight: 500;
    font-family: theme('fontFamily.font-family-base');
    line-height: 0.963rem;
}

.br .plp__category-nav-header,
.on .plp__category-nav-header {
    color: theme('colors.bk');
    font-weight: 700;
    font-family: theme('fontFamily.font-family-base');
    line-height: 1.063rem;
}