import { render } from '@testing-library/react';
import CategoryBannerBottomContainer from '../CategoryBannerBottomContainer';
import CategoryBanner from '../CategoryBanner';
import { SelectedNodes } from '../../../../../category/src/components/legacy/components/category-page/types';

jest.mock('../CategoryBanner', () => {
  return jest.fn(() => <div>Mocked CategoryBanner</div>);
});

describe('CategoryBannerBottomContainer', () => {
  it('renders Bottom CategoryBanner with correct props', () => {
    const categoryName = 'Suits';
    const subcategoryName = 'Suits & Blazers';
    const selectedNodes: SelectedNodes = [{ type: 'nodeType1', name: 'value1' }];

    const { getByText } = render(<CategoryBannerBottomContainer categoryName={categoryName} subcategoryName={subcategoryName} selectedNodes={selectedNodes} />);

    expect(CategoryBanner).toHaveBeenCalledWith(
      {
        categoryName,
        subcategoryName,
        marketingType: 'category-banner',
        position: 'bottom',
        selectedNodes,
        marketingData: {
          PageMarketing: expect.any(Function),
        },
      },
      {}
    );

    expect(getByText('Mocked CategoryBanner')).toBeInTheDocument();
  });
});
