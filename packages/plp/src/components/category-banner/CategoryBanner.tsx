import { withBreakpointData } from '@ecom-next/core/breakpoint-provider';
import { withMarketingData } from '@ecom-next/marketing-ui/legacy-marketing-provider';
import { withBrandData } from '@ecom-next/sitewide/brand-info-provider';
import { getCategoryID, getDivisionID } from '../category-banner/collaborators/categoryBannerUtils';
import { FallbackHeader } from './FallbackHeader';
import { CategoryBannerProps } from './types';

export const CategoryBanner = ({
  categoryName,
  subcategoryName,
  position,
  marketingType,
  selectedNodes,
  marketingData: { PageMarketing, contentData },
}: CategoryBannerProps): JSX.Element => {
  const did = getDivisionID(selectedNodes) ?? '';
  const cid = getCategoryID(selectedNodes) ?? '';
  const hasEbbKey = Object.keys(contentData ?? {}).some(key => key.includes('/ebb'));
  const hasHiddenTitle = hasEbbKey && (categoryName || subcategoryName);
  const shouldHideTitle = !!hasHiddenTitle;
  const fallbackHeader = position === 'bottom' ? <></> : <FallbackHeader shouldHideTitle={shouldHideTitle} bannerName={categoryName} />;

  return <PageMarketing did={did} cid={cid} defaultContent={fallbackHeader} position={position} marketingType={marketingType} />;
};

export { CategoryBanner as RawCategoryBanner };

export default withBreakpointData<CategoryBannerProps>(withBrandData(withMarketingData(CategoryBanner as React.ComponentType<CategoryBannerProps>)));
