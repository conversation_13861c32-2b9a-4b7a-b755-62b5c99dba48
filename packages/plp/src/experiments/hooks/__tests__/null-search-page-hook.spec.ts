import { renderHook } from 'test-utils';
import { usePsDataContext } from '@ecom-next/plp';
import { usePLPState } from '../../../data';
import { useFeature } from '../../features';
import { useFacets } from '../../../api/ps-data-provider/hooks';
import { useIsNullSearchPage, useIsNewArrivalsFeatureActive } from '../null-search-page-hook';

jest.mock('../../../data');
jest.mock('../../../api/ps-data-provider/hooks');
jest.mock('../../features');
jest.mock('@ecom-next/plp', () => ({
  usePsDataContext: jest.fn(),
}));

describe('useIsNullSearchPage', () => {
  const mockUsePLPState = usePLPState as jest.Mock;
  const mockUseFacets = useFacets as jest.Mock;
  const mockUsePsDataContext = usePsDataContext as jest.Mock;

  beforeEach(() => {
    mockUsePLPState.mockReturnValue({ pageType: 'search' });
    mockUseFacets.mockReturnValue({ totalItemCount: 0 });
    mockUsePsDataContext.mockReturnValue({
      state: { isLoading: false, isSuccess: true },
    });
  });

  it('should return true when pageType is search and totalItemCount is 0', () => {
    const { result } = renderHook(() => useIsNullSearchPage());
    expect(result.current).toBe(true);
  });

  it('should return false when pageType is not search', () => {
    mockUsePLPState.mockReturnValue({ pageType: 'category' });
    const { result } = renderHook(() => useIsNullSearchPage());
    expect(result.current).toBe(false);
  });

  it('should return false when totalItemCount is greater than 0', () => {
    mockUseFacets.mockReturnValue({ totalItemCount: 1 });
    const { result } = renderHook(() => useIsNullSearchPage());
    expect(result.current).toBe(false);
  });

  it('should return false when isLoading is true', () => {
    mockUsePsDataContext.mockReturnValue({
      state: { isLoading: true, isSuccess: false },
    });
    const { result } = renderHook(() => useIsNullSearchPage());
    expect(result.current).toBe(false);
  });
});
describe('useIsNewArrivalsFeatureActive', () => {
  const mockUseFeature = useFeature as jest.Mock;

  beforeEach(() => {
    mockUseFeature.mockReturnValue(true);
  });

  it('should return true when the feature is active', () => {
    const { result } = renderHook(() => useIsNewArrivalsFeatureActive());
    expect(result.current).toBe(true);
  });

  it('should return false when the feature is not active', () => {
    mockUseFeature.mockReturnValue(false);
    const { result } = renderHook(() => useIsNewArrivalsFeatureActive());
    expect(result.current).toBe(false);
  });
});
