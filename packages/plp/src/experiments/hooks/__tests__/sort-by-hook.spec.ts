import { renderHook } from 'test-utils';
import { usePLPState } from '../../../data';
import { useSortByBestSellers, useSortByNew } from '../sort-by-hook';

jest.mock('../../../data');

describe('useSortByBestSellers', () => {
  it('should return true if correct values are informed', () => {
    (usePLPState as jest.Mock).mockReturnValue({ brand: 'gap', abSeg: { gap231: 'a' }, enabledFeatures: { 'plp-sort-by': true } });
    const { result } = renderHook(() => useSortByBestSellers());
    expect(result.current).toBe(true);
  });
});

describe('useSortByNew', () => {
  it('should return true if correct values are informed', () => {
    (usePLPState as jest.Mock).mockReturnValue({ brand: 'gap', abSeg: { gap232: 'a' }, enabledFeatures: { 'plp-sort-by': true } });
    const { result } = renderHook(() => useSortByNew());
    expect(result.current).toBe(true);
  });
});
