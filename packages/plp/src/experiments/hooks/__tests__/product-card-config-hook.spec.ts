import { renderHook } from 'test-utils';
import { usePLPState } from '../../../data';
import { useProductCardConfig } from '../product-card-config-hook';

jest.mock('../../../data');

describe('The useProductCardConfig() hook.', () => {
  describe('GIVEN: the experiment for certain product card features (badging, marketing flags & star ratings) is active,', () => {
    const experimentIsActive = { gap229: 'a' };
    const featureFlagIsActive = { 'plp-product-card-config': true };
    describe('WHEN: the feature flag is active as well,', () => {
      it('THEN: returns an object with booleans for those 3 features, each evaluating to `true`', () => {
        (usePLPState as jest.Mock).mockReturnValue({
          brand: 'gap',
          pageType: 'category',
          abSeg: experimentIsActive,
          enabledFeatures: featureFlagIsActive,
          featureVariables: {
            'plp-product-card-config': {
              'gap-category-display-badge': true,
              'gap-category-display-marketing-flag': true,
              'gap-category-display-star-ratings': true,
            },
          },
        });

        const { result } = renderHook(() => useProductCardConfig());

        expect(result.current.displayBadge).toBe(true);
        expect(result.current.displayMarketingFlag).toBe(true);
        expect(result.current.displayStarRatings).toBe(true);
      });
      it('THEN: it behaves the same for the Search page as well.', () => {
        const experimentIsActiveForSearch = { gap230: 'a' };
        (usePLPState as jest.Mock).mockReturnValue({
          brand: 'gap',
          pageType: 'category',
          abSeg: experimentIsActiveForSearch,
          enabledFeatures: featureFlagIsActive,
          featureVariables: {
            'plp-product-card-config': {
              'gap-category-display-badge': true,
              'gap-category-display-marketing-flag': true,
              'gap-category-display-star-ratings': true,
            },
          },
        });
      });
    });
    describe('WHEN: the feature flag is NOT active,', () => {
      it('should return false if experiment is turned off', () => {
        (usePLPState as jest.Mock).mockReturnValue({
          brand: 'gap',
          pageType: 'category',
          abSeg: experimentIsActive,
          enabledFeatures: featureFlagIsActive,
          'plp-product-card-config': {
            'gap-category-display-badge': true,
            'gap-category-display-marketing-flag': true,
            'gap-category-display-star-ratings': true
          },
        });

        const { result } = renderHook(() => useProductCardConfig());

        expect(result.current.displayBadge).toBe(false);
        expect(result.current.displayMarketingFlag).toBe(false);
        expect(result.current.displayStarRatings).toBe(false);
      });
    });
    describe('WHEN: the feature flag is active but feature variables are empty', () => {
      it('should return true if experiment is turned off and false if no variables available', () => {
        (usePLPState as jest.Mock).mockReturnValue({
          brand: 'gap',
          pageType: 'category',
          abSeg: experimentIsActive,
          enabledFeatures: featureFlagIsActive,
          featureVariables: { 'plp-product-card-config': {} },
        });
        const { result } = renderHook(() => useProductCardConfig());
        expect(result.current.displayBadge).toBe(false);
        expect(result.current.displayMarketingFlag).toBe(false);
        expect(result.current.displayStarRatings).toBe(false);
      });
    });
  });
  describe('GIVEN: the experiment enabling certain product card features is inactive,', () => {
    const experimentIsActive = { gap229: 'x' };
    const featureFlagIsActive = { 'plp-product-card-config': true };
    describe('WHEN: the feature flag is active', () => {
      it('THEN: still returns an object with booleans for those 3 features, each evaluating to `false`', () => {
        (usePLPState as jest.Mock).mockReturnValue({
          brand: 'gap',
          pageType: 'category',
          abSeg: experimentIsActive,
          enabledFeatures: featureFlagIsActive,
          featureVariables: { 'plp-product-card-config': {} },
        });
        const { result } = renderHook(() => useProductCardConfig());
        expect(result.current.displayBadge).toBe(false);
        expect(result.current.displayMarketingFlag).toBe(false);
        expect(result.current.displayStarRatings).toBe(false);
      });
    });
  });
});
