import { FacetDisplay, FacetLayout, FacetOption, FacetRange, FacetSelectionType, FacetType } from '../generics';

export type AppliedFacetsOption = {
  applied?: boolean;
  displayName?: string;
  facetData?: AppliedFacets;
  facetName?: string;
  id?: string;
  isActive?: boolean;
  localeName?: string;
  name: string;
  tagDisplayLabel?: string;
  value?: string;
};

export type AppliedFacets = Record<string, AppliedFacetsOption[]>;

export type SortByOptions = {
  sortByDir: [
    {
      applied: boolean;
      id: string;
    },
  ];
  sortByField: [
    {
      applied: boolean;
      id: string;
    },
  ];
};

export interface Facet {
  appliedRange?: FacetRange;
  appliedValues?: AppliedValue[];
  displayName?: string;
  dynamicFacetName?: boolean;
  facetDisplay: FacetDisplay;
  facetLayout: FacetLayout;
  isActive: boolean;
  localizedName?: string;
  name: string;
  options?: Array<FacetOption>;
  order?: number;
  range?: FacetRange;
  selectionType: FacetSelectionType;
  sizeVariants?: Variant[];
  type: FacetType;
  value?: string;
}

export interface FacetsAdapterResult {
  appliedFacets?: AppliedFacets;
  facetOptions: Facet[];
  hasTags?: boolean;
  localeData?: string;
}

export interface SortByAdapterResult {
  sortByDir?: string;
  sortByField?: string;
}

export type SortByDir = SortByAdapterResult['sortByDir'];

export type SortByField = SortByAdapterResult['sortByField'];

export type FacetOptions = {
  displayName?: string;
  facetName: string;
  id?: string;
  name: string;
};

export interface InlineFacetAdapter {
  facetOptions: FacetOptions[];
  onClear?: (option: { applied: boolean; facetName: string; id: string; name: string }) => void;
}

export interface FacetWithConfig {
  appliedRange?: FacetRange;
  displayName?: string;
  facetDisplay: FacetDisplay;
  facetLayout: FacetLayout;
  hidden?: boolean;
  isActive: boolean;
  localeName: string;
  name: string;
  options?: Array<FacetOption>;
  order?: number;
  range?: FacetRange;
  searchFacetName?: string;
  selectionType: FacetSelectionType;
  type: FacetType;
}

export interface AppliedValue {
  description?: string;
  facetName?: string;
  id?: string;
  name?: string;
  selected?: boolean;
  tagDisplayLabel?: string;
  value?: string;
}

export interface StyleGroup {
  id: string;
  name: string;
  sizes: AppliedValue[];
}

export interface Variant {
  id: string;
  name: string;
  styleGroups: StyleGroup[];
}

export interface SizeFacet extends Facet {
  sizeVariants: Variant[];
}
