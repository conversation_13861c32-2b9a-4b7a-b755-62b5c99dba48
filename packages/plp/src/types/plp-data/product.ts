import { ColorSwatch } from './color-swatches';
import { PriceData } from './price';

export type MergeType = 'STYLE' | 'VARIANT' | 'NONE' | undefined;

export interface PositionObj {
  bottom?: string;
  left?: string;
  right?: string;
  top?: string;
}

export interface ProductImageData {
  path: string;
  type: string;
}

export interface ProductImages {
  avImages: {
    av1QuickLookImagePath: string;
    av2QuickLookImagePath: string;
    av3QuickLookImagePath: string;
    av4QuickLookImagePath: string;
    av5QuickLookImagePath: string;
    av6QuickLookImagePath: string;
    av9QuickLookImagePath: string;
    p1QuickLookImagePath: string;
  };
  otherImages: {
    categoryLargeImage: string;
    colorSwatchImage: string;
    quickLookImage: string;
  };
  pristineImages: {
    pristine1ImagePath: string;
  };
  searchImages: {
    pristineImage: string;
    swatchImage: string;
    swatchImageAttribute: string;
    thumbImage: string;
    zoomImage: string;
  };
  zoomImages: {
    av1ZoomImagePath: string;
    av2ZoomImagePath: string;
    av3ZoomImagePath: string;
    av4ZoomImagePath: string;
    av5ZoomImagePath: string;
    av6ZoomImagePath: string;
    av7ZoomImagePath: string;
    p01ZoomImagePath: string;
  };
}

export interface Product {
  additionalQueryParams?: string;
  altText: string;
  avImages: ProductImages['avImages'];
  businessCatalogItemId?: string;
  categoryLargeImage: string;
  defaultSizeVariantId?: string;
  inventoryStatusId?: number;
  isDropship?: boolean;
  marketingFlag: {
    badgingName?: string;
    marketingFlagName?: string;
  };
  name: string;
  outOfStock: boolean;
  price: PriceData | null;
  pristineImages: ProductImages['pristineImages'];
  productID: string;
  productURL: string;
  quickLookImage: string;
  reviewCount?: number;
  reviewScore?: number;
  searchImages: ProductImages['searchImages'];
  sellerName?: string;
  swatchesProps?: Array<ColorSwatch>;
  url: string;
  vendorId?: string;
  vid: string;
  zoomImages: ProductImages['zoomImages'];
}

export type GridParams = {
  cardIndex?: number;
  pageIndex?: string;
  productGridSource?: string;
  totalCount?: number;
};

export type QueryParams = {
  cid?: string;
  nav?: string | null;
  pcid?: string;
  pid?: string;
  vid?: string;
};

export type BuildProductLinkParams = {
  additionalQueryParams?: string;
  grid?: GridParams;
} & QueryParams &
  Partial<Record<string, unknown>>;
