import { isSearchAndSizeHashForSearchPageInterim, sizeQueryBuilderForSearchPageInterim } from './interimForSearchPage/interimSizeHashMapper';
import { AppliedValue, SelectedParams } from './types';

const commonFacetQueryBuilder = (options: AppliedValue[]): string | undefined => options.map(option => option.id).join(',') || undefined;

const rangeFacetQueryBuilder = (options: AppliedValue[]): string | undefined => {
  const [option] = options;

  if (option?.range) {
    return `${option.range.min}-${option.range.max}`;
  }

  return undefined;
};

const sizeQueryBuilderNewContract = (selectedSizes: AppliedValue[]): string | undefined => {
  if (selectedSizes.length > 0) {
    const groupedSizes = selectedSizes.reduce((sizes: Record<string, AppliedValue[]>, size) => {
      const group = size.id !== undefined ? `${size.id}` : '';

      return {
        ...sizes,
        [group]: [...(sizes[group] ?? []), size],
      };
    }, {});

    return Object.keys(groupedSizes).join(',');
  }

  return undefined;
};

const joinQueryStringSegments = (queryStringSegments: Record<string, string | undefined>): string => {
  return Object.keys(queryStringSegments)
    .filter(key => !!queryStringSegments[key])
    .map(key => `${key}=${queryStringSegments[key]}`)
    .join('&');
};

/**
 * Creates hash parameter by concatenating key and value.
 */
const joinParam = ([key, value]: [string, string]): string => `${key}=${value}`;

const isValidParam = ([key]: [string, string]): boolean => !!key && /^[a-zA-Z]/.test(key);

/**
 * Returns a valid hash string containing only the parameters specified in the valid hash params.
 */
export const removeInvalidHashParams = (urlHash: string): string =>
  urlHash
    .substring(1)
    .split('&')
    .map(param => param.split('=') as [string, string])
    .filter(isValidParam)
    .map(joinParam)
    .join('&');

/**
 * Constructs a URL hash string to make a products request with selected params from facets, pagination, sort by, and BOPIS.
 */

type UrlHash = string;

const sortByAndStoreOptionsHandler = (options: AppliedValue[]): string | undefined => {
  const [option] = options;
  return option?.id ?? option?.searchFacetOptionId ?? undefined;
};

const buildOrderOption = (facetName: string, id: string | undefined): string => `${facetName + (id ? `:${id}` : '')}`;

const getFacetId = (facet: AppliedValue): string | undefined => facet.id ?? facet.searchFacetOptionId;

const mapOrderedFacets = (facet: AppliedValue): string => {
  const { facetName } = facet;
  const id = getFacetId(facet);
  return buildOrderOption(facetName, id);
};

const filterUndefinedAndOutOfStockFacet = (facet: AppliedValue): boolean => {
  /*filtering out-of-stock facet because we don't need to send it to PDS
    we are sending ignoreInventory flag to PDS based on out-of-stock toggle swtich state
  */
  return !!facet?.facetName && facet?.facetName !== 'outOfStock';
};

const facetOrderQueryBuilder = (orderedFacets: AppliedValue[] | undefined): string =>
  orderedFacets ? orderedFacets.filter(filterUndefinedAndOutOfStockFacet).map(mapOrderedFacets).join(',') : '';

const optionsHandlers: Record<string, (options: AppliedValue[]) => string | undefined> = {
  size: options => sizeQueryBuilderNewContract(options),
  price: rangeFacetQueryBuilder,
  reviewCount: rangeFacetQueryBuilder,
  inventoryCount: rangeFacetQueryBuilder,
  storeId: sortByAndStoreOptionsHandler,
  sortByField: sortByAndStoreOptionsHandler,
  sortByDir: sortByAndStoreOptionsHandler,
  pageId: options => options.map(option => option.id).join(','),
  facetOrder: facetOrderQueryBuilder,
};

const adaptQueryParam = (facets: SelectedParams, name: string): string | undefined => {
  const options = facets[name] as AppliedValue[];
  // TODO: remove after migration to new size hash params on search page
  if (isSearchAndSizeHashForSearchPageInterim(name)) {
    return sizeQueryBuilderForSearchPageInterim(options);
  }
  const handle = optionsHandlers[name];
  return handle ? handle(options) : commonFacetQueryBuilder(options);
};

const toQuery = (params: SelectedParams) => {
  return (query: Record<string, string | undefined>, param: string): Record<string, string | undefined> => {
    query[param] = adaptQueryParam(params, param);
    return query;
  };
};

const buildUrlHash = (selectedParams: SelectedParams = {}): UrlHash => {
  const queryStrings = Object.keys(selectedParams).reduce(toQuery(selectedParams), {});
  return joinQueryStringSegments(queryStrings);
};

export default buildUrlHash;
