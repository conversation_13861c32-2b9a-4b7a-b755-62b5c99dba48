import { SortByAdapterResult } from '../../../types/plp-data';
import { StateWrapper } from '../types';
import { PsDataAction, PsDataActionTypes } from './types';
import { mergeSubcategories } from './';

export const psDataReducer = (state: StateWrapper, { type, data, isSuccess, isLoading, isError }: PsDataAction): StateWrapper => {
  switch (type) {
    case PsDataActionTypes.UpdateState:
      return {
        data: {
          ...state.data,
          ...data,
        },
        isSuccess: isSuccess,
        isLoading: isLoading,
        isError: isError,
      };
    case PsDataActionTypes.ViewMoreProducts:
      return {
        data: {
          ...state.data,
          ...data,
          productImages: { ...state.data.productImages, ...data?.productImages },
          productsSubcategories: mergeSubcategories(state.data.productsSubcategories, data?.productsSubcategories || []),
          productInfos: {
            ...state.data.productInfos,
            ...data?.productInfos,
          },
          productPrices: {
            ...state.data.productPrices,
            ...data?.productPrices,
          },
          productReview: {
            ...state.data.productReview,
            ...data?.productReview,
          },
          productSwatches: {
            ...state.data.productSwatches,
            ...data?.productSwatches,
          },
        },
        isSuccess: isSuccess,
        isLoading: isLoading,
      };
    case PsDataActionTypes.SortByOption: {
      const sortingOptions: SortByAdapterResult = {
        sortByDir: data?.sortingOptions?.sortByDir ?? 'asc',
        sortByField: data?.sortingOptions?.sortByField ?? 'price',
      };
      return { ...state, data: { ...state.data, sortingOptions } };
    }
    default:
      return state;
  }
};
