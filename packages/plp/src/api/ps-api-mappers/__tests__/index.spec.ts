import { getAdaptedData } from '../index';
import { PsData, PsPagination } from '../../../types/ps-api-response';
import facetsAdapter from '../shared/facet/facets';
import { searchAdapter } from '../search';
import { categoryAdapter } from '../category';

jest.mock('../shared/facet/facets');
jest.mock('../search');
jest.mock('../category');

describe('getAdaptedData', () => {
  const data = {
    facets: {},
    totalColors: '10',
    totalProducts: '20',
    pagination: { currentPage: '1', pageNumberTotal: '2', pageSize: '10' } as PsPagination,
  } as PsData;

  const plpFeaturesForApi = {
    facetsOrder: {
      isDynamicFacetsEnabled: false,
      isFacetsOrderingEnabled: false,
      isManualFacetsEnabled: false,
    },
  };

  beforeEach(() => {
    (facetsAdapter as jest.Mock).mockReturnValue({ facets: 'facets' });
    (searchAdapter as jest.Mock).mockReturnValue({ search: 'search' });
    (categoryAdapter as jest.Mock).mockReturnValue({ category: 'category' });
  });

  it('should return adapted data for categories', () => {
    const adaptedData = getAdaptedData(data, 'category', plpFeaturesForApi);
    expect(adaptedData).toEqual({
      facets: {
        facets: 'facets',
      },
      totalItemCount: 10,
      pagination: { pageNumberRequested: 1, pageNumberTotal: 2, pageSize: 10 },
      outOfStock: false,
      category: 'category',
    });
  });

  it('should return itemCount as zero when totalColors is not available  in psData for category page', () => {
    data.totalColors = '';
    const adaptedData = getAdaptedData(data, 'category', plpFeaturesForApi);
    expect(adaptedData).toEqual({
      facets: {
        facets: 'facets',
      },
      totalItemCount: 0,
      pagination: { pageNumberRequested: 1, pageNumberTotal: 2, pageSize: 10 },
      outOfStock: false,
      category: 'category',
    });
  });

  it('should return adapted data for search', () => {
    const adaptedData = getAdaptedData(data, 'search', plpFeaturesForApi);
    expect(adaptedData).toEqual({
      facets: {
        facets: 'facets',
      },
      totalItemCount: 20,
      pagination: { pageNumberRequested: 1, pageNumberTotal: 2, pageSize: 10 },
      outOfStock: false,
      search: 'search',
    });
  });

  it('should return itemCount as zero when totalProducts is not available  in psData for search page', () => {
    data.totalProducts = '';
    const adaptedData = getAdaptedData(data, 'search', plpFeaturesForApi);
    expect(adaptedData).toEqual({
      facets: {
        facets: 'facets',
      },
      totalItemCount: 0,
      pagination: { pageNumberRequested: 1, pageNumberTotal: 2, pageSize: 10 },
      outOfStock: false,
      search: 'search',
    });
  });
});
