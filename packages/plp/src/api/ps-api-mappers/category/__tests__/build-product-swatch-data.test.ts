import { buildProductSwatches, placeCcSwatchFirst } from '../build-product-swatch-data';
import { isOosCategory } from '../../category/is-oos-category';
import { PsCategoriesProduct } from '@/src/types';

jest.mock('../../category/is-oos-category');
const testStyle = {
  styleColors: [
    { ccId: '1', images: [{ type: 'S', path: 'path1' }] },
    { ccId: '2', images: [{ type: 'S', path: 'path2' }] },
    { ccId: '3', images: [{ type: 'S', path: 'path3' }] },
  ],
} as unknown as PsCategoriesProduct;

describe('Product Swatch Data Builder', () => {
  beforeEach(() => {
    (isOosCategory as jest.Mock).mockReturnValue(false);
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  it('should build a swatch object for each style color', () => {
    const productSwatchData = buildProductSwatches(testStyle);
    expect(productSwatchData).toHaveLength(3);
    expect(productSwatchData[0]).toEqual({ id: '1', swatchImage: 'path1', outOfStock: false });
    expect(productSwatchData[1]).toEqual({ id: '2', swatchImage: 'path2', outOfStock: false });
    expect(productSwatchData[2]).toEqual({ id: '3', swatchImage: 'path3', outOfStock: false });
  });
  it('should call isOutOfStock with the correct argument and set outOfStock key in productSwatchData', () => {
    (isOosCategory as jest.Mock).mockReturnValue(true);
    const productSwatchData = buildProductSwatches(testStyle);
    expect(productSwatchData[0].outOfStock).toBe(true);
    expect(productSwatchData[1].outOfStock).toBe(true);
    expect(productSwatchData[2].outOfStock).toBe(true);
    expect(isOosCategory).toHaveBeenCalledWith(testStyle.styleColors[0]);
    expect(isOosCategory).toHaveBeenCalledWith(testStyle.styleColors[1]);
    expect(isOosCategory).toHaveBeenCalledWith(testStyle.styleColors[2]);
  });

  it('should return an empty string if there is no swatch image', () => {
    const style = {
      styleColors: [{ ccId: '1', images: [] }]
    } as unknown as PsCategoriesProduct;
    const productSwatchData = buildProductSwatches(style);
    expect(productSwatchData[0].swatchImage).toBe('');
  });

  it('should return an empty array if there are no style colors', () => {
    const productSwatchData = buildProductSwatches({} as unknown as PsCategoriesProduct);
    expect(productSwatchData).toEqual([]);
  });
});

describe('Place CC Swatch First', () => {
  it('should place the swatch with the provided ccId first', () => {
    const swatches = buildProductSwatches(testStyle);
    const arrangedSwatches = placeCcSwatchFirst('2', swatches);
    expect(arrangedSwatches[0]).toEqual({ id: '2', swatchImage: 'path2', outOfStock: false });
    expect(arrangedSwatches[1]).toEqual({ id: '1', swatchImage: 'path1', outOfStock: false });
    expect(arrangedSwatches[2]).toEqual({ id: '3', swatchImage: 'path3', outOfStock: false });
  });

  it('should return original array if no matching swatch is found', () => {
    const swatches = buildProductSwatches(testStyle);
    const arrangedSwatches = placeCcSwatchFirst('4', swatches);
    expect(arrangedSwatches).toEqual(swatches);
  });
});
