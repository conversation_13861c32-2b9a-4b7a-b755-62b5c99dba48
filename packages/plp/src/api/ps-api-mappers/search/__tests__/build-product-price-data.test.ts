import { buildProductPriceData } from '../build-product-price-data';
import { PsSearchColor } from '@/src/types';

const priceData = {
  effectivePrice: '100',
  regularPrice: '200',
  percentageOff: '50',
  priceType: 'P',
} as PsSearchColor;

describe('Product Price Data Builder', () => {
  it('sets effective price to current price', () => {
    const productPriceData = buildProductPriceData(priceData);
    expect(productPriceData.currentPrice).toBe(100);
  });

  it('sets regular price to regular price', () => {
    const productPriceData = buildProductPriceData(priceData);
    expect(productPriceData.regularPrice).toBe(200);
  });

  it('sets percentage off to percentage off', () => {
    const productPriceData = buildProductPriceData(priceData);
    expect(productPriceData.percentageOff).toBe(50);
  });

  it('sets price type to price type', () => {
    const productPriceData = buildProductPriceData(priceData);
    expect(productPriceData.priceType).toBe('P');
  });
});
