import { PsSearchColor } from '../../../types';
import { ProductImageState } from '../../ps-data-provider/types';

export const buildProductImageData = (styleColor: PsSearchColor): ProductImageState => {
  const { type, path } = styleColor.images.find(image => image.type === 'zoomImage') || { type: '', path: '' };
  const zoomImages = styleColor.images.filter(image => image.type.includes('_Z')).map(image => ({ type: image.type, path: image.path }));

  return {
    mainImage: { type, path },
    zoomImages,
  };
};
