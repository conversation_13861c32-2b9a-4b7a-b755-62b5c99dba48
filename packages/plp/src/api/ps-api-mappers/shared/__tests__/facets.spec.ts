import facetsAdapter from '../facet/facets';
import { PsFacet, FacetSelectionType } from '../../../../types';

const sizeFacet = {
  name: 'size',
  localeName: 'Size',
  type: 'single-select' as FacetSelectionType,
};

const priceFacet = {
  name: 'price',
  localeName: 'Price',
  type: 'range' as FacetSelectionType,
  isActive: true,
  range: { min: 19, max: 49 },
  appliedRange: { min: 20, max: 50 },
};

const simpleFacet = {
  name: 'anySimpleFacet',
  localeName: 'Something',
  type: 'simple',
  options: [{ id: '198', name: 'T shirts' }],
};

const plpFeaturesForApi = {
  facetsOrder: {
    isDynamicFacetsEnabled: true,
    isFacetsOrderingEnabled: true,
    isManualFacetsEnabled: false,
  },
};

describe('GIVEN: facetsAdapter() is passed an array of PSFacet objects and an array of facets to hide,', () => {
  describe('WHEN: facetsAdapter() is executed,', () => {
    it('returns object with (1) applied facets, (2) options for them, and (3) boolean stating whether there are tags.', () => {
      const facet: PsFacet[] = [priceFacet, sizeFacet, simpleFacet];
      const hiddenFacets: string[] = ['price'];

      const expectedResult = {
        appliedFacets: {},
        facetOptions: [],
        hasTags: false,
      };
      const mockPageType = 'category';
      const localeData = 'en-US';
      const result = facetsAdapter(facet, mockPageType, plpFeaturesForApi, localeData, hiddenFacets);

      expect(result).toEqual(expectedResult);
    });
  });
});
