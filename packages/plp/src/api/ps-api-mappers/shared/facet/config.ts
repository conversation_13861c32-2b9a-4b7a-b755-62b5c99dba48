import { FacetDisplay } from '@ecom-next/plp-ui/types/response-types';
import { FacetLayout, FacetWithConfig, FacetSelectionType } from '../../../../types';

const multiSelect = 'multi-select';

export const defaultConfig = {
  facetDisplay: 'checkbox' as FacetDisplay,
  facetLayout: 'list' as FacetLayout,
  selectionType: multiSelect as FacetSelectionType,
};

const department = {
  displayName: 'Department',
  facetDisplay: 'radio' as FacetDisplay,
  facetLayout: 'list' as FacetLayout,
  selectionType: 'single-select' as FacetSelectionType,
  order: 1,
};

const style = {
  displayName: 'Category',
  facetDisplay: 'checkbox' as FacetDisplay,
  facetLayout: 'list' as FacetLayout,
  selectionType: multiSelect as FacetSelectionType,
  order: 2,
};

const size = {
  order: 3,
};

const color = {
  displayName: 'Color',
  facetDisplay: 'swatch' as FacetDisplay,
  facetLayout: 'grid' as FacetLayout,
  selectionType: multiSelect as FacetSelectionType,
  order: 4,
};

const price = {
  displayName: 'Price',
  order: 5,
};

const reviewScore = {
  displayName: 'Rating',
  facetDisplay: 'ratings' as FacetDisplay,
  facetLayout: 'list' as FacetLayout,
  selectionType: 'single-select' as FacetSelectionType,
  order: 6,
};

const performanceTechnology = {
  displayName: 'Performance / Technology',
};

const activityOccasion = {
  displayName: 'Activity / Occasion',
};

const fabricMaterial = {
  displayName: 'Fabric / Material',
};

const newStyle = {
  hidden: true,
};

const inventoryCount = {
  hidden: true,
};

const config: Record<string, Partial<FacetWithConfig>> = {
  color,
  department,
  style,
  price,
  size,
  reviewScore,
  performanceTechnology,
  activityOccasion,
  fabricMaterial,
  newStyle,
  inventoryCount,
};

export default config;
