import channel from '../channel';

/**
 * you can look the attributes from this dock:
 * https://deploytealium.com/verify/dataLayer.php?account=gapinc&pin=58daa2b95cfea&title=main
 * Find for the channel attribute
 */
describe('Datalayer/category-page-attributes/collaborators/channel', () => {
  it('can use the brandName into channel when is Gapfs to Tealium', () => {
    const data = {
      brandName: 'gapfs',
      abbrNameForTealium: 'gpfs',
      selectedNodes: [
        {
          type: 'division',
          name: 'Womens',
        },
      ],
    };

    const result = channel(data);
    expect(result).toEqual(`${data.brandName}:Womens`);
  });

  it('can use the abbrNameForTealium into channel when is Gap to Tealium', () => {
    const data = {
      brandName: 'gap',
      abbrNameForTealium: 'gp',
      selectedNodes: [
        {
          type: 'sub-division',
          name: 'Man',
        },
      ],
    };

    const result = channel(data);
    expect(result).toEqual(`${data.abbrNameForTealium}:Man`);
  });

  it("can't broken when not have selectedNodes", () => {
    const data = {
      brandName: 'gap',
      abbrNameForTealium: 'gp',
      selectedNodes: [],
    };

    const result = channel(data);
    expect(result).toEqual(`${data.abbrNameForTealium}:`);
  });
});
