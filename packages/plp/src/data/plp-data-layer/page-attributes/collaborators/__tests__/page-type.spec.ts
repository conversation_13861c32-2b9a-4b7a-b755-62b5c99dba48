import pageType from '../page-type';

/**
 * you can look the attributes from this dock:
 * https://deploytealium.com/verify/dataLayer.php?account=gapinc&pin=58daa2b95cfea&title=main
 * Find for the page_type attribute
 */
describe('Datalayer/category-page-attributes/collaborators/page-type', () => {
  it('check if first letter is Upper case', () => {
    const props = { pageType: 'categoryPageType' };
    expect(pageType(props)).toEqual('CategoryPageType');
  });
});
