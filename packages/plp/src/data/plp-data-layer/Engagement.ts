import { storageHelper } from '@ecom-next/core/legacy/utility';
import { DataLayer } from '@ecom-next/sitewide/app-state-provider';
import {
  BopisDataType,
  ProductStore,
} from '../../components/grid-header/components/grid-drawer/components/facet-bar/components/facet-selectors/bopis-selector/types';
import { ProductInfoState } from '../../api/ps-data-provider/types';
import prepareDatalayerObject from './datalayer-object/prepare-datalayer-object';
import { engagementsString } from './events/adapters/adapter';
import { bopisInteractionDataLayerBuilder } from './events/bopis-engagement';
import { BopisDatalayerAttributes } from './types';

const PLP_DATALAYER_REPORT_OBJECT = 'plpDatalayerReportObject';

const onProductClick = (type: string = 'product-image-click', product: ProductInfoState, isBadgingEnabled: boolean, showStarRatings: boolean) => {
  const { setItem } = storageHelper(localStorage);
  const dataLayerObject = prepareDatalayerObject({
    type,
    product,
    isBadgingEnabled,
    showStarRatings,
  });
  setItem(`${PLP_DATALAYER_REPORT_OBJECT}-${product.pcid}`, JSON.stringify(dataLayerObject));
};

/* eslint-disable @typescript-eslint/no-explicit-any */
const onFacetEngagement = (() => {
  let prevFacetEngagementString = '';
  return (datalayer: any, facetData: any) => {
    datalayer.build().then((builtData: any) => {
      const { page_name, page_type, business_unit_id, recognition_status } = builtData;
      const hashString = typeof window !== 'undefined' ? window.location.hash : '';
      const appliedQuickFacets: string[] = [];
      const facetEngagementString = engagementsString({
        hashString,
        facetData,
        appliedQuickFacets,
        modelToggleData: { mixedGrid: false, sizeValue: '' },
      });
      if (facetEngagementString !== prevFacetEngagementString) {
        datalayer.link({
          event_name: 'refinement',
          page_name,
          page_type,
          business_unit_id,
          recognition_status: recognition_status || '',
          facet_engagement: facetEngagementString,
        });
        prevFacetEngagementString = facetEngagementString;
      }
    });
  };
})();

const onBopisEngagement = (
  datalayer: DataLayer,
  bopisData: BopisDataType,
  props: BopisDatalayerAttributes,
  switchStatus: boolean,
  selectedStore: ProductStore | undefined
) => {
  const dataLayerObject = {
    datalayer: datalayer,
    pageType: props?.pageType,
    businessUnitId: props?.businessUnitId,
    categoryName: props?.categoryName,
    abbrNameForTealium: props?.abbrNameForTealium,
    selectedNodes: props?.selectedNodes,
    bopisData: bopisData,
    isSwitchChecked: switchStatus,
    selectedStore: selectedStore,
  };

  bopisInteractionDataLayerBuilder(dataLayerObject, false);
};

export { onFacetEngagement, onProductClick, onBopisEngagement };
