import { ProductInfo } from '../types';
import colorsArray from './colors-array';

const highPriorityEvents = [
  {
    name: '',
    deferred: {
      promise: new Promise(resolve => resolve(true)),
      reject: (): void => {
        return;
      },
      resolve: (): void => {
        return;
      },
    },
  },
];

const onFirstColorSwatchClick = (): void => {
  return;
};
const onFirstCarouselArrowClick = (): void => {
  return;
};
const onCarouselArrowClick = (): void => {
  return;
};

export const productInfo = {
  abbrBrand: 'gap',
  additionalQueryParams: '',
  productName: 'Sherpa fleece hoodie',
  name: 'Sherpa fleece hoodie',
  productURL: 'http://www.gap.com/browse/product.do?cid=1072543&pcid=1066503&vid=1&pid=864228032',
  productID: '123',
  marketingFlag: {
    marketingFlagName: 'Final Sale',
    badgingName: 'Best seller',
  },
  avImages: {
    av1QuicklookImagePath: '',
    av2QuicklookImagePath: '',
    av3QuicklookImagePath: '',
    av4QuicklookImagePath: '',
    av5QuicklookImagePath: '',
    av6QuicklookImagePath: '',
    av9QuicklookImagePath: '',
    p1QuicklookImagePath: '',
  },
  zoomImages: {
    av1ZoomImagePath: '',
    p01ZoomImagePath: '',
  },
  url: 'http://www3.assets-gap.com/webcontent/0013/882/196/cn13882196.jpg',
  altText: 'sherpa fleece hoodie',
  swatchesEnabledPrice: {
    currentMaxPrice: 54.99,
    currentMinPrice: 49.99,
    regularMaxPrice: 69.95,
    regularMinPrice: 69.94,
    minPercentageOff: 0,
    maxPercentageOff: 0,
    localizedRegularMaxPrice: '$54.99',
  },
  price: {
    currentMaxPrice: '54.99',
    currentMinPrice: '49.99',
    regularMaxPrice: '69.95',
    regularMinPrice: '69.94',
    localizedCurrentMaxPrice: '$54.99',
    localizedCurrentMinPrice: '$49.99',
    localizedRegularMaxPrice: '$69.95',
    localizedRegularMinPrice: '$69.94',
    minPercentageOff: 0,
    maxPercentageOff: 0,
    localizedCurrencySymbol: '$',
  },
  outOfStock: false,
  modelSize: undefined,
  reviewCount: 41,
  reviewScore: '1.8',
  swatchesProps: colorsArray,
  dropshipData: {
    vendorName: 'SAN DISTRIBUTION COMPANY',
    isShowSellerName: false,
    isFreeShipping: '',
    isLowestPriceCC: false,
  },
} as ProductInfo;

export const propsObject = {
  productInfo: productInfo,
  priceAdapter: () => {},
  cid: '12345',
  pcid: '123',
  locale: 'us',
  brand: 'gap',
  cardIndex: 0,
  groupIndex: 12345,
  pageIndex: '1',
  productGridSource: 'pds',
  totalCount: 100,
  hoverAlt: '',
  isQuickAddEnabled: false,
  highPriorityEvents,
  onFirstColorSwatchClick,
  onFirstCarouselArrowClick,
  onCarouselArrowClick,
};

export const productInfoWithMissingData = {
  productID: '12345',
  name: '',
  price: {
    localizedRegularMaxPrice: '$69.95',
    localizedRegularMinPrice: '$69.94',
  },
  outOfStock: false,
  pristineImageUrl: '',
  altText: '',
  reviewCount: 0,
  additionalQueryParams: '',
  productName: '',
  avImages: {
    av1QuicklookImagePath: '',
    av2QuicklookImagePath: '',
    av3QuicklookImagePath: '',
    av4QuicklookImagePath: '',
    av5QuicklookImagePath: '',
    av6QuicklookImagePath: '',
    av9QuicklookImagePath: '',
    p1QuicklookImagePath: '',
  },
  zoomImages: {
    av1ZoomImagePath: '',
    p01ZoomImagePath: '',
  },
  url: '',
};

export const propsObjectWithMissingData = {
  cardIndex: 0,
  cid: '',
  pageIndex: '1',
  pcid: '',
  priceAdapter: (): void => {
    return;
  },
  productInfo: productInfoWithMissingData,
  productGridSource: 'pds',
  totalCount: 100,
  locale: 'us',
  hoverAlt: '',
  isQuickAddEnabled: false,
  highPriorityEvents,
  onFirstColorSwatchClick,
  onFirstCarouselArrowClick,
  onCarouselArrowClick,
};
