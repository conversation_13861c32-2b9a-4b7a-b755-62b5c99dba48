import { modelToggleEngagementAdapter } from '../model-toggle';

describe('Datalayer/events/adapters/adapters/model-toggle', () => {
  it('should fail back undefined modelToggleData', () => {
    expect(modelToggleEngagementAdapter()).toEqual('');
  });

  it('should return modelOn=size all if mixedGrid is equal true', () => {
    expect(modelToggleEngagementAdapter({ mixedGrid: true, sizeValue: 'S' })).toEqual('modelOn=size all');
  });

  it('should return modelOn=size SIZEVALUE if mixedGrid is not equal true', () => {
    expect(modelToggleEngagementAdapter({ mixedGrid: false, sizeValue: 'S' })).toEqual('modelOn=size S');
  });
});
