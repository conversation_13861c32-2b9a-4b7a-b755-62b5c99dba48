import { GridToggle } from '../../../types';
import getPageAttributes from '../page-attributes/attributes';
import getPageType from '../page-attributes/collaborators/page-type';
import { datalayerInstance } from '../DataLayerInstance';
import { AppProps } from '../types';

const PLP_GRID_TOGGLE_SELECTION = 'PLPGridToggle';

export const allGridSizes: { [key: string]: string } = {
  [GridToggle.One]: '1-Grid',
  [GridToggle.Two]: '2-Grid',
  [GridToggle.Three]: '3-Grid',
  [GridToggle.Four]: '4-Grid',
  [GridToggle.Five]: '5-Grid',
  [GridToggle.Six]: '6-Grid',
};

export const gridToggleEngagementDatalayerEvent = (props: AppProps): void => {
  try {
    datalayerInstance().link({
      test_coe: PLP_GRID_TOGGLE_SELECTION,
      ...getPageAttributes(props),
    });
    /* eslint-disable @typescript-eslint/no-explicit-any */
  } catch (err: any) {
    datalayerInstance().link({
      business_unit_id: props.businessUnitId,
      error_message: `[${getPageType(props)} Grid Toggle Data Layer]: error: ${err.message}`,
    });
  }
};
