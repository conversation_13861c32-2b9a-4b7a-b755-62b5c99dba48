import { datalayerInstance } from '../../DataLayerInstance';
import getPageAttributes from '../../page-attributes/attributes';
import webHierarchyGap from '../../fixtures/webHierarchy.json';
import { facetEngagementDatalayerEvent } from '../facet-engagement';

jest.mock('../../index');
jest.mock('../../page-attributes/attributes');
jest.mock('../../DataLayerInstance');

const linkFuncSpyed = jest.fn();

(datalayerInstance as jest.Mock).mockImplementation(() => ({
  link: linkFuncSpyed,
}));

(getPageAttributes as jest.Mock).mockImplementation(props => props);

describe('Datalayer/events/facet-engagements', () => {
  const datalayer = datalayerInstance();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const props = {
    exampleOfCategoryPageAttributes: true,
    businessUnitId: 'unitId',
    webHierarchy: webHierarchyGap,
    selectedNodes: [
      {
        id: '5065',
        name: 'Men',
        type: 'sub-division',
      },
      {
        id: '80799',
        name: 'Pants',
        type: 'category',
      },
    ],
  };

  it('should send events of exception', () => {
    getPageAttributes.mockImplementationOnce(() => {
      throw new Error('mocked exception');
    });

    facetEngagementDatalayerEvent(datalayer, '', props, {});
    expect(linkFuncSpyed).toHaveBeenCalledWith({
      business_unit_id: props.businessUnitId,
      error_message: '[Facet Engagement Data Layer]: error: mocked exception',
    });
  });

  it('should handle facetData param undefined (case of FactedGrid compnent)', () => {
    facetEngagementDatalayerEvent(datalayer, '', props);
    expect(linkFuncSpyed).toHaveBeenCalledWith({
      event_name: 'refinement',
      exampleOfCategoryPageAttributes: true,
      businessUnitId: props.businessUnitId,
      facet_engagement: '',
      test_coe: undefined,
      selectedNodes: [
        {
          id: '5065',
          name: 'Men',
          type: 'sub-division',
        },
        {
          id: '80799',
          name: 'Pants',
          type: 'category',
        },
      ],
      webHierarchy: webHierarchyGap,
    });
  });
});
