import { gridToggleEngagementDatalayerEvent } from '../grid-buttons-engagement';
import { datalayerInstance } from '../../DataLayerInstance';
import getPageAttributes from '../../page-attributes/attributes';

jest.mock('../../page-attributes/attributes');
jest.mock('../../DataLayerInstance');

const linkFuncSpyed = jest.fn();

(datalayerInstance as jest.Mock).mockImplementation(() => ({
  link: linkFuncSpyed,
}));

(getPageAttributes as jest.Mock).mockImplementation(props => props);

describe('Datalayer/events/grid-buttons-engagements', () => {
  const props = {
    pageType: 'categoryPageType',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should send events of exception', () => {
    getPageAttributes.mockImplementationOnce(() => {
      throw new Error('mocked exception');
    });

    gridToggleEngagementDatalayerEvent(props);
    expect(linkFuncSpyed).toHaveBeenCalledWith({
      business_unit_id: undefined,
      error_message: '[CategoryPageType Grid Toggle Data Layer]: error: mocked exception',
    });
  });

  it('should handle grid-button param undefined', () => {
    gridToggleEngagementDatalayerEvent(props);
    expect(linkFuncSpyed).toHaveBeenCalledWith({
      pageType: 'categoryPageType',
      test_coe: 'PLPGridToggle',
    });
  });
});
