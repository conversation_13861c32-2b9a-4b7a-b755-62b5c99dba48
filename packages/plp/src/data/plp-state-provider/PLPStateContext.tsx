import React, { useContext } from 'react';
import { PLPStateContextProps } from './types';

export const PLPStateContext = React.createContext<PLPStateContextProps | null>(null);

export const usePLPState = (): PLPStateContextProps => {
  const context = useContext(PLPStateContext);
  if (context === null) {
    throw new Error('usePLPState must be used within a PLPStateProvider');
  }
  return context;
};
