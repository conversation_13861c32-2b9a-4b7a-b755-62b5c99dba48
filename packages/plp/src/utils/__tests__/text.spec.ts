import { toSentenceCase } from '../text';
import { decodeHtmlEntities } from './../decodeEntities';

describe('toSentenceCase', () => {
  it('should capitalize the first letter and lowercase the rest', () => {
    expect(toSentenceCase('hello world')).toBe('Hello world');
    expect(toSentenceCase('HELLO WORLD')).toBe('Hello world');
    expect(toSentenceCase('hELLO wORLD')).toBe('Hello world');
  });

  it('should handle single character strings', () => {
    expect(toSentenceCase('a')).toBe('A');
    expect(toSentenceCase('A')).toBe('A');
  });

  it('should handle empty string', () => {
    expect(toSentenceCase('')).toBe('');
  });

  it('should handle strings with only whitespace', () => {
    expect(toSentenceCase(' ')).toBe(' ');
    expect(toSentenceCase('   ')).toBe('   ');
  });

  it('should handle strings with numbers and symbols', () => {
    expect(toSentenceCase('123abc')).toBe('123abc');
    expect(toSentenceCase('!hello')).toBe('!hello');
  });

  it('should handle undefined input', () => {
    // @ts-expect-error testing default parameter
    expect(toSentenceCase()).toBe('');
  });
});

describe('decodeHtmlEntities', () => {
  it('decodes &#124; to |', () => {
    expect(decodeHtmlEntities('babyGap &#124; Sesame Street 100%')).toBe('babyGap | Sesame Street 100%');
  });

  it('decodes multiple entities', () => {
    expect(decodeHtmlEntities('Tom &amp; Jerry &#124; 50%')).toBe('Tom & Jerry | 50%');
  });

  it('returns empty string for empty input', () => {
    expect(decodeHtmlEntities('')).toBe('');
  });

  it('returns the same string if no entities', () => {
    expect(decodeHtmlEntities('plain text')).toBe('plain text');
  });
});
