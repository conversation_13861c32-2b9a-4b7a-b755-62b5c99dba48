// @ts-nocheck
'use client'

// TODO: this types need a review
import { RawFacetProps } from './adapt-facets/types';
import { FacetWithOptions } from './adapt-facet/types';

export interface DataProps {
  facets: RawFacetProps[];
}

export interface FlexFacetsAdapterProps {
  appliedFacets: Record<string, unknown>;
  facetOptions: FacetWithOptions[];
  hasTags: boolean;
}

type FacetType = 'simple' | 'complex' | 'range';

type FacetSelectionType = 'single-select' | 'multi-select' | FacetType;

export type FacetOption = {
  id: string;
  name: string;
  value?: string;
  isActive?: string;
  applied?: boolean;
};

export interface Facet {
  isActive: boolean;
  order?: number;
  type: FacetType;
  name: string;
  searchFacetId?: string;
  displayName?: string;
  selectionType?: FacetSelectionType;
  searchFacetName: string;
  searchFacetOptionGroupList?: SearchFacetOptionStyleParam;
  options?: FacetOption[];
}

export interface FacetWithConfig extends Facet {
  displayName?: string;
  facetDisplay?: string;
  facetLayout?: string;
  selectionType?: string;
  order?: number;
  hidden?: boolean;
  type: FacetType;
  isActive?: boolean;
  searchFacetName?: string;
  options: Option[];
  localizedName?: string;
}

export interface Option {
  id?: number;
  name?: string | null;
  applied?: boolean;
  options?: Option[];
}

export interface Options extends Option {
  options: Option[];
}

export interface AttributeMap {
  dimensionId: string;
  index: string;
  variantId: string;
  searchFacetOptionName2?: string;
}

export interface Sizes {
  isActive: string;
  searchFacetOptionId: string;
  variantId: string;
  variantName: string;
  sfcId: string;
  sfcName: string;
  sfcDimName: string;
  dimId: string;
  attributeMap: AttributeMap;
  id: string;
  isSelected: string;
  selected: boolean;
  name: string;
  searchFacetOptionValue: string;
  tagDisplayLabel?: string;
  description?: string;
  dimName?: string;
  searchFacetOptionName: string;
}

export interface Styles extends Array<Sizes> {
  sfcId?: string;
}

interface StyleGroups {
  id?: string;
  name: string;
  sizes: Styles;
}

export interface SizeVariantsResponse {
  id: number;
  name: string;
  styleGroups: Array<StyleGroups>;
  selected?: boolean;
}

export interface SearchFacetOptionStyle {
  searchFacetOptionGroupList: SearchFacetOptionStyleParam[];
  searchFacetOptionList: Sizes[];
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
}

export interface SearchFacetOptionStyleParam extends SearchFacetOptionStyle {
  searchFacetOptionGroupList: SearchFacetOptionStyle | SearchFacetOptionStyle[];
}

export interface SearchFacetOptionList {
  searchFacetOptionList: Sizes[];
}

type WebItemType =
  | 'division'
  | 'sub-division'
  | 'category'
  | 'trimheader'
  | 'header'; // TODO: Get rest of WebItemTypes

export interface WebItem {
  brandCode: string;
  children: Array<WebItem>;
  customUrl?: string;
  hasSubDivision: boolean;
  hidden: boolean;
  id: string;
  link: string;
  name: string;
  parents: WebItem.id[];
  selected: boolean;
  type: WebItemType;
}

export interface OptionAttributeMap {
  index: string;
  variantId: string;
  dimensionId: string;
  searchFacetOptionName2?: string;
}

export interface AdaptedFacetStyle {
  id: number;
  name: string;
  localeName: string;
  options: AdaptedFacetDimension[];
}

export interface AdaptedFacetVariant {
  id: number;
  name: string;
  localeName: string;
  options: FacetOption[];
}

export interface AdaptedFacetDimension {
  id: number;
  name: string;
  localeName: string;
  options: AdaptedFacetVariant[];
}

export type SizeFacet = FacetWithConfig & {
  sizeVariants: SizeVariantsResponse[],
};

export type FacetConfig = {
  displayName: string,
  type: FacetType,
  facetDisplay?: string,
  facetLayout?: string,
  selectionType?: string,
  order?: number,
  hidden?: boolean,
};

export type SizeFacetWithConfig = SizeFacet & FacetConfig;

export type OldFacet = {
  isActive: string;
  searchFacetId?: string;
  searchFacetName?: string;
  searchFacetOptionGroupList: OldFacetStyle[];
  sizeVariants?: SizeVariantsResponse[];
};

export interface OldFacetOption {
  isActive: string;
  isSelected: string;
  searchFacetOptionId: string;
  searchFacetOptionName: string;
  searchFacetOptionValue: string;
  parentSearchFacetOptionGroupId?: string;
  attributeMap: OptionAttributeMap;
}

export interface OldFacetDimension {
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
  parentSearchFacetOptionGroupId: string;
  searchFacetOptionGroupList: OldFacetVariant[];
}

export interface OldFacetVariant {
  searchFacetOptionList: OldFacetOption[];
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
  parentSearchFacetOptionGroupId: string;
}

export interface OldFacetStyle {
  searchFacetOptionGroupId: string;
  searchFacetOptionGroupName: string;
  searchFacetOptionGroupList?: OldFacetDimension | OldFacetDimension[];
}

export interface FlattenedSize {
  sizeOptions?: Array<Sizes>;
  selectedSizes?: Array<Sizes>;
}

export type FacetWithApplied = {
  facet: Facet;
  appliedValues: FacetOption[];
};

export interface PriceFacet extends Facet {
  range: PriceRange;
  appliedRange: PriceRange;
}

export interface PriceRange {
  min: number | string;
  max: number | string | null;
}
