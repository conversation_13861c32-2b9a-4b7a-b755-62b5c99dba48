// @ts-nocheck
import { adaptSizeFacet, adaptSizeFacetToList } from './adapt-size-facet';
import { SizeVariant } from '../types';

import atData from '../fixtures/athleta-size-facet-variants.json';
import atFormattedData from '../fixtures/athleta-formatted-size-facet-dim.json';
import atSizeFacetListData from '../fixtures/athleta-size-facet-list-data.json';
import brData from '../fixtures/br-size-facet-variants.json';
import brFormattedData from '../fixtures/br-formatted-size-facet-dim.json';
import brSizeFacetListData from '../fixtures/br-size-facet-list-data.json';
import gapData from '../fixtures/gap-size-facet-variants.json';
import gapFormattedData from '../fixtures/gap-formatted-size-facet-dim.json';
import gapSizeFacetListData from '../fixtures/gap-size-facet-list-data.json';
import gapBabyData from '../fixtures/gap-baby-size-facet-variants.json';
import gapBabyFormattedData from '../fixtures/gap-baby-formatted-size-facet-dim.json';
import gapBabySizeFacetListData from '../fixtures/gap-baby-size-facet-list-data.json';

describe('Adapt Simple Facet', () => {
  it('should adapt GAP size facet data for quick filters', () => {
    expect(adaptSizeFacet(gapData as SizeVariant[])).toEqual(gapFormattedData);
  });

  it('should adapt Athleta size facet data for quick filter', () => {
    expect(adaptSizeFacet(atData as SizeVariant[])).toEqual(atFormattedData);
  });

  it('should adapt Banana Republic size facet data for quick filter', () => {
    expect(adaptSizeFacet(brData as SizeVariant[])).toEqual(brFormattedData);
  });

  it('should adapt GAP Baby size facet data for quick filter', () => {
    expect(adaptSizeFacet(gapBabyData as SizeVariant[])).toEqual(
      gapBabyFormattedData
    );
  });
});

describe('Adapt Formatted Simple Facet object to list', () => {
  it('should adapt GAP size facet data quick filters object into an iterable list', () => {
    expect(adaptSizeFacetToList(gapFormattedData)).toEqual(
      gapSizeFacetListData
    );
  });

  it('should adapt Athleta size facet data quick filters object into an iterable list', () => {
    expect(adaptSizeFacetToList(atFormattedData)).toEqual(atSizeFacetListData);
  });

  it('should adapt Banana Republic size facet data quick filters object into an iterable list', () => {
    expect(adaptSizeFacetToList(brFormattedData)).toEqual(brSizeFacetListData);
  });

  it('should adapt GAP Baby size facet data quick filters object into an iterable list', () => {
    expect(adaptSizeFacetToList(gapBabyFormattedData)).toEqual(
      gapBabySizeFacetListData
    );
  });
});
