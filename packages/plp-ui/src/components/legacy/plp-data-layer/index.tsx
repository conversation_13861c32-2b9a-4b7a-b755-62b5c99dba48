// @ts-nocheck
'use client'

import React, { createContext, useContext } from "react";
import { onFacetEngagement, onProductClick } from "./plpDataLayer";
import { ProductInfoState } from "@ecom-next/plp-ui/legacy/ps-data-provider";
import { DataLayer } from "@ecom-next/core/legacy/app-state-provider/types";

const productClickEngagement = (product: ProductInfoState) => {
  onProductClick("product-image-click", product, false, false);
};

const facetEngagement = (datalayer: DataLayer, facets: any) => {
  onFacetEngagement(datalayer, facets);
};

export const PlpDataLayerContext = createContext<{
  productClickEngagement: (product: ProductInfoState) => void;
  facetEngagement: (datalayer: DataLayer, facets: any) => void;
}>({
  productClickEngagement: () => {},
  facetEngagement: () => {},
});

export const PlpDataLayerProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  return (
    <PlpDataLayerContext.Provider
      value={{ productClickEngagement, facetEngagement }}
    >
      {children}
    </PlpDataLayerContext.Provider>
  );
};

export const usePLPDataLayerContext = () => {
  return useContext(PlpDataLayerContext);
};
