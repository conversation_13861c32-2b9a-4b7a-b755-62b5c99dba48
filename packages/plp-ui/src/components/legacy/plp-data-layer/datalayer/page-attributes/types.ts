// @ts-nocheck
'use client'

import { SmartCrossLinks } from '../../types';

export type SelectedNodes = Array<{ type: string; name: string }>;

export interface CategoryPageAttributesProps {
  categoryName: string;
  businessUnitId: string;
  subcategoryName: string;
  params: string[];
  brandName: string;
  abbrNameForTealium: string;
  selectedNodes: SelectedNodes;
  pageType: string;
  smartCrossLinks: SmartCrossLinks;
}

export interface PageAttributes {
  brand_short_name: string;
  browse_refine_type: [];
  browse_refine_value: [];
  business_unit_id: number;
  category_name?: string;
  channel: string;
  page_name: string;
  page_type: string;
  br_iuid: string;
  br_related_rid: string;
  subcategory_name?: string;
  pre_facet_engagement?: string | null;
  grid_layout_type?: string;
  event_name?: string;
}
