// @ts-nocheck
'use client';

import React, { useContext, useState, lazy, Suspense } from 'react';
import { withToggleManager } from '@ecom-next/core/legacy/toggle-manager';
import { SearchBox } from './SearchBox';
import {
  features,
  SEARCH_PLACEHOLDER,
  useKeyboardNavigation,
  useSearchExperiments,
  TEALIUM_EVENT,
  useNewGapFontColorHook,
  useIsBrWhiteBackground,
  useGapRedesign2024,
} from '@ecom-next/plp-ui/legacy/utils';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { CSSObject, useTheme } from '@ecom-next/core/react-stitch';
import { DesktopSearchProps } from './types';
import { RecentSearchesProps, useRecentSearches } from '@ecom-next/plp-ui/legacy/recent-searches';
import { AutosuggestProps, useSuggestions } from '@ecom-next/plp-ui/legacy/autosuggest';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { getIndexStyles, useTopSearchTerms } from '@ecom-next/plp-ui/legacy/search-terms';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { useFeature } from '@ecom-next/plp-ui/legacy/experiments';
import { TopSearchTermsProps } from '@ecom-next/plp-ui/legacy/top-search-terms';
import { SearchKeywordAnimationType } from '@ecom-next/plp-ui/legacy/search-keyword-animation';
import { useFindState } from '@ecom-next/plp-ui/legacy/find-state-provider';

const searchBoxToggleIdentifier = 'SearchBoxToggleIdentifier';

const containerStyles: CSSObject = {
  position: 'relative',
  width: '100%',
};

const NullComponent = () => null;

const AutosuggestLazy = lazy(() =>
  import('@ecom-next/plp-ui/legacy/autosuggest').then(module => {
    return { default: module.Autosuggest };
  })
);
const Autosuggest = (props: AutosuggestProps) => (
  <Suspense fallback={<NullComponent />}>
    <AutosuggestLazy {...props} />
  </Suspense>
);

const RecentSearchesLazy = lazy(() =>
  import('@ecom-next/plp-ui/legacy/recent-searches').then(module => {
    return { default: module.RecentSearches };
  })
);
const RecentSearches = (props: RecentSearchesProps) => (
  <Suspense fallback={<NullComponent />}>
    <RecentSearchesLazy {...props} />
  </Suspense>
);

const TopSearchTermsLazy = lazy(() =>
  import('@ecom-next/plp-ui/legacy/top-search-terms').then(module => {
    return { default: module.TopSearchTerms };
  })
);
const TopSearchTerms = (props: TopSearchTermsProps) => (
  <Suspense fallback={<NullComponent />}>
    <TopSearchTermsLazy {...props} />
  </Suspense>
);

const SearchKeywordAnimationLazy = lazy(() =>
  import('@ecom-next/plp-ui/legacy/search-keyword-animation').then(module => {
    return { default: module.SearchKeywordAnimation };
  })
);

const SearchKeywordAnimation = (props: SearchKeywordAnimationType) => (
  <Suspense fallback={<NullComponent />}>
    <SearchKeywordAnimationLazy {...props} />
  </Suspense>
);

const checkShouldShowRecentAndTopSearches = (open: boolean, searchText: string, isFocusOnSearch: boolean, forceRecentSearchOpen: boolean): boolean =>
  open && searchText === '' && (isFocusOnSearch || forceRecentSearchOpen);

const useSearchAnimation = (isSearchAnimationExperimentActive: boolean): { isSearchAnimationActive: boolean } => {
  const isSearchAnimationActive = useFeature(features.SEARCH_KEYWORD_ANIMATION);

  return {
    isSearchAnimationActive: isSearchAnimationActive && isSearchAnimationExperimentActive,
  };
};

const checkShouldRenderSearchKeywordAnimation = (isSearchAnimationEnabled: boolean, searchText: string): boolean =>
  isSearchAnimationEnabled && searchText === '';

const getActiveDescendantId = (selectedSuggestionIndex: number): string => (selectedSuggestionIndex !== -1 ? `suggestedOption${selectedSuggestionIndex}` : '');

const getSearchBoxPlaceholder = (isSearchAnimationEnabled: boolean, placeholder: string): string => (!isSearchAnimationEnabled ? placeholder : '');

const Search = ({ className, toggleOpen, open, searchText: searchTextProp = '', forceRecentSearchOpen = false }: DesktopSearchProps): JSX.Element => {
  const { localize } = useLocalize();
  const placeholder = localize(SEARCH_PLACEHOLDER);
  const [searchText, setSearchText] = useState(searchTextProp);
  const { locale, datalayer } = useAppState();
  const { abSeg, errorLogger, keywordsFromWCD, useStickyStatus, topSearchTerms } = useFindState();

  const theme = useTheme();
  const isGapAndFontColorUpdateEnabled = useNewGapFontColorHook();

  const { minWidth } = useContext(BreakpointContext);
  const isDesktop = minWidth(XLARGE);

  const { isVisualSearchActive, isRecentSearchesActive, isSearchAnimationExperimentActive } = useSearchExperiments(isDesktop, abSeg);

  const { addRecentSearch, getRecentSearches, clearRecentSearch } = useRecentSearches();

  const isTopSearchTermsEnabled = useTopSearchTerms(isDesktop);

  const topSearchTermsList = isTopSearchTermsEnabled ? topSearchTerms : [];
  const isBrPdpRedesignEnabled = useFeature(features.PDP_REDESIGN_2022);
  const isBRColor2023FFEnabled = useFeature(features.BR_COLOR_2023);
  const isATRedesignEnabled = useFeature(features.AT_REDESIGN_2023);
  const isBRWhiteBackgroundEnabled = useIsBrWhiteBackground();
  const isGapRedesign2024Enabled = useGapRedesign2024();

  const recentSearchesList = getRecentSearches()[locale];

  const {
    suggestions,
    autosuggestOpen,
    isSuggestionHighlighted,
    cycleSuggestions,
    selectedSuggestionIndex,
    setSelectedSuggestionIndex,
    visualSearchProducts,
    departmentsList,
  } = useSuggestions(searchText, open, isVisualSearchActive, recentSearchesList, isRecentSearchesActive, topSearchTermsList, isDesktop);

  const handleClearAllClick = (): void => {
    clearRecentSearch(locale);
    setSelectedSuggestionIndex(-1);
  };

  const { isFocusOnSearch, startingIndexForTopSearchTerms, handleSearch, handleKeyEvent, hasRecentSearches, hasTopSearchTerms, setIsFocusOnSearch } =
    useKeyboardNavigation(
      isDesktop,
      searchText,
      suggestions,
      topSearchTerms,
      departmentsList,
      recentSearchesList,
      visualSearchProducts,
      selectedSuggestionIndex,
      isSuggestionHighlighted,
      isTopSearchTermsEnabled,
      abSeg,
      cycleSuggestions,
      handleClearAllClick,
      setSelectedSuggestionIndex,
      addRecentSearch
    );

  const recentOrTopSearchesListsExist =
    hasRecentSearches(isRecentSearchesActive, recentSearchesList) || hasTopSearchTerms(isTopSearchTermsEnabled, topSearchTerms);

  const { containerStyles: searchTermsContainer } = getIndexStyles(
    theme,
    true,
    false,
    isGapAndFontColorUpdateEnabled,
    isTopSearchTermsEnabled,
    isBrPdpRedesignEnabled,
    isBRColor2023FFEnabled,
    isBRWhiteBackgroundEnabled,
    isGapRedesign2024Enabled
  );

  const handleSearchTextChange: React.ChangeEventHandler<HTMLInputElement> = event => {
    const searchText = event.target.value;
    !open && toggleOpen();
    setSearchText(searchText);
    setSelectedSuggestionIndex(-1);
  };

  const { isSearchAnimationActive } = useSearchAnimation(isSearchAnimationExperimentActive);

  const shouldShowRecentAndTopSearches = checkShouldShowRecentAndTopSearches(open, searchText, isFocusOnSearch, forceRecentSearchOpen);
  const searchBoxOpen = autosuggestOpen || shouldShowRecentAndTopSearches;

  const searchBoxPlaceholder = getSearchBoxPlaceholder(isSearchAnimationActive, placeholder);

  const shouldRenderSearchKeywordAnimation = checkShouldRenderSearchKeywordAnimation(isSearchAnimationActive, searchText);

  const closeAutosuggest = (): void => {
    open && toggleOpen();
  };

  const handleDepartmentClick = (): void => {
    open && toggleOpen();
  };

  const activeDescendantId = getActiveDescendantId(selectedSuggestionIndex);

  const handleSearchFormFocus = (): void => {
    datalayer.build().then(data => {
      const { channel, page_name, page_type, business_unit_id, recognition_status } = data;
      datalayer.link({
        channel,
        page_name,
        page_type,
        business_unit_id,
        event_name: TEALIUM_EVENT.SEARCH_BAR_CLICK,
        recognition_status: recognition_status,
      });
    });
    !open && toggleOpen();
    setIsFocusOnSearch(true);
  };

  return (
    <div className={className} css={containerStyles} id={searchBoxToggleIdentifier}>
      {shouldRenderSearchKeywordAnimation && (
        <SearchKeywordAnimation
          abSeg={abSeg}
          errorLogger={errorLogger}
          isFocused={open}
          keywordsFromWCD={keywordsFromWCD}
          placeholder={placeholder}
          recentSearchesList={recentSearchesList}
          topSearchTerms={topSearchTerms}
          useStickyStatus={useStickyStatus}
        />
      )}
      <SearchBox
        activeDescendantId={activeDescendantId}
        focused={open}
        handleChange={handleSearchTextChange}
        handleFocus={handleSearchFormFocus}
        handleKeyDown={handleKeyEvent}
        handleSearch={handleSearch}
        isATRedesignEnabled={isATRedesignEnabled}
        isSearchAnimationEnabled={isSearchAnimationExperimentActive}
        placeholder={searchBoxPlaceholder}
        searchBoxOpen={searchBoxOpen}
        searchText={searchText}
      />
      {checkShouldShowRecentAndTopSearches(open, searchText, isFocusOnSearch, forceRecentSearchOpen) && recentOrTopSearchesListsExist && (
        <div css={searchTermsContainer} data-testid='search-terms-container'>
          <RecentSearches
            clearRecentSearches={handleClearAllClick}
            displayRecentSearches={isRecentSearchesActive}
            recentSearchesList={recentSearchesList}
            selectedSuggestionIndex={selectedSuggestionIndex}
          />
          <TopSearchTerms
            displayTopSearchTerms={isTopSearchTermsEnabled}
            selectedSuggestionIndex={selectedSuggestionIndex}
            startingIndexForTopSearchTerms={startingIndexForTopSearchTerms}
          />
        </div>
      )}
      {autosuggestOpen && (
        <Autosuggest
          closeAutosuggest={closeAutosuggest}
          departmentsList={departmentsList}
          focused={open}
          onDepartmentClick={handleDepartmentClick}
          searchText={searchText}
          selectedSuggestionIndex={selectedSuggestionIndex}
          suggestionsList={suggestions}
          visualSearchProducts={visualSearchProducts}
        />
      )}
    </div>
  );
};

export const DesktopSearchBar = withToggleManager({
  Component: Search,
  containerID: searchBoxToggleIdentifier,
});

export { Search as SearchWithoutToggleManager };
