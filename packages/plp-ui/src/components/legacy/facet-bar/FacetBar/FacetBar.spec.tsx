// @ts-nocheck
import React from 'react';
import { mount, MountOptions, act } from "test-utils";
import { BrandInfoProvider } from '@ecom-next/sitewide/brand-info-provider';
import { FacetBar } from './';
import facets from './fixtures/data/facet-data';
import RangeFacet from '../Facet/components/RangeFacet';
import SizeFacet from '../Facet/components/SizeFacet';
import SimpleFacet from '../Facet/components/SimpleFacet';
import {
  FACET_REFERENCE_FACET_LABEL_COLOR,
  FACET_REFERENCE_FACET_LABEL_DEPARTMENT,
  FACET_REFERENCE_FACET_LABEL_PRICE,
  FACET_REFERENCE_FACET_LABEL_SIZE,
  FACET_REFERENCE_FACET_LABEL_STYLE,
} from './translations';
import { Mock, vi } from 'vitest';
import { PLPStateProvider } from '@ecom-next/plp-ui/legacy/plp-state-provider';
import { FacetBarProps } from '../types';

jest.mock('../Facet/components/RangeFacet');
(RangeFacet as Mock).mockReturnValue(
  <div className="fake-range-facet">Range Facet</div>
);

jest.mock('../Facet/components/SizeFacet');
(SizeFacet as Mock).mockReturnValue(
  <div className="fake-size-facet">Size Facet</div>
);

jest.mock('../Facet/components/SimpleFacet');
(SimpleFacet as Mock).mockReturnValue(
  <div className="fake-simple-facet">Simple Facet</div>
);

describe('<FacetBar />', () => {
  const mountFacetBarWithProviders = ({ breakpoint, ...props }: MountOptions & Partial<FacetBarProps>) =>
    mount(
      <BrandInfoProvider abbrBrand="gap" brand="gap" brandCode={1}>
        <PLPStateProvider abSeg={{ at189: 'a' }}>
        <FacetBar appliedFacets={{}} facets={[]} {...props} />
        </PLPStateProvider>
      </BrandInfoProvider>,
      {
        breakpoint,
        localization: {
          [FACET_REFERENCE_FACET_LABEL_DEPARTMENT]: 'Department',
          [FACET_REFERENCE_FACET_LABEL_STYLE]: 'Category',
          [FACET_REFERENCE_FACET_LABEL_COLOR]: 'Color',
          [FACET_REFERENCE_FACET_LABEL_PRICE]: 'Price',
          [FACET_REFERENCE_FACET_LABEL_SIZE]: 'Size',
        },
      }
    );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    const wrapper = mountFacetBarWithProviders({
      facets,
      appliedFacets: {},
      position: 'scrollable',
      breakpoint: 'x-large',
    });

    expect(wrapper.debug()).toMatchSnapshot();
  });

  it('renders correctly on mobile', () => {
    const wrapper = mountFacetBarWithProviders({
      facets,
      appliedFacets: {},
      position: 'scrollable',
      breakpoint: 'small',
    });

    expect(wrapper.debug()).toMatchSnapshot();
  });
});
