// @ts-nocheck
const { expect } = require('chai');
const rewire = require('rewire');
const {createSandbox, spy, restore} = require('sinon');

const {
  FORCED_FEATURE_FLAGS,
  ENABLE_FEATURE_FLAGS,
  DISABLE_FEATURE_FLAGS,
} = require('./constants')

describe('server side behavior', () => {
  const FAKE_OPT_URL = "";

  describe('when feature flag already configured', () => {
    it('should print error message', async done => {
      const sandbox = createSandbox();
      const errorStub = sandbox.stub();
      const loggerStub = { error: errorStub };
      const cronJobStub = sandbox.stub().returns({ start: () => { console.log('fake start'); } });

      const featureFlags = rewire('.');
      featureFlags.__set__('cron', { "CronJob": cronJobStub });
      featureFlags.__set__('optimizelyInstance', "anything");
      featureFlags.__set__('loadDataFile', () => Promise.resolve({ 'feature-flag': true }));
      try {
        await featureFlags.initialize(FAKE_OPT_URL, { logger: loggerStub });
      } catch (err) {
        expect(errorStub.called, 'error stub should be called').to.be.true;
        expect(err.message).to.equal('Start only needs to be called once; usage: let featureFlags = require("feature-flags-helper");');
      } finally {
        done();
      }
    });
  });

  describe('when no configuration object provided', () => {
    describe('startUpdateDataFileJob', () => {
      it('should create cron job that runs every second', async () => {
        const sandbox = createSandbox();
        const cronJobStub = sandbox.stub().returns({ start: () => { console.log('fake start'); } });

        const featureFlags = rewire('.');
        featureFlags.__set__('cron', { "CronJob": cronJobStub });
        featureFlags.__set__('loadDataFile', () => Promise.resolve({ 'feature-flag': true }));

        await featureFlags.initialize(FAKE_OPT_URL, console);
        expect(cronJobStub.calledOnce).to.be.true;
        expect(cronJobStub.args[0][0].cronTime).to.equal("* * * * *");
      });
    });
  });

  describe('when configuration object is provided', () => {
    const options = {
      logger: undefined,
      cronTime: "every 5 minutes",
    }
    describe('and initialise is called ', () => {
      it('should create cron job with the correct cronTime', async () => {
        const sandbox = createSandbox();
        const cronJobStub = sandbox.stub().returns({ start: () => { console.log('fake start'); } });

        const featureFlags = rewire('.');
        featureFlags.__set__('cron', { "CronJob": cronJobStub });
        featureFlags.__set__('loadDataFile', () => Promise.resolve({ 'feature-flag': true }));

        await featureFlags.initialize(FAKE_OPT_URL, options);
        expect(cronJobStub.calledOnce).to.be.true;
        expect(cronJobStub.args[0][0].cronTime).to.equal(options.cronTime);
      });
    });
  });

  describe(`${FORCED_FEATURE_FLAGS} query parameter`, () => {
    describe(`when a ${FORCED_FEATURE_FLAGS} parameter is present`, () => {
      it('should return an object with the configured feature flags enabled', async () => {
        const featureName = 'example-feature-gap-us';

        const requestQuery = {
          [FORCED_FEATURE_FLAGS]: `["${featureName}"]`,
          anotherThing: 'testValue',
          lastThing: 'finalValue',
        };

        const featureFlags = rewire('.');
        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});

        const enabledFeatures = featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(enabledFeatures[featureName]).to.be.true;
      });

      it('should log when an invalid query param is present', () => {
        const consoleSpy = spy(console, 'error')
        const featureFlags = rewire('.');
        
        const requestQuery = {
          [FORCED_FEATURE_FLAGS]: `"invalid"`,
          anotherThing: 'testValue',
          lastThing: 'finalValue',
        };

        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});
        featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(consoleSpy.called, `Invalid ${FORCED_FEATURE_FLAGS} value: "invalid"`).to.be.true;
        restore()
      })
    })
  });

  describe(`${ENABLE_FEATURE_FLAGS} query parameter`, () => {
    describe(`when a ${ENABLE_FEATURE_FLAGS} parameter is present`, () => {
      it('should return an object with the configured feature flags enabled', async () => {
        const featureName = 'example-feature-gap-us';

        const requestQuery = {
          [ENABLE_FEATURE_FLAGS]: `["${featureName}"]`,
          anotherThing: 'testValue',
          lastThing: 'finalValue',
        };

        const featureFlags = rewire('.');
        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});

        const enabledFeatures = featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(enabledFeatures[featureName]).to.be.true;
      });

      it('should log when an invalid query param is present', () => {
        const consoleSpy = spy(console, 'error')
        const featureFlags = rewire('.');
        
        const requestQuery = {
          [ENABLE_FEATURE_FLAGS]: `"invalid"`,
          anotherThing: 'testValue',
          lastThing: 'finalValue',
        };

        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});
        featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(consoleSpy.called, `Invalid ${ENABLE_FEATURE_FLAGS} value: "invalid"`).to.be.true;
        restore()
      })
    })
  });

  describe(`${DISABLE_FEATURE_FLAGS} query parameter`, () => {
    describe('when a disable feature flags query parameter is present', () => {
      it('should return an object with the configured feature flags disabled', async () => {
        const featureName = 'example-feature-gap-us';

        const requestQuery = {
          [DISABLE_FEATURE_FLAGS]: `["${featureName}"]`,
          anotherThing: 'testValue',
          lastThing: 'finalValue',
        };

        const featureFlags = rewire('.');
        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});

        const disabledFeatures = featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(disabledFeatures[featureName]).to.be.false;
      });

      it('should log when an invalid query param is present', () => {
        const consoleSpy = spy(console, 'error')
        const featureFlags = rewire('.');
        
        const requestQuery = {
          [DISABLE_FEATURE_FLAGS]: `"invalid"`,
          anotherThing: 'testValue',
          lastThing: 'finalValue',
        };

        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});
        featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(consoleSpy.called, `Invalid ${DISABLE_FEATURE_FLAGS} value: "invalid"`).to.be.true;
        restore()
      })
    });
    describe('when a disabled feature flags query parameter is not present', () => {
      it('should return an empty object', async () => {

        const requestQuery = undefined;

        const featureFlags = rewire('.');
        featureFlags.__set__('optimizelyInstance', {getEnabledFeatures: () => []});

        const disabledFeatures = featureFlags.getEnabledFeatures(undefined, undefined, requestQuery);

        expect(Object.keys(disabledFeatures).length).to.equal(0);
      });
    })
  });

  describe('getFeatureVariables method test',() => {
    it('should process and return  JSON type data when feature variable type is JSON',() => {
      const variableSchema = [
        {
          "flag": "cat-model-toggle-us",
          "variables": [
            {
              "type": "JSON",
              "name": "sizeGroups",
            },
          ],
        },
      ];
      const sizeGroupsData = {
        "gap": {
          "S": {
            "placement": "p1",
            "numeric": "4 (27)",
            "alpha": "model_toggle.size_model_small",
          },
        },
      };
      const featureFlags = rewire('.');
      featureFlags.__set__('optimizelyInstance', {getFeatureVariable: () => sizeGroupsData});
      const featureVariablesData = featureFlags.getFeatureVariables('87814ae3-b280-4963-b27a-f6b54e6ecd29',
        {
          'cat-model-toggle-us':true,
        },
        variableSchema
      );
      expect(featureVariablesData['cat-model-toggle-us']).not.to.be.undefined;
      expect(featureVariablesData['cat-model-toggle-us'].sizeGroups).not.to.be.undefined;
      expect(featureVariablesData['cat-model-toggle-us'].sizeGroups).to.equal(sizeGroupsData);
    });
    it('should parse the JSON string ',()=>{
      const variableSchema = [
        {
          "flag": "cat-model-toggle-us",
          "variables": [
            {
              "type": "JSON",
              "name": "sizeGroups",
            },
          ],
        },
      ];
      const sizeGroupsData = '{"gap":{}}';
      const featureFlags = rewire('.');
      featureFlags.__set__('optimizelyInstance', {getFeatureVariable: () => sizeGroupsData});
      const featureVariablesData = featureFlags.getFeatureVariables('87814ae3-b280-4963-b27a-f6b54e6ecd29',
        {
          'cat-model-toggle-us':true,
        },
        variableSchema
      );
      expect(featureVariablesData['cat-model-toggle-us']).not.to.be.undefined;
      expect(featureVariablesData['cat-model-toggle-us'].sizeGroups).not.to.be.undefined;
      expect(typeof featureVariablesData['cat-model-toggle-us'].sizeGroups).to.equal('object');
    });
  });
});
