// @ts-nocheck
import { storeScrollPosition } from '.';
import { SCROLLED_CID, SCROLL_Y_POSITION } from '../../utils/constants';

const event = { preventDefault: (): void => {} };
const pcid = '1234';

describe('storeScrollPosition()', () => {
  it('stores the scrolledCid and scrollYPosition in session storage', () => {
    // @ts-ignore
    storeScrollPosition(event, pcid);

    expect(window.sessionStorage.getItem(SCROLLED_CID)).toEqual('1234');
    expect(window.sessionStorage.getItem(SCROLL_Y_POSITION)).toEqual('0');
  });
});
