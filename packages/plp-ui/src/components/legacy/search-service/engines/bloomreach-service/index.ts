// @ts-nocheck
'use client'

// @flow
import { bloomreachAutosuggest } from './autosuggest/bloomreach-autosuggest';
import { fetchBloomreach } from './search-service/bloomreach-search';
import StorageHelper from '../../helpers/storage-helper';
import { createEngine } from '../../model/search-engine';
import WindowHelper from '../../helpers/window-helper';

const storage = global.window ? new StorageHelper(global.window.sessionStorage) : {};

const getBloomreachUid = () => {
  const bloomreachUid = WindowHelper.getCookie('_br_uid_2');

  if (!bloomreachUid) {
    console.warn('bloomreachUid::Unable to set bloomreachUid from cookie');
  }

  return bloomreachUid || '';
};

const callBloomreachAutosuggestWithCache = (
  resolve,
  reject,
  searchText,
  brand = '',
  bloomreachUid,
  locale,
  configuration,
) => {
  bloomreachAutosuggest(searchText, brand.toUpperCase(), bloomreachUid, locale, configuration)
    .then((suggestions) => {
      if (storage.isStorageSupported()) {
        storage.setItem(`autosuggest:${searchText}`, JSON.stringify(suggestions));
      }
      resolve(suggestions);
    })
    .catch(reject);
};

type StringMap = { [key: string]: string };
type EngineFeatureConfiguration = {
  engineName: string,
  url: string,
  apikey?: string,
};
type BloomreachSearchOptions = {
  abbrBrand: string,
  locale: string,
  enabledFeatures?: StringMap,
  engineConfig: {
    [key: string]: EngineFeatureConfiguration,
  },
  abSeg?: {[key: string]: string}
};

export const getSearchResults = (
  facetQueryParams: {},
  searchText: string,
  searchOptions: BloomreachSearchOptions,
) => {
  const bloomreachUid = getBloomreachUid();
  const { abbrBrand, locale, enabledFeatures, engineConfig, abSeg } = searchOptions;
  const configuration = abSeg
    ? { enabledFeatures, engineConfig, abSeg }
    : { enabledFeatures, engineConfig };
  return fetchBloomreach(
    facetQueryParams,
    abbrBrand,
    searchText,
    locale,
    bloomreachUid,
    configuration,
  );
};

export const getSuggestions = (
  searchText: string,
  autosuggestOptions: BloomreachSearchOptions,
): Promise<[string]> =>
  new Promise((resolve, reject) => {
    const searchTerm = searchText.trim();
    const bloomreachUid = getBloomreachUid();
    if (!searchTerm) {
      resolve([]);
    }
    if (!storage.isStorageSupported() || !storage.hasKey(`autosuggest:${searchTerm}`)) {
      const configuration = {
        engineConfig: autosuggestOptions.engineConfig,
        enabledFeatures: autosuggestOptions.enabledFeatures,
      };
      callBloomreachAutosuggestWithCache(
        resolve,
        reject,
        searchTerm,
        autosuggestOptions.abbrBrand,
        bloomreachUid,
        autosuggestOptions.locale,
        configuration,
      );
    } else {
      resolve(JSON.parse(storage.getItem(`autosuggest:${searchTerm}`)));
    }
  });

export const engine = createEngine('BloomReach', getSearchResults, getSuggestions);
