// @ts-nocheck
'use client'

import { reporting, onPerceivedRender } from '@ecom-next/core/reporting';
import { HighPriorityEvent } from '../../types';

const cat1ImageLoaded = async (
  highPriorityEvents: Array<HighPriorityEvent>
): Promise<void> => {
  const cat1LoadedEvent = highPriorityEvents.find(
    ({ name }) => name === 'cat1ImageLoaded'
  );
  await onPerceivedRender()
  // @ts-expect-error
  if (window.gap && !window.gap?.isCat1ImageLoadedSent && cat1LoadedEvent) {
    // @ts-expect-error
    window.gap.isCat1ImageLoadedSent = true;
    reporting('cat1ImageLoaded', 'cat1ImageVersion', 'componentMode');
    cat1LoadedEvent.deferred.resolve('cat1ImageLoaded');
  }
};

export default cat1ImageLoaded;
