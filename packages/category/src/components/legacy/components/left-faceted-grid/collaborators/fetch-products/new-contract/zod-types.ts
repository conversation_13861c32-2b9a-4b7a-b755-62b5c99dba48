// @ts-nocheck
import { ColorSwatchProps } from '@ecom-next/core/legacy/color-swatch';
import z from 'zod';
import { ParsedPagination, PriceData, AdaptedProduct, SubcategoryArray, AdaptedResponse } from './types';
import { FacetsAdapterResultSchema } from '../../../adapters/zod-types';

const ParsedPaginationSchema: z.ZodType<ParsedPagination> = z.object({
  pageNumberRequested: z.number(),
  pageNumberTotal: z.number(),
  pageSize: z.number(),
});

const PriceDataSchema: z.ZodType<PriceData> = z.object({
  currentMinPrice: z.number(),
  currentMaxPrice: z.number(),
  regularMinPrice: z.number(),
  regularMaxPrice: z.number(),
  minPercentageOff: z.number().optional(),
  maxPercentageOff: z.number().optional(),
  priceType: z.number().optional(),
  localizedRegularMaxPrice: z.string().optional(),
});

const avImagesSchema = z.object({
  av1QuicklookImagePath: z.string().optional(),
  av2QuicklookImagePath: z.string().optional(),
  av3QuicklookImagePath: z.string().optional(),
  av4QuicklookImagePath: z.string().optional(),
  av5QuicklookImagePath: z.string().optional(),
  av6QuicklookImagePath: z.string().optional(),
  av9QuicklookImagePath: z.string().optional(),
  p1QuicklookImagePath: z.string().optional(),
}).transform((obj) => ({ 
  av1QuicklookImagePath: obj.av1QuicklookImagePath,
  av2QuicklookImagePath: obj.av2QuicklookImagePath,
  av3QuicklookImagePath: obj.av3QuicklookImagePath,
  av4QuicklookImagePath: obj.av4QuicklookImagePath,
  av5QuicklookImagePath: obj.av5QuicklookImagePath,
  av6QuicklookImagePath: obj.av6QuicklookImagePath,
  av9QuicklookImagePath: obj.av9QuicklookImagePath,
  p1QuicklookImagePath: obj.p1QuicklookImagePath,
}));

const pristineImagesSchema = z.object({
  pristine1ImagePath: z.string().optional(),
}).transform((obj) => ({
  pristine1ImagePath: obj.pristine1ImagePath,
}));

const zoomImagesSchema = z.object({
  av1ZoomImagePath: z.string(),
  p01ZoomImagePath: z.string(),
});

export const AdaptedProductSchema: z.ZodType<AdaptedProduct> = z.object({
  altText: z.string(),
  avImages: avImagesSchema,
  marketingFlag: z.object({
    marketingFlagName: z.string(),
    badgingName: z.string()
  }),
  name: z.string(),
  outOfStock: z.boolean(),
  categoryLargeImage: z.string(),
  quicklookImage: z.string(),
  personalizationTrackingParams: z.string().optional(),
  price: PriceDataSchema,
  pristineImages: pristineImagesSchema,
  productID: z.string(),
  productURL: z.string(),
  reviewCount: z.string().optional(),
  reviewScore: z.string().optional(),
  swatchesEnabledPrice: PriceDataSchema,
  swatchesProps: z.any(),
  url: z.string(),
  vid: z.string(),
  zoomImages: zoomImagesSchema,
})

const SubcategoryArraySchema: z.ZodType<SubcategoryArray> = z.object({
  heading: z.string(),
  cid: z.string(),
  products: z.array(AdaptedProductSchema.nullable()).optional(),
});

export const AdaptedResponseSchema: z.ZodType<AdaptedResponse> = z.object({
  gridData: z.array(SubcategoryArraySchema),
  paginator: ParsedPaginationSchema,
  facetData: FacetsAdapterResultSchema,
  totalItemCount: z.number(),
  });
