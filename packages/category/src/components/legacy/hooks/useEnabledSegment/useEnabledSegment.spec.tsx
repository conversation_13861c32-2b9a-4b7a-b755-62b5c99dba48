// @ts-nocheck
import React from 'react'; // eslint-disable-line
import { renderHook } from '@testing-library/react-hooks';
import { ABSegmentContextProvider } from '../../components/ab-segment-context/ABSegmentContext';
import useEnabledSegment from '.';

describe('useEnabledSegment Hook', () => {
  const wrapper =
    (segments: Record<string, string>) =>
    ({ children }: { children: JSX.Element }) =>
      (
        <ABSegmentContextProvider value={segments}>
          {children}
        </ABSegmentContextProvider>
      );

  it('returns enabled if segment is on', () => {
    const {
      result: { current },
    } = renderHook(() => useEnabledSegment('gap01'), {
      wrapper: wrapper({ gap01: 'a' }),
    });

    expect(current).toBe(true);
  });

  it('returns not enabled if segment is off', () => {
    const {
      result: { current },
    } = renderHook(() => useEnabledSegment('gap01'), { wrapper: wrapper({}) });

    expect(current).toBe(false);
  });
});
