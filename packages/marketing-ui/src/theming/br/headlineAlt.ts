import { Plugin<PERSON><PERSON> } from 'tailwindcss/types/config';
// import { MEDIA_QUERY_LG, SCALING_TEXT, makeFontSize, makeLetterSpacing, makeLineHeight, makeScalingFontSize, makeScalingLetterSpacing } from '../utils';
import { CSSRuleObject } from '../typography-plugin';

const HEADLINE_ALT_1 = '.amp-cms--headlineAlt-1';
const HEADLINE_ALT_2 = '.amp-cms--headlineAlt-2';
const HEADLINE_ALT_3 = '.amp-cms--headlineAlt-3';
const HEADLINE_ALT_4 = '.amp-cms--headlineAlt-4';
const ALL_HEADLINE_ALT = [HEADLINE_ALT_1, HEADLINE_ALT_2, HEADLINE_ALT_3, HEADLINE_ALT_4].join(', ');

export const headlineAltTypographyStyles = (theme: PluginAPI['theme']): CSSRuleObject => ({
  [ALL_HEADLINE_ALT]: {
    fontFamily: theme('fontFamily.alt'),
  },
  // [HEADLINE_ALT_1]: {
  //   fontSize: makeFontSize(64),
  //   lineHeight: makeLineHeight(64 / 64),
  //   letterSpacing: makeLetterSpacing(-0.3),
  // },
  // [HEADLINE_ALT_2]: {
  //   fontSize: makeFontSize(54),
  //   lineHeight: makeLineHeight(54 / 54),
  //   letterSpacing: makeLetterSpacing(-0.3),
  // },
  // [HEADLINE_ALT_3]: {
  //   fontSize: makeFontSize(34),
  //   lineHeight: makeLineHeight(34 / 34),
  //   letterSpacing: makeLetterSpacing(-0.3),
  // },
  // [HEADLINE_ALT_4]: {
  //   fontSize: makeFontSize(24),
  //   lineHeight: makeLineHeight(34 / 24),
  //   letterSpacing: makeLetterSpacing(-0.3),
  // },
  // [SCALING_TEXT]: {
  //   [HEADLINE_ALT_1]: {
  //     fontSize: makeScalingFontSize(64, 13),
  //     letterSpacing: makeScalingLetterSpacing(-0.3),
  //   },
  //   [HEADLINE_ALT_2]: {
  //     fontSize: makeScalingFontSize(54, 13),
  //     letterSpacing: makeScalingLetterSpacing(-0.3),
  //   },
  //   [HEADLINE_ALT_3]: {
  //     fontSize: makeScalingFontSize(34, 13),
  //     letterSpacing: makeScalingLetterSpacing(-0.3),
  //   },
  //   [HEADLINE_ALT_4]: {
  //     fontSize: makeScalingFontSize(24, 13),
  //     letterSpacing: makeScalingLetterSpacing(-0.3),
  //   },
  // },
  // [MEDIA_QUERY_LG]: {
  //   [HEADLINE_ALT_1]: {
  //     fontSize: makeFontSize(124),
  //     lineHeight: makeLineHeight(124 / 124),
  //     letterSpacing: makeLetterSpacing(-0.4),
  //   },
  //   [HEADLINE_ALT_2]: {
  //     fontSize: makeFontSize(84),
  //     lineHeight: makeLineHeight(84 / 84),
  //     letterSpacing: makeLetterSpacing(-0.4),
  //   },
  //   [HEADLINE_ALT_3]: {
  //     fontSize: makeFontSize(64),
  //     lineHeight: makeLineHeight(64 / 64),
  //     letterSpacing: makeLetterSpacing(-0.4),
  //   },
  //   [HEADLINE_ALT_4]: {
  //     fontSize: makeFontSize(34),
  //     lineHeight: makeLineHeight(34 / 34),
  //     letterSpacing: makeLetterSpacing(-0.4),
  //   },
  //   [SCALING_TEXT]: {
  //     [HEADLINE_ALT_1]: {
  //       fontSize: makeScalingFontSize(124, 14),
  //       letterSpacing: makeScalingLetterSpacing(-0.4),
  //     },
  //     [HEADLINE_ALT_2]: {
  //       fontSize: makeScalingFontSize(84, 14),
  //       letterSpacing: makeScalingLetterSpacing(-0.4),
  //     },
  //     [HEADLINE_ALT_3]: {
  //       fontSize: makeScalingFontSize(64, 14),
  //       letterSpacing: makeScalingLetterSpacing(-0.4),
  //     },
  //     [HEADLINE_ALT_4]: {
  //       fontSize: makeScalingFontSize(34, 14),
  //       letterSpacing: makeScalingLetterSpacing(-0.4),
  //     },
  //   },
  // },
});
