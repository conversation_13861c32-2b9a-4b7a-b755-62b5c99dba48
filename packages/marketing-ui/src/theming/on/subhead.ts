import { PluginAPI } from 'tailwindcss/types/config';
// import { MEDIA_QUERY_LG, SCALING_TEXT, makeFontSize, makeLetterSpacing, makeLineHeight, makeScalingFontSize, makeScalingLetterSpacing } from '../utils';
import { CSSRuleObject } from '../typography-plugin';

const SUBHEAD_1 = '.amp-cms--subhead-1';
const SUBHEAD_2 = '.amp-cms--subhead-2';
const SUBHEAD_3 = '.amp-cms--subhead-3';
const ALL_HEADLINE_ALT = [SUBHEAD_1, SUBHEAD_2, SUBHEAD_3].join(', ');

export const subheadTypographyStyles = (theme: PluginAPI['theme']): CSSRuleObject => ({
  [ALL_HEADLINE_ALT]: {
    fontFamily: theme('fontFamily.brand'),
    // textTransform: 'uppercase',
  },
  // [SUBHEAD_1]: {
  //   fontSize: makeFontSize(16),
  //   lineHeight: makeLineHeight(28 / 16),
  //   letterSpacing: makeLetterSpacing(0.32),
  //   fontWeight: theme('fontWeight.regular'),
  // },
  // [SUBHEAD_2]: {
  //   fontSize: makeFontSize(14),
  //   lineHeight: makeLineHeight(22 / 14),
  //   letterSpacing: makeLetterSpacing(0.7),
  //   fontWeight: theme('fontWeight.medium'),
  // },
  // [SUBHEAD_3]: {
  //   fontSize: makeFontSize(13),
  //   lineHeight: makeLineHeight(13 / 13),
  //   letterSpacing: makeLetterSpacing(0.97),
  //   fontWeight: theme('fontWeight.semibold'),
  // },
  // [SCALING_TEXT]: {
  //   [SUBHEAD_1]: {
  //     fontSize: makeScalingFontSize(16, 13),
  //     letterSpacing: makeScalingLetterSpacing(0.32),
  //   },
  //   [SUBHEAD_2]: {
  //     fontSize: makeScalingFontSize(14, 13),
  //     letterSpacing: makeScalingLetterSpacing(0.7),
  //   },
  //   [SUBHEAD_3]: {
  //     fontSize: makeScalingFontSize(13, 13),
  //     letterSpacing: makeScalingLetterSpacing(0.97),
  //   },
  // },
  // [MEDIA_QUERY_LG]: {
  //   [SUBHEAD_1]: {
  //     fontSize: makeFontSize(24),
  //     lineHeight: makeLineHeight(36 / 24),
  //     letterSpacing: makeLetterSpacing(1.2),
  //   },
  //   [SUBHEAD_2]: {
  //     fontSize: makeFontSize(18),
  //     lineHeight: makeLineHeight(28 / 18),
  //     letterSpacing: makeLetterSpacing(0.36),
  //   },
  //   [SUBHEAD_3]: {
  //     fontSize: makeFontSize(14),
  //     lineHeight: makeLineHeight(16 / 14),
  //     letterSpacing: makeLetterSpacing(0.7),
  //   },
  //   [SCALING_TEXT]: {
  //     [SUBHEAD_1]: {
  //       fontSize: makeScalingFontSize(24, 14),
  //       letterSpacing: makeScalingLetterSpacing(1.2),
  //     },
  //     [SUBHEAD_2]: {
  //       fontSize: makeScalingFontSize(18, 14),
  //       letterSpacing: makeScalingLetterSpacing(0.36),
  //     },
  //     [SUBHEAD_3]: {
  //       fontSize: makeScalingFontSize(14, 14),
  //       letterSpacing: makeScalingLetterSpacing(0.7),
  //     },
  //   },
  // },
});
