// @ts-nocheck
'use client';
import React from 'react';
import README from './README.mdx';

export default {
  title: 'Common/JSON Components (Marketing)/CMS/Utilities/useSelectedState',
  parameters: {
    docs: { page: README },
    viewMode: 'docs',
    previewTabs: { canvas: { hidden: true } },
  },
  tags: ['exclude'],
};

export const Default = (): JSX.Element => <div>This is not a Visual Component. Please see the Docs tab.</div>;
