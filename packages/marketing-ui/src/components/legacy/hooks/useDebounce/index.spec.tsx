// @ts-nocheck
import { renderHook, act } from 'test-utils';
import { useDebouncedCallback } from '.';

describe('debounce', () => {
  let func: jest.Mock;

  beforeEach(() => {
    jest.useFakeTimers();
    jest.clearAllTimers();
    func = jest.fn();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('execute just once', () => {
    const { result } = renderHook(() => useDebouncedCallback(func, 250));
    for (let i = 0; i < 100; i += 1) {
      result.current();
    }
    jest.runAllTimers();

    expect(func).toBeCalledTimes(1);
  });

  test('should execute after each debounce delay', () => {
    const { result } = renderHook(() => useDebouncedCallback(func, 250));
    result.current();
    result.current();
    expect(func).toBeCalledTimes(0);

    jest.advanceTimersByTime(250);
    expect(func).toBeCalledTimes(1);

    result.current();
    result.current();
    jest.advanceTimersByTime(249);
    expect(func).toBeCalledTimes(1);

    jest.advanceTimersByTime(1);
    expect(func).toBeCalledTimes(2);
  });
});
