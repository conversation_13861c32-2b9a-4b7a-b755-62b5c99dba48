// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { AppState, AppStateProvider } from '@ecom-next/sitewide/app-state-provider';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { Brands } from '@ecom-next/core/legacy/utility';
import { DynamicMarketing as Marketing } from '../../../json-marketing.client';
import { RedpointDecorator } from '../RedpointDecorator';

type PersonalizationContextData = typeof PersonalizationContext;

describe('<RedpointDecorator />', () => {
  const renderComponent = (props: any, personalizationData: PersonalizationContextData) => {
    const WrappedComponent = () => <div>1</div>;
    const DMComponent = () => <div>2</div>;
    {
      (Marketing as jest.Mock).mockImplementation(DMComponent);
    }
    render(
      <PersonalizationContext.Provider
        // @ts-ignore
        value={personalizationData}
      >
        <BreakpointProvider>
          <AppStateProvider value={appState}>
            <RedpointDecorator WrappedComponent={WrappedComponent} {...props} />
          </AppStateProvider>
        </BreakpointProvider>
      </PersonalizationContext.Provider>
    );
  };

  const appState: AppState = {
    brandName: Brands.Gap,
    criticalCss: [],
    criticalResources: [],
    locale: 'en_US',
    market: 'us',
    pageType: 'sitewide',
    datalayer: {
      add: () => {},
      build: () => Promise.resolve({ business_unit_id: 69 }),
      builderNames: () => 'string',
      isTealiumReady: () => null,
      link: () => {},
      view: () => Promise.resolve(),
    } as any,
  };

  const personalizationDefaults = {
    isEmpty: false,
    isError: false,
    shoppingBag: {
      totalItemCount: 0,
    },
  };

  const redpointTest2Component = {
    Redpoint_Test2: {
      instanceName: 'Redpoint_Test',
      instanceDesc: '070821_redpoint_test',
      redpointExperimentRunning: true,
      type: 'builtin',
      name: 'div',
      data: {
        lazy: true,
        defaultHeight: {
          small: '500px',
          large: '250px',
        },
        style: {
          background: '#cce5ff',
          display: 'flex',
          padding: '20px',
          justifyContent: 'center',
        },
        components: ['REDPOINT DATA.'],
      },
    },
  };

  const testProps = {
    instanceName: 'Redpoint_Test2',
    maxAttempts: 47,
    isAsyncExperiment: true,
    redpointExperimentRunning: true,
    displayName: 'some name',
    name: '',
    type: '',
    errorLogger: () => null,
    data: { title: 'something' },
  };

  test.skip('should display the dynamic marketing component if there is marketing data', () => {
    const redpointPersonalizedMarketingData = {
      components: {
        ...redpointTest2Component,
      },
    };

    const personalizationData = {
      isLoading: false,
      redpointPersonalizedMarketingData,
      ...personalizationDefaults,
    } as unknown as PersonalizationContextData;

    renderComponent(testProps, personalizationData);
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  test('should display the Wrapped component if personalized marketing data for different instance', () => {
    const redpointPersonalizedMarketingData = {
      components: {
        ...redpointTest2Component,
      },
    };

    const personalizationData = {
      isLoading: false,
      redpointPersonalizedMarketingData,
      ...personalizationDefaults,
    } as unknown as PersonalizationContextData;

    const props = {
      ...testProps,
      instanceName: 'Redpoint_Test1',
    };

    renderComponent(props, personalizationData);
    expect(screen.getByText('1')).toBeInTheDocument();
  });

  test('should display the loader component if it is waiting for Redpoint data', () => {
    const personalizationData = {
      isLoading: true,
      ...personalizationDefaults,
    } as unknown as PersonalizationContextData;

    renderComponent(testProps, personalizationData);
    const placeholder = screen.getByTestId('homeloadingPlaceholder');
    expect(placeholder).toBeInTheDocument();
  });

  test('should display the Wrapped component if marketing data is empty', () => {
    const redpointPersonalizedMarketingData = {
      components: {},
    };

    const personalizationData = {
      isLoading: false,
      redpointPersonalizedMarketingData,
      ...personalizationDefaults,
    } as unknown as PersonalizationContextData;

    renderComponent(testProps, personalizationData);
    expect(screen.getByText('1')).toBeInTheDocument();
  });
});
