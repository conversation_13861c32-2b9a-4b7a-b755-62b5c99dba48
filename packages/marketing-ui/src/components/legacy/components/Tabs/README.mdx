# Tabs Component

`Tabs` is a JSON-configurable version of our @core-ui/core/tabs component.

Both the tab panels and the tab buttons can use either a string, HTML, a React Component, or a JSON object that `LayoutComponent` would accept.

## Technical Notes

### UX Guidelines

- CSS props have been added to allow custom styling of the tabs-list container, the tabs (buttons), the panels container and the panels. These are optional props which will create CSS-in-JS classes. Examples of these props can be seen in the example below, `Tabs: panels with Layout content and UX-styled navigator buttons`, within the data object. At the time of writing, this JSON example contains the button styles specified by UX.

- Default styles include white text color & secondary brand font. Check with UX before changing anything about the font beyond color.

- NOTE: If using `className`, the style rules used in the CSS props will not override the style rules in `className`. It is recommended to avoid using `classname` with the CSS props.

### Tabs with hover for Desktop

Hover state is now available to use with `Tabs` on desktop by enabaling `useHoverButtons` to true.

- NOTE:
  - Hover event **is not available** on mobile devices so click event will be preserved.
  - When hover is in use, no click event will occur to the button elements (but any click events inside the buttons in the layout component should still continue to work).
  - When using `useHoverButtons` at desktop, the first panel will be the background image and first button tab is hidden by default.
  - Transition in/out timing can be customized by using the `transition` object to pass values in miliseconds.
    `transition: {in: number, out: number}`

### Examples

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Tabs: panels with simple content</summary>
  <pre>

```typescript

  const tabsData = {
    data: {
      id: "example-mui-tabs",
      tabListData: {
        label: "Default Tabs",
        tabButtonData: [
          {
            id: "one",
            tabPanelId: "one",
            label: "accessible label for tab one",
            title: "tab 1",
            className: ""
          },
          {
            id: "two",
            tabPanelId: "two",
            label: "accessible label for tab two",
            title: "tab 2",
            className: ""
          },
          {
            id: "three",
            tabPanelId: "three",
            label: "accessible label for tab three",
            title: "tab 3",
            className: ""
          }
        ]
      },
      tabPanelsData: {
        tabPanelData: [
          {
            id: "1",
            children: "Panel 1"
          },
          {
            id: "2",
            children: <div>Panel 2</div>
          },
          {
            id: "3",
            children: Image /* React component */
          }
        ]
      }
    };
  };

const NewTabsComponent = (): JSX.Element => <Tabs {...tabsData} />;
```

</pre>
</details>

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0",}}> 
<summary>Tabs: with required props only</summary>
<pre>
```javascript
const minimalData = {
  data: {
    id: "example-mui-tabs",
    tabListData: {
      tabButtonData: [
        {
          label: "accessible label for tab one",
        },
        {
          label: "accessible label for tab two",
        },
        {
          label: "accessible label for tab three",
        },
      ],
    },
    tabPanelsData: {
      tabPanelData: [
        {
          children: "Panel 1",
        },
        {
          children: "Panel 2",
        },
        {
          children: "Panel 3",
        },
      ],
    },
  },
};
```

</pre>
</details>

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Tabs: JSON for ON Women's Denim Banner with a11y enhancements</summary>
  <pre>
  
  ```json
 {
  "name": "LayoutComponent",
  "type": "sitewide",
  "data": {
    "lazy": true,
    "defaultHeight": { "large": "auto", "small": "auto" },
    "desktopAndMobile": {
      "shouldDisplay": true,
      "data": {
        "style": {{
          "maxWidth": "1248px",
          "margin": "0 auto",
          "position": "relative",
          "flexDirection": "column"
        }},
        "components": [
          {
            "name": "LayeredContentModule",
            "type": "sitewide",
            "data": {
              "overlay": {
                "children": [
                  {
                    "font": "secondary",
                    "text": "Fits for all of us.",
                    "style": {
                      "fontSize": "6vw",
                      "margin": "0 0.45rem",
                      "color": "#113761"
                    },
                    "desktopStyle": {
                      "fontSize": "min(2.81vw, 37px)",
                      "margin": "0 0.45rem",
                      "color": "#113761"
                    }
                  },
                  {
                    "font": "primary",
                    "text": "Iconic denim, in all the ways.  Welcome to DenimAmerica.",
                    "style": {
                      "fontSize": "3.1vw",
                      "color": "#113761"
                    },
                    "desktopStyle": {
                      "fontSize": "min(1.55vw, 20.5px)",
                      "verticalAlign": "bottom",
                      "marginBottom": "1.5%"
                    }
                  }
                ],
                "style": {
                  "position": "absolute",
                  "top": "0",
                  "display": "flex",
                  "flexDirection": "column",
                  "justifyContent": "center",
                  "width": "100%",
                  "height": "100%"
                },
                "desktopStyle": {
                  "alignItems": "baseline",
                  "verticalAlign": "bottom",
                  "flexDirection": "row"
                }
              },
              "container": {
                "style": {
                  "position": "relative",
                  "textAlign": "center"
                }
              },
              "background": {
                "style": {
                  "height": "8rem",
                  "margin": "1rem auto 1.5% auto"
                },
                "desktopStyle": {
                  "height": "39px",
                  "padding": "0"
                }
              }
            }
          },
          {
            "name": "LayoutComponent",
            "type": "sitewide",
            "instanceName": "womens-vi-banner-with-tabs",
            "data": {
              "lazy": true,
              "defaultHeight": { "large": "250px", "small": "179px" },
              "desktopAndMobile": {
                "shouldDisplay": true,
                "data": {
                  "style": {
                    "display": "block",
                    "margin": "0 auto",
                    "position": "relative"
                  },
                  "classes": "tabs-vi-banner",
                  "components": [
                    {
                      "name": "HTMLInjectionComponent",
                      "type": "sitewide",
                      "note": "For tab CSS customizations between viewports and enabling it to be responsive",
                      "data": {
                        "html": "<link rel=\"stylesheet\" href=\"https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/vi-banner-styles.css?v=9\" type=\"text/css\" /><picture><source media=\"(max-width: 767px)\" srcset=\"https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/transparent-767x495.png\"><img alt=\"\" class=\"placeholder-img sds_full sds_block\" src=\"https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/transparent-1400x374.png\"></picture>"
                      }
                    },
                    {
                      "name": "Tabs",
                      "type": "sitewide",
                      "data": {
                        "crossBrand": false,
                        "id": "size-tabs",
                        "tabFontStyle": "secondary",
                        "tabListCSS": { "float": "left", "border": "none" },
                        "tabCSS": {
                          "background": "#ffffff",
                          "border": "1px solid #113761",
                          "color": "#113761",
                          "height": "50%",
                          "padding": "0",
                          "margin": "0",
                          "textAlign": "center",
                          "whiteSpace": "pre-line",
                          "textTransform": "uppercase",
                          "width": "100%",
                          "borderTopLeftRadius": "10px",
                          "borderBottomLeftRadius": "10px",
                          "&:hover": {
                            "color": "#0A5694",
                            "borderColor": "#0A5694"
                          }
                        },
                        "tabActiveCSS": {
                          "backgroundColor": "#113761",
                          "color": "#FFFFFF",
                          "&:hover": {
                            "backgroundColor": "#113761",
                            "color": "#FFFFFF"
                          }
                        },
                        "tabListData": {
                          "label": "Size Tabs",
                          "tabButtonData": [
                            {
                              "id": "tab-01",
                              "tabPanelId": "tab-01",
                              "label": "Model size 4",
                              "title": "Model\nsize\n4",
                              "className": ""
                            },
                            {
                              "id": "tab-02",
                              "tabPanelId": "tab-02",
                              "label": "Model size 12",
                              "title": "Model\nsize\n12",
                              "className": ""
                            }
                          ]
                        },
                        "panelContainerCSS": {
                          "display": "block",
                          "height": "100%"
                        },
                        "panelCSS": {
                          "overflow": "hidden",
                          "height": "100%"
                        },
                        "tabPanelsData": {
                          "tabPanelData": [
                            {
                              "id": "womensDenim01",
                              "tabId": "womensDenim01",
                              "className": "",
                              "children": {
                                "desktop": {
                                  "shouldDisplay": true,
                                  "data": {
                                    "style": { "flexDirection": "row" },
                                    "components": [
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",
                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1100320,1017776,91608,1162806",
                                            "tid": "banner=vi_denim_w_straight_size4_click",
                                            "aria-labelledby": "straight-label",
                                            "aria-describedby": "straight-description"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "185px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_default.jpg",
                                                        "altText": "Straight Jeans in size 4",
                                                        "id": "straight-label"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                        "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                        "id": "straight-description"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_hover.jpg",
                                                        "altText": "Straight Jeans in size 4",
                                                        "id": "straight-label"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                        "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                        "id": "straight-description"
                                                      },
                                                      "containerStyle": {}
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1137790,1051513",
                                            "tid": "banner=vi_denim_w_super_skinny_size4_click",
                                            "aria-labelledby": "super-skinny-label",
                                            "aria-describedby": "super-skinny-description"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_default.jpg",
                                                        "altText": "Super-Skinny Jeans in size 4",
                                                        "id": "super-skinny-label"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                        "altText": "Super-Skinny Jeans in size 4",
                                                        "id": "super-skinny-label"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_hover.jpg",
                                                        "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                        "id": "super-skinny-description"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                        "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                        "id": "super-skinny-description"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1118459",
                                            "tid": "banner=vi_denim_w_skinny_size4_click",
                                            "aria-labelledby": "skinny-label",
                                            "aria-describedby": "skinny-description"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_default.jpg",
                                                        "altText": "Skinny Jeans in size 4",
                                                        "id": "skinny-label"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                        "altText": "Available in: Pop-Icon Skinny",
                                                        "id": "skinny-description"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_hover.jpg",
                                                        "altText": "Skinny Jeans in size 4",
                                                        "id": "skinny-label"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                        "altText": "Available in: Pop-Icon Skinny",
                                                        "id": "skinny-description"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1017737",
                                            "tid": "banner=vi_denim_w_bootcut_taper_size4_click",
                                            "aria-labelledby": "bootcut-label",
                                            "aria-describedby": "bootcut-description"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_default.jpg",
                                                        "altText": "Bootcut Jeans in size 4",
                                                        "id": "bootcut-label"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                        "altText": "Available in: Kicker Bootcut",
                                                        "id": "bootcut-description"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_hover.jpg",
                                                        "altText": "Bootcut Jeans in size 4",
                                                        "id": "bootcut-label"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                        "altText": "Available in: Kicker Bootcut",
                                                        "id": "bootcut-description"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1028930",
                                            "tid": "banner=vi_denim_w_flare_size4_click",
                                            "aria-labelledby": "flare-label",
                                            "aria-describedby": "flare-description"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_default.jpg",
                                                        "altText": "Flare Jeans in size 4",
                                                        "id": "flare-label"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "altText": "",
                                                        "id": "flare-description"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_hover.jpg",
                                                        "altText": "Flare Jeans in size 4",
                                                        "id": "flare-label"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "altText": "",
                                                        "id": "flare-description"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1128874",
                                            "tid": "banner=vi_denim_w_wideleg_size4_click",
                                            "aria-labelledby": "wide-leg-label",
                                            "aria-describedby": "wide-leg-description"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_default.jpg",
                                                        "altText": "Wide leg Jeans in size 4",
                                                        "id": "wide-leg-label"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "altText": "",
                                                        "id": "wide-leg-description"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_hover.jpg",
                                                        "altText": "Wide leg Jeans in size 4",
                                                        "id": "wide-leg-label"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "altText": "",
                                                        "id": "wide-leg-description"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                },
                                "mobile": {
                                  "shouldDisplay": true,
                                  "data": {
                                    "style": {
                                      "flexDirection": "column",
                                      "margin": "0 auto"
                                    },
                                    "components": [
                                      {
                                        "name": "LayoutComponent",
                                        "type": "sitewide",
                                        "data": {
                                          "mobile": {
                                            "shouldDisplay": true,
                                            "data": {
                                              "style": {
                                                "display": "block",
                                                "padding": "0",
                                                "margin": "0 auto",
                                                "overflow-x": "scroll",
                                                "overflow-y": "hidden",
                                                "-webkit-overflow-scrolling": "touch"
                                              },
                                              "components": [
                                                {
                                                  "name": "LayoutComponent",
                                                  "type": "sitewide",
                                                  "data": {
                                                    "mobile": {
                                                      "shouldDisplay": true,
                                                      "data": {
                                                        "style": {
                                                          "padding": "0",
                                                          "margin": "0 -5.5px",
                                                          "width": "234%"
                                                        },
                                                        "components": [
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1100320,1017776,91608,1162806",
                                                                "tid": "banner=vi_denim_w_straight_size4_click",
                                                                "aria-labelledby": "straight-label",
                                                                "aria-describedby": "straight-description"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_default.jpg",
                                                                            "altText": "Straight Jeans in size 4",
                                                                            "id": "straight-label"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                                            "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                                            "id": "straight-description"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/straight_hover.jpg",
                                                                            "altText": "Straight Jeans in size 4",
                                                                            "id": "straight-label"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                                            "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                                            "id": "straight-description"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1137790,1051513",
                                                                "tid": "banner=vi_denim_w_super_skinny_size4_click",
                                                                "aria-labelledby": "super-skinny-label",
                                                                "aria-describedby": "super-skinny-description"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_default.jpg",
                                                                            "altText": "Super-Skinny Jeans in size 4",
                                                                            "id": "super-skinny-label"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                                            "altText": "Super-Skinny Jeans in size 4",
                                                                            "id": "super-skinny-label"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/superskinny_hover.jpg",
                                                                            "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                                            "id": "super-skinny-description"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                                            "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                                            "id": "super-skinny-description"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1118459",
                                                                "tid": "banner=vi_denim_w_skinny_size4_click",
                                                                "aria-labelledby": "skinny-label",
                                                                "aria-describedby": "skinny-description"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_default.jpg",
                                                                            "altText": "Skinny Jeans in size 4",
                                                                            "id": "skinny-label"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                                            "altText": "Available in: Pop-Icon Skinny",
                                                                            "id": "skinny-description"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/skinny_hover.jpg",
                                                                            "altText": "Skinny Jeans in size 4",
                                                                            "id": "skinny-label"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                                            "altText": "Available in: Pop-Icon Skinny",
                                                                            "id": "skinny-description"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1017737",
                                                                "tid": "banner=vi_denim_w_bootcut_taper_size4_click",
                                                                "aria-labelledby": "bootcut-label",
                                                                "aria-describedby": "bootcut-description"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_default.jpg",
                                                                            "altText": "Bootcut Jeans in size 4",
                                                                            "id": "bootcut-label"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                                            "altText": "Available in: Kicker Bootcut",
                                                                            "id": "bootcut-description"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/bootcut_hover.jpg",
                                                                            "altText": "Bootcut Jeans in size 4",
                                                                            "id": "bootcut-label"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                                            "altText": "Available in: Kicker Bootcut",
                                                                            "id": "bootcut-description"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1028930",
                                                                "tid": "banner=vi_denim_w_flare_size4_click",
                                                                "aria-labelledby": "flare-label",
                                                                "aria-describedby": "flare-description"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_default.jpg",
                                                                            "altText": "Flare Jeans in size 4",
                                                                            "id": "flare-label"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "altText": "",
                                                                            "id": "flare-description"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/flare_hover.jpg",
                                                                            "altText": "Flare Jeans in size 4",
                                                                            "id": "flare-label"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "altText": "",
                                                                            "id": "flare-description"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1128874",
                                                                "tid": "banner=vi_denim_w_wideleg_size4_click",
                                                                "aria-labelledby": "wide-leg-label",
                                                                "aria-describedby": "wide-leg-description"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_default.jpg",
                                                                            "altText": "Wide leg Jeans in size 4",
                                                                            "id": "wide-leg-label"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "altText": "",
                                                                            "id": "wide-leg-description"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size4/wideleg_hover.jpg",
                                                                            "altText": "Wide leg Jeans in size 4",
                                                                            "id": "wide-leg-label"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "altText": "",
                                                                            "id": "wide-leg-description"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  }
                                                }
                                              ]
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                }
                              }
                            },
                            {
                              "id": "womensDenim02",
                              "tabId": "womensDenim02",
                              "className": "",
                              "children": {
                                "desktop": {
                                  "shouldDisplay": true,
                                  "data": {
                                    "style": { "flexDirection": "row" },
                                    "components": [
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1100320,1017776,91608,1162806",
                                            "tid": "banner=vi_denim_w_straight_size12_click",
                                            "aria-labelledby": "straight-label-size-12",
                                            "aria-describedby": "straight-description-size-12"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_default.jpg?v=1",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_default.jpg?v=1",
                                                        "altText": "Straight Jeans in size 12",
                                                        "id": "straight-label-size-12"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                        "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                        "id": "straight-description-size-12"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_hover.jpg?v=1",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_hover.jpg?v=1",
                                                        "altText": "Straight Jeans in size 12",
                                                        "id": "straight-label-size-12"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                        "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                        "id": "straight-description-size-12"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1137790,1051513",
                                            "tid": "banner=vi_denim_w_super_skinny_size12_click",
                                            "aria-labelledby": "super-skinny-label-size-12",
                                            "aria-describedby": "super-skinny-description-size-12"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_default.jpg",
                                                        "altText": "Super-Skinny Jeans in size 12",
                                                        "id": "super-skinny-label-size-12"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                        "altText": "Super-Skinny Jeans in size 12",
                                                        "id": "super-skinny-label-size-12"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_hover.jpg",
                                                        "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                        "id": "super-skinny-description-size-12"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                        "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                        "id": "super-skinny-description-size-12"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1118459",
                                            "tid": "banner=vi_denim_w_skinny_size12_click",
                                            "aria-labelledby": "skinny-label-size-12",
                                            "aria-describedby": "skinny-description-size-12"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_default.jpg",
                                                        "altText": "Skinny Jeans in size 12",
                                                        "id": "skinny-label-size-12"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                        "altText": "Available in: Pop-Icon Skinny",
                                                        "id": "skinny-description-size-12"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_hover.jpg",
                                                        "altText": "Skinny Jeans in size 12",
                                                        "id": "skinny-label-size-12"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                        "altText": "Available in: Pop-Icon Skinny",
                                                        "id": "skinny-description-size-12"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1017737",
                                            "tid": "banner=vi_denim_w_bootcut_taper_size12_click",
                                            "aria-labelledby": "bootcut-label-size-12",
                                            "aria-describedby": "bootcut-description-size-12"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_default.jpg",
                                                        "altText": "Bootcut Jeans in size 12",
                                                        "id": "bootcut-label-size-12"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                        "altText": "Available in: Kicker Bootcut",
                                                        "id": "bootcut-description-size-12"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_hover.jpg",
                                                        "altText": "Bootcut Jeans in size 12",
                                                        "id": "bootcut-label-size-12"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                        "altText": "Available in: Kicker Bootcut",
                                                        "id": "bootcut-description-size-12"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1028930",
                                            "tid": "banner=vi_denim_w_flare_size12_click",
                                            "aria-labelledby": "flare-label-size-12",
                                            "aria-describedby": "flare-description-size-12"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_default.jpg",
                                                        "altText": "Flare Jeans in size 12",
                                                        "id": "flare-label-size-12"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "altText": "",
                                                        "id": "flare-description-size-12"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_hover.jpg",
                                                        "altText": "Flare Jeans in size 12",
                                                        "id": "flare-label-size-12"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                        "altText": "",
                                                        "id": "flare-description-size-12"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      },
                                      {
                                        "type": "sitewide",
                                        "name": "JumpLink",

                                        "data": {
                                          "isAJumplink": true,
                                          "jumplinkCSSSelector": "#faceted-grid",
                                          "link": {
                                            "to": "#pageId=0&style=1128874",
                                            "tid": "banner=vi_denim_w_wideleg_size12_click",
                                            "aria-labelledby": "wide-leg-label-size-12",
                                            "aria-describedby": "wide-leg-description-size-12"
                                          },
                                          "layoutData": {
                                            "desktopAndMobile": {
                                              "shouldDisplay": true,
                                              "data": {
                                                "style": {
                                                  "marginRight": "11px"
                                                },
                                                "desktopStyle": {
                                                  "width": "187px"
                                                },
                                                "components": [
                                                  {
                                                    "type": "sitewide",
                                                    "name": "HoverImage",
                                                    "data": {
                                                      "background": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_default.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_default.jpg",
                                                        "altText": "Wide leg Jeans in size 12",
                                                        "id": "wide-leg-label-size-12"
                                                      },
                                                      "svgOverlay": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "altText": "",
                                                        "id": "wide-leg-description-size-12"
                                                      },
                                                      "backgroundHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_hover.jpg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_hover.jpg",
                                                        "altText": "Wide leg Jeans in size 12",
                                                        "id": "wide-leg-label-size-12"
                                                      },
                                                      "svgOverlayHover": {
                                                        "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                        "altText": "",
                                                        "id": "wide-leg-description-size-12"
                                                      }
                                                    }
                                                  }
                                                ]
                                              }
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                },
                                "mobile": {
                                  "shouldDisplay": true,
                                  "data": {
                                    "style": {
                                      "flexDirection": "column",
                                      "margin": "0 auto"
                                    },
                                    "components": [
                                      {
                                        "name": "LayoutComponent",
                                        "type": "sitewide",
                                        "data": {
                                          "mobile": {
                                            "shouldDisplay": true,
                                            "data": {
                                              "style": {
                                                "display": "block",
                                                "padding": "0",
                                                "margin": "0 auto",
                                                "overflow-x": "scroll",
                                                "overflow-y": "hidden",
                                                "-webkit-overflow-scrolling": "touch"
                                              },
                                              "components": [
                                                {
                                                  "name": "LayoutComponent",
                                                  "type": "sitewide",
                                                  "data": {
                                                    "mobile": {
                                                      "shouldDisplay": true,
                                                      "data": {
                                                        "style": {
                                                          "padding": "0",
                                                          "margin": "0 -6px",
                                                          "width": "234%"
                                                        },
                                                        "components": [
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1100320,1017776,91608,1162806",
                                                                "tid": "banner=vi_denim_w_straight_size12_click",
                                                                "aria-labelledby": "straight-label-size-12",
                                                                "aria-describedby": "straight-description-size-12"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_default.jpg?v=1",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_default.jpg?v=1",
                                                                            "altText": "Straight Jeans in size 12",
                                                                            "id": "straight-label-size-12"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_default.svg",
                                                                            "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                                            "id": "straight-description-size-12"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_hover.jpg?v=1",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/straight_hover.jpg?v=1",
                                                                            "altText": "Straight Jeans in size 12",
                                                                            "id": "straight-label-size-12"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/straight_hover.svg",
                                                                            "altText": "Available in: Power Slim Straight, Boyfriend Straight, Sky-Hi Straight, OG Straight",
                                                                            "id": "straight-description-size-12"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1137790,1051513",
                                                                "tid": "banner=vi_denim_w_super_skinny_size12_click",
                                                                "aria-labelledby": "super-skinny-label-size-12",
                                                                "aria-describedby": "super-skinny-description-size-12"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_default.jpg",
                                                                            "altText": "Super-Skinny Jeans in size 12",
                                                                            "id": "super-skinny-label-size-12"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_default.svg",
                                                                            "altText": "Super-Skinny Jeans in size 12",
                                                                            "id": "super-skinny-label-size-12"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/superskinny_hover.jpg",
                                                                            "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                                            "id": "super-skinny-description-size-12"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/superskinny_hover.svg",
                                                                            "altText": "Available in: Rockstar Super-Skinny, Rockstar Super-Skinny Legging",
                                                                            "id": "super-skinny-description-size-12"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1118459",
                                                                "tid": "banner=vi_denim_w_skinny_size12_click",
                                                                "aria-labelledby": "skinny-label-size-12",
                                                                "aria-describedby": "skinny-description-size-12"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_default.jpg",
                                                                            "altText": "Skinny Jeans in size 12",
                                                                            "id": "skinny-label-size-12"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_default.svg",
                                                                            "altText": "Available in: Pop-Icon Skinny",
                                                                            "id": "skinny-description-size-12"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/skinny_hover.jpg",
                                                                            "altText": "Skinny Jeans in size 12",
                                                                            "id": "skinny-label-size-12"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/skinny_hover.svg",
                                                                            "altText": "Available in: Pop-Icon Skinny",
                                                                            "id": "skinny-description-size-12"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1017737",
                                                                "tid": "banner=vi_denim_w_bootcut_taper_size12_click",
                                                                "aria-labelledby": "bootcut-label-size-12",
                                                                "aria-describedby": "bootcut-description-size-12"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_default.jpg",
                                                                            "altText": "Bootcut Jeans in size 12",
                                                                            "id": "bootcut-label-size-12"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_default.svg",
                                                                            "altText": "Available in: Kicker Bootcut",
                                                                            "id": "bootcut-description-size-12"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/bootcut_hover.jpg",
                                                                            "altText": "Bootcut Jeans in size 12",
                                                                            "id": "bootcut-label-size-12"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/bootcut_hover.svg",
                                                                            "altText": "Available in: Kicker Bootcut",
                                                                            "id": "bootcut-description-size-12"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1028930",
                                                                "tid": "banner=vi_denim_w_flare_size12_click",
                                                                "aria-labelledby": "flare-label-size-12",
                                                                "aria-describedby": "flare-description-size-12"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_default.jpg",
                                                                            "altText": "Flare Jeans in size 12",
                                                                            "id": "flare-label-size-12"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "altText": "",
                                                                            "id": "flare-description-size-12"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/flare_hover.jpg",
                                                                            "altText": "Flare Jeans in size 12",
                                                                            "id": "flare-label-size-12"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/flare_default.svg",
                                                                            "altText": "",
                                                                            "id": "flare-description-size-12"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          },
                                                          {
                                                            "type": "sitewide",
                                                            "name": "JumpLink",

                                                            "data": {
                                                              "isAJumplink": true,
                                                              "jumplinkCSSSelector": "#faceted-grid",
                                                              "link": {
                                                                "to": "#pageId=0&style=1128874",
                                                                "tid": "banner=vi_denim_w_wideleg_size12_click",
                                                                "aria-labelledby": "wide-leg-label-size-12",
                                                                "aria-describedby": "wide-leg-description-size-12"
                                                              },
                                                              "layoutData": {
                                                                "desktopAndMobile": {
                                                                  "shouldDisplay": true,
                                                                  "data": {
                                                                    "style": {
                                                                      "marginRight": "11px"
                                                                    },
                                                                    "desktopStyle": {
                                                                      "width": "187px"
                                                                    },
                                                                    "components": [
                                                                      {
                                                                        "type": "sitewide",
                                                                        "name": "HoverImage",
                                                                        "data": {
                                                                          "background": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_default.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_default.jpg",
                                                                            "altText": "Wide leg Jeans in size 4",
                                                                            "id": "wide-leg-label-size-12"
                                                                          },
                                                                          "svgOverlay": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "altText": "",
                                                                            "id": "wide-leg-description-size-12"
                                                                          },
                                                                          "backgroundHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_hover.jpg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/size12/wideleg_hover.jpg",
                                                                            "altText": "Wide leg Jeans in size 4",
                                                                            "id": "wide-leg-label-size-12"
                                                                          },
                                                                          "svgOverlayHover": {
                                                                            "img": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "desktopImg": "https://oldnavy.gap.com/Asset_Archive/ONWeb/content/0019/281/605/assets/svgs/wideleg_default.svg",
                                                                            "altText": "",
                                                                            "id": "wide-leg-description-size-12"
                                                                          }
                                                                        }
                                                                      }
                                                                    ]
                                                                  }
                                                                }
                                                              }
                                                            }
                                                          }
                                                        ]
                                                      }
                                                    }
                                                  }
                                                }
                                              ]
                                            }
                                          }
                                        }
                                      }
                                    ]
                                  }
                                }
                              }
                            }
                          ]
                        }
                      }
                    }
                  ]
                }
              }
            }
          }
        ]
      }
    }

}
}

````
</pre>
</details>


<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Tabs: panels with Layout content and UX-styled navigator tabs</summary>
  <pre>

  ```javascript
  //  * refers to UX specified styles
const tabsWithLayoutData = {
    data: {
      crossBrand: true,
      id: "example-mui-tabs",
      tabFontStyle: "secondary", *
      tabActiveCSS: {
        backgroundColor: "#0A5694",
      },
      tabListCSS: {
        float: "left", *
        height: "33%", * // For buttons to span the full height, this % should be 1/# of buttons. ex: 1/(4 buttons) -> 25%
        border: "none", *
        width: "200px", *
      },
      tabCSS: {
        background: "none", *
        backgroundColor: "#333", *
        border: "none", *
        height: "100%", *
        padding: "1rem",
        margin: "0 0 1px 0", *
        textAlign: "center", *
        textTransform: "capitalize",
        width: "100%", *
        ":hover": { * // Sets the style of the tab on hover
          backgroundColor: "#91AFD7",
        },
      },
      panelContainerCSS: {
        display: "block", *
        height: "100%", *
      },
      panelCSS: {
        overflow: "hidden",
        height: "100%", *
      },
      tabListData: {
        label: "Your Tabs",
        tabButtonData: [
          {
            id: "one",
            tabPanelId: "one",
            label: "accessible label for tab one",
            title: "size 28",
            className: ""
          },
          {
            id: "two",
            tabPanelId: "two",
            label: "accessible label for tab two",
            title: "size 32",
            className: ""
          },
          {
            id: "three",
            tabPanelId: "three",
            label: "accessible label for tab three",
            title: "size 30 plus",
            className: ""
          }
        ]
      },
      tabPanelsData: {
        tabPanelData: [
          {
            id: "panel1",
            tabId: "panel1",
            className: "",
            children: layoutData ^
          },
          {
            id: "panel2",
            tabId: "panel2",
            className: "",
            children: layoutData ^
          },
          {
            id: "panel3",
            tabId: "panel3",
            className: "",
            children: layoutData ^
          }
        ]
      }
    }
}
````

</pre>
</details>

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
<summary>Tabs: first tab is hidden</summary>
<pre>

```javascript
const tabsWithHiddenFirstTab = {
  data: {
    id: "example-mui-tabs",
    tabListCSS: ...
    tabCSS: {
      ...yourTabButtonStyles,
      ":first-of-type": {
        "display": "none",
      },   // add this style rule, quotes and all, to your JSON to hide the first tab while still showing its panel content. Once the user has navigated away, they will not be able to return to that content.
    },
    panelContainerCSS: ...
    panelCSS: ...
    tabListData: ...
    tabPanelsData: ...
  }
};
```

</pre>
</details>

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
<summary>Tabs: Buttons are LayoutComponents</summary>
<pre>

```json

  {
    "name": "LayoutComponent",
    "type": "sitewide",
    "data": {
        "desktopAndMobile": {
          "shouldDisplay": true,
          "data": {
            "style": {
              "maxWidth": "1248px",
              "margin": "0 auto",
              "position": "relative",
              "flexDirection": "column"
            },
            "components": [
              {
                "name": "Tabs",
                "type": "sitewide",
                "data": {
                  "crossBrand": false,
                  "id": "example-mui-tabs",
                  "tabFontStyle": "secondary",
                  "tabListCSS": {
                    "position": "absolute",
                    "bottom": "15px",
                    "border": "none",
                    "zIndex": 5,
                    "backgroundColor": "transparent",
                    "display": "flex",
                    "width": "100%",
                    "justifyContent": "space-evenly",
                    "padding": 0
                  },
                  "tabCSS": {
                    "background": "none",
                    "border": "none",
                    "height": "100%",
                    "textAlign": "center",
                    "textTransform": "capitalize",
                    "margin": 0,
                    "padding": 0
                  },
                  "panelContainerCSS": {
                    "display": "block",
                    "height": "100%"
                  },
                  "panelCSS": {
                    "overflow": "hidden",
                    "height": "100%"
                  },
                  "useHoverButtons": true,
                  "tabListData": {
                    "label": "Your Tabs",
                    "tabButtonData": [
                      {
                        "id": "one",
                        "tabPanelId": "one",
                        "label": "accessible label for tab one",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "type": "builtin",
                                  "name": "div",
                                  "data": {
                                    "props": {
                                      "style": {
                                        "display": "flex",
                                        "justifyContent": "center"
                                      }
                                    },
                                    "components": [
                                      "BasicHtml1"
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        }
                      },
                      {
                        "id": "two",
                        "tabPanelId": "two",
                        "label": "accessible label for tab two",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "type": "builtin",
                                  "name": "div",
                                  "data": {
                                    "props": {
                                      "style": {
                                        "display": "flex",
                                        "justifyContent": "center"
                                      }
                                    },
                                    "components": [
                                      "BasicHtml2"
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        }
                      },
                      {
                        "id": "three",
                        "tabPanelId": "three",
                        "label": "accessible label for tab three",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "type": "builtin",
                                  "name": "div",
                                  "data": {
                                    "props": {
                                      "style": {
                                        "display": "flex",
                                        "justifyContent": "center"
                                      }
                                    },
                                    "components": [
                                      "BasicHtml3"
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        }
                      },
                      {
                        "id": "four",
                        "tabPanelId": "four",
                        "label": "accessible label for tab four",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "tileStyle": {
                                    "desktop": {},
                                    "mobile": {}
                                  },
                                  "type": "builtin",
                                  "name": "div",
                                  "data": {
                                    "props": {
                                      "style": {
                                        "display": "flex",
                                        "justifyContent": "center"
                                      }
                                    },
                                    "components": [
                                      "BasicHtml4"
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        }
                      }
                    ]
                  },
                  "tabPanelsData": {
                    "tabPanelData": [
                      {
                        "id": "panel1",
                        "tabId": "panel1",
                        "className": "",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "name": "LayeredContentModule",
                                  "type": "sitewide",
                                  "data": {
                                    "background": {
                                      "className": "exampleBackgroundClassName",
                                      "image": {
                                        "alt": "A Placeholder Image",
                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image1",
                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image1"
                                      }
                                    },
                                    "container": {
                                      "style": {}
                                    }
                                  }
                                }
                              ]
                            }
                          }
                        }
                      },
                      {
                        "id": "panel2",
                        "tabId": "panel2",
                        "className": "",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "name": "LayeredContentModule",
                                  "type": "sitewide",
                                  "data": {
                                    "background": {
                                      "className": "exampleBackgroundClassName",
                                      "image": {
                                        "alt": "A Placeholder Image",
                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image2",
                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image2"
                                      }
                                    },
                                    "container": {
                                      "style": {}
                                    }
                                  }
                                }
                              ]
                            }
                          }
                        }
                      },
                      {
                        "id": "panel3",
                        "tabId": "panel3",
                        "className": "",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "name": "LayeredContentModule",
                                  "type": "sitewide",
                                  "data": {
                                    "background": {
                                      "className": "exampleBackgroundClassName",
                                      "image": {
                                        "alt": "A Placeholder Image",
                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image3",
                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image3"
                                      }
                                    },
                                    "container": {
                                      "style": {}
                                    }
                                  }
                                }
                              ]
                            }
                          }
                        }
                      },
                      {
                        "id": "panel4",
                        "tabId": "panel4",
                        "className": "",
                        "children": {
                          "desktopAndMobile": {
                            "shouldDisplay": true,
                            "data": {
                              "style": {},
                              "components": [
                                {
                                  "name": "LayeredContentModule",
                                  "type": "sitewide",
                                  "data": {
                                    "background": {
                                      "className": "exampleBackgroundClassName",
                                      "image": {
                                        "alt": "A Placeholder Image",
                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image4",
                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image4"
                                      }
                                    },
                                    "container": {
                                      "style": {}
                                    }
                                  }
                                }
                              ]
                            }
                          }
                        }
                      }
                    ]
                  }
                }
              }
            ]
          }
        }
      }
    }
  }
```

</pre>
</details>

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
  <summary>Tabs: JSON with hover on desktop and seperate mobile</summary>
  <pre>
  
  ```json
    {
    "desktop": {
        "shouldDisplay": true,
        "data": {
            "style": {
                "maxWidth": "1248px",
                "margin": "0 auto",
                "position": "relative",
                "flexDirection": "column"
            },
            "components": [{
                "name": "Tabs",
                "type": "sitewide",
                "data": {
                    "crossBrand": false,
                    "id": "example-mui-tabs",
                    "tabFontStyle": "secondary",
                    "tabListCSS": {
                        "position": "absolute",
                        "bottom": "15px",
                        "border": "none",
                        "zIndex": 5,
                        "backgroundColor": "transparent",
                        "display": "flex",
                        "width": "100%",
                        "justifyContent": "space-evenly",
                        "padding": 0
                    },
                    "tabCSS": {
                        "background": "none",
                        "border": "none",
                        "height": "100%",
                        "textAlign": "center",
                        "textTransform": "capitalize",
                        "margin": 0,
                        "padding": 0
                    },
                    "panelContainerCSS": {
                        "display": "block",
                        "height": "100%"
                    },
                    "panelCSS": {
                        "overflow": "hidden",
                        "height": "100%"
                    },
                    "useHoverButtons": true,
                    "tabListData": {
                        "label": "Your Tabs",
                        "tabButtonData": [{
                            "id": "one",
                            "tabPanelId": "one",
                            "label": "accessible label for tab one",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "two",
                            "tabPanelId": "two",
                            "label": "accessible label for tab two",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml2"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "three",
                            "tabPanelId": "three",
                            "label": "accessible label for tab three",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml3"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "four",
                            "tabPanelId": "four",
                            "label": "accessible label for tab four",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "tileStyle": {
                                                "desktop": {},
                                                "mobile": {}
                                            },
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml4"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }]
                    },
                    "tabPanelsData": {
                        "tabPanelData": [{
                            "id": "panel1",
                            "tabId": "panel1",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image1",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image1"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "panel2",
                            "tabId": "panel2",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image2",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image2"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "panel3",
                            "tabId": "panel3",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image3",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image3"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "panel4",
                            "tabId": "panel4",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image4",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image4"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }]
                    }
                }
            }]
        }
    },
    "mobile": {
        "shouldDisplay": true,
        "data": {
            "style": {
                "maxWidth": "767px",
                "margin": "0 auto",
                "position": "relative",
                "flexDirection": "column"
            },
            "components": [{
                "name": "Tabs",
                "type": "sitewide",
                "data": {
                    "crossBrand": false,
                    "id": "example-mui-tabs",
                    "tabFontStyle": "secondary",
                    "tabListCSS": {
                        "position": "absolute",
                        "bottom": "15px",
                        "border": "none",
                        "zIndex": 5,
                        "backgroundColor": "transparent",
                        "display": "flex",
                        "width": "100%",
                        "justifyContent": "space-evenly",
                        "padding": 0,
                        "fontSize": "8px"
                    },
                    "tabCSS": {
                        "background": "none",
                        "border": "none",
                        "height": "100%",
                        "textAlign": "center",
                        "textTransform": "capitalize",
                        "margin": 0,
                        "padding": 0
                    },
                    "panelContainerCSS": {
                        "display": "block",
                        "height": "100%"
                    },
                    "panelCSS": {
                        "overflow": "hidden",
                        "height": "100%"
                    },
                    "tabListData": {
                        "label": "Your Tabs",
                        "tabButtonData": [{
                            "id": "one",
                            "tabPanelId": "one",
                            "label": "accessible label for tab one",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "two",
                            "tabPanelId": "two",
                            "label": "accessible label for tab two",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml2"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "three",
                            "tabPanelId": "three",
                            "label": "accessible label for tab three",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml3"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "four",
                            "tabPanelId": "four",
                            "label": "accessible label for tab four",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "tileStyle": {
                                                "desktop": {},
                                                "mobile": {}
                                            },
                                            "type": "builtin",
                                            "name": "div",
                                            "data": {
                                                "props": {
                                                    "style": {
                                                        "display": "flex",
                                                        "justifyContent": "center"
                                                    }
                                                },
                                                "components": ["BasicHtml4"]
                                            }
                                        }]
                                    }
                                }
                            }
                        }]
                    },
                    "tabPanelsData": {
                        "tabPanelData": [{
                            "id": "panel1",
                            "tabId": "panel1",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image1",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image1"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "panel2",
                            "tabId": "panel2",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image2",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image2"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "panel3",
                            "tabId": "panel3",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image3",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image3"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }, {
                            "id": "panel4",
                            "tabId": "panel4",
                            "className": "",
                            "children": {
                                "desktopAndMobile": {
                                    "shouldDisplay": true,
                                    "data": {
                                        "style": {},
                                        "components": [{
                                            "name": "LayeredContentModule",
                                            "type": "sitewide",
                                            "data": {
                                                "background": {
                                                    "className": "exampleBackgroundClassName",
                                                    "image": {
                                                        "alt": "A Placeholder Image",
                                                        "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image4",
                                                        "srcUrl": "https://via.placeholder.com/709x492?text=Image4"
                                                    }
                                                },
                                                "container": {
                                                    "style": {}
                                                }
                                            }
                                        }]
                                    }
                                }
                            }
                        }]
                    }
                }
            }]
        }
    }
}
```
  </pre>
  </details>

<details style={{border: "1px solid #afafaf", padding: "5px", borderRadius: "5px", margin: "5px 0 5px 0"}}>
<summary>Tabs: JSON with hover, same content for desktop and mobile & transition</summary>
<pre>

```json
{
  "desktopAndMobile": {
    "shouldDisplay": true,
    "data": {
      "style": {
        "maxWidth": "1248px",
        "margin": "0 auto",
        "position": "relative",
        "flexDirection": "column"
      },
      "components": [
        {
          "name": "Tabs",
          "type": "sitewide",
          "data": {
            "crossBrand": false,
            "id": "example-mui-tabs",
            "tabFontStyle": "secondary",
            "tabListCSS": {
              "position": "absolute",
              "bottom": "15px",
              "border": "none",
              "zIndex": 5,
              "backgroundColor": "transparent",
              "display": "flex",
              "width": "100%",
              "justifyContent": "space-evenly",
              "padding": 0
            },
            "tabCSS": {
              "background": "none",
              "border": "none",
              "height": "100%",
              "textAlign": "center",
              "textTransform": "capitalize",
              "margin": 0,
              "padding": 0
            },
            "panelContainerCSS": {
              "display": "block",
              "height": "100%"
            },
            "panelCSS": {
              "overflow": "hidden",
              "height": "100%"
            },
            "useHoverButtons": true,
            "transition": {
              "in": 1500,
              "out": 1500
            },
            "tabListData": {
              "label": "Your Tabs",
              "tabButtonData": [
                {
                  "id": "one",
                  "tabPanelId": "one",
                  "label": "accessible label for tab one",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "type": "builtin",
                            "name": "div",
                            "data": {
                              "props": {
                                "style": {
                                  "display": "flex",
                                  "justifyContent": "center"
                                }
                              },
                              "components": ["BasicHtml"]
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                {
                  "id": "two",
                  "tabPanelId": "two",
                  "label": "accessible label for tab two",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "type": "builtin",
                            "name": "div",
                            "data": {
                              "props": {
                                "style": {
                                  "display": "flex",
                                  "justifyContent": "center"
                                }
                              },
                              "components": ["BasicHtml2"]
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                {
                  "id": "three",
                  "tabPanelId": "three",
                  "label": "accessible label for tab three",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "type": "builtin",
                            "name": "div",
                            "data": {
                              "props": {
                                "style": {
                                  "display": "flex",
                                  "justifyContent": "center"
                                }
                              },
                              "components": ["BasicHtml3"]
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                {
                  "id": "four",
                  "tabPanelId": "four",
                  "label": "accessible label for tab four",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "tileStyle": {
                              "desktop": {},
                              "mobile": {}
                            },
                            "type": "builtin",
                            "name": "div",
                            "data": {
                              "props": {
                                "style": {
                                  "display": "flex",
                                  "justifyContent": "center"
                                }
                              },
                              "components": ["BasicHtml4"]
                            }
                          }
                        ]
                      }
                    }
                  }
                }
              ]
            },
            "tabPanelsData": {
              "tabPanelData": [
                {
                  "id": "panel1",
                  "tabId": "panel1",
                  "className": "",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "name": "LayeredContentModule",
                            "type": "sitewide",
                            "data": {
                              "background": {
                                "className": "exampleBackgroundClassName",
                                "image": {
                                  "alt": "A Placeholder Image",
                                  "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image1",
                                  "srcUrl": "https://via.placeholder.com/709x492?text=Image1"
                                }
                              },
                              "container": {
                                "style": {}
                              }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                {
                  "id": "panel2",
                  "tabId": "panel2",
                  "className": "",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "name": "LayeredContentModule",
                            "type": "sitewide",
                            "data": {
                              "background": {
                                "className": "exampleBackgroundClassName",
                                "image": {
                                  "alt": "A Placeholder Image",
                                  "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image2",
                                  "srcUrl": "https://via.placeholder.com/709x492?text=Image2"
                                }
                              },
                              "container": {
                                "style": {}
                              }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                {
                  "id": "panel3",
                  "tabId": "panel3",
                  "className": "",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "name": "LayeredContentModule",
                            "type": "sitewide",
                            "data": {
                              "background": {
                                "className": "exampleBackgroundClassName",
                                "image": {
                                  "alt": "A Placeholder Image",
                                  "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image3",
                                  "srcUrl": "https://via.placeholder.com/709x492?text=Image3"
                                }
                              },
                              "container": {
                                "style": {}
                              }
                            }
                          }
                        ]
                      }
                    }
                  }
                },
                {
                  "id": "panel4",
                  "tabId": "panel4",
                  "className": "",
                  "children": {
                    "desktopAndMobile": {
                      "shouldDisplay": true,
                      "data": {
                        "style": {},
                        "components": [
                          {
                            "name": "LayeredContentModule",
                            "type": "sitewide",
                            "data": {
                              "background": {
                                "className": "exampleBackgroundClassName",
                                "image": {
                                  "alt": "A Placeholder Image",
                                  "desktopSrcUrl": "https://via.placeholder.com/1259x420?text=Image4",
                                  "srcUrl": "https://via.placeholder.com/709x492?text=Image4"
                                }
                              },
                              "container": {
                                "style": {}
                              }
                            }
                          }
                        ]
                      }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  }
}
```

  </pre>
</details>
> You can find example `LayoutComponent` JSON data here:

> - The Notes section of [`LayoutComponent` in Storybook](/info/common-json-components-marketing-layoutcomponent--with-1-svgoverlay-images#json-config).

> - The Notes section of [`JSON Components > Carousel` in Storybook](/info/common-json-components-marketing-carousel--with-image-components#general-format).
