// @ts-nocheck
import { Brands, Theme } from '@ecom-next/core/react-stitch';
import getTypographyStyles from '.';
import { TypographyVariant } from '..';
import getBrandTypographyConfig from '../config';

const brands = ['gap', 'gapfs', 'br', 'brfs', 'on', 'at'];

describe('styles helpers', () => {
  describe('getTypographyStyles', () => {
    brands.forEach(brand => {
      const typographyConfig = getBrandTypographyConfig(brand as Brands);
      const theme = { brand, color: { b1: '#000000' } } as Theme;
      it('should get legal styles as body5', () => {
        const legalStyles = getTypographyStyles({
          variant: 'legal' as TypographyVariant,
          config: typographyConfig.body5,
          isDesktop: true,
          theme,
        });

        const bodyStyles = getTypographyStyles({
          variant: 'legal' as TypographyVariant,
          config: typographyConfig.body5,
          isDesktop: true,
          theme,
        });

        expect(legalStyles).toEqual(bodyStyles);
      });
    });

    describe('Old Navy brand', () => {
      const typographyConfig = getBrandTypographyConfig('on' as Brands);
      const theme = { brand: 'on', brandFont: { fontFamily: 'ON Sans Text' }, color: { b1: '#000000' } } as Theme;
      const headlines = ['headlineAlt1', 'headlineAlt2', 'headlineAlt3', 'headlineAlt4'];

      describe('feature flag enabled', () => {
        headlines.forEach(headline => {
          it(`should get ${headline} styles`, () => {
            const headlineStyles = getTypographyStyles({
              variant: headline as TypographyVariant,
              config: typographyConfig[headline],
              isDesktop: true,
              theme,
              enabledFeatures: {
                'on-font-redesign-2025': true,
              },
            });
            expect(headlineStyles.fontFamily).toBe('ON Sans Text');
          });
        });
      });

      describe('feature flag disabled', () => {
        headlines.forEach(headline => {
          it(`should get ${headline} styles`, () => {
            const headlineStyles = getTypographyStyles({
              variant: headline as TypographyVariant,
              config: typographyConfig[headline],
              isDesktop: true,
              theme,
              enabledFeatures: {
                'on-font-redesign-2025': false,
              },
            });
            expect(headlineStyles.fontFamily).toBeUndefined();
          });
        });
      });
    });
  });
});
