// @ts-nocheck
'use client';
import { ButtonProps } from '@ecom-next/core/legacy/button';
import { ModalProps as BaseModalProps } from '@ecom-next/core/legacy/modal';
import { Optional } from '@ecom-next/core/legacy/utility';
import { CSSObject, SerializedStyles, StyleFn, Theme } from '@ecom-next/core/react-stitch';
import { Size as BreakpointSize, SMALL } from '@ecom-next/core/breakpoint-provider';
import { EnabledFeaturesType } from '@ecom-next/core/legacy/feature-flags';

export enum BorderThickness {
  thin = 'thin',
  medium = 'medium',
  thick = 'thick',
}

export enum Capitalization {
  lowercase = 'lowercase',
  uppercase = 'uppercase',
  capitalize = 'capitalize',
}

export enum Color {
  dark = 'dark',
  light = 'light',
  custom = 'custom',
  black = 'black',
  white = 'white',
  primary = 'primary',
}

export enum Font {
  brandFont = 'brandFont',
  brandFontAlt = 'brandFontAlt',
}

export enum Size {
  xs = 'xs',
  small = 'small',
  medium = 'medium',
  large = 'large',
  xl = 'xl',
}

export enum Variant {
  solid = 'solid',
  outline = 'outline',
  border = 'border',
  underline = 'underline',
  flat = 'flat',
  chevron = 'chevron',
}

export type CustomColors = {
  foregroundColor: string;
  backgroundColor: string;
};

export type ComposableButtonBaseProps = ComposableButtonProps & {
  /**
   * Passes the viewport size
   */
  viewport?: BreakpointSize;
  /**
   * Passes any applicable feature flag
   */
  enabledFeatures?: EnabledFeaturesType;
  useOnCtaRedesign2024?: boolean;
};

export type ComposableButtonProps = ButtonProps & {
  /**
   * Specifies a border width
   */
  borderThickness?: BorderThickness;
  /**
   * Bright styles *no longer included
   */
  bright?: boolean;
  /** Sets the `text-transform` CSS property
   */
  capitalization?: Capitalization;
  /**
   * Text label for the button
   */
  children: React.ReactNode;
  /**
   * Allows for custom styling. See documentation for specifics
   */
  className?: string;
  /**
   * Sets the composable button color based on a dark, light or custom theme
   */
  color?: Color;
  /**
   * Sets custom composable button colors
   */
  customColors?: CustomColors;
  /**
   * Uses cross-brand styling, including fonts and colors
   */
  crossBrand?: boolean;
  /**
   * Sets the composable button font. Cross-brand and Bright fonts are used
   * when the respective props are set to `true`
   */
  font?: Font;
  /**
   * If true, button will be full width of the container
   */
  fullWidth?: boolean;
  /**
   * Adds :hover & :active states to the Composable Button
   */
  interactiveStyles?: boolean;
  /**
   * Set the size based on the Stitch size definitions in Composable Button
   */
  size?: Size;
  /**
   * Will create a "pill" button
   */
  pillShape?: boolean;
  /**
   * Determine if the button has rounded corners
   */
  roundedCorners?: boolean;
  /**
   * Set the variant of the button based on the Stitch definitions in Composable Button
   */
  variant?: Variant;
  /**
   * Set a prop "selected" on the composable button
   */
  selected?: boolean;
  borderRadius?: string;
};

export type ComposableButtonStyleFn<Return = SerializedStyles> = StyleFn<
  Partial<ComposableButtonBaseProps> & { enabledFeatures?: EnabledFeaturesType },
  Return
>;

export interface VariantSizeConfig extends CSSObject {
  fontSize?: string | number;
  fontWeight?: 'thin' | 'ultraLight' | 'extraLight' | 'light' | 'regular' | 'book' | 'medium' | 'demiBold' | 'semiBold' | 'bold' | 'black' | 'heavy';
  letterSpacing?: string | number;
  lineHeight?: string | number;
  minHeight?: string | number;
  maxWidth?: string | number;
  padding?: [string] | [string, string] | [string, string, string] | [string, string, string, string];
}

type SharedCssProps = {
  shared?: Partial<VariantSizeConfig>;
};

export type ComposableButtonViewportConfig = SharedCssProps & Record<Variant, SharedCssProps & Record<Size, VariantSizeConfig>>;

export type ComposableButtonConfig = Partial<Record<BreakpointSize, ComposableButtonViewportConfig>> & {
  defaults: {
    size: Size;
    variant: Variant;
  };
  [SMALL]: ComposableButtonViewportConfig;
};

export type ComposableButtonInteractiveStyleFn = StyleFn<ComposableButtonProps & Theme>;

export type ComposableButtonInteractiveConfig = {
  hoverFocus: Record<Variant, CSSObject>;
  active: Record<Variant, CSSObject>;
};

// TODO Rename this type
export const buttonStyles = {
  borderThickness: BorderThickness,
  capitalization: Capitalization,
  color: Color,
  customColors: {},
  font: Font,
  size: Size,
  variant: Variant,
};

export type ModalProps = Omit<Optional<BaseModalProps, 'isOpen'>, 'onClose' | 'children'> & {
  src: string;
  height?: string;
  width?: string;
};

export type IframeModalProps = ModalProps & Pick<BaseModalProps, 'isOpen' | 'onClose'>;
