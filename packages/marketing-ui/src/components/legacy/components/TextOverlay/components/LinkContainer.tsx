// @ts-nocheck
'use client';
import { Link } from '@ecom-next/core/migration/link';
import { CSSObject, forBrands, styled } from '@ecom-next/core/react-stitch';

const styledLink = styled(Link);

const basicLinkContainerStyle: CSSObject = {
  marginRight: '0.75rem',
  '&:last-child': {
    marginRight: 0,
  },
  '@media (max-width: 768px)': {
    '&:first-of-type': { borderTop: '1px solid #ccc' },
    background: '#FFFFFF',
    borderBottom: '1px solid #ccc',
    borderLeft: '0',
    borderRight: '0',
    color: '#122344',
    fontWeight: 'normal',
    margin: '0',
    padding: '1rem 0',
    textAlign: 'left',
    whiteSpace: 'normal',
    width: '100%',
  },
};

export const LinkContainer = styledLink(
  ({ theme }) =>
    forBrands(theme, {
      gap: () => basicLinkContainerStyle,
      on: () => basicLinkContainerStyle,
      br: () => ({
        marginRight: '0.75rem',
        '&:last-child': {
          marginRight: '0',
        },
        '@media (max-width: 768px)': {
          border: '1px solid #ccc',
          fontSize: '2.8vw',
          letterSpacing: '3px',
          lineHeight: 1,
          padding: '8px',
          textTransform: 'uppercase',
          width: '100%',
        },
        '@media (min-width: 1024px)': {
          fontSize: '0.75rem',
        },
      }),
    }) as string
);
