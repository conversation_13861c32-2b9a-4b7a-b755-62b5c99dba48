// @ts-nocheck
import React from 'react';
import { screen, render, RenderResult, RenderOptions, act } from 'test-utils';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { merge } from 'lodash';
import { DeprecatedStyleProps } from '../../types';
import {
  data,
  customContainerStyle,
  customLinksContainerStyle,
  expectedCustomContainerStyle,
  expectedCustomLinksContainerStyle,
  customLinkStyle,
  expectedCustomLinkStyle,
} from './__fixtures__';
import { ButtonList } from './index';
import { ViewStylesOptions } from './types';

const renderButtonList = (props = data, options?: RenderOptions): RenderResult => render(<ButtonList {...merge({}, props, data)} />, options);

const getListCtaContainer = (): HTMLElement => screen.getByTestId('list-cta-container');

describe('<ButtonList />', () => {
  test.each`
    isDesktop | styleProp
    ${true}   | ${'desktop'}
    ${false}  | ${'mobile'}
  `('should render ButtonList with custom $styleProp style', ({ isDesktop, styleProp }) => {
    renderButtonList(
      {
        containerStyle: customContainerStyle,
      },
      { breakpoint: isDesktop ? LARGE : SMALL }
    );

    expect(screen.getByTestId('button-list')).toHaveStyleRules(expectedCustomContainerStyle[styleProp as keyof DeprecatedStyleProps]);
  });

  test.each(['oneRow', 'twoRows', 'twoCols'] as ViewStylesOptions[])('should render ButtonList with viewStyle %p', viewStyle => {
    renderButtonList({
      viewStyle,
    });

    expect(getListCtaContainer()).toMatchSnapshot();
  });

  describe('<ListCtaContainer />', () => {
    test.each`
      isDesktop | styleProp
      ${true}   | ${'desktop'}
      ${false}  | ${'mobile'}
    `('should render listCtaContainer with custom $styleProp style', ({ isDesktop, styleProp }) => {
      renderButtonList(
        {
          linksContainerStyle: customLinksContainerStyle,
        },
        { breakpoint: isDesktop ? LARGE : SMALL }
      );

      expect(getListCtaContainer()).toHaveStyleRules(expectedCustomLinksContainerStyle[styleProp as keyof DeprecatedStyleProps]);
    });
  });

  describe('<Link />', () => {
    test.each`
      isDesktop | styleProp
      ${true}   | ${'desktop'}
      ${false}  | ${'mobile'}
    `('should render links with custom $styleProp style', ({ isDesktop, styleProp }) => {
      renderButtonList(
        {
          links: {
            style: customLinkStyle,
          },
        },
        { breakpoint: isDesktop ? LARGE : SMALL }
      );

      data.links?.content?.forEach(link => {
        expect(screen.getByText(link.text).parentElement).toHaveStyleRules(expectedCustomLinkStyle[styleProp as keyof DeprecatedStyleProps]);
      });
    });
  });
});
