// @ts-nocheck
'use client';
import React, { useMemo } from 'react';

import { logMessageHandler } from './util/logMessageHandler';
import { useViewportIsLarge } from './hooks';

import { parsePixelValue } from './helper/DimensionWrapper';
import { safeDocument, safeWindow } from './helper/safeWindow';

export const findFixedHeightByViewport = (fixedHeight, isLargeVP) => {
  if (typeof fixedHeight !== 'object') {
    return 0;
  }
  return isLargeVP ? fixedHeight?.desktop : fixedHeight?.mobile;
};

export const findDefaultHeightByViewport = (defaultHeight, isLargeVP) => {
  if (typeof defaultHeight === 'string' || typeof defaultHeight === 'number') {
    return defaultHeight;
  }
  if (typeof defaultHeight === 'object') {
    return isLargeVP ? defaultHeight?.large : defaultHeight?.small;
  }
  return 0;
};

export const findDimensionsWrapperHeight = (props, isLargeVP) => {
  try {
    if (!props) return 0;
    const { data, output, marketing } = props;

    const rootData = [data, output?.data, output?.output?.data, marketing, marketing?.data].find(field => field?.fixedHeight);

    const components = [
      data?.components,
      output?.data?.components,
      output?.output?.data.components,
      marketing?.components,
      marketing?.data?.components,
      [],
    ].find(item => Array.isArray(item));

    const compsHaveAnyFixedHeightProp = components
      .map(comp => !!(comp && Object.prototype.hasOwnProperty.call(comp, 'data') && Object.prototype.hasOwnProperty.call(comp.data, 'fixedHeight')))
      .some(comp => comp);
    if (!rootData && !compsHaveAnyFixedHeightProp) return 0;

    const rootFixedHeight = rootData?.fixedHeight ?? {};

    const getHeight = fixedHeight => {
      const height = findFixedHeightByViewport(fixedHeight, isLargeVP) || 0;
      return parsePixelValue(height);
    };

    const rootMinHeight = getHeight(rootFixedHeight);
    if (rootMinHeight && !components.length) {
      return rootMinHeight;
    }

    const childrenMinHeightCalcStr = components?.reduce((totalHeight, component, index) => {
      if (component?.data?.fixedHeight) {
        const componentMinHeight = getHeight(component.data.fixedHeight);

        return `${totalHeight}${componentMinHeight}${index < components.length - 1 ? ' + ' : ')'}`;
      }

      return `${totalHeight}0px${index < components.length - 1 ? ' + ' : ')'}`;
    }, 'calc(');

    const zeroHeightOrClosedCalcStr = childrenMinHeightCalcStr === 'calc(' ? '0px' : childrenMinHeightCalcStr;
    return zeroHeightOrClosedCalcStr ? `max(${rootMinHeight}, ${zeroHeightOrClosedCalcStr})` : rootMinHeight;
  } catch (error) {
    logMessageHandler({
      messageType: 'error',
      componentName: 'DimensionsWrapper',
      message: `Error calculating dimensions height: ${error}, props: ${props}`,
    });
    return 0;
  }
};

const findDimensionsWrapperHeightByContentData = (props, contentData, isLargeVP) => {
  try {
    const deliveryId = props?._meta?.deliveryId ?? '';
    if (!contentData || !deliveryId) return 0;
    const slotItems = Object.values(contentData);

    if (slotItems.length === 0) return 0;
    const contentItems = slotItems.flatMap(x => x?.contentItems || []).filter(x => x !== undefined);
    if (contentItems.length === 0) return 0;

    const matchingContentItem = contentItems.find(item => item?._meta?.deliveryId === deliveryId);

    if (!matchingContentItem?.data && !matchingContentItem?.output) return 0;

    return findDimensionsWrapperHeight(matchingContentItem, isLargeVP);
  } catch (error) {
    logMessageHandler({
      messageType: 'error',
      componentName: 'DimensionsWrapper',
      message: `Error calculating dimensions height by content data: ${error}, props: ${props}`,
    });
    return 0;
  }
};

export const generateId = (() => {
  let id = 999;
  return {
    reset: () => {
      id = 999;
    },
    next: () => {
      id += 1;
      return `${id}`;
    },
  };
})();

export const DimensionsWrapper = props => {
  const instance = useMemo(generateId.next, []);
  const { contentData, children } = props;
  const isLargeVP = useViewportIsLarge();
  let minHeight = 0;

  try {
    minHeight =
      contentData && Object.keys(contentData).length > 0
        ? findDimensionsWrapperHeightByContentData(props, contentData, isLargeVP)
        : findDimensionsWrapperHeight(props, isLargeVP);
  } catch (error) {
    logMessageHandler({
      messageType: 'error',
      componentName: 'DimensionsWrapper',
      message: `Error calculating dimensions height: ${error}, props: ${props}`,
    });

    minHeight = 0;
  }

  const isCSR = !!safeWindow() && !!safeDocument();
  let computedMinHeight = '0px';

  if (isCSR) {
    // this try-catch would not actually be needed in a browser environment,
    // but is needed for some of the jest tests since jest-dom does
    // a simulated mock of the `window.getComputedStyle`
    try {
      const window = safeWindow();
      // <template /> has a `display: none` initially so it doesn't render by default
      const elem = document.createElement('template');
      elem.style['min-height'] = minHeight;
      document.body.appendChild(elem);
      computedMinHeight = window.getComputedStyle(elem).getPropertyValue('min-height');
      document.body.removeChild(elem);
    } catch (e) {
      computedMinHeight = minHeight;
    }
  }

  const selector = `&[data-instance="${instance}"]:has([class]), &:has([data-testid='homeloadingPlaceholder'])`;
  const minHeightStyles = {
    [selector]: {
      minHeight: !isCSR ? minHeight : computedMinHeight,
    },
  };

  return (!isCSR && minHeight) || (isCSR && minHeight && computedMinHeight !== '0px') ? (
    <div css={minHeightStyles} data-instance={instance} data-testid='dimensions-wrapper'>
      {children}
    </div>
  ) : (
    children
  );
};
