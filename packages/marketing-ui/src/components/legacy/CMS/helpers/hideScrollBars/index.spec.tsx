// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { hideScrollBars } from '.';

describe('ScrollComponent', () => {
  it('should hide scroll bars', () => {
    const result = render(
      <div
        css={{
          maxWidth: '736px',
          maxHeight: '414px',
          overflow: 'scroll',
          ...hideScrollBars,
        }}
      >
        <div
          css={{
            width: '1024px',
            height: '100%',
          }}
        >
          Inner div (children) is larger than outer div (parent)
        </div>
      </div>
    );

    expect(result.container.firstChild.firstChild).toHaveStyleRule('scrollbar-width', 'none');
  });
});
