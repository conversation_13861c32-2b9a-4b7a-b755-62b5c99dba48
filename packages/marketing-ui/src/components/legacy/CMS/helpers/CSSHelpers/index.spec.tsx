// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { generateBackgroundTypeCSSWithAspectRatio, generateDualImageBackgroundCSS, getSubCatBannerSlotStyles } from './index';

describe('IsDoubleBackground', () => {
  it('should return a background with Two url when receive two images', () => {
    const expectedReturn = {
      background: 'url(image1.jpg) left no-repeat, url(image2.jpg) right no-repeat',
      backgroundSize: '50% 100%',
    };

    expect(generateDualImageBackgroundCSS('image1.jpg', 'image2.jpg')).toStrictEqual(expectedReturn);
  });

  it('should return a background with One url when receive one image', () => {
    const expectedReturn = {
      background: 'url(image1.jpg) left no-repeat',
      backgroundSize: 'cover',
    };

    expect(generateDualImageBackgroundCSS('image1.jpg')).toStrictEqual(expectedReturn);
  });

  it('should return sub category banner slot styles', () => {
    expect(getSubCatBannerSlotStyles('subcat-header')).toEqual({
      marginLeft: '0.5rem',
      marginRight: '0.5rem',
    });
  });
});

describe('helper functions', () => {
  it('should render with background as gradient for background type gradient', () => {
    const styleObj: CSSObject = {
      ...generateBackgroundTypeCSSWithAspectRatio({
        viewport: 'desktop',
        background: {
          type: 'gradient',
          gradient: { from: '#535435', to: '#2342' },
        },
      }),
    };
    const { container } = render(<div css={styleObj} />);
    expect(container.querySelector('div')).toHaveStyle({
      backgroundColor: '-webkit-linear-gradient(45deg, #535435,#2342',
    });
  });

  it('should render with background as color for solid background type', () => {
    const styleObj: CSSObject = {
      ...generateBackgroundTypeCSSWithAspectRatio({
        viewport: 'desktop',
        background: {
          type: 'solid',
          color: 'red',
        },
      }),
    };
    const { container } = render(<div css={styleObj} />);
    expect(container.firstChild.querySelector('div')).toHaveStyle({ background: 'red' });
  });
});
