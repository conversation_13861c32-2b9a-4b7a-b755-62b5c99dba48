# Math Library

This whole library is immutable, all return values are new values

## Purpose

The math functions in this library enable the Amplience crop function, `get_amplience_crop_rect`, and the point of interest (`POI`) function, `get_amplience_poi_rect`, to edit Amplience images on the client side.

### `get_centered_rect`

This function centers a constrained Rect around a point.
arguments:

- `rect`: the rectangle we want to center
- `center`: the point we want to center it around
- `bounds`: the bounding rectangle we want to constrain ourselves to

What it returns is a new Rect object that represents the shape we want to cut out of the image, as a percentage of the input rectangle.

### `get_centered_rect_position`

This function finds the bounded top-left corner of the rectangle we want to place within a bounding-box.
arguments:

- `rect`: the rectangle we want to place
- `center`: the center point of the rectangle
- `bounds`: a rectangle that represents the bounding-box where we will place the `rect`

### `convert_from_percent_to_pixels`

This function takes a point in percentage and a rectangle. It returns a new point that represents the point that was passed into the function in pixel units.

### `find_biggest_rect`

It takes `input` and `output` rects and returns a rect that represents the biggest rect that can be cut from `input` in the shape of `output`. Biggest denotes the greatest size, tallest or widest.

### `get_aspect_ratio`

Given a `rect` object, it returns a number that represents the aspect ratio of the rectangle.

### `scale_by_height`

Given a `rect` object and a number that represents a `height`. It returns a rect scaled by the `height`.

### `scale_by_width`

Given a `rect` object and a number that represents a `width`. It returns a rect scaled by the `width`.
