// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { SMALL, LARGE, Size } from '@ecom-next/core/breakpoint-provider';
import { CommunitySpotlightGeneral, CommunitySpotlightGeneralType } from './CommunitySpotlightGeneral';

const defaultData: CommunitySpotlightGeneralType = {
  general: {
    background: {
      type: 'solid',
      color: '#DDDDDD',
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#555555',
        },
      },
    ],
  },
};

const renderComponent = (props?: Partial<CommunitySpotlightGeneralType>, breakpoint: Size = LARGE) =>
  render(
    <CommunitySpotlightGeneral {...defaultData} {...props}>
      <p>Hello world!</p>
    </CommunitySpotlightGeneral>,
    {
      breakpoint,
    }
  );

describe('CommunitySpotlightGeneral', () => {
  it('should match snapshot', () => {
    const result = renderComponent();
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('Should pick the correct background on desktop', () => {
    const { container } = renderComponent();
    const background = container.querySelector('div div div');
    expect(background).toHaveStyleRule('background', '#DDDDDD');
  });

  it('Should pick the correct background on mobile', () => {
    const { container } = renderComponent({}, SMALL);
    const background = container.querySelector('div div div');
    expect(background).toHaveStyleRule('background', '#555555');
  });

  it('should default to desktop background', () => {
    const noMobileBgProps: CommunitySpotlightGeneralType = {
      general: {
        background: {
          type: 'solid',
          color: '#DDDDDD',
        },
      },
    };
    const { container } = renderComponent(noMobileBgProps, SMALL);
    const background = container.querySelector('div div div');
    expect(background).toHaveStyleRule('background', '#DDDDDD');
  });
});
