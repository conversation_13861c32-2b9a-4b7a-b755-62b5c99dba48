// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { SMALL, LARGE, Size } from '@ecom-next/core/breakpoint-provider';
import { RichTextContent, RichTextContentType } from './RichTextContent';

const defaultData: RichTextContentType = {
  text: {
    defaultText: 'Text on desktop',
    mobileOverride: 'Text on mobile',
  },
  css: {
    width: '75%',
  },
};

const renderComponent = (props?: Partial<RichTextContentType>, breakpoint: Size = LARGE) =>
  render(<RichTextContent {...defaultData} {...props} />, {
    breakpoint,
  });

describe('RichTextContent', () => {
  it('should match snapshots', () => {
    const result = renderComponent();
    expect(result.asFragment()).toMatchSnapshot();
  });

  describe('Mobile override', () => {
    it('should pick text for desktop', () => {
      const { getByText } = renderComponent();
      expect(getByText('Text on desktop')).toBeInTheDocument();
    });

    it('should pick text for mobile', () => {
      const { getByText } = renderComponent({}, SMALL);
      expect(getByText('Text on mobile')).toBeInTheDocument();
    });
  });

  describe('RichText style', () => {
    it('should have width 75%', () => {
      const { container } = renderComponent();
      const richText = container.firstChild.querySelector('div div');
      expect(richText).toHaveStyleRule('width', '75%');
    });
  });
});
