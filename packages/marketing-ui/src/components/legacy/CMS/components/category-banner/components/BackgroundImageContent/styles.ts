// @ts-nocheck
'use client';
import { CSSObject, styled } from '@ecom-next/core/react-stitch';
import { BackgroundImageContainerProps, BackgroundImageStylesProps } from './types';

const generateGridTemplateColumnsValue = (imageRatio: string[], isMobile: boolean): string => {
  const mobileGrid = '1fr 1fr';

  if (imageRatio.length >= 3) {
    return isMobile ? mobileGrid : `${imageRatio[0]}fr ${imageRatio[1]}fr ${imageRatio[2]}fr`;
  }

  if (imageRatio.length === 2) {
    return isMobile ? mobileGrid : `${imageRatio[0]}fr ${imageRatio[1]}fr`;
  }

  return '1fr';
};

const backgroundImageStyles = ({ imageRatio, imageUrl, index, isMobile }: BackgroundImageStylesProps): CSSObject => {
  const gridMappings = {
    1: {
      row: imageRatio.length < 3 ? '1/3' : '1/2',
      column: '2/4',
    },
    2: {
      row: '2/3',
      column: '2/4',
    },
    default: {
      row: '1/3',
      column: 'auto',
    },
  };

  const { row: gridRowValue, column: gridColumnValue } = gridMappings[index] || gridMappings.default;

  return {
    boxSizing: 'border-box',
    width: '100%',
    height: '100%',
    gridRow: isMobile ? gridRowValue : gridMappings.default.row,
    gridColumn: isMobile ? gridColumnValue : gridMappings.default.column,
    backgroundImage: imageUrl ? `url(${imageUrl})` : '',
    backgroundPosition: 'center',
    backgroundSize: 'cover',
  } as CSSObject;
};

const backgroundImageContainerStyles = ({ imageRatio, isMobile }: BackgroundImageContainerProps): CSSObject => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  display: 'grid',
  gridTemplateColumns: generateGridTemplateColumnsValue(imageRatio, isMobile),
  gap: isMobile ? '7.5px' : '15px',
  gridTemplateRows: '1fr 1fr',
});

export const BackgroundImage = styled.div<BackgroundImageStylesProps>(backgroundImageStyles);

export const Container = styled.div<BackgroundImageContainerProps>(backgroundImageContainerStyles);
