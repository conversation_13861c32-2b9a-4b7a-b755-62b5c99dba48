// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { fontWeightMap } from '../../types/amplience';
import { Typography } from '../../../components/Typography';
import { CountdownTimerType } from '../../content-types/SitewideBanner/types';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
import { useCountdown } from './hooks/useCountdown';

const CountdownClock = (props: CountdownTimerType): JSX.Element => {
  const { format, typography, hideDays, endDate: endDateInput } = props;

  const { defaultTypography, mobileOverrideTypography } = typography;
  const displayDays = !hideDays;
  const displayAbbr = format === 'abbreviatedTimeFormat';
  const isDesktop = useViewportIsLarge();

  const hasMobileOverrides = !isDesktop && !!mobileOverrideTypography;
  const renderedStyles = hasMobileOverrides ? { ...defaultTypography, ...mobileOverrideTypography } : { ...defaultTypography };

  const { size, weight, color, clockCapitalized } = renderedStyles;
  const fontWeightValue = weight ? fontWeightMap[weight.toString()] : undefined;
  const fontWeight = fontWeightValue === undefined ? {} : { fontWeight: fontWeightValue };

  const customStyles: CSSObject = {
    color,
    ...fontWeight,
    whiteSpace: 'nowrap',
  };

  const timeDisplayText = {
    second: {
      showAtZero: true,
      textSingular: 'secs',
      singleDigitPrefix: '0',
    },
    minute: {
      showAtZero: true,
      singleDigitPrefix: '0',
    },
    hour: {
      showAtZero: true,
      singleDigitPrefix: '0',
    },
    day: {
      showAtZero: true,
      singleDigitPrefix: '0',
    },
  };
  const { timeToDisplay } = useCountdown({
    endDateInput,
    displayDays,
    displayAbbr,
    clockCapitalized,
    timeDisplayText,
  });
  return (
    <Typography customStyles={customStyles} variant={size}>
      <span data-testid='timer' suppressHydrationWarning>
        {timeToDisplay}
      </span>
    </Typography>
  );
};

export default CountdownClock;
