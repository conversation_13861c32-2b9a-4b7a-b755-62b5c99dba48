// @ts-nocheck
import React from 'react';
import { render, screen } from '@testing-library/react';
import IIIFImage, { IIIFImageProps } from './index';

const imageData: IIIFImageProps['imageData'] = {
  image: {
    _meta: {
      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
    },
    defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
    endpoint: '/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/max/0.0/default.jpg',
    name: '',
    id: '9ee7799f-3cf6-42b7-a818-a69497399263',
  },
  altText: 'Test Image',
  variations: [
    {
      variation: 'desktop',
    },
    {
      variation: 'mobile',
      poi: {
        x: 0.5,
        y: 0.5,
      },
    },
  ],
  fliph: false,
  flipv: false,
  enableChroma: false,
  chromaQuality: 80,
};

describe('IIIFImage component', () => {
  test('renders the component with image data', () => {
    render(<IIIFImage imageData={imageData} />);
    const imgElement = screen.getByAltText('Test Image');
    expect(imgElement).toBeInTheDocument();
  });

  test('constructs the correct image URL', () => {
    render(<IIIFImage imageData={imageData} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/max/0.0/default.jpg';
    expect(imgElement.src).toBe(expectedUrl);
  });

  test('applies correct transformations for flip horizontally and vertically', () => {
    const flippedImageData = { ...imageData, fliph: true, flipv: true };
    render(<IIIFImage imageData={flippedImageData} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    expect(imgElement).toHaveStyle('transform: scaleX(-1) scaleY(-1)');
  });

  test('applies correct transformation for flip horizontally only', () => {
    const fliphImageData = { ...imageData, fliph: true, flipv: false };
    render(<IIIFImage imageData={fliphImageData} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    expect(imgElement).toHaveStyle('transform: scaleX(-1)');
  });

  test('applies correct transformation for flip vertically only', () => {
    const flipvImageData = { ...imageData, fliph: false, flipv: true };
    render(<IIIFImage imageData={flipvImageData} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    expect(imgElement).toHaveStyle('transform: scaleY(-1)');
  });

  test('constructs the correct image request URI syntax', () => {
    render(<IIIFImage imageData={imageData} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/max/0.0/default.jpg';
    const uriRegex = /^https:\/\/2hmc8flz4ofg1dy89cc38f2xm\.staging\.bigcontent\.io\/IIIF3\/Image\/[\w-]+\/full\/max\/0.0\/default\.jpg$/;
    expect(imgElement.src).toMatch(uriRegex);
    expect(imgElement.src).toBe(expectedUrl);
  });

  test('constructs the correct image information request URI syntax', () => {
    const { defaultHost, endpoint } = imageData.image;
    const baseUrl = `https://${defaultHost}${endpoint.split('/').slice(0, -4).join('/')}`;
    const expectedInfoUrl = `${baseUrl}/info.json`;
    const uriRegex = /^https:\/\/2hmc8flz4ofg1dy89cc38f2xm\.staging\.bigcontent\.io\/IIIF3\/Image\/[\w-]+\/info\.json$/;
    expect(expectedInfoUrl).toMatch(uriRegex);
  });

  test('constructs the correct image URL with custom region', () => {
    render(<IIIFImage imageData={imageData} region='square' />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/square/max/0.0/default.jpg';
    expect(imgElement.src).toBe(expectedUrl);
  });

  test('constructs the correct image URL with custom size', () => {
    render(<IIIFImage imageData={imageData} size='150,' />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/150,/0.0/default.jpg';
    expect(imgElement.src).toBe(expectedUrl);
  });

  test('constructs the correct image URL with custom rotation', () => {
    render(<IIIFImage imageData={imageData} rotation={90} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/max/90.0/default.jpg';
    expect(imgElement.src).toBe(expectedUrl);
  });

  test('constructs the correct image URL with custom quality', () => {
    render(<IIIFImage imageData={imageData} quality='gray' />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/max/0.0/gray.jpg';
    expect(imgElement.src).toBe(expectedUrl);
  });

  test('constructs the correct image URL with custom format', () => {
    render(<IIIFImage format='png' imageData={imageData} />);
    const imgElement = screen.getByAltText('Test Image') as HTMLImageElement;
    const expectedUrl = 'https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/IIIF3/Image/c21h2u34skb8o2k4e4ek50632sla520e/full/max/0.0/default.png';
    expect(imgElement.src).toBe(expectedUrl);
  });
});
