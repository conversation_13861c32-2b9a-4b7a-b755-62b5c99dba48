'use client';
import React from 'react';
import { AdvanceImageData, MediaImageLink } from '../../global/types';
import isAdvanceImageDataArray from '../../helpers/type-guards/isAdvanceImageDataArray';
import AmplienceImage from '../../subcomponents/AmplienceImage/index';

export interface ISMBannerImageOrIconProps {
  src: MediaImageLink | AdvanceImageData[] | AdvanceImageData;
  size: string;
  className?: string;
  fetchPriorty?: HTMLImageElement['fetchPriority'];
}

const ISMBannerImageOrIcon = (props: ISMBannerImageOrIconProps): JSX.Element => {
  const { src, size, className, fetchPriorty } = props;
  if (isAdvanceImageDataArray(src)) {
    const imageIconSize: number = parseInt(size, 10);
    return <AmplienceImage className={className} css={{ maxHeight: size }} height={imageIconSize} {...(fetchPriorty ? { fetchPriorty } : {})} src={src[0]} />;
  }

  return <AmplienceImage className={className} css={{ maxHeight: size }} {...(fetchPriorty ? { fetchPriorty } : {})} src={src} />;
};

export default ISMBannerImageOrIcon;
