// @ts-nocheck
'use client';
import { useTheme, CSSObject } from '@ecom-next/core/react-stitch';
import React from 'react';
import { Typography, TypographyProps } from '../../../components/Typography';
import { RichText } from '../../subcomponents/RichText';
import { containsHTML } from '../../../helper/containsHTML';

interface ISMBannerBodyTextProps extends Pick<TypographyProps, 'variant'> {
  color?: string;
  fontSize?: string | number;
  children?: string;
  customStyles?: CSSObject;
}

const ISMBannerBodyText = (props: ISMBannerBodyTextProps): JSX.Element | null => {
  const { children, color, fontSize, variant, customStyles = {} } = props;
  const theme = useTheme();
  const isAT = theme.brand === 'at';

  if (!children) {
    return null;
  }
  if (containsHTML(children)) {
    return <RichText text={children} />;
  }
  return (
    <Typography
      css={{
        color: color || theme.color.bk,
        ...(!isAT
          ? {
              ...theme.brandFont,
              fontSize: fontSize || '14px',
              lineHeight: fontSize ? 'normal' : 18 / 14,
            }
          : {}),
        ...customStyles,
      }}
      variant={variant}
    >
      {children}
    </Typography>
  );
};

export default ISMBannerBodyText;
