// @ts-nocheck
import { ControlButtonPlacement } from './types';
import { getMuteUnmuteButtonStylesAT, getPlayPauseButtonStylesAT, getVolumeSlideStylesAT } from './utils.at';

const placements: ControlButtonPlacement[] = ['lowerLeft', 'lowerRight', 'upperLeft', 'upperRight'];

describe('ISM Banner AT utils', () => {
  describe('invoke getPlayPauseButtonStylesAT', () => {
    placements.forEach(place => {
      test(`should match snapshot for ${place}`, () => {
        const result = getPlayPauseButtonStylesAT({
          playPauseButtonPlacement: place,
        });

        expect(result).toMatchSnapshot();
      });

      test(`should match snapshot for ${place} and sound icons`, () => {
        const result = getPlayPauseButtonStylesAT({
          playPauseButtonPlacement: place,
          nonVimeovideoSoundIcons: true,
        });

        expect(result).toMatchSnapshot();
      });
    });
  });

  describe('invoke getMuteUnmuteButtonStylesAT', () => {
    placements.forEach(place => {
      test(`should match snapshot for ${place}`, () => {
        const result = getMuteUnmuteButtonStylesAT({
          playPauseButtonPlacement: place,
        });

        expect(result).toMatchSnapshot();
      });
      test(`should match snapshot for ${place} and sound icons`, () => {
        const result = getMuteUnmuteButtonStylesAT({
          playPauseButtonPlacement: place,
          nonVimeovideoSoundIcons: true,
        });

        expect(result).toMatchSnapshot();
      });
    });
  });

  describe('invoke getVolumeSlideStylesAT', () => {
    placements.forEach(place => {
      test(`should match snapshot for ${place}`, () => {
        const result = getVolumeSlideStylesAT({
          playPauseButtonPlacement: place,
        });

        expect(result).toMatchSnapshot();
      });
      test(`should match snapshot for ${place} and sound icons`, () => {
        const result = getVolumeSlideStylesAT({
          playPauseButtonPlacement: place,
          nonVimeovideoSoundIcons: true,
        });

        expect(result).toMatchSnapshot();
      });
    });
  });
});
