'use client';
import React from 'react';
import AspectRatioContainer from '../../subcomponents/AspectRatioContainer/index';
import { BackgroundTypeExtensionValue } from '../../types/amplience';
import { ContentDiv, WrapperDiv } from './styles';
import { ISMBannerCarouselSlideProps } from './types';

const ISMBannerCarouselSlide = (props: ISMBannerCarouselSlideProps): JSX.Element => {
  const { src, className, children, aspectRatio } = props;

  const [width, height]: number[] = aspectRatio ? aspectRatio.split(':').map(part => +part) : [];
  return (
    <WrapperDiv>
      <AspectRatioContainer
        background={
          {
            type: 'image',
            images: src,
          } as BackgroundTypeExtensionValue
        }
        className={className}
        height={height}
        width={width}
      />
      {children && <ContentDiv>{children}</ContentDiv>}
    </WrapperDiv>
  );
};

export default ISMBannerCarouselSlide;
