// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { cleanup, render, screen, act } from 'test-utils';
import { SMALL, XLARGE, Size, LARGE } from '@ecom-next/core/breakpoint-provider';
import ShoppingBag from './index';
import { shoppingBagPropData } from '../../content-types/ShoppingBag/__fixtures__/test-data';
import { ShoppingBagProps } from './types';

const defaultProps: ShoppingBagProps = shoppingBagPropData;

jest.mock('../../subcomponents/RichText', () => {
  const actualModule = jest.requireActual('../../subcomponents/RichText');
  return {
    __esModule: true,
    ...actualModule,
    RichText: jest.fn(actualModule.RichText),
  };
});

jest.mock('../../subcomponents/CTAButton', () => ({
  __esModule: true,
  CtaButton: jest.fn(jest.fn(jest.requireActual('../../subcomponents/CTAButton').CtaButton)),
}));

describe('ShoppingBag Component', () => {
  afterEach(cleanup);
  const renderComponent = (props?: Partial<ShoppingBagProps>, breakpoint: Size = XLARGE) =>
    render(<ShoppingBag {...defaultProps} {...props} />, {
      breakpoint,
    });

  it('should match snapshots on mobile', () => {
    const { container } = renderComponent({}, SMALL);
    expect(container).toMatchSnapshot();
  });

  it('should match snapshots on desktop', () => {
    const { container } = renderComponent();
    expect(container).toMatchSnapshot();
  });

  it('should render wtih correct width and minHeight', () => {
    const { container } = renderComponent();
    const element = container.firstChild.firstChild;
    expect(element).toHaveStyle({ minHeight: '268px', maxWidth: '325px' });
  });

  it('text should alignment to the top', () => {
    const { container } = renderComponent();
    expect(container).toMatchSnapshot();
  });

  it('text should alignment to the middle', () => {
    const { container } = renderComponent({
      verticalCtaAlignment: 'middle',
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('cta should alignment to the middle', () => {
    const { container } = renderComponent({
      verticalCtaAlignment: 'middle',
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('cta should alignment to the bottom', () => {
    const { container } = renderComponent({
      verticalCtaAlignment: 'bottom',
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('text should alignment to the bottom', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'bottom',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('should cover for key value ', () => {
    const bannerLink = { ...defaultProps.bannerLink };
    expect(bannerLink).toHaveProperty('value', 'gap.com');
  });

  it('icon should vertically align to the top', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('icon should vertically align to the middle', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'middle',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('icon should vertically align to the bottom', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'bottom',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('icon should horizontally align to the left', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'left',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('icon should horizontally align to the center', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'center',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('icon should horizontally align to the right', () => {
    const { container } = renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'right',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(container).toMatchSnapshot();
  });

  it('should have padding bottom and margin bottom 10px & 0px', () => {
    const { container } = renderComponent({
      details: {
        detailsContent: '',
        detailsLink: '',
        detailsPrefix: '',
      },
      ctaButton: {
        label: '',
        value: '',
      },
    });

    expect(container?.firstChild.firstChild.firstChild.firstChild).toHaveStyleRule('padding', '10px');
  });

  it('should have only Rich Text area 2', () => {
    const { asFragment } = renderComponent({
      slide: {
        background: {
          type: 'solid',
          color: '#6fd2e9',
        },
        icon: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '6c1cf159-efdf-491b-8520-094ff8f030f6',
              name: '220307_43-M6559_Color_Yellow_CTA_W_VIBanner',
              endpoint: 'oldnavy',
              defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
            },
            altText: '',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 2</span></p>',
      },
    });
    expect(asFragment()).toMatchSnapshot();
  });

  it('should have css order 1', () => {
    renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'right',
        iconVerticalPlacement: 'top',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(screen.getByRole('presentation')?.parentElement).toHaveStyleRule('order', '1');
  });
  it('should have css order 2', () => {
    renderComponent({
      webAppearance: {
        iconHorizontalPlacement: 'right',
        iconVerticalPlacement: 'middle',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(screen.getByRole('presentation')?.parentElement).toHaveStyleRule('order', '2');
  });
  it('should have css order 3', () => {
    renderComponent({
      ctaButton: undefined,
      webAppearance: {
        iconHorizontalPlacement: 'right',
        iconVerticalPlacement: 'bottom',
        verticalTextAlignment: 'middle',
        ctaButtonStyling: {
          buttonStyle: 'border',
          buttonColor: 'dark',
          primaryHex: '#e45',
          secondaryHex: '#e67',
        },
        desktopImageOrIconSize: '64px',
        detailsLinkFontColor: '#e45',
      },
    });
    expect(screen.getByRole('presentation')?.parentElement).toHaveStyleRule('order', '3');
  });

  it('should not have a11y violations', async () => {
    const { container } = renderComponent();
    expect(await axe(container)).toHaveNoViolations();
  });

  it('should have 325px width when viewport is large', () => {
    const { container } = renderComponent({}, LARGE);

    const wrapper = container.firstChild.querySelector('div div');

    expect(wrapper).toHaveStyleRule('max-width', '325px');
  });

  it('should have 100% width when viewport is small', () => {
    const { container } = renderComponent({}, SMALL);

    const wrapper = container.firstChild.querySelector('div div');

    expect(wrapper).toHaveStyleRule('max-width', '100%');
  });
});
