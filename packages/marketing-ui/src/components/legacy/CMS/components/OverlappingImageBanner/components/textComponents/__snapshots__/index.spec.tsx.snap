// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Text Components should match the snapshots: Eyebrow 1`] = `
.emotion-0 {
  color: #34343;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 12px;
  letter-spacing: 0;
  text-align: right;
  margin-right: 30px;
  width: 15%;
  margin-left: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    />
  </div>
</div>
`;

exports[`Text Components should match the snapshots: Headline 1`] = `
.emotion-0 {
  color: #34343;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
  padding-left: 7%;
  width: -webkit-min-content;
  width: -moz-min-content;
  width: min-content;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <h1
      class="emotion-0"
    >
      I'm sample text
    </h1>
  </div>
</div>
`;

exports[`Text Components should match the snapshots: HeadlineDivider 1`] = `
.emotion-0 {
  height: 1px;
  background-color: #34343;
  margin: 5% 0 3%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <hr
      class="emotion-0"
      role="presentation"
    />
  </div>
</div>
`;

exports[`Text Components should match the snapshots: SubCopy 1`] = `
.emotion-0 {
  color: #34343;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: max(14px, min(1.5vw,18px));
  letter-spacing: 0;
  text-align: right;
  margin-right: 30px;
  width: 15%;
  margin-left: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <span
      class="emotion-0"
    />
  </div>
</div>
`;

exports[`Text Components should match the snapshots: SubHeading should match snapshots 1`] = `
.emotion-0 {
  color: #34343;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
  padding-left: 7%;
  box-sizing: border-box;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <p
      class="emotion-0"
    />
  </div>
</div>
`;
