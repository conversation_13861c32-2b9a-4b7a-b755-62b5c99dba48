// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Helpers HOC Styles for Overlapping 2 Image Banner should match snapshots 1`] = `
.emotion-0 {
  padding-top: 7.8125%;
  grid-column: 1/span 8;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    />
  </div>
</div>
`;

exports[`Helpers HOC Styles for Overlapping 3 Image Banner should match snapshots 1`] = `
.emotion-0 {
  padding-top: 10%;
  grid-column: 1/span 9;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    />
  </div>
</div>
`;
