// @ts-nocheck
import { StorytellingAndProductCollectionType } from '../../../content-types/StorytellingAndProductCollection/types';

export const exampleCardText1 = 'Soccer Shorts';
export const exampleCardText2 = 'Soft Sweatshirt';
export const exampleCardText3 = '$20';
export const exampleCardText4 = '$40';

export const defaultComponentData: StorytellingAndProductCollectionType = {
  _meta: {
    name: 'Storytelling and Product Collection (Storybook)',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-collection.json',
    deliveryId: '7d87b008-f3e5-43ec-ac25-c60162740839',
  },
  mainImage: {
    defaultImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'd5466084-b6a0-4fcd-b7c4-27f6193fc0e6',
          name: 'HOL1_Winter-Essentials_S',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'Girl skating in winter clothes',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'a5a0bcfa-adea-4f8c-a10b-0a2406f63fda',
          name: 'CMS_HOL22_D1_ATG_Tops_S',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'Girl in skating gear',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    imageHandle:
      '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">Winter Collection</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">Wearing Girls Size 4</span></p>',
  },
  storytellingProductCollection: {
    general: {
      background: {
        background: {
          type: 'gradient',
          gradient: {
            from: '#EBFAFF',
            to: '#39CED4',
          },
        },
        mobileBackground: [
          {
            mobileBackground: {
              type: 'solid',
              color: '#D7A6FF',
            },
          },
        ],
      },
    },
    content: {
      icon: {
        iconSize: '48px',
        icon: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'c29f3caf-c3fb-4df1-b88b-a2b5b2f1064e',
              name: 'GIRL_logo@2x',
              endpoint: 'athleta',
              defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
            },
            altText: 'Athleta Girl Logo',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      mobileIcon: {
        iconSize: '24px',
        icon: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '281bc951-a693-4dcf-88f7-b24d3bd475fb',
              name: 'ATHLETA_logo',
              endpoint: 'athleta',
              defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
            },
            altText: 'Athleta Brand',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      text: {
        upperText: {
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4" style="color:#FFFFFF">For the Love of the Game</span></p>',
          defaultText:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4" style="color:#FFFFFF">For the Love of the Game</span></p>',
        },
        lowerText: {
          defaultText:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Simone embodies our brand through her advocacy, mentorship and work in the community. We’ve collaborated to bring the same elements of confidence, strength, and grace she demonstrates on and off the mat into future collections for girls.</span></p>',
          mobileOverride:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1"> We’ve collaborated to bring the same elements of confidence, strength, and grace Simone Biles demonstrates on and off the mat into future collections for girls.</span></p>',
        },
      },
      ctaButtons: [
        {
          buttonStyle: {
            buttonStyle: 'solid',
            buttonColor: 'light',
          },
          cta: {
            label: 'Tops',
            value: '#',
          },
        },

        {
          buttonStyle: {
            buttonStyle: 'chevron',
            buttonColor: 'custom',
            primaryHex: '#FFFFFF',
            secondaryHex: '#BF3FBF',
          },
          cta: {
            label: 'Bottoms',
            value: '#',
          },
        },
      ],
      productCards: [
        {
          cards: {
            image: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '4ebc548d-ae60-422a-a868-c1ffe1a60edf',
                  name: 'stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_girl_grid_jpg',
                  endpoint: 'athleta',
                  defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                },
                altText: 'Young girl in running outfit',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            text: `<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="font-weight:600">${exampleCardText1}</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">${exampleCardText3}</span></p>`,
            url: {
              value: 'www.gap.com',
            },
          },
        },
        {
          cards: {
            image: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '649c7eea-3679-4528-8f80-ce99904fc0ec',
                  name: 'stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_GIRL_jpg',
                  endpoint: 'athleta',
                  defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
                },
                altText: 'Girls stretching',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            text: `<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="font-weight:600">${exampleCardText2}</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">${exampleCardText4}</span></p>`,
            url: {
              value: 'www.gap.athleta.com',
              label: 'Girls stretching',
            },
          },
        },
      ],
    },
  },
};
