// @ts-nocheck
import React from 'react';
import { render } from 'test-utils';
import { SMALL, LARGE } from '@ecom-next/core/breakpoint-provider';
import { spotlightInsetData } from '../../content-types/SpotlightImage/__fixtures__/test-data';
import { SpotlightImageWrapper, SpotlightImageWrapperProps } from './SpotlightImageWrapper';
import 'jest-extended';

describe('SpotlightImageWrapper', () => {
  beforeEach(() => {
    // eslint-disable-next-line no-console
    console.error = jest.fn();
  });
  it('should render default container', () => {
    const result = render(<SpotlightImageWrapper background={spotlightInsetData.general.background} />);
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('renders a container with the spacing', () => {
    const { container } = render(<SpotlightImageWrapper background={spotlightInsetData.general.background} />);
    expect(container.firstChild.firstChild).toHaveStyle({ overflow: 'hidden' });
  });

  const backgroundProps: SpotlightImageWrapperProps = {
    background: {
      type: 'solid',
      color: '#e9f807',
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#A2AC9E',
        },
      },
    ],
  };
  it('uses the mobile background if the viewport is small and one is provided', () => {
    const { container } = render(
      <SpotlightImageWrapper background={backgroundProps.background} mobileBackground={backgroundProps.mobileBackground}>
        Hi
      </SpotlightImageWrapper>,
      { breakpoint: SMALL }
    );
    const background = container.querySelector('div div div');
    expect(background).toHaveStyleRule('background', '#A2AC9E');
  });
  it('uses the desktop background as a fallback when the viewport is small and a mobile background not provided', () => {
    const noMobileBgProps: SpotlightImageWrapperProps = {
      background: {
        type: 'solid',
        color: '#e9f807',
      },
    };
    const { container } = render(
      <SpotlightImageWrapper background={backgroundProps.background} mobileBackground={noMobileBgProps?.mobileBackground}>
        Hi
      </SpotlightImageWrapper>,
      { breakpoint: SMALL }
    );
    const background = container.querySelector('div div div');
    expect(background).toHaveStyleRule('background', '#e9f807');
  });
  it('uses the desktop background if the viewport is large', () => {
    const { container } = render(
      <SpotlightImageWrapper background={backgroundProps.background} mobileBackground={backgroundProps.mobileBackground}>
        Hi
      </SpotlightImageWrapper>,
      { breakpoint: LARGE }
    );
    const background = container.querySelector('div div div');
    expect(background).toHaveStyleRule('background', '#e9f807');
  });
});
