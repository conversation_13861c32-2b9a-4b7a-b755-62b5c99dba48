// @ts-nocheck
'use client';
import React, { DetailedHTMLProps, HTMLAttributes } from 'react';
import { BackgroundTypeExtensionValue } from '../../types/amplience';
import BackgroundTypeContainer from '../../subcomponents/BackgroundTypeContainer';
import { useViewportIsLarge } from '../../../hooks';
import { MobileBackgroundHelper } from '../../content-types/SpotlightImage/types';
import { isValidBackground } from '../../subcomponents/shared-components/helpers';

export interface SpotlightImageWrapperProps extends DetailedHTMLProps<HTMLAttributes<HTMLElement>, HTMLElement> {
  background: BackgroundTypeExtensionValue;
  hasContent?: boolean;
  mobileBackground?: MobileBackgroundHelper[];
  className?: string;
  isFullBleed?: boolean;
}

export const SpotlightImageWrapper = (props: SpotlightImageWrapperProps): JSX.Element => {
  const { children, background, mobileBackground, className, isFullBleed, hasContent } = props;
  const isLargeVP = useViewportIsLarge();

  const renderedBackground =
    isLargeVP || !(mobileBackground !== undefined && isValidBackground(mobileBackground?.[0]?.mobileBackground))
      ? background
      : mobileBackground[0]?.mobileBackground;

  const padding = () => {
    if (isFullBleed) {
      return 0;
    }
    if (hasContent) {
      return isLargeVP ? 64 : 40;
    }
    return isLargeVP ? 20 : 10;
  };

  return (
    <BackgroundTypeContainer
      background={renderedBackground}
      className={className}
      css={{
        position: 'relative',
        width: '100%',
        height: '100%',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
        paddingTop: padding(),
      }}
    >
      {children}
    </BackgroundTypeContainer>
  );
};
