// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject } from '@emotion/serialize';
import { ScalableText } from '../../../components/Typography';
import { useViewportIsLarge } from '../../../hooks';
import { SpotlightImageHeaderContent } from './SpotlightImageHeaderContent';
import { SpotlightImageHeaderWrap } from './styles';
import { SpotlightImageContentAlign } from './types';
import { SpotlightImageHeaderType } from '../../content-types/SpotlightImage/types';
import { hasContent } from '../../helpers/SpotlightContent';

export const SpotlightImageHeader = (
  props: SpotlightImageHeaderType & {
    desktopScalingPoint?: ScalableText['desktopScalingPoint'];
    overlaysHeight?: number;
  }
): JSX.Element => {
  const { content, isFullBleed, className, desktopScalingPoint, overlaysHeight } = props;
  const { contentJustification: desktopContentJustification, mobileContentJustification, mobileVerticalAlignment, spotlightText, verticalAlignment } = content;
  const { useGradientBackfill } = spotlightText;
  const isDesktop = useViewportIsLarge();
  const textAlignment = SpotlightImageContentAlign[isDesktop ? desktopContentJustification : mobileContentJustification];

  const styles: CSSObject = {
    alignItems: textAlignment,
    pointerEvents: 'none',
  };

  return hasContent(content) ? (
    <SpotlightImageHeaderWrap
      className={className}
      css={styles}
      isDesktop={isDesktop}
      isFullBleed={isFullBleed}
      mobileVerticalAlignment={mobileVerticalAlignment}
      overlaysHeight={overlaysHeight}
      useSpotlightGradient={useGradientBackfill}
      verticalAlignment={verticalAlignment}
    >
      <SpotlightImageHeaderContent content={content} desktopScalingPoint={desktopScalingPoint} isFullBleed={isFullBleed} />
    </SpotlightImageHeaderWrap>
  ) : (
    <></>
  );
};
