// @ts-nocheck
import React from 'react';
import dayjs from 'dayjs';
import { render, screen, fireEvent, act } from 'test-utils';
import { SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import PromoSticker, { EXIT_ANIMATION_DELAY } from './index';
import { promoStickerData } from '../../content-types/PromoSticker/__fixtures__/test-data';

const CONTENT = 'CONTENT';

describe('Promo Sticker', () => {
  it('should match snapshots', async () => {
    const { container } = render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
      breakpoint: XLARGE,
    });
    expect(container).toMatchSnapshot();
  });

  it('should hidden in mobile', async () => {
    render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
      breakpoint: SMALL,
    });

    const elements = await screen.queryAllByText(CONTENT);
    expect(elements.length).toBe(0);
  });

  it('should show in desktop', async () => {
    render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
      breakpoint: XLARGE,
    });

    expect(screen.getByText(CONTENT)).toBeInTheDocument();
  });

  it('should have a custom close button', async () => {
    const CLOSE = 'Close';

    render(
      <PromoSticker {...promoStickerData} closeButton={<button>{CLOSE}</button>}>
        {CONTENT}
      </PromoSticker>,
      {
        breakpoint: XLARGE,
      }
    );

    const btn = await screen.getByText(CLOSE);

    expect(btn?.nodeName).toBe('BUTTON');
    expect(btn).toBeInTheDocument();
  });

  it('should have a min height', async () => {
    const MIN_HEIGHT = 250;
    const { getAllByRole } = render(
      <PromoSticker {...promoStickerData} minHeightValue={MIN_HEIGHT}>
        {CONTENT}
      </PromoSticker>,
      {
        breakpoint: XLARGE,
      }
    );

    expect(getAllByRole('link')[0]).toBeInTheDocument();
    expect(getAllByRole('link')[0].firstChild).toHaveStyleRule('min-height', `${MIN_HEIGHT}px`);
  });

  it('should align  to the center', async () => {
    const { getAllByRole } = render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
      breakpoint: XLARGE,
    });

    const contentContainer = getAllByRole('link')[0].firstChild.children.item(1);
    expect(contentContainer).toHaveStyleRule('justify-content', 'center');
  });

  it('should align  to the top', async () => {
    const { getAllByRole } = render(
      <PromoSticker {...promoStickerData} verticalTextAlignment='top'>
        {CONTENT}
      </PromoSticker>,
      {
        breakpoint: XLARGE,
      }
    );

    const contentContainer = getAllByRole('link')[0].firstChild.children.item(1);
    expect(contentContainer).toHaveStyleRule('justify-content', 'flex-start');
  });

  it('should align  to the bottom', async () => {
    const { getAllByRole } = render(
      <PromoSticker {...promoStickerData} verticalTextAlignment='bottom'>
        {CONTENT}
      </PromoSticker>,
      {
        breakpoint: XLARGE,
      }
    );

    const contentContainer = getAllByRole('link')[0].firstChild.children.item(1);
    expect(contentContainer).toHaveStyleRule('justify-content', 'flex-end');
  });

  describe('dismiss behavior', () => {
    it('should be hidden after close button is clicked', async () => {
      const { container } = render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
        breakpoint: XLARGE,
      });

      const btn = screen.getByRole('button');
      expect(container.firstChild).not.toBeNull();
      await act(async () => {
        fireEvent.click(btn);
      });
      setTimeout(() => {
        expect(container.firstChild).toBeNull();
      }, EXIT_ANIMATION_DELAY);
    });
  });

  it.skip('should be hidden if it was dismissed today', async () => {
    const todaysDate = dayjs(new Date()).format('DDMMYYYY');

    localStorage.setItem('promo-sticker-suppressed', todaysDate);
    const { container } = render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
      breakpoint: XLARGE,
    });
    expect(container.firstChild).toBeNull();
  });

  it('should not be hidden if it was dismissed on a different day', async () => {
    const bogusDate = '01012001';
    localStorage.setItem('promo-sticker-suppressed', bogusDate);
    const { container } = render(<PromoSticker {...promoStickerData}>{CONTENT}</PromoSticker>, {
      breakpoint: XLARGE,
    });
    expect(container.firstChild).not.toBeNull();
  });
});
