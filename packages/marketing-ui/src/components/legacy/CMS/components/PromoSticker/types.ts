'use client';
import React from 'react';
import { BackgroundTypeExtensionValue } from '../../types/amplience';

export interface PromoStickerProps {
  verticalTextAlignment?: 'middle' | 'top' | 'bottom';
  colorClose?: string;
  closeButton?: JSX.Element;
  background: BackgroundTypeExtensionValue;
  bannerLink: {
    label: string;
    value: string;
  };
  minHeightValue?: number;
  widthValue?: number;
  verticalPadding?: number;
  children: React.ReactNode;
  className?: string;
}
export interface ContainerProps {
  minHeight?: number;
  show?: boolean;
  widthValue?: number;
}
export interface WrapperProps {
  justifyContent?: string;
  verticalPadding?: number;
}
