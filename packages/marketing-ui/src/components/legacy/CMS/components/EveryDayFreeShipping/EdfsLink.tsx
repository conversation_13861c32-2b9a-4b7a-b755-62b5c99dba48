// @ts-nocheck
'use client';
import React from 'react';
import { useTheme, styled, CSSObject, forBrands, Theme, Brands } from '@ecom-next/core/react-stitch';
import { DetailsButton, getDetailsContent } from '../../subcomponents/Details';
import { EdfsLinkProps } from './types';
import { useViewportIsXLarge } from '../../../hooks/useViewportIsLarge';

const getLinkWrapperPadding = (theme: Theme): CSSObject =>
  forBrands(theme, {
    at: () => ({ paddingLeft: 16, paddingRight: 16 }),
    default: () => ({ paddingLeft: 13, paddingRight: 13 }),
  }) as CSSObject;

const LinkWrapper = styled.div<{
  isDesktop?: boolean;
  className?: string;
}>(
  {
    display: 'flex',
  },
  props =>
    !props.isDesktop && {
      flexGrow: 1,
      ...getLinkWrapperPadding(props.theme),
    }
);

export const EdfsLink = ({ detailsFontColor, detailsLinkText, pemoleCode, htmlModalUrl, className }: EdfsLinkProps): JSX.Element => {
  const theme = useTheme();
  const detailsContent = getDetailsContent(theme.brand, pemoleCode, htmlModalUrl);
  const isDesktop = useViewportIsXLarge();
  return (
    <LinkWrapper className={className} isDesktop={isDesktop}>
      <DetailsButton
        color={detailsFontColor}
        customStyles={{ padding: 0, fontSize: 10 }}
        label={detailsLinkText}
        useBaseStylesOnlyForBrands={[Brands.Athleta]}
        value={detailsContent}
      />
    </LinkWrapper>
  );
};

export default EdfsLink;
