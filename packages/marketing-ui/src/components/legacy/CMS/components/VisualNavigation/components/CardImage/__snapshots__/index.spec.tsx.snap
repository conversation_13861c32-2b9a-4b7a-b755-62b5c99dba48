// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Card Image Components test styled component CardImageOuterDiv should render CardImageOuterDiv with styles passing percentHeight prop 1`] = `
.emotion-0 {
  position: relative;
  display: block;
  overflow: hidden;
  padding-bottom: 2%;
  min-height: 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    />
  </div>
</div>
`;

exports[`Card Image Components test styled component CardImgHoverColorOverlay should render CardImgHoverColorOverlay with styles 1`] = `
.emotion-0 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: -1;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    />
  </div>
</div>
`;

exports[`Card Image Components test styled component InnerCardImgDiv should render InnerCardImgDiv with styles passing imageUrl prop 1`] = `
.emotion-0 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(www.fake.com/img.png) no-repeat center center;
  -webkit-background-size: cover;
  background-size: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    />
  </div>
</div>
`;
