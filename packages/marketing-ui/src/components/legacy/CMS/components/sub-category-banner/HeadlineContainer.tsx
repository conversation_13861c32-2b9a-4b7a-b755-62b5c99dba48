// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import useBrandValue from '../../../hooks/useBrandValue';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';

type HeadlineContainerProps = {
  onlyHeading: boolean;
};

export const HeadlineContainer = styled.div<HeadlineContainerProps>(({ onlyHeading }) => {
  const isDesktop = useViewportIsLarge();
  const mobilePadding = {
    paddingTop: onlyHeading ? '15px' : '0px',
    paddingBottom: '15px',
  };
  const desktopPadding = {
    paddingTop: onlyHeading ? '30px' : '0px',
    paddingBottom: '20px',
  };

  const padding = useBrandValue(
    {
      gap: isDesktop ? { ...desktopPadding } : { ...mobilePadding },
      gapfs: isDesktop ? { ...desktopPadding } : { ...mobilePadding },
    },
    {}
  );

  return {
    order: 2,
    display: 'flex',
    flexDirection: 'row',
    ...padding,
  };
});
