// @ts-nocheck
'use client';
import { styled, CSSObject } from '@ecom-next/core/react-stitch';

interface TextNavigationExposedExposedHeaderProps {
  isDesktop: boolean;
  fontColor?: string;
  backgroundColor?: string;
  outlineColor?: string;
  textNavExposedHeaderCustomStyles?: CSSObject;
}

const TextNavigationExposedHeader = styled.h2<TextNavigationExposedExposedHeaderProps>(props => ({
  ...props.theme.brandFont,
  color: props.theme.color.b1,
  marginLeft: 'auto',
  marginRight: 'auto',
  borderColor: 'initial',
  boxSizing: 'border-box',
  textAlign: 'center',
  height: 'auto',
  whiteSpace: 'normal',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  textDecoration: 'none',
  lineHeight: '22px',
  paddingBottom: props.isDesktop ? '16px' : '24px',
  textTransform: 'uppercase',
  ...props.textNavExposedHeaderCustomStyles,
}));

export default TextNavigationExposedHeader;
