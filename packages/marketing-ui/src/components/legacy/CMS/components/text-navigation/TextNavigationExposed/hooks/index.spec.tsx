// @ts-nocheck
import * as stitch from '@ecom-next/core/react-stitch';
import { Theme } from '@ecom-next/core/react-stitch';
import { renderHook, act } from 'test-utils';
import { useSelectedButtonStyles, useSideBySideStyles } from '.';

const { Brands } = stitch;
jest.mock('@ecom-next/core/react-stitch', () => {
  const orig = jest.requireActual('@ecom-next/core/react-stitch');
  return {
    ...orig,
    __esModule: true,
  };
});

describe('useSideBySideStyles', () => {
  it('should return a border radius on all four corners when Old Navy and is even', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.OldNavy,
          color: { bk: 'black', wh: 'white', b1: 'red' },
          borderRadius: { tile: '8px' },
        }) as Theme
    );
    const { result } = renderHook(() => useSideBySideStyles(4));
    expect(result.current).toMatchObject({
      '& > a': {
        border: '1px solid red',
        borderBottom: 'none',
      },
      ':last-of-type': {
        '& > a': {
          borderBottom: '1px solid red',
          borderBottomLeftRadius: '0',
          borderBottomRightRadius: '8px',
        },
        flex: 1,
      },
      ':nth-last-of-type(2) > a': {
        borderBottom: 'solid 1px red',
        borderBottomLeftRadius: '8px',
      },
      ':nth-of-type(1) > a': {
        borderTopLeftRadius: '8px',
      },
      ':nth-of-type(2) > a': {
        borderTopRightRadius: '8px',
      },
      ':nth-of-type(2n+1) > a': {
        borderLeft: 'solid 1px red',
      },
      ':nth-of-type(even) > a': {
        borderLeft: 'none',
      },
      display: 'flex',
      width: '50%',
    });
  });

  it('should return a border radius on bottom button bottom corners only when odd and Old Navy', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.OldNavy,
          color: { bk: 'black', wh: 'white', b1: 'red' },
          borderRadius: { tile: '8px' },
        }) as Theme
    );
    const { result } = renderHook(() => useSideBySideStyles(5));
    expect(result.current).toMatchObject({
      '& > a': {
        border: '1px solid red',
        borderBottom: 'none',
      },
      ':last-of-type': {
        '& > a': {
          borderBottom: '1px solid red',
          borderBottomLeftRadius: '8px',
          borderBottomRightRadius: '8px',
        },
        flex: 1,
      },
      ':nth-last-of-type(2) > a': {
        borderBottom: 'none',
        borderBottomLeftRadius: '0',
      },
      ':nth-of-type(1) > a': {
        borderTopLeftRadius: '8px',
      },
      ':nth-of-type(2) > a': {
        borderTopRightRadius: '8px',
      },
      ':nth-of-type(2n+1) > a': {
        borderLeft: 'solid 1px red',
      },
      ':nth-of-type(even) > a': {
        borderLeft: 'none',
      },
      display: 'flex',
      width: '50%',
    });
  });

  it('should not return a border radius when not OldNavy', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.Gap,
          color: { bk: 'black', wh: 'white', b1: 'red' },
          borderRadius: { tile: 'none' },
        }) as Theme
    );
    const { result } = renderHook(() => useSideBySideStyles(4));
    expect(result.current).toMatchObject({
      '& > a': {
        border: '1px solid red',
        borderBottom: 'none',
      },
      ':last-of-type': {
        '& > a': {
          borderBottom: '1px solid red',
          borderBottomLeftRadius: '0',
          borderBottomRightRadius: '0',
        },
        flex: 1,
      },
      ':nth-last-of-type(2) > a': {
        borderBottom: 'solid 1px red',
        borderBottomLeftRadius: '0',
      },
      ':nth-of-type(1) > a': {
        borderTopLeftRadius: '0',
      },
      ':nth-of-type(2) > a': {
        borderTopRightRadius: '0',
      },
      ':nth-of-type(2n+1) > a': {
        borderLeft: 'solid 1px red',
      },
      ':nth-of-type(even) > a': {
        borderLeft: 'none',
      },
      display: 'flex',
      width: '50%',
    });
  });
});

describe('useSelectedButtonStyles', () => {
  it('should return Athleta styles when on Athleta', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.Athleta,
          color: { bk: 'black', wh: 'white', b1: 'red' },
        }) as Theme
    );
    const { result } = renderHook(() => useSelectedButtonStyles());
    expect(result.current).toEqual({
      color: 'black',
      fontWeight: 700,
      backgroundColor: 'transparent',
    });
  });

  it('should return OldNavy styles when on OldNavy', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.OldNavy,
          color: { bk: 'black', wh: 'white', b1: 'red' },
        }) as Theme
    );
    const { result } = renderHook(() => useSelectedButtonStyles());
    expect(result.current).toEqual({
      color: 'white',
      fontWeight: 600,
      backgroundColor: 'red',
      'span > svg path': { fill: 'white' },
    });
  });

  it('should return Gap styles when on Gap', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.Gap,
          color: { bk: 'black', wh: 'white', b1: 'red' },
        }) as Theme
    );
    const { result } = renderHook(() => useSelectedButtonStyles());
    expect(result.current).toEqual({
      color: 'white',
      fontWeight: 400,
      backgroundColor: 'red',
    });
  });

  it('should return GapFactoryStore styles when on GapFactoryStore', () => {
    jest.spyOn(stitch, 'useTheme').mockImplementation(
      () =>
        ({
          brand: Brands.GapFactoryStore,
          color: { bk: 'black', wh: 'white', b1: 'red' },
        }) as Theme
    );
    const { result } = renderHook(() => useSelectedButtonStyles());
    expect(result.current).toEqual({
      color: 'white',
      fontWeight: 500,
      backgroundColor: 'red',
    });
  });
});
