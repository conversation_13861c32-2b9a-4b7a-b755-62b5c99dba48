// @ts-nocheck
import React, { useState } from 'react';
import { fireEvent, render, act } from 'test-utils';
import { circleNavigationCategoryData } from './__fixtures__/test-data';
import CircleNavigationCategoryCircles from '.';

describe('CircleNavigationCategoryCircles', () => {
  it('should match snapshots', () => {
    const { container } = render(<CircleNavigationCategoryCircles {...circleNavigationCategoryData} />);
    expect(container).toMatchSnapshot();
  });

  it('should be same style on hover state', () => {
    const { container } = render(<CircleNavigationCategoryCircles {...circleNavigationCategoryData} />);

    const linkElement = container.querySelector('a');
    expect(linkElement).toBeInTheDocument();
    const initialStyle = getComputedStyle(linkElement!);

    fireEvent.mouseOver(linkElement!);
    const hoverStyle = getComputedStyle(linkElement!);
    expect(hoverStyle.textDecoration).toBe(initialStyle.textDecoration);
  });

  const MockCircleNavigationCategoryCircles = props => {
    const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);

    return <CircleNavigationCategoryCircles {...props} selectedCategory={selectedCategory} setSelectedCategory={setSelectedCategory} />;
  };

  it('should change background color when selected', async () => {
    const { container } = render(<MockCircleNavigationCategoryCircles {...circleNavigationCategoryData} />);

    const linkElement = container.querySelector('a');
    expect(linkElement).toBeInTheDocument();
    await act(async () => {
      fireEvent.click(linkElement!);
    });

    const categoryCircle = container.querySelector('div');
    expect(categoryCircle).toHaveStyle('background-color: rgb(244 244 244)');
  });
});
