// @ts-nocheck
'use client';
import { styled } from '@ecom-next/core/react-stitch';
import AmplienceImage, { AmplienceImageProps } from '../../../subcomponents/AmplienceImage';

export const CategoryCircle = styled.div<{ isSelected: boolean }>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: ${props => (props.isSelected ? '#F4F4F4' : '')};
`;

export const ImageContainer = styled.div`
  border-radius: 50%;
  overflow: hidden;
`;

export const CategoryImage = styled(AmplienceImage)<AmplienceImageProps>`
  object-fit: cover;
  height: 100%;
  width: 100%;
  object-position: top;
`;
