// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { render, fireEvent, act, screen } from 'test-utils';
import { SMALL, XLARGE, Size } from '@ecom-next/core/breakpoint-provider';
import CategoryBannerPriceCard from '.';
import { CategoryBannerPriceCardData } from '../__fixtures__/testdata';
import { jumpToSection } from '../../../../helper/jumpToSection';
import { PRODUCT_GRID_CLASSNAME } from '../../../constants';

jest.mock('../../../../helper/jumpToSection');

describe('CategoryBannerPriceCard Component', () => {
  const renderComponent = (breakpoint: Size = XLARGE) =>
    render(<CategoryBannerPriceCard {...CategoryBannerPriceCardData} />, {
      breakpoint,
    });
  describe('should match snapshots', () => {
    it('in mobile view', () => {
      const { container } = renderComponent(SMALL);
      expect(container).toMatchSnapshot();
    });
    it('in desktop view', () => {
      const { container } = renderComponent();
      expect(container).toMatchSnapshot();
    });
  });

  it('should render an image ', () => {
    renderComponent();
    expect(screen.getByAltText('image')).toBeInTheDocument();
  });

  it('should not have a11y violations', async () => {
    const { container } = renderComponent();
    expect(await axe(container)).toHaveNoViolations();
  });

  it('Card Banner link should jump to section', async () => {
    const { container } = renderComponent();
    const ctaButton = container.querySelector(':nth-child(2) a');
    if (ctaButton) {
      await act(async () => {
        fireEvent.click(ctaButton);
      });
      expect(ctaButton).toHaveAttribute('target', '_self');
      expect(jumpToSection).toHaveBeenCalled();
      expect(jumpToSection).toHaveBeenCalledWith(true, PRODUCT_GRID_CLASSNAME);
    }
  });
});
