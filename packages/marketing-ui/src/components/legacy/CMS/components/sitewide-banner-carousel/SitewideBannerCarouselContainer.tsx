// @ts-nocheck
'use client';
import { CSSObject, styled } from '@ecom-next/core/react-stitch';

// banner must have a minHeight to render server side
export const defaultBannerMinHeight = '44px';

export const SitewideBannerCarouselContainer = styled.div(
  ({ minHeight, customStyles }: { minHeight?: string; customStyles?: CSSObject }): CSSObject => ({
    minHeight: minHeight ?? defaultBannerMinHeight,
    ...customStyles,
  })
);
