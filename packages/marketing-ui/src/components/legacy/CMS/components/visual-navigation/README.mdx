# VisualNavigation (CMS Configurable)

- What is `VisualNavigation`?
  - `VisualNavigation` is a navigation menu made up of images and text.
  - It is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience.
  - Visual Navigation has an adjustable webAppearance. See [here](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/marketing-ui/CMS/components/VisualNavigation/types.ts) for all the options
- To see the maturity of this component in terms of Usability, Performance, Citizenship, and Code Quality, refer to the [Visual Navigation Scorecard](https://confluence.gapinc.com/pages/viewpage.action?pageId=607222318)."

## Default Behavior

- `VisualNavigation` creates a pre-styled set of `HoverImage`s with the range of 2-6 cards, with a `header` for the navigation, and `title` and `description` for each card. There are a few configurations like light / dark theme and image size / position, and that will be in a types doc coming soon.

## Technical Notes

- The styling in the `VisualNavigation` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/tree/packages/react-stitch/README.md)."

- To help reduce content layout shift, the outer container of `VisualNavigation` will have the height or aspect ratio based on the card height, since all other elements are optional and have a flexible height.
  This way, the content type will have a placeholder that is the height of the cards, as opposed to 0 height, causing less of a shift as the site starts with server-side content and later renders the client-side banner.
  This should not have an effect on the appearance of the banner, even if it has an actual aspect ratio that is taller.

### Cautions
