// @ts-nocheck
import { WayfindingAndProductCardsProps } from '../../../content-types/WayfindingAndProductCards/types';

export const wayfindingWithCategoryCtaLightTheme: WayfindingAndProductCardsProps = {
  _meta: {
    name: 'Wayfinding w Cat CTA theme - Alina',
    schema: 'https://cms.gap.com/schema/content/v1/wayfinding-and-product-cards.json',
    deliveryId: '95ee7bee-51b2-481e-a466-8523c7392545',
  },
  general: {
    background: {
      type: 'solid',
      color: '#3AD',
    },
    mobileLayout: 'scroll',
    showHideBasedOnScreenSize: 'alwaysShow',
    categoryButtonColor: 'light',
  },
  content: {
    categories: [
      {
        image: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '0911a527-89f1-49bf-af57-871ede17948b',
              name: 'stage_auto_1__Asset_Archive_ATWeb_content_0028_480_963_assets_images_results_SP22_SleepQuiz_Desktop_R1_jpg',
              endpoint: 'athleta',
              defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
            },
            altText: '',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        cta: {
          label: 'Tops',
          value: 'www.gap.athleta.com',
        },
      },
      {
        image: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'e6fea961-1c9b-4013-a500-baadf3ba1739',
              name: 'SU22_D1_BestOfBottoms_Ultimate_IMG_XL_1',
              endpoint: 'athleta',
              defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
            },
            altText: '',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        cta: {
          label: 'Bottoms',
          value: 'www.gap.oldnavy.com',
        },
      },
    ],
    carouselSettings: {
      transition: 'slide',
      type: 'clickThrough',
      continuousLoop: false,
      autoplay: {
        delay: 3000,
        pauseOnHover: false,
      },
      animation: {
        speed: 500,
        ease: false,
      },
      styling: {
        controlsIconsColor: 'primary',
        pagination: 'hide',
        hideChevrons: false,
      },
    },
  },
};
