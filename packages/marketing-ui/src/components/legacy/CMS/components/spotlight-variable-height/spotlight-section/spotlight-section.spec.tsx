// @ts-nocheck
import React from 'react';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { render } from 'test-utils';
import { MediaSize } from '../../../content-types/SpotlightVariableHeight/types';
import { SpotlightSectionType } from '../types';
import { defaultData } from '../../../content-types/SpotlightVariableHeight/__fixtures__/test-data';
import { OnCtaRedesign2024Context } from '../../../../contexts/OnCtaRedesign2024Context';
import { desktopAspectRatioMap, mobileAspectRatioMap } from './utilities';
import { SpotlightSection } from '.';

const renderSectionWithTheme = (props: SpotlightSectionType, brand = Brands.OldNavy) => {
  return render(
    <StitchStyleProvider brand={brand}>
      <SpotlightSection {...props} />
    </StitchStyleProvider>
  );
};

describe('<SpotlightSection /> container', () => {
  describe('image', () => {
    const testData = {
      ...defaultData.image,
    };
    it('should be fullBleed', () => {
      const { getByRole } = renderSectionWithTheme({
        image: testData,
        isDesktop: true,
      });
      const img = getByRole('img');
      const imgParent = img.parentElement;
      expect(img).toHaveStyleRule('width', '100%');
      expect(imgParent).toHaveStyleRule('background-size', 'cover');
    });

    it('should render the background image', () => {
      const res = renderSectionWithTheme({
        image: testData,
        isDesktop: true,
      });
      const img = res.getByRole('img');
      expect(img).toBeInTheDocument();
    });

    describe('Old Navy Aspect Ratio', () => {
      const deviceSizeAspectRatioMap = {
        desktop: desktopAspectRatioMap[Brands.OldNavy],
        mobile: mobileAspectRatioMap[Brands.OldNavy],
      };
      Object.keys(deviceSizeAspectRatioMap).forEach(device => {
        const aspectRatioMap = deviceSizeAspectRatioMap[device as keyof typeof deviceSizeAspectRatioMap];
        Object.keys(aspectRatioMap).forEach(imageSize => {
          it(`should render the image using the appropriate aspect ratio for ${device} ${imageSize}`, () => {
            const isDesktop = device === 'desktop';
            const res = renderSectionWithTheme({
              image: { ...testData, imageSize: imageSize as MediaSize },
              isDesktop,
            });
            const expectedAspectRatio = aspectRatioMap[imageSize as keyof typeof aspectRatioMap];
            expect(res.getByRole('img')).toHaveStyleRule('aspect-ratio', expectedAspectRatio.replace(/ /g, ''));
          });
        });
      });

      Object.keys(deviceSizeAspectRatioMap).forEach(device => {
        const aspectRatioMap = deviceSizeAspectRatioMap[device as keyof typeof deviceSizeAspectRatioMap];
        Object.keys(aspectRatioMap).forEach(imageSize => {
          it(`should match the expected snapshot for ${device} ${imageSize}`, () => {
            const isDesktop = device === 'desktop';
            const { container } = renderSectionWithTheme({
              image: { ...testData, imageSize: imageSize as MediaSize },
              isDesktop,
            });

            expect(container).toMatchSnapshot();
          });
        });
      });

      Object.keys(deviceSizeAspectRatioMap.mobile).forEach(imageSize => {
        it(`should match the expected snapshot for mobile hero image size ${imageSize}`, () => {
          const { container } = renderSectionWithTheme({
            image: { ...testData, imageSize: imageSize as MediaSize },
            isDesktop: false,
          });
          expect(container).toMatchSnapshot();
        });
      });
    });

    describe('Athleta Aspect Ratio', () => {
      const deviceSizeAspectRatioMap = {
        desktop: desktopAspectRatioMap[Brands.Athleta],
        mobile: mobileAspectRatioMap[Brands.Athleta],
      };
      Object.keys(deviceSizeAspectRatioMap).forEach(device => {
        const aspectRatioMap = deviceSizeAspectRatioMap[device as keyof typeof deviceSizeAspectRatioMap];
        Object.keys(aspectRatioMap).forEach(imageSize => {
          it(`should render the image using the appropriate aspect ratio for ${device} ${imageSize}`, () => {
            const isDesktop = device === 'desktop';
            const res = renderSectionWithTheme(
              {
                image: { ...testData, imageSize: imageSize as MediaSize },
                isDesktop,
              },
              Brands.Athleta
            );
            const expectedAspectRatio = aspectRatioMap[imageSize as keyof typeof aspectRatioMap];
            expect(res.getByRole('img')).toHaveStyleRule('aspect-ratio', expectedAspectRatio.replace(/ /g, ''));
          });
        });
      });

      Object.keys(deviceSizeAspectRatioMap).forEach(device => {
        const aspectRatioMap = deviceSizeAspectRatioMap[device as keyof typeof deviceSizeAspectRatioMap];
        Object.keys(aspectRatioMap).forEach(imageSize => {
          it(`should match the expected snapshot for ${device} ${imageSize}`, () => {
            const isDesktop = device === 'desktop';
            const { container } = renderSectionWithTheme(
              {
                image: { ...testData, imageSize: imageSize as MediaSize },
                isDesktop,
              },
              Brands.Athleta
            );

            expect(container).toMatchSnapshot();
          });
        });
      });

      Object.keys(deviceSizeAspectRatioMap.mobile).forEach(imageSize => {
        it(`should match the expected snapshot for mobile hero image size ${imageSize}`, () => {
          const { container } = renderSectionWithTheme(
            {
              image: { ...testData, imageSize: imageSize as MediaSize },
              isDesktop: false,
            },
            Brands.Athleta
          );
          expect(container).toMatchSnapshot();
        });
      });
    });
  });
  describe('image overlay', () => {
    const imageOverlayData = {
      ...defaultData.imageOverlay,
    };

    it('should match snapshot with default styles', () => {
      // TODO: replace these with renders with the render wrapper helper at the top
      const { container } = render(
        <StitchStyleProvider brand={Brands.OldNavy} enabledFeatures={{ 'on-cta-redesign-2024': false }}>
          <SpotlightSection imageOverlay={imageOverlayData} isDesktop />
        </StitchStyleProvider>
      );
      expect(container).toMatchSnapshot();
    });
    it("should match snapshot when 'on-cta-redesign-2024' feature flag is enabled", () => {
      const { container } = render(
        <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
          <StitchStyleProvider brand={Brands.OldNavy} enabledFeatures={{ 'on-cta-redesign-2024': true }}>
            <SpotlightSection imageOverlay={imageOverlayData} isDesktop />
          </StitchStyleProvider>
        </OnCtaRedesign2024Context.Provider>
      );
      expect(container).toMatchSnapshot();
    });
  });
});
