'use client';
// @ts-ignore
import { CSSObject } from '@ecom-next/core/react-stitch';
// @ts-ignore
import { ReactPlayerProps } from 'react-player';
import { SpotlightImage, ImageOverlay, ContentBlock, SpotlightVariableHeightWebAppearance } from '../../content-types/SpotlightVariableHeight/types';
import { SpotlightVideo, VideoOverlay } from '../../content-types/SpotlightVariableHeightVideo/types';

type ContentBlockKeys = 'aboveImage' | 'belowImage' | 'aboveVideo' | 'belowVideo';

export type ContentBlocks = {
  [key in ContentBlockKeys]?: ContentBlock;
};

export interface DetailsLinkPlacement {
  detailsLinkPlacement?: 'left' | 'right';
}

export interface CustomCtaContainerStyles {
  aboveMediaCtaContainerStyles?: CSSObject;
  belowMediaCtaContainerStyles?: CSSObject;
  spotlightMediaCtaContainerStyles?: CSSObject;
}

interface SVHMediaOnlyBase {
  contentBlocks?: ContentBlocks;
  ctaNaturalWidth?: boolean;
  customContentBlockStyles?: CSSObject;
  customCtaContainerStyles?: CustomCtaContainerStyles;
  customCtaStyles?: CSSObject;
  webAppearance?: SpotlightVariableHeightWebAppearance;
}

interface SVHOnly extends SVHMediaOnlyBase {
  image: SpotlightImage;
  imageOverlay: ImageOverlay;
  onVideoReady?: never;
  playerCustomControlStylesByBrand?: never;
  playerStyles?: CSSObject;
  video?: never;
  videoOverlay?: never;
}

interface SVHVideoOnly extends SVHMediaOnlyBase {
  image?: never;
  imageOverlay?: never;
  onVideoReady?: (ref: ReactPlayerProps) => void;
  playerCustomControlStylesByBrand?: CSSObject;
  playerStyles?: CSSObject;
  video: SpotlightVideo;
  videoOverlay: VideoOverlay;
}

export type SpotlightVariableHeightComponent = SVHOnly | SVHVideoOnly;

export interface SpotlightSectionType extends DetailsLinkPlacement {
  ctaContainerStyles?: CSSObject;
  ctaNaturalWidth?: boolean;
  customContentBlockStyles?: CSSObject;
  customCtaStyles?: CSSObject;
  image?: SpotlightImage;
  imageOverlay?: ImageOverlay;
  isDesktop: boolean;
  key?: string;
  onVideoReady?: (ref: ReactPlayerProps) => void;
  playInLoop?: boolean;
  playerCustomControlStylesByBrand?: CSSObject;
  playerStyles?: CSSObject;
  video?: SpotlightVideo;
  videoOverlay?: VideoOverlay;
}
