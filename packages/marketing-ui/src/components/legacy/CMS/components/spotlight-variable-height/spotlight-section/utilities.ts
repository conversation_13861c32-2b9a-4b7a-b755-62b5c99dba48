'use client';
// @ts-ignore
import { Brands } from '@ecom-next/core/react-stitch';
import { MediaSize } from '../../../content-types/SpotlightVariableHeight/types';
import { Overlay } from './index';

// TO DO: move branded out code to content-types dir
export type AspectRatioMap = Record<Brands, Record<MediaSize, string>>;
//@ts-ignore
export const desktopAspectRatioMap: AspectRatioMap = {
  //@ts-ignore
  [Brands.Athleta]: {
    large: '1440 / 810',
    medium: '1440 / 650',
    small: '1440 / 360',
  },
  //@ts-ignore
  [Brands.Gap]: {
    large: '1440 / 712',
    medium: '1440 / 650',
    small: '1440 / 360',
  },
  // @ts-ignore
  [Brands.GapFactoryStore]: {
    large: '1440 / 712',
    medium: '1440 / 650',
    small: '1440 / 360',
  },
  [Brands.BananaRepublic]: {
    large: '1440 / 818',
    medium: '1440 / 650',
    small: '1440 / 320',
    xsmall: '1440 / 150',
  },
  [Brands.BananaRepublicFactoryStore]: {
    large: '1440 / 818',
    medium: '1440 / 650',
    small: '1440 / 320',
    xsmall: '1440 / 150',
  },
  [Brands.OldNavy]: {
    large: '1440 / 800',
    medium: '1440 / 480',
    small: '1440 / 320',
    xsmall: '1440 / 150',
  },
};

// TO DO: move branded out code to content-types dir
//@ts-ignore
export const mobileAspectRatioMap: AspectRatioMap = {
  //@ts-ignore
  [Brands.Athleta]: {
    large: '375 / 667',
    medium: '375 / 500.25',
    small: '375 / 333.5',
  },
  //@ts-ignore
  [Brands.Gap]: {
    large: '390 / 530',
    medium: '375 / 500.25',
    small: '375 / 333.5',
  },
  // @ts-ignore
  [Brands.GapFactoryStore]: {
    large: '390 / 530',
    medium: '375 / 500.25',
    small: '375 / 333.5',
  },
  [Brands.BananaRepublic]: {
    large: '375 / 468',
    medium: '3 / 4',
    small: '375 / 234',
    xsmall: '375 / 200',
  },
  [Brands.BananaRepublicFactoryStore]: {
    large: '375 / 468',
    medium: '3 / 4',
    small: '375 / 234',
    xsmall: '375 / 200',
  },
  [Brands.OldNavy]: {
    large: '375 / 468',
    medium: '375 / 375',
    small: '375 / 234',
    xsmall: '375 / 200',
  },
};

export const HorizontalContentPlacementMap = {
  left: 'start',
  middle: 'center',
  right: 'end',
} as const;

export const VerticalContentPlacementMap = {
  top: 'start',
  middle: 'center',
  bottom: 'end',
} as const;

export const footerOverlayContentExists = (overlay: Overlay) =>
  overlay?.detailsLink?.label || overlay?.detailsLink?.prefixLabel || overlay?.handle?.text || overlay?.handle?.mobileOverride;

// copied from Amplience
export const overlayDefaults: Overlay = {
  cta: {
    buttonStyle: {
      buttonStyle: 'border',
      buttonColor: 'dark',
    },
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  useGradientBackfill: false,
  contentPlacement: {
    horizontal: 'middle',
    vertical: 'middle',
  },
  contentJustification: 'middle',
  handle: {
    placement: 'left',
  },
  useGradientBackfillFooter: false,
};
