// @ts-nocheck
'use client';
import React from 'react';
import { useViewportIsLarge } from '../../../../../hooks/useViewportIsLarge';
import { useCBVHCenterPosition } from '../../hooks/useCBVHCenterPosition';
import { CMSMarketingCarousel } from '../../../../subcomponents/CMSMarketingCarousel';
import { CategoryBannerVHCarouselTile, StaticTile } from '..';
import { useShowHideBasedOnScreenSize } from '../../../../../hooks/useShowHideBasedOnScreenSize';
import { useBrand } from '../../../../../hooks/useBrand';
import { CBVHCarouselWrapper, CategoryBannerVHCarouselProps, Tile } from '../..';
import { ConditionalPaginationTargetWrapperSection } from '../../styles';
import { heightMap, widthMap } from '../../../CategoryBannerVariableHeight/styles';

export const CategoryBannerVariableHeightCarousel = (props: CategoryBannerVHCarouselProps): JSX.Element | null => {
  const { frames, contentConfiguration, desktopBannerSize, mobileBannerSize, webAppearance, mobileTextTreatment, carouselSettings } = props;

  const { showHideBasedOnScreenSize } = webAppearance;
  const numberOfCarouselFrames = frames.length;
  const isDesktop: boolean = useViewportIsLarge();
  const isPersistentContent = contentConfiguration;
  const showCarouselOnViewPort = useShowHideBasedOnScreenSize(showHideBasedOnScreenSize);
  const isMobilePersistentAndBelow = mobileTextTreatment.includes('below') && isPersistentContent && !isDesktop;

  const arrowPosition = useCBVHCenterPosition(desktopBannerSize, mobileBannerSize);
  const getFramesTiles: Tile[][] = frames.reduce(
    (frameTiles: Tile[][], { tiles }) => [...frameTiles, ...(!isDesktop ? tiles.map(tile => [tile]) : [tiles])],
    []
  );
  const arrowPositionValue = `min(${arrowPosition}vw, 50%)`;

  const brandName = useBrand();
  const viewPort = isDesktop ? 'desktop' : 'mobile';
  const bannerSizeDevice = isDesktop ? desktopBannerSize : mobileBannerSize;
  const bannerSize = ['medium', 'large'].includes(bannerSizeDevice) ? bannerSizeDevice : 'medium';

  const HEIGHT: number = heightMap.categoryBanner[viewPort][bannerSize][brandName];
  const WIDTH: number = widthMap.categoryBanner[viewPort][brandName];

  const aspectRatio = `${WIDTH} / ${HEIGHT}`;

  const carousel =
    numberOfCarouselFrames < 2 ? null : (
      <CBVHCarouselWrapper css={{ aspectRatio }}>
        {isPersistentContent && !isMobilePersistentAndBelow && <StaticTile tile={frames[0].tiles[0]} />}

        <CMSMarketingCarousel arrowPosition={arrowPositionValue} carouselSettings={carouselSettings}>
          {getFramesTiles.map((tiles: Tile[], index: number) => {
            const key = `${index}_${tiles.map(tile => tile.bannerLink?.value || tile.text).join(':')}`;
            return (
              <CategoryBannerVHCarouselTile
                key={key}
                desktopBannerSize={desktopBannerSize}
                mobileBannerSize={mobileBannerSize}
                mobileTextTreatment={mobileTextTreatment}
                showContent={!isPersistentContent}
                tiles={tiles}
              />
            );
          })}
        </CMSMarketingCarousel>
        {isMobilePersistentAndBelow && <StaticTile isMobilePersistentAndBelow={isMobilePersistentAndBelow} tile={frames[0].tiles[0]} />}
      </CBVHCarouselWrapper>
    );

  return (
    <>
      {showCarouselOnViewPort && (
        <ConditionalPaginationTargetWrapperSection isMobilePersistentAndBelow={isMobilePersistentAndBelow}>
          {carousel}
        </ConditionalPaginationTargetWrapperSection>
      )}
    </>
  );
};
