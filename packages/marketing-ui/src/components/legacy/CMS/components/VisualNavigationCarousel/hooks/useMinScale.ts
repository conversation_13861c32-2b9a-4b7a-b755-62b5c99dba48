// @ts-nocheck
'use client';
import { defaultComponentMaxWidths, MOBILE_MAX_WIDTH_PX } from '../../../subcomponents/ComponentMaxWidth';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge';

const DESKTOP_MAX_WIDTH = defaultComponentMaxWidths.visualNavigation;
/**
 *
 * @param value value to scale based on Visual Navigation max width
 * @returns value scaled
 */
export function useScaleValue(value: number): number {
  const isLargeVP = useViewportIsLarge();
  const maxWidth = isLargeVP ? DESKTOP_MAX_WIDTH : MOBILE_MAX_WIDTH_PX;
  const scalableValue = (value / maxWidth) * 100;
  return scalableValue;
}

/**
 *
 * @param value value to scale based on Visual Navigation max width
 * @returns min() css function value
 */
function useMinScale(value: number): string {
  const scalableValue = useScaleValue(value);
  return `min(${scalableValue}vw, ${value}px)`;
}

export default useMinScale;
