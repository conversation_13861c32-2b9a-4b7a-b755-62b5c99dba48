# CategoryLinkRibbonBanner

- What is `CategoryLinkRibbonBanner`?
  - `CategoryLinkRibbonBanner` is a text link used in different areas on the category page.
  - It is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience. Note that this format differs from earlier JSON formats.

## Default Behavior

- `CategoryLinkRibbonBanner` creates a pre-styled banner with a `background color`, and `CTA links`
- Depending on the author's selection, `CTA links` can be aligned into different positions, `background color` of the banners and `font color` of the links can be changed,

## Limitations

- `CategoryLinkRibbonBanner` accepts JSON keys generated by our Content Management System, Amplience. JSON can be manually written as well, as long as it remains compatible with Amplience data.
- Note that Amplience JSON shape differs from earlier JSON shapes. It is intended to be much simpler, with more styles built into the component, and therefore less configurable via JSON.

## Technical Notes

- The styling in the `CategoryLinkRibbonBanner` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/tree/packages/react-stitch/README.md)."

### API

To view documentation about the API for `CategoryLinkRibbonBanner`, go [here](https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/marketing-ui/CMS/components/CategoryLinkRibbonBanner/types.ts).

## Testing the Component in Storybook

- Changes in the Storybook knobs JSON should be reflected in the visual example above.

## Breaking Changes Information

To view information regarding BREAKING CHANGES, please view the [Marketing UI MIGRATION.md file](/src/MIGRATION.md).
