// @ts-nocheck
import React from 'react';
import { render } from 'test-utils';
import { LARGE, Size, SMALL } from '@ecom-next/core/breakpoint-provider';
import { axe } from 'jest-axe';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import {
  productDetailsPageData,
  productDetailsPageDataMinimal,
  mobileSolidBackground,
  solidBackground,
  productDetailsPageWithBannerLinkData,
} from '../../content-types/ProductDetailsPage/__fixtures__/test-data';
import { productDetailsPageUseCase4ForON } from '../../content-types/ProductDetailsPage/__fixtures__/vr-pdp-usecase4';
import { ProductDetailsPageContentProps as FlexibleBannerContentProps } from '../../content-types/ProductDetailsPage/types';
import { DesktopImageOrIconSizeType } from '../../subcomponents/ImageOrIcon/types';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';
import { getVideoPlayerStyles } from './styles';
import { FlexibleBanner } from './index';

const ContentWrapper = (props: FlexibleBannerContentProps): JSX.Element => <FlexibleBanner {...props} />;

const renderContent = (data: FlexibleBannerContentProps, breakpoint: Size = LARGE, scaleLimitPixels?: number, brand = Brands.Athleta) =>
  render(
    // <StitchStyleProvider brand={brand}>
    <ContentWrapper {...data} scaleLimitPixels={scaleLimitPixels} />,
    // </StitchStyleProvider>,
    {
      appState: { brandName: brand },
      breakpoint,
    }
  );

let defaultData: FlexibleBannerContentProps;

describe('FlexibleBanner Component', () => {
  beforeEach(() => {
    defaultData = { ...productDetailsPageData };
  });

  describe('should match snapshots', () => {
    describe('in mobile view', () => {
      it('on full view 4/5', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageData,
            general: {
              desktopLayout: '4/1',
              mobileLayout: '4/5',
            },
          },
          SMALL
        );

        expect(container).toMatchSnapshot();
      });

      it('on full view 9/16', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageData,
            general: {
              desktopLayout: '4/1',
              mobileLayout: '9/16',
            },
          },
          SMALL
        );

        expect(container).toMatchSnapshot();
      });

      it('on split view', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageData,
            general: {
              desktopLayout: '4/1',
              mobileLayout: '3/4',
            },
          },
          SMALL
        );

        expect(container).toMatchSnapshot();
      });

      it('with minimal content', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageDataMinimal,
          },
          SMALL
        );

        expect(container).toMatchSnapshot();
      });

      it('on split view mobile background', () => {
        const pageData: FlexibleBannerContentProps = {
          ...productDetailsPageData,
          general: {
            desktopLayout: '4/1',
            mobileLayout: '3/4',
          },
        };
        pageData.content.mobileBackground = [{ mobileBackground: mobileSolidBackground }];
        const { container } = renderContent(pageData, SMALL);
        const backgroundTypeContainer = container.querySelector("div[class$='BackgroundTypeContainer']");
        expect(backgroundTypeContainer).toHaveStyle({
          background: mobileSolidBackground.color,
        });
      });

      it('displays the default background if a mobile background is not provided', () => {
        // doing a deep clone here based on productDetailsPageData to make sure the mutations
        // on data should not effect later test cases
        const productDetailClonedData = JSON.parse(JSON.stringify(productDetailsPageData));
        const pageData: FlexibleBannerContentProps = {
          ...productDetailClonedData,
          general: {
            desktopLayout: '2/1',
            mobileLayout: '3/4',
          },
        };
        pageData.content.background = solidBackground;
        delete pageData.content.mobileBackground;

        const { container } = renderContent(pageData, SMALL);
        const backgroundTypeContainer = container.querySelector("div[class$='BackgroundTypeContainer']");
        expect(backgroundTypeContainer).toHaveStyle({
          background: solidBackground.color,
        });
      });

      it('renders a mobile icon if one is provided', () => {
        const { getByAltText } = render(
          <StitchStyleProvider brand={Brands.Athleta}>
            <FlexibleBanner {...productDetailsPageData} />
          </StitchStyleProvider>,
          { breakpoint: SMALL }
        );
        expect(getByAltText('Mobile Icon')).toBeInTheDocument();
      });

      it('renders the default icon on mobile viewports if a mobile icon is not provided', () => {
        const altText = 'Default Icon';
        const props = {
          ...productDetailsPageData,
          content: {
            ...productDetailsPageData.content,
            mobileIcon: undefined,
          },
        };
        const { getByAltText } = render(
          <StitchStyleProvider brand={Brands.Athleta}>
            <FlexibleBanner {...props} />
          </StitchStyleProvider>,
          {
            breakpoint: SMALL,
          }
        );
        expect(getByAltText(altText)).toBeInTheDocument();
      });

      it('renders the icon at the equivalent mobileIcon size type on small viewports if a mobileIcon is not provided', () => {
        const props = {
          ...productDetailsPageData,
          content: {
            ...productDetailsPageData.content,
            icon: {
              icon: productDetailsPageData.content.icon?.icon,
              iconSize: '105px' as DesktopImageOrIconSizeType,
            },
            mobileIcon: undefined,
          },
        };
        const { getByAltText } = render(
          <StitchStyleProvider brand={Brands.Athleta}>
            <FlexibleBanner {...props} />
          </StitchStyleProvider>,
          {
            breakpoint: SMALL,
          }
        );
        const icon = getByAltText('Default Icon');
        expect(icon).toBeInTheDocument();
        // default icon of 105px -> mobile icon 80px/21.333333333333336vw
        expect(icon).toHaveAttribute('maxminheight', '21.333333333333336vw');
      });

      it('with defined scaleLimitPixels', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageDataMinimal,
            scaleLimitPixels: 1400,
          },
          SMALL
        );

        expect(container).toMatchSnapshot();
      });
    });

    describe('in desktop view', () => {
      it('on full view', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageData,
            general: {
              desktopLayout: '4/1',
              mobileLayout: '3/4',
            },
          },
          LARGE
        );

        expect(container).toMatchSnapshot();
      });

      it('on split view 50/50', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageData,
            general: {
              desktopLayout: '2/1',
              mobileLayout: '3/4',
            },
          },
          LARGE
        );

        expect(container).toMatchSnapshot();
      });

      it('on split view 33/66', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageData,
            general: {
              desktopLayout: '8/3',
              mobileLayout: '3/4',
            },
          },
          LARGE
        );

        expect(container).toMatchSnapshot();
      });

      it('with minimal content', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageDataMinimal,
          },
          LARGE
        );

        expect(container).toMatchSnapshot();
      });

      it('with defined scaleLimitPixels', () => {
        const { container } = renderContent(
          {
            ...productDetailsPageDataMinimal,
            scaleLimitPixels: 1400,
          },
          LARGE
        );

        expect(container).toMatchSnapshot();
      });

      it('renders the default icon on desktop viewports if a default icon is provided', () => {
        const props = {
          ...productDetailsPageData,
          content: { ...productDetailsPageData.content },
        };
        const { getByAltText } = render(
          <StitchStyleProvider brand={Brands.Athleta}>
            <FlexibleBanner {...props} />
          </StitchStyleProvider>,
          {
            breakpoint: LARGE,
          }
        );
        expect(getByAltText('Default Icon')).toBeInTheDocument();
      });
    });
  });

  describe('should have the expected aspect ratio in mobile view for old navy brand', () => {
    it('on full view 2/1', () => {
      const { container, queryByLabelText, queryByRole } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            ...productDetailsPageData.general,
            mobileLayout: '2/1_on',
          },
        },
        SMALL,
        Brands.OldNavy
      );
      const sectionElement = container.querySelector('section');
      const firstDivElement = sectionElement.querySelector('div');

      expect(firstDivElement).toHaveStyleRule('aspect-ratio', '2/1');
    });

    it('on full view 4/3', () => {
      const { container } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            ...productDetailsPageData.general,
            mobileLayout: '4/3_on',
          },
        },
        SMALL,
        Brands.OldNavy
      );
      const sectionElement = container.querySelector('section');
      const firstDivElement = sectionElement.querySelector('div');

      expect(firstDivElement).toHaveStyleRule('aspect-ratio', '4/3');
    });

    it('on split view 2/1', () => {
      const { container } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            ...productDetailsPageData.general,
            mobileLayout: '2/1_above',
          },
        },
        SMALL,
        Brands.OldNavy
      );
      const sectionElement = container.querySelector('section');
      const firstDivElement = sectionElement.querySelector('div');

      expect(firstDivElement).toHaveStyleRule('aspect-ratio', '2/1');
    });

    it('on split view 4/3', () => {
      const { container } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            ...productDetailsPageData.general,
            mobileLayout: '4/3_above',
          },
        },
        SMALL,
        Brands.OldNavy
      );
      const sectionElement = container.querySelector('section');
      const firstDivElement = sectionElement.querySelector('div');

      expect(firstDivElement).toHaveStyleRule('aspect-ratio', '4/3');
    });

    it('on split view 4/5', () => {
      const { container } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            ...productDetailsPageData.general,
            mobileLayout: '4/5_above',
          },
        },
        SMALL,
        Brands.OldNavy
      );
      const sectionElement = container.querySelector('section');
      const firstDivElement = sectionElement.querySelector('div');

      expect(firstDivElement).toHaveStyleRule('aspect-ratio', '4/5');
    });
  });

  describe('should not have a11y violations', () => {
    it('in mobile with default data', async () => {
      const { container } = renderContent(defaultData, SMALL);
      const rules = {
        rules: {
          /* Decorative images don’t add information to the content of a page.
           * In these cases, a null (empty) alt text (alt="")
           * is a legitimate authoring choice.
           * https://www.w3.org/WAI/tutorials/images/decorative/
           */
          'image-alt': { enabled: false },
          'role-img-alt': { enabled: false },
        },
      };

      expect(await axe(container, rules)).toHaveNoViolations();
    });

    it('in mobile with wrapper link data', async () => {
      const { container } = renderContent(productDetailsPageWithBannerLinkData, SMALL);
      const rules = {
        rules: {
          /* Decorative images don’t add information to the content of a page.
           * In these cases, a null (empty) alt text (alt="")
           * is a legitimate authoring choice.
           * https://www.w3.org/WAI/tutorials/images/decorative/
           */
          'image-alt': { enabled: false },
          'role-img-alt': { enabled: false },
        },
      };

      expect(await axe(container, rules)).toHaveNoViolations();
    });

    it('in desktop with default data', async () => {
      const { container } = renderContent(defaultData);
      expect(await axe(container)).toHaveNoViolations();
    });

    it('in desktop with wrapper link data', async () => {
      const { container } = renderContent(productDetailsPageWithBannerLinkData);
      expect(await axe(container)).toHaveNoViolations();
    });
  });

  describe('ctaButton', () => {
    it('should not render button when is not authored', () => {
      const { queryByText } = renderContent({
        ...defaultData,
        content: { ...defaultData.content, cta: undefined },
      });
      const [cta] = defaultData.content.cta || [];
      expect(queryByText(cta.cta.label)).not.toBeInTheDocument();
    });

    it('should render button when is authored', () => {
      const { getByText } = renderContent(defaultData);
      const [cta] = defaultData.content.cta || [];
      expect(getByText(cta.cta.label)).toBeInTheDocument();
    });
  });

  describe('useGradientBackfill', () => {
    it('should have gradient background if useGradientBackfill is true', () => {
      const { getByTestId } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            desktopLayout: '4/1',
            mobileLayout: '3/4',
          },
        },
        LARGE
      );
      expect(getByTestId('gradient-wrapper')).toHaveStyle({
        background: 'linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%)',
      });
    });

    it('should not have gradient background if useGradientBackfill is false', () => {
      const useGradientBackfillFalseData = JSON.parse(JSON.stringify(productDetailsPageData));
      useGradientBackfillFalseData.media.useGradientBackfill = false;
      const { getByTestId } = renderContent(
        {
          ...useGradientBackfillFalseData,
          general: {
            desktopLayout: '4/1',
            mobileLayout: '3/4',
          },
        },
        LARGE
      );
      expect(getByTestId('gradient-wrapper')).toHaveStyle({
        background: 'none',
      });
    });
  });

  describe('media', () => {
    describe('should accept a gap-hosted, non-vimeo video', () => {
      it('in desktop', () => {
        const { getByTestId } = renderContent({
          ...productDetailsPageUseCase4ForON,
          content: { ...productDetailsPageUseCase4ForON.content },
          brand: Brands.OldNavy,
        });
        expect(getByTestId('videocomponent-container')).toBeInTheDocument();
      });
      it('in mobile', () => {
        const { getByTestId } = renderContent({
          ...productDetailsPageUseCase4ForON,
          content: { ...productDetailsPageUseCase4ForON.content },
          brand: Brands.OldNavy,
          size: SMALL,
        });
        expect(getByTestId('videocomponent-container')).toBeInTheDocument();
      });
    });
    describe('should render a fallback image for non-vimeo video', () => {
      const videoDataWithInvalidVideoUrl = {
        _meta: productDetailsPageUseCase4ForON._meta,
        general: productDetailsPageUseCase4ForON.general,
        defaultImage: productDetailsPageUseCase4ForON.defaultImage,
        media: productDetailsPageUseCase4ForON.media,
        content: productDetailsPageUseCase4ForON.content,
      };

      it('in desktop', () => {
        videoDataWithInvalidVideoUrl.media.video![0].nonVimeoVideo.desktop.url = 'invalid-desktop-url';
        const { getByRole, queryByTestId } = renderContent({
          ...videoDataWithInvalidVideoUrl,
          brand: Brands.OldNavy,
        });
        expect(getByRole('img')).toBeInTheDocument();
        expect(queryByTestId('videocomponent-container')).not.toBeInTheDocument();
      });
      it('in mobile', () => {
        videoDataWithInvalidVideoUrl.media.video![0].nonVimeoVideo.mobile.url = 'invalid-mobile-url';
        const { getByRole, queryByTestId } = renderContent({
          ...videoDataWithInvalidVideoUrl,
          brand: Brands.OldNavy,
          size: SMALL,
        });
        expect(getByRole('img')).toBeInTheDocument();
        expect(queryByTestId('videocomponent-container')).not.toBeInTheDocument();
      });
    });
  });

  describe('show / hide', () => {
    const renderWithShowHide = (showHide: ShowHideBasedOnScreenSizeProps, size: Size) =>
      renderContent(
        {
          ...productDetailsPageData,
          general: {
            ...productDetailsPageData.general,
            showHideBasedOnScreenSize: showHide,
          },
        },
        size
      );

    it("should show on desktop and mobile if authors choose 'alwaysShow'", () => {
      const resultDesktop = renderWithShowHide('alwaysShow', LARGE);
      const resultMobile = renderWithShowHide('alwaysShow', SMALL);

      expect(resultDesktop.container.children.length).toBe(1);
      expect(resultMobile.container.children.length).toBe(1);
    });

    it("should hide on mobile if authors choose 'hideOnMobile'", () => {
      const resultDesktop = renderWithShowHide('hideOnMobile', LARGE);
      const resultMobile = renderWithShowHide('hideOnMobile', SMALL);

      expect(resultDesktop.container.firstChild.children.length).toBe(1);
      expect(resultMobile.container.firstChild.children.length).toBe(0);
    });

    it("should hide on desktop if authors choose 'hideOnDesktop'", () => {
      const resultDesktop = renderWithShowHide('hideOnDesktop', LARGE);
      const resultMobile = renderWithShowHide('hideOnDesktop', SMALL);

      expect(resultDesktop.container.firstChild.children.length).toBe(0);
      expect(resultMobile.container.firstChild.children.length).toBe(1);
    });
    it('should render videos with default getPlayerStyles', () => {
      const { container } = renderContent(
        {
          ...productDetailsPageData,
          general: {
            desktopLayout: '4/1',
            mobileLayout: '3/4',
          },
        },
        LARGE
      );
      expect(container).toMatchSnapshot();
    });

    it('should render getPlayerStyles with diaplyAlways styles', () => {
      const styleData = getVideoPlayerStyles({
        displayVideoControls: true,
        videoDisplaySettings: 'displayAlways',
      });
      expect(styleData).toEqual({
        width: '100% !important',
        '& video': {
          objectFit: 'cover',
          width: '100%',
        },
        '& + div': {
          display: 'flex',
        },
      });
    });
    it('should render getPlayerStyles with default styles', () => {
      const styleData = getVideoPlayerStyles({
        displayVideoControls: true,
        videoDisplaySettings: '',
      });
      expect(styleData).toEqual({
        width: '100% !important',
        '& video': {
          objectFit: 'cover',
          width: '100%',
        },
      });
    });
  });
});
