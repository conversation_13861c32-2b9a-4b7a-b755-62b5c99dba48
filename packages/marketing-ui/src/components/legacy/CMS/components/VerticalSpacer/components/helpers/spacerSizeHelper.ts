'use client';
import { SizeType } from '../../../../content-types/VerticalSpacer/types';

const getSize = (size: SizeType, override?: string) => (size === 'custom' ? `${override}px` : size);

export const getSpacerHeight = (isMobile: boolean, desktopSize: SizeType, mobileSize?: SizeType, desktopOverride?: string, mobileOverride?: string) => {
  if (isMobile && mobileSize) return getSize(mobileSize, mobileOverride);
  return getSize(desktopSize, desktopOverride);
};
