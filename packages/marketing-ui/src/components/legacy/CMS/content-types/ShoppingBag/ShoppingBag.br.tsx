'use client';
import { styled, Brands } from '@ecom-next/core/react-stitch';
import React from 'react';
import { CSSObject } from '@emotion/serialize';
import ShoppingBag from '../../components/ShoppingBag';
import { ShoppingBagCarousel } from '../../components/ShoppingBag/ShoppingBagCarousel';
import { ControlColorTypes } from '../../subcomponents/CMSMarketingCarousel/types';
import { getDetailsContent } from '../../subcomponents/Details';
import { usePlayPausePosition } from '../ISMCarousel/helpers';
import { ShoppingBagContentType } from './types';

const ShoppingBagTileWrapper = styled.div`
  width: 100%;
`;

const ShoppingBagBr = ({
  backgroundSlides,
  bannerLink,
  ctaButton,
  webAppearance,
  detailsPrefix,
  detailsLink,
  pemoleCode,
  htmlModalUrl,
  verticalCtaAlignment,
}: ShoppingBagContentType): JSX.Element => {
  const detailsContent = getDetailsContent(Brands.BananaRepublic, pemoleCode, htmlModalUrl);

  const position = usePlayPausePosition('bottom-left', 10, 10);

  const settings = {
    css: { ...position },
    size: 44,
    variant: 'primary' as ControlColorTypes,
  };

  const iconMargin = '12px 0 12px 0';

  const customStyles: CSSObject = {
    padding: 0,
  };

  const details = {
    customStyles,
    detailsContent,
    detailsLink,
    detailsPrefix,
  };

  return (
    <>
      {backgroundSlides.length >= 2 ? (
        <ShoppingBagCarousel
          backgroundSlides={backgroundSlides}
          bannerLink={bannerLink}
          ctaButton={ctaButton}
          details={details}
          iconMargin={iconMargin}
          playButtonSettings={settings}
          verticalCtaAlignment={verticalCtaAlignment}
          webAppearance={webAppearance}
        />
      ) : (
        <ShoppingBagTileWrapper>
          <ShoppingBag
            bannerLink={bannerLink}
            ctaButton={ctaButton}
            details={details}
            slide={backgroundSlides[0]}
            verticalCtaAlignment={verticalCtaAlignment}
            webAppearance={webAppearance}
          />
        </ShoppingBagTileWrapper>
      )}
    </>
  );
};

export default ShoppingBagBr;
