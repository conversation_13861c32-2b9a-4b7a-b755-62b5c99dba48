import React from 'react';
import { ShoppingBagCarouselProps, ShoppingBagProps } from '../../../components/ShoppingBag/types';
import { ShoppingBagContentType } from '../types';

const gapLogo = require('../../../../assets/gap-logo.png').default?.src;
const athletaLogo = require('../../../../assets/athleta-logo.svg').default?.src;
const background1Png = require('../../../../assets/background-1.png').default?.src;
const background2Png = require('../../../../assets/background-2.png').default?.src;

export const shoppingBagSingleSlideData: ShoppingBagContentType = {
  _meta: {
    name: 'Shopping Bag ED',
    schema: 'https://cms.gap.com/schema/content/v1/shopping-bag.json',
    deliveryId: '1c5d7442-8077-41e9-886a-952af46d24cd',
  },
  backgroundSlides: [
    {
      background: {
        type: 'solid',
        color: '#78ab78',
      },
      richTextArea1: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">EARN 2 POINTS FOR EVERY $1 SPENT*</span></p>',
      richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-3">Sign up by 12/31</span></p>',
      icon: [
        {
          svgPath: gapLogo,
          altText: 'icon for shopping bag slide',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
  ],
  bannerLink: {
    label: 'banner link label for screen readers',
    value: '#bannerLink',
  },
  ctaButton: {
    label: 'JOIN FOR FREE',
    value: '#ctaButtonValue',
  },
  verticalCtaAlignment: 'middle',
  webAppearance: {
    iconHorizontalPlacement: 'center',
    iconVerticalPlacement: 'top',
    verticalTextAlignment: 'middle',
    ctaButtonStyling: {
      buttonStyle: 'border',
      buttonColor: 'primary',
    },
    desktopImageOrIconSize: '48px',
    detailsLinkFontColor: '#FFFFFF',
  },
  detailsPrefix: 'Prefix',
  detailsLink: '*Details',
  pemoleCode: '12333',
  htmlModalUrl: 'gap.com',
};

export const shoppingBagCarouselData: ShoppingBagCarouselProps = {
  backgroundSlides: [
    {
      background: {
        type: 'image',
        images: [
          {
            svgPath: background1Png,
            altText: 'background image for shopping bag slide',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      richTextArea2:
        '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1" style=";color:#FFFFFF;font-weight:600">ALMOST EVERYTHING</span></p>',
      richTextArea1: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1" style=";color:#FFFFFF">40% OFF</span></p>',
    },
    {
      background: {
        type: 'gradient',
        gradient: {
          from: '#3756F1',
          to: '#F180DD',
        },
      },
      icon: [
        {
          svgPath: athletaLogo,
          altText: 'logo',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--subhead-1">CURBSIDE &amp; IN-STORE PICKUP</span></p>',
    },
    {
      background: {
        type: 'solid',
        color: '#6fd2e9',
      },
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '6c1cf159-efdf-491b-8520-094ff8f030f6',
            name: '220307_43-M6559_Color_Yellow_CTA_W_VIBanner',
            endpoint: 'oldnavy',
            defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
          },
          altText: '',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      richTextArea1: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 1</span></p>',
      richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 2</span></p>',
    },
    {
      background: {
        type: 'image',
        images: [
          {
            svgPath: background2Png,
            altText: 'background image for shopping bag slide',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      richTextArea1: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 1</span></p>',
      richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 2</span></p>',
    },
  ],
  bannerLink: {
    label: 'BannerLink',
    value: '#bannerLink',
  },
  ctaButton: {
    label: 'CTA 1',
    value: '#ctaButtonValue',
  },
  verticalCtaAlignment: 'middle',
  webAppearance: {
    iconHorizontalPlacement: 'center',
    iconVerticalPlacement: 'top',
    verticalTextAlignment: 'middle',
    ctaButtonStyling: {
      buttonStyle: 'border',
      buttonColor: 'dark',
      primaryHex: '#e45',
      secondaryHex: '#e67',
    },
    desktopImageOrIconSize: '48px',
    detailsLinkFontColor: '#e45',
  },
  details: {
    detailsContent: '',
    detailsLink: 'Details',
    detailsPrefix: 'Prefix',
  },
  playButtonSettings: {
    size: 44,
    variant: 'primary',
  },
  appendDots: (dots: JSX.Element): JSX.Element => <div>{dots}</div>,
  customPaging: undefined,
};

export const shoppingBagPropData: ShoppingBagProps = {
  slide: {
    background: {
      type: 'solid',
      color: '#6fd2e9',
    },
    icon: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '6c1cf159-efdf-491b-8520-094ff8f030f6',
          name: '220307_43-M6559_Color_Yellow_CTA_W_VIBanner',
          endpoint: 'oldnavy',
          defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    richTextArea1: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 1</span></p>',
    richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-1">Rich Text 2</span></p>',
  },
  bannerLink: {
    label: 'Banner',
    value: 'gap.com',
  },
  ctaButton: {
    label: 'CTA 1',
    value: 'gap.com',
  },
  verticalCtaAlignment: 'middle',
  webAppearance: {
    iconHorizontalPlacement: 'center',
    iconVerticalPlacement: 'top',
    verticalTextAlignment: 'middle',
    ctaButtonStyling: {
      buttonStyle: 'border',
      buttonColor: 'dark',
      primaryHex: '#e45',
      secondaryHex: '#e67',
    },
    desktopImageOrIconSize: '64px',
    detailsLinkFontColor: '#e45',
  },
  details: {
    detailsContent: '',
    detailsLink: 'Details',
    detailsPrefix: 'Prefix',
  },
  className: '',
};

export const shoppingBagSingleSlideDataBr: ShoppingBagContentType = {
  _meta: {
    name: 'Shopping Bag ED',
    schema: 'https://cms.gap.com/schema/content/v1/shopping-bag.json',
    deliveryId: '1c5d7442-8077-41e9-886a-952af46d24cd',
  },
  backgroundSlides: [
    {
      background: {
        type: 'solid',
        color: '#78ab78',
      },
      richTextArea1: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">EARN 2 POINTS FOR EVERY $1 SPENT*</span></p>',
      richTextArea2: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-3">Sign up by 12/31</span></p>',
      icon: [
        {
          svgPath: gapLogo,
          altText: 'icon for shopping bag slide',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
  ],
  bannerLink: {
    label: 'banner link label for screen readers',
    value: 'https://www.oldnavy.com',
  },
  ctaButton: {
    label: 'JOIN FOR FREE',
    value: 'https://www.gap.com',
  },
  verticalCtaAlignment: 'middle',
  webAppearance: {
    iconHorizontalPlacement: 'center',
    iconVerticalPlacement: 'top',
    verticalTextAlignment: 'middle',
    ctaButtonStyling: {
      buttonStyle: 'solid',
      buttonColor: 'primary',
      buttonSize: 'small',
    },
    desktopImageOrIconSize: '48px',
    detailsLinkFontColor: '#FFFFFF',
  },
  detailsPrefix: 'Prefix',
  detailsLink: '*Details',
  pemoleCode: '12333',
  htmlModalUrl: 'gap.com',
};
