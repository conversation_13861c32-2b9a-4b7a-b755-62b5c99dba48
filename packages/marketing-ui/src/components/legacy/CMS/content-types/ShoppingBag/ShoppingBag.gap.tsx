// @ts-nocheck
'use client';
import React from 'react';
import { Brands, useTheme } from '@ecom-next/core/react-stitch';
import { ShoppingBagContentType } from './types';
import { getDetailsContent } from '../../subcomponents/Details';
import ShoppingBag from '../../components/ShoppingBag';
import { ShoppingBagCarousel } from '../../components/ShoppingBag/ShoppingBagCarousel';
import { ControlColorTypes } from '../../subcomponents/CMSMarketingCarousel/types';
import { usePlayPausePosition } from '../ISMCarousel/helpers';
import { LinePaginationContainer } from '../../components/ShoppingBag/ShoppingBagCarousel/LinePaginationContainer';
import { LinePaginationButton } from '../../components/ShoppingBag/ShoppingBagCarousel/LinePaginationButton';
import { linePaginationCarouselCss, carouselWrapperCss } from './styles/gap';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';

const ShoppingBagGap = ({
  backgroundSlides,
  bannerLink,
  ctaButton,
  webAppearance,
  detailsPrefix,
  detailsLink,
  pemoleCode,
  htmlModalUrl,
  verticalCtaAlignment,
}: ShoppingBagContentType): JSX.Element => {
  const theme = useTheme();
  const isLarge = useViewportIsLarge();

  const paginationColorType = 'secondary';
  const paginationAlignment = 'center';

  const detailsContent = getDetailsContent(Brands.Gap, pemoleCode, htmlModalUrl);

  const position = usePlayPausePosition('bottom-left', 13, 18);

  const settings = {
    css: { ...position },
    size: 18,
    variant: 'primary' as ControlColorTypes,
  };

  const iconMargin = '12px 0 25px 0';

  return (
    <>
      {backgroundSlides.length >= 2 ? (
        <ShoppingBagCarousel
          appendDots={dots => (
            <LinePaginationContainer brand={Brands.Gap} paginationAlignment={paginationAlignment}>
              {dots}
            </LinePaginationContainer>
          )}
          backgroundSlides={backgroundSlides}
          bannerLink={bannerLink}
          carouselClassName='pagination-line'
          carouselCss={linePaginationCarouselCss({
            isLarge,
            theme,
            paginationColorType,
          })}
          ctaButton={ctaButton}
          customPaging={() => <LinePaginationButton brand={Brands.Gap} />}
          details={{
            detailsContent,
            detailsLink,
            detailsPrefix,
          }}
          iconMargin={iconMargin}
          playButtonSettings={settings}
          verticalCtaAlignment={verticalCtaAlignment}
          webAppearance={webAppearance}
          wrapperCss={carouselWrapperCss}
        />
      ) : (
        <ShoppingBag
          bannerLink={bannerLink}
          ctaButton={ctaButton}
          details={{
            detailsContent,
            detailsLink,
            detailsPrefix,
          }}
          iconMargin={iconMargin}
          slide={backgroundSlides[0]}
          verticalCtaAlignment={verticalCtaAlignment}
          webAppearance={webAppearance}
        />
      )}
    </>
  );
};

export default ShoppingBagGap;
