// @ts-nocheck
'use client';
import { CSSObject } from '@ecom-next/core/legacy/react-stitch/types';
import { BaseContentType, AlignmentProps, VerticalAlignmentProps, VerticalCtaAlignment, RowPositionType, BannerLinkType } from '../../global/types';
import { CarouselSettings } from '../../subcomponents/CMSMarketingCarousel/types';
import { AdvanceImageData, MediaImageLink } from '../../helpers/ImageEncoder/types';
import { CtaProps } from '../../subcomponents/CTAButton';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';
import { DetailsLinkAlignment } from '../ISMBanner/types';

export type ISMCarouselSchema =
  | 'https://cms.gap.com/schema/content/v1/ism-double-partial-image-carousel.json'
  | 'https://cms.gap.com/schema/content/v1/ism-single-full-image-carousel.json'
  | 'https://cms.gap.com/schema/content/v1/ism-double-full-image-carousel.json'
  | 'https://cms.gap.com/schema/content/v1/ism-single-partial-image-carousel.json';

// type AmpIconSizeType = "1X" | "2X" | "3X" | "4X" | "5X";
type AmpIconSizeType = string;
export type InSortMarketingBannerCarouselWebAppearance = {
  imageOrIconHorizontalAlignment?: AlignmentProps;
  imageOrIconPlacement?: 'above' | 'below';
  ctaHorizontalAlignment?: AlignmentProps;
  verticalTextAlignment?: VerticalAlignmentProps;
  verticalCtaAlignment?: VerticalCtaAlignment;
  desktopImageOrIconSize?: string;
  mobileImageOrIconSize?: string;
  ctaButtonStyling?: CtaProps['ctaButtonStyling'];
  detailsLinkFontColor?: string;
  ctaButton1Styling?: CtaProps['ctaButtonStyling'];
  ctaButton2Styling?: CtaProps['ctaButtonStyling'];
  showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps;
};
export interface InSortMarketingBannerCarouselDoublePartialWebAppearance {
  imageOrIconHorizontalAlignment?: AlignmentProps;
  imageOrIconPlacement?: 'above' | 'below';
  desktopImageOrIconSize?: string;
  mobileImageOrIconSize?: string;
}
export interface InSortMarketingCarouselContentType extends BaseContentType<ISMCarouselSchema> {
  frames: InSortMarketingBannerCarouselFrame[];
  contentConfiguration?: 'unique' | 'persistent';
  controlsIconsColor?: 'primary' | 'secondary';
  image?: MediaImageLink | AdvanceImageData[];
  text?: string;
  richTextArea?: string;
  bodyCopy?: string;
  cta?: CtaProps['ctaButton'];
  cta1?: CtaProps['ctaButton'];
  cta2?: CtaProps['ctaButton'];
  bannerLink?: BannerLinkType;
  detailsLink?: string;
  detailsPrefix?: string;
  detailsLinkLocation?: DetailsLinkAlignment;
  detailsLinkFontColor?: string;
  pemoleCode?: string;
  htmlModalUrl?: string;
  webAppearance?: InSortMarketingBannerCarouselWebAppearance;
  rowPosition?: RowPositionType;
  instanceName?: string;
  carouselSettings?: CarouselSettings;
}
export type InSortMarketingBannerCarouselFrame = {
  backgroundImage: AdvanceImageData[];
  image?: MediaImageLink | AdvanceImageData[];
  cta2?: CtaProps['ctaButton'];
  cta1?: CtaProps['ctaButton'];
  cta?: CtaProps['ctaButton'];
  richTextArea: string;
  webAppearance: Pick<
    InSortMarketingBannerCarouselWebAppearance,
    | 'verticalTextAlignment'
    | 'imageOrIconHorizontalAlignment'
    | 'imageOrIconPlacement'
    | 'ctaHorizontalAlignment'
    | 'ctaButtonStyling'
    | 'mobileImageOrIconSize'
    | 'desktopImageOrIconSize'
    | 'verticalCtaAlignment'
  >;
};

export interface CarouselIconAndRTEProps {
  avoidOverlapping?: boolean;
  shouldDisplayPlayPauseButton?: boolean;
  richTextArea?: string;
  imageOrIcon?: MediaImageLink | AdvanceImageData[];
  imageOrIconPlacement?: 'above' | 'below';
  desktopImageOrIconSize?: AmpIconSizeType;
  mobileImageOrIconSize?: AmpIconSizeType;
  imageOrIconHorizontalAlignment?: AlignmentProps;
  bannerLink?: BannerLinkType;
  padding?: string;
  verticalTextAlignment?: VerticalAlignmentProps;
}

export interface ImageOrIconProps {
  imageOrIconHorizontalAlignment?: AlignmentProps;
}

export type ContainerProps = Pick<CSSObject, 'justifyContent'>;
export interface ISMCarouselDoublePartialContentType extends InSortMarketingCarouselContentType {
  carouselSettings: CarouselSettings;
}

export interface CarouselSlideOverlayProps {
  width: number;
  height: number;
}
export interface ISMBannerContainerCarouselProps {
  width: number;
  height: number;
}
