// @ts-nocheck
'use client';
import React from 'react';
import ISMOverlay from '../../../components/ism-banner/ISMOverlay';
import ISMBannerImageOrIcon from '../../../components/ism-banner/ISMBannerImageOrIcon';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge';
import { RichText } from '../../../subcomponents/RichText';
import { CarouselIconAndRTEProps } from '../types';
import { IconAndRTEContainer, ImageOrIcon, ContainerDiv } from './styles';

const CarouselIconAndRTE = ({
  avoidOverlapping,
  bannerLink,
  imageOrIcon: image,
  imageOrIconPlacement,
  imageOrIconHorizontalAlignment,
  desktopImageOrIconSize = '24px',
  mobileImageOrIconSize = '14px',
  richTextArea: text,
  shouldDisplayPlayPauseButton,
  padding,
  verticalTextAlignment,
}: CarouselIconAndRTEProps): JSX.Element => {
  const isAbove = imageOrIconPlacement === 'above';
  const isDesktop = useViewportIsLarge();
  const imageOrIconSize: string = isDesktop ? desktopImageOrIconSize : mobileImageOrIconSize;

  let justifyContent = 'center';
  if (verticalTextAlignment === 'top') {
    justifyContent = 'flex-start';
  } else if (verticalTextAlignment === 'bottom') {
    justifyContent = 'flex-end';
  }

  const isPlayingMode = verticalTextAlignment === 'bottom' && shouldDisplayPlayPauseButton;

  let paddingIconAndRTEContainer = {};

  if (padding) {
    paddingIconAndRTEContainer = { padding };
  } else if (avoidOverlapping) {
    paddingIconAndRTEContainer = {
      padding: isDesktop ? `16px 60px ${isPlayingMode ? 70 : 16}px 60px` : `16px 44px ${isPlayingMode ? 65 : 16}px 44px`,
    };
  }

  const imageOrIcon = image ? (
    <ImageOrIcon imageOrIconHorizontalAlignment={imageOrIconHorizontalAlignment}>
      <ISMBannerImageOrIcon size={imageOrIconSize} src={image} />
    </ImageOrIcon>
  ) : undefined;

  return (
    <>
      <ContainerDiv justifyContent={justifyContent}>
        <IconAndRTEContainer
          css={{
            flexDirection: isAbove ? 'column' : 'column-reverse',
            ...paddingIconAndRTEContainer,
          }}
        >
          {imageOrIcon}
          {text && (
            <RichText
              css={{
                width: '100%',
              }}
              isDesktop={isDesktop}
              text={text}
            />
          )}
        </IconAndRTEContainer>
      </ContainerDiv>

      {bannerLink?.label && bannerLink?.value && <ISMOverlay title={bannerLink.label} to={bannerLink.value} type='link' />}
    </>
  );
};

export default CarouselIconAndRTE;
