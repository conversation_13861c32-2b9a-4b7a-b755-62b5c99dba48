// @ts-nocheck
import { StorytellingAndProductRatingContentType } from '../types';

// CompA
export const storytellingAndProductratingUseCase1: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling and Product Rating - VR1',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating.json',
    deliveryId: '4000f1e3-4962-45db-a50a-2a01a32497bf',
  },
  mainImage: {
    defaultImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'da67b26b-8240-4215-91b8-25ebaef46444',
          name: '798591_002_PFCO_AT_WOMENS_B2_SU22_LifeStyle_1_036',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: 'img',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Allyson Felix, Run</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Wearing Size M</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" />',
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'solid',
          color: '#EAD1B1',
        },
      },
    },
    content: {
      text: {
        upperText: {
          defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-3">Meet the new Jacket</span></p>',
        },
        lowerText: {
          defaultText:
            '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4">Stay comfortable on cold-weather commutes and chilly evening walks.  Gives you room to move without the bulk—and is made of recycled materials.</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'border',
            buttonColor: 'light',
          },
          cta: {
            label: 'Shop all Jackets',
            value: 'https://www.gap.com',
          },
        },
      ],
      quoteText: {
        defaultText:
          '<p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--headlineAlt-4 ">“Love this lightweight jacket. Super warm and great for layering.”</span></p><hr style= "display:block;border:0;height:8px;margin:0;background:transparent; " aria-hidden= "true " /><hr style= "display:block;border:0;height:8px;margin:0;background:transparent; " aria-hidden= "true " /><p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--body-1 "> - Lisa Smith, Portland Or.</span></p>',
      },
      ratingColor: 'dark',
      rating: 5,
    },
    productCard: {
      image: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '696bd084-3ae4-437b-9195-c2d98d9e2a9c',
            name: 'HP_Test_Surf_S',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: 'img',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: {
        label: 'title',
        value: 'https://google.com',
      },
      text: '<p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--body-1 ">Product Name</span></p><p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--body-1 ">3 colors</span></p><p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--body-1 ">$00</span></p>',
    },
  },
};
// Comp B
export const storytellingAndProductratingUseCase2: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling and Product Rating-VR2',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating.json',
    deliveryId: 'd995b3a1-1a5c-49a7-8915-76e0fe1c1426',
  },
  mainImage: {
    defaultImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'da67b26b-8240-4215-91b8-25ebaef46444',
          name: '798591_002_PFCO_AT_WOMENS_B2_SU22_LifeStyle_1_036',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'gradient',
          gradient: {
            from: '#FFFFFF',
            to: '#EAD1B1',
          },
        },
      },
    },
    content: {
      text: {
        upperText: {
          defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-3">Meet the new Jacket</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'border',
            buttonColor: 'light',
          },
          cta: {
            label: 'Shop all Jackets',
            value: 'https://www.gap.com',
          },
        },
      ],
    },
    productCard: {
      image: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '4bf91beb-5929-40e9-9fe1-86c0fe063c19',
            name: 'SPR23_D2_GirlDP_Sec_S_IMG2',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: 'img',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      url: {
        label: 'title',
        value: 'https://www.google.com',
      },
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Product Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">3 colors</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">$00</span></p>',
    },
  },
};
// Comp C
export const storytellingAndProductratingUseCase3: StorytellingAndProductRatingContentType = {
  _meta: {
    name: 'Storytelling and Product Rating - VR3',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating.json',
    deliveryId: 'baac2cc1-609e-4597-bff3-8de15071268a',
  },
  mainImage: {
    defaultImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '46090f09-afb9-4246-b724-4d18a235bb5d',
          name: 'wayfinding_sp3b_card_tops@2x',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: 'img',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Allyson Felix, Run</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Wearing Size M</span></p>',
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'image',
          images: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: '1a0a7a61-f504-48b4-a4bc-0d1cef77c637',
                name: 'HP_Storytelling_DowntoEarth_XL@2x',
                endpoint: 'athleta',
                defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
              },
              altText: 'img',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
        },
      },
    },
    content: {
      text: {
        lowerText: {
          defaultText:
            '<p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--body-3 ">Stay comfortable on cold-weather commutes and chilly evening walks.  Gives you room to move without the bulk—and is made of recycled materials.</span></p>',
        },
      },
      ctaButton: [
        {
          buttonStyle: {
            buttonStyle: 'border',
            buttonColor: 'light',
          },
          cta: {
            label: 'Shop all Jackets',
            value: 'https://www.gap.com',
          },
        },
      ],
      ratingColor: 'dark',
      quoteText: {
        defaultText:
          '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-4">“Love this lightweight jacket. Super warm and great for layering.”</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">- Lisa Smith, Portland Or.</span></p>',
      },
      rating: 4.5,
    },
  },
};
