// @ts-nocheck
import React from 'react';
import { <PERSON><PERSON>, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { render, RenderResult, act } from 'test-utils';
import { defaultData as defaultProps } from '../__fixtures__/test-data';
import { setupRender } from '../../../../test-helpers';
import CTADropdownON from '..';
import ButtonLayoutWrapper from '../../../subcomponents/ButtonLayoutWrapper';
import * as RichTextModule from '../../../subcomponents/RichText';
jest.mock('../../../subcomponents/RichText', () => {
  const orig = jest.requireActual('../../../subcomponents/RichText');
  return { ...orig, __esModule: true };
});

jest.mock('../../../subcomponents/RichText', () => {
  const orig = jest.requireActual('../../../subcomponents/RichText');
  return { ...orig, __esModule: true };
});

import * as CTADropdownModule from '../../../subcomponents/CTADropdown';
jest.mock('../../../subcomponents/CTADropdown', () => {
  const orig = jest.requireActual('../../../subcomponents/CTADropdown');
  return { ...orig, __esModule: true };
});

import { OnCtaRedesign2024Context } from '../../../../contexts/OnCtaRedesign2024Context';

const _render = setupRender(CTADropdownON, defaultProps);
const ButtonLayoutWrapperSpy = jest.spyOn(ButtonLayoutWrapper as any, 'render');
describe('CTADropdownON', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('on desktop', () => {
    it('should match snapshot', () => {
      const { container } = _render({}, Brands.OldNavy, LARGE);
      expect(container).toMatchSnapshot();
    });
    it('should render CTADropdown base component', () => {
      const CTADropdownSpy = jest.spyOn(CTADropdownModule, 'default');
      _render({}, Brands.OldNavy, LARGE);
      expect(CTADropdownSpy).toHaveBeenCalled();
      expect(CTADropdownSpy).toHaveBeenCalledTimes(defaultProps.ctaDropdownList.length);
    });
    it('should render RichText base component', () => {
      const RichTextSpy = jest.spyOn(RichTextModule, 'RichText');
      _render({}, Brands.OldNavy, LARGE);
      expect(RichTextSpy).toHaveBeenCalled();
    });
    it('should not render RichText base component', () => {
      const RichTextSpy = jest.spyOn(RichTextModule, 'RichText');
      _render({ heading: undefined }, Brands.OldNavy, LARGE);
      expect(RichTextSpy).not.toHaveBeenCalled();
    });

    it('should match snapshot when both on-cta-redesign-2024 feature flag and OnCtaRedesign2024Context are enabled', () => {
      const { container } = render(
        <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
          <StitchStyleProvider brand={Brands.OldNavy} enabledFeatures={{ 'on-cta-redesign-2024': true }}>
            <CTADropdownON {...defaultProps} />
          </StitchStyleProvider>
        </OnCtaRedesign2024Context.Provider>
      );

      expect(container).toMatchSnapshot();
    });
  });

  describe('on mobile', () => {
    it('should match snapshot', () => {
      const { container } = _render({}, Brands.OldNavy, SMALL);
      expect(container).toMatchSnapshot();
    });
    it('should render ButtonLayoutContainer when any number of 2,3 or 4 is in mobileGridLayout string', () => {
      _render({ mobileGridLayout: '2/1/3' }, Brands.OldNavy, LARGE);
      expect(ButtonLayoutWrapperSpy).toHaveBeenCalled();
    });
    it('should NOT render ButtonLayoutContainer when any number of 2,3 or 4 is NOT in mobileGridLayout string', () => {
      _render({ mobileGridLayout: '1/1/1' }, Brands.OldNavy, LARGE);
      expect(ButtonLayoutWrapperSpy).not.toHaveBeenCalled();
    });
  });
});
