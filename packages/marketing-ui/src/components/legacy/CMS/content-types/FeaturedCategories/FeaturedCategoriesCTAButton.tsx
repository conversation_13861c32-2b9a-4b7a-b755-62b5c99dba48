// @ts-nocheck
'use client';
import React from 'react';
import { Brands, useTheme } from '@ecom-next/core/react-stitch';
import { BRCTAButton } from '../../global/types';
import { isComposableButtonData } from '../../../components/ComposableButton/helpers';
import { CtaButton } from '../../subcomponents/CTAButton';
import { ComposableButtonBR } from '../../../components/ComposableButtonBR';
import { isComposableButtonBRData } from '../../../components/ComposableButtonBR/helpers';
import { CtaButton as CtaButtonType, FeaturedCategoriesCTAButtonProps } from './types';

const FeaturedCategoriesCTAButton = (props: FeaturedCategoriesCTAButtonProps) => {
  const { categoryCardContentCustomStyles, contentJustification = 'center', ctaButtons: unknownCTAs = [], isDesktop = true } = props;

  const { BananaRepublic: BR, BananaRepublicFactoryStore: BRFS } = Brands;
  const theme = useTheme();
  const { brand } = theme;

  const isBR = [BR, BRFS].includes(brand);
  const BRCTAs = isBR ? (unknownCTAs as BRCTAButton[]).filter(({ buttonStyle }) => buttonStyle && isComposableButtonBRData(buttonStyle)) : [];

  const CTAButtons = !isBR
    ? (unknownCTAs as CtaButtonType[]).filter(({ buttonStyle, cta }) => buttonStyle && isComposableButtonData(buttonStyle) && cta !== undefined)
    : [];

  const isNaturalWidth = isDesktop && BRCTAs.length === 1;
  return unknownCTAs.length ? (
    <div
      css={{
        ...categoryCardContentCustomStyles,
        justifyContent: contentJustification,
      }}
    >
      {isBR
        ? BRCTAs.map(({ buttonStyle, cta }) => (
            <ComposableButtonBR
              key={`cta-${cta?.label}`}
              color={buttonStyle.buttonColor}
              css={{
                ...(isNaturalWidth ? {} : { width: '140px' }),
                pointerEvents: 'all',
              }}
              label={cta?.label ?? ''}
              size={!isDesktop ? 'small' : buttonStyle.buttonSize}
              value={cta?.value ?? ''}
              variant={buttonStyle.buttonStyle}
            />
          ))
        : CTAButtons.map(({ buttonStyle, cta }) => (
            <CtaButton
              key={`cta-${cta?.label}`}
              ctaButton={{ label: cta?.label ?? '', value: cta?.value ?? '' }}
              ctaButtonStyling={buttonStyle}
              css={{ pointerEvents: 'all' }}
            />
          ))}
    </div>
  ) : null;
};

export default FeaturedCategoriesCTAButton;
