// @ts-nocheck
'use client';
import { Brands, styled, type CSSObject } from '@ecom-next/core/react-stitch';
import { CategoryCardsContainerProps, ContentJustification, SizeHeight, VerticalAlignment } from './types';

interface CrossBrandStylesBaseTypes {
  brand: Brands;
  isDesktop: boolean;
  size: SizeHeight;
}

interface CrossBrandBackgroundStyles {
  backgroundSize?: CSSObject['backgroundSize'];
  brand: Brand<PERSON>;
  contentJustification: ContentJustification;
  customBackgroundStyles?: CSSObject;
  height: string;
  verticalAlignment: VerticalAlignment;
}

const getBrandMaxCarouselHeight = {
  br: {
    large: [810, 576, 480],
    medium: [640, 341, 400],
    small: [360, 256, 195],
  },
  brfs: {
    large: [810, 576, 480],
    medium: [640, 341, 400],
    small: [360, 256, 195],
  },
};
const getBrandMaxImageHeight = {
  at: {
    large: [810, 340],
    medium: [650, 340],
    small: [360, 187.5],
  },
  br: {
    large: [810, 576, 340],
    medium: [640, 341, 248],
    small: [360, 256, 195],
  },
  brfs: {
    large: [810, 576, 340],
    medium: [640, 341, 248],
    small: [360, 256, 195],
  },
  gap: {
    large: [810, 340],
    medium: [650, 284],
    small: [360, 187.5],
  },
  gapfs: {
    large: [810, 340],
    medium: [650, 284],
    small: [360, 187.5],
  },
  on: {
    large: [810, 340],
    medium: [650, 340],
    small: [360, 187.5],
  },
};

export const getMaxHeightForCarousel = ({ brand, size, isDesktop }: CrossBrandStylesBaseTypes) =>
  brand === Brands.BananaRepublic || brand === Brands.BananaRepublicFactoryStore
    ? getBrandMaxCarouselHeight[brand][size][isDesktop ? 0 : 2]
    : getBrandMaxCarouselHeight[brand][size][isDesktop ? 0 : 1];

export const getMaxHeightForImage = ({ brand, size, isDesktop }: CrossBrandStylesBaseTypes) =>
  brand === Brands.BananaRepublic || brand === Brands.BananaRepublicFactoryStore
    ? getBrandMaxImageHeight[brand][size][isDesktop ? 0 : 2]
    : getBrandMaxImageHeight[brand][size][isDesktop ? 0 : 1];

export const getBackgroundImageStyles = ({
  backgroundSize,
  contentJustification,
  verticalAlignment,
  customBackgroundStyles = {},
  height,
}: CrossBrandBackgroundStyles) => {
  const contentJustificationMap = {
    left: 'start',
    center: 'center',
    right: 'end',
  };
  const verticalAlignmentMap = { top: 'start', middle: 'center', bottom: 'end' };

  return {
    width: '100% !important',
    // get minimum of the scaled height - if the image reaches max height - height is fixed to maxheight else scales according to width.
    backgroundSize: backgroundSize || 'cover',
    display: 'flex',
    justifyContent: contentJustificationMap[contentJustification],
    alignItems: verticalAlignmentMap[verticalAlignment],
    boxSizing: 'border-box',
    height,
    ...customBackgroundStyles,
  } as CSSObject;
};

export const getCustomControlStyles = ({ isDesktop, videoSoundIcons }) =>
  ({
    display: 'none',
    gap: videoSoundIcons ? 15 : 0,
    position: 'absolute',
    maxWidth: 'max-content',
    maxHeight: 'max-content',
    margin: isDesktop ? '10px 15px' : '10px',
    right: 0,
    bottom: 0,

    '& > button': {
      left: 0,
      bottom: 0,
      zIndex: 4,
      position: 'relative',
    },

    '& > div > button': {
      display: videoSoundIcons ? 'flex' : 'none',
      left: 0,
      bottom: 0,
      zIndex: 4,
      position: 'relative',
    },

    '& > div > div': {
      display: videoSoundIcons ? 'flex' : 'none',
      left: 0,
      zIndex: 3,
      bottom: 0,
    },
  }) as CSSObject;

export const getFallbackImageStyles = ({ backgroundSize, height }): CSSObject => ({
  width: '100% !important',
  backgroundSize: backgroundSize || 'cover',
  '& > div > img': {
    height,
  },
  height,
});

export const getPlayerStyles = ({ displayVideoControls, videoDisplaySettings, height }): CSSObject => {
  const basePlayerStyles = {
    width: '100% !important',
    '& video': {
      objectFit: 'cover',
      width: '100%',
      height,
    },
  } as CSSObject;

  if (displayVideoControls && videoDisplaySettings === 'displayAlways') {
    return {
      ...basePlayerStyles,
      '& + div': {
        display: 'flex',
      },
    };
  }
  if (displayVideoControls && videoDisplaySettings === 'displayOnHover') {
    return {
      ...basePlayerStyles,
      ':hover': {
        '& + div': { display: 'flex' },
      },
      '& + div': {
        display: 'none',
        ':hover': { display: 'flex' },
      },
    };
  }
  if (!displayVideoControls) {
    return {
      ...basePlayerStyles,
      '& + div': { display: 'none' },
    };
  }

  return { ...basePlayerStyles };
};

const getScrollStyles = (numberOfCards: number, customScrollStyles?: CSSObject): CSSObject => ({
  display: 'grid',
  gridTemplateColumns: `repeat(${numberOfCards}, 250px)`,
  overflowX: 'scroll',
  scrollbarWidth: 'none',
  padding: 0,
  '-ms-overflow-style': 'none',
  '::-webkit-scrollbar': {
    display: 'none',
  },
  ...customScrollStyles,
});

const getExposedStyles = (customExposedStyles): CSSObject => ({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  ...customExposedStyles,
});

export const CategoryCardsContainer = styled.div<CategoryCardsContainerProps>(({ isDesktop, numberOfCards, variant, categoryCardContainerStyles = {} }) => {
  const baseCSS = {
    width: '100%',
    display: 'flex',
    flexWrap: isDesktop ? 'nowrap' : 'wrap',
    ...categoryCardContainerStyles,
  } as CSSObject;

  switch (variant) {
    case 'basic':
      return baseCSS;
    case 'scroll':
      return getScrollStyles(numberOfCards, categoryCardContainerStyles);
    case 'exposed':
      return getExposedStyles(categoryCardContainerStyles);
    default:
      return baseCSS;
  }
});
