import React from 'react';
// @ts-ignore
import { Brands, useEnabledFeatures } from '@ecom-next/core/react-stitch';
// @ts-ignore
import { ISMBannerCarouselContainer, ISMBannerContainer } from '../../../components/ism-banner';
import { DetailsButton, getDetailsContent } from '../../../subcomponents/Details/index';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge/index';
import ISMBannerCarouselFullImageFrame from '../../../components/ism-banner/ISMBannerCarouselFullImageFrame';
import { InSortMarketingCarouselFullImageContentType } from '../index';
import { ContentDiv, WrapperDiv } from '../styles';
import ISMBannerCarouselSlide from '../../../components/ism-banner/ISMBannerCarouselSlide';
import { InSortMarketingBannerCarouselFrame } from '../../ISMCarousel/types';
import CustomPlayPauseButton from '../../../subcomponents/PlayPauseButton/index';
import { usePlayPausePosition } from '../../ISMCarousel/helpers';
import { DetailsButtonWrapper } from '../../ISMBanner/styles';

export type InSortMarketingCarouselFullImageProps = InSortMarketingCarouselFullImageContentType;

const AthletaInSortMarketingCarouselFullImage = (props: InSortMarketingCarouselFullImageProps): JSX.Element => {
  const { _meta, contentConfiguration, frames, bannerLink, detailsLink, detailsPrefix, pemoleCode, htmlModalUrl, webAppearance, carouselSettings } = props;

  const isDesktopVP = useViewportIsLarge();
  const enabledFeatures = useEnabledFeatures();
  const newPlpGridFeatureFlag = !!enabledFeatures?.['mui-new-plp-grid-2025'];
  const detailsContent = getDetailsContent(Brands.Athleta, pemoleCode, htmlModalUrl);

  const { schema } = _meta || {};
  const isDouble = schema?.includes('double');
  let containerHeight = isDesktopVP ? 408 : 300;
  let aspectWidth = isDesktopVP ? 239 : 167;

  if (isDouble) {
    aspectWidth = isDesktopVP ? 492 : 343;
    containerHeight = isDesktopVP ? 408 : 300;
  }

  const containerAspect = `${aspectWidth}:${containerHeight}`;

  const isContentPersistent = contentConfiguration === 'persistent';
  const edgePositionSpace = 8;
  const position = usePlayPausePosition(carouselSettings?.styling?.playPausePosition, edgePositionSpace, edgePositionSpace);

  const playIsShowing =
    !carouselSettings ||
    carouselSettings?.transition === 'pan' ||
    (carouselSettings?.continuousLoop && carouselSettings.styling.hideChevrons && carouselSettings?.type === 'autoplay');

  const wrapperPadding = isDesktopVP ? 15 : 8;
  const paddingDetailLink = isDesktopVP ? 20 : 16;
  return (
    <ISMBannerContainer
      showHideBasedOnScreenSize={webAppearance?.showHideBasedOnScreenSize}
      {...(!newPlpGridFeatureFlag ? { aspectRatio: containerAspect } : { useAspectRatio: false })}
    >
      <ISMBannerCarouselContainer
        carouselSettings={carouselSettings}
        customPlayPauseButton={
          <CustomPlayPauseButton
            css={position}
            size={carouselSettings?.playPauseButtonSize || 24}
            variant={carouselSettings?.styling?.controlsIconsColor}
            customShape={carouselSettings?.styling?.playPauseShape || 'altCircle'}
          />
        }
        frames={frames.map((frame: InSortMarketingBannerCarouselFrame) => (
          <ISMBannerCarouselSlide
            key={frame.backgroundImage[0]?.image?.id || `${frame?.cta1?.value}_${frame?.cta1?.label}`}
            showContent
            src={frame.backgroundImage}
            {...(!newPlpGridFeatureFlag && { aspectRatio: containerAspect })}
          >
            {!isContentPersistent && (
              <WrapperDiv padding={wrapperPadding}>
                <ISMBannerCarouselFullImageFrame
                  allowSecondaryCta
                  bannerLink={bannerLink}
                  frame={frame}
                  isDouble={isDouble}
                  paddingBottomDefault={detailsLink ? paddingDetailLink : 0}
                  playIsShowing={playIsShowing}
                />
              </WrapperDiv>
            )}
          </ISMBannerCarouselSlide>
        ))}
      >
        {isContentPersistent && frames.length > 0 && (
          <ContentDiv>
            <WrapperDiv padding={wrapperPadding}>
              <ISMBannerCarouselFullImageFrame
                allowSecondaryCta
                bannerLink={bannerLink}
                frame={frames[0]}
                isDouble={isDouble}
                paddingBottomDefault={detailsLink ? paddingDetailLink : 0}
                playIsShowing={playIsShowing}
              />
            </WrapperDiv>
          </ContentDiv>
        )}

        {detailsLink && (
          <DetailsButtonWrapper>
            <DetailsButton color={webAppearance?.detailsLinkFontColor} label={detailsLink} prefix={detailsPrefix} value={detailsContent} />
          </DetailsButtonWrapper>
        )}
      </ISMBannerCarouselContainer>
    </ISMBannerContainer>
  );
};

export default AthletaInSortMarketingCarouselFullImage;
