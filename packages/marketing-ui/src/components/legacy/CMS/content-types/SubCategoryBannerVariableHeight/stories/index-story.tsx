// @ts-nocheck
'use client';
import React, { useEffect } from 'react';
import { addons } from '@storybook/addons';
import { BreakpointContext, BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import CategoryBannerVariableHeight from '../../../components/CategoryBannerVariableHeight';
import { brandSubCatExamples } from '../../../components/CategoryBannerVariableHeight/__fixtures__/test-data';
import README from '../README.mdx';
import CategoryPaddingImage from '../static/category-page-mobile-padding.png';
import useBrandValue from '../../../../hooks/useBrandValue';
import { useBrand } from '../../../../hooks/useBrand';

export default {
  title: 'Common/JSON Components (Marketing)/content-types/SubCategoryBannerVariableHeight',
  parameters: {
    sandbox: true,
    knobs: { disable: true },
    docs: {
      page: README,
    },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

const MOBILE_GRID_PADDING = '17px';
const GAP_MOBILE_GRID_PADDING = '20px';

type RenderedStoryProps = {
  variationSize: string;
};
const RenderedStory = (props: RenderedStoryProps): JSX.Element => {
  const brand = useBrand();
  const { variationSize } = props;
  const channel = addons.getChannel();
  let brandData = brandSubCatExamples[variationSize][brand];

  const mobileGridPadding = useBrandValue({ gap: GAP_MOBILE_GRID_PADDING, gapfs: GAP_MOBILE_GRID_PADDING }, MOBILE_GRID_PADDING);
  useEffect(() => {
    const brandChange = (brandInfo: string) => {
      brandData = brandSubCatExamples[variationSize][brandInfo];
      channel.emit('CHANGE', {
        name: 'data',
        value: brandData,
      });
    };
    channel.addListener('setTheme', brandChange);
    return () => {
      channel.removeListener('setTheme', brandChange);
    };
  }, []);
  return (
    <BreakpointProvider>
      <BreakpointContext.Consumer>
        {value => (
          <>
            <style>
              {`
              .sb-main-padded{
                padding: ${value.size === 'small' ? `0 ${mobileGridPadding} !important` : '0 !important'}
              }`}
            </style>
            <CategoryBannerVariableHeight {...brandData} />
            {value.size === 'small' && (
              <p style={{ marginTop: '30px', fontSize: '12px' }}>
                Note: Because this component typically appears within a subcategoryBanner slot, this example mimics the{' '}
                <a href={CategoryPaddingImage} rel='noreferrer' style={{ textDecoration: 'underline' }} target='_blank' title='Category page mobile padding'>
                  padding added from the product grid
                </a>{' '}
                on a category page at mobile breakpoint.
              </p>
            )}
          </>
        )}
      </BreakpointContext.Consumer>
    </BreakpointProvider>
  );
};

export const small = (): JSX.Element => <RenderedStory variationSize='small' />;

export const medium = (): JSX.Element => <RenderedStory variationSize='medium' />;

export const large = (): JSX.Element => <RenderedStory variationSize='large' />;
