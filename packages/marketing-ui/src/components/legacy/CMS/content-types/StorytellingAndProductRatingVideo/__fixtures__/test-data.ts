// @ts-nocheck
import { StorytellingAndProductRatingContentType } from '../../StorytellingAndProductRating/types';

type TestSPRVideoData = Pick<StorytellingAndProductRatingContentType, '_meta' | 'defaultVideo'> & {
  storytellingProductRating: Required<StorytellingAndProductRatingContentType['storytellingProductRating']>;
};

export const storytellingAndProductRatingVideoData: TestSPRVideoData = {
  _meta: {
    name: 'Storytelling and Product Rating Video',
    schema: 'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json',
    deliveryId: '96a627ca-864c-4d46-aaba-93b4d874d882',
  },
  defaultVideo: {
    desktop: {
      url: 'https://player.vimeo.com/video/797530501?h=ecd25dc777&autopause=1&muted=1&controls=1',
      fallbackImage: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '0ceb5937-f23b-4039-9330-8b05c5893b13',
            name: 'SP1_Spot_S',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: 'Women doing yoga',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
    mobile: {
      url: 'https://player.vimeo.com/video/797530501?h=ecd25dc777&autopause=1&muted=1&controls=1',
    },
  },
  storytellingProductRating: {
    general: {
      background: {
        background: {
          type: 'solid',
          color: '#ffffbf',
        },
        mobileBackground: [
          {
            mobileBackground: {
              type: 'gradient',
              gradient: {
                from: '#2D2',
                to: '#4DD',
              },
            },
          },
        ],
      },
      showHideBasedOnScreenSize: 'alwaysShow',
    },
    content: {
      text: {
        upperText: {
          defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-3">Upper Rich Text Default</span></p>',
          mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headlineAlt-3">Upper Rich Text Mobile</span></p>',
        },
        lowerText: {
          defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Lower Rich Text Default</span></p>',
          mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Lower Rich Text Mobile</span></p>',
        },
      },
      ctaButton: [
        {
          cta: {
            label: 'Shop More',
            value: 'more',
          },
          buttonStyle: {
            buttonStyle: 'border',
            buttonColor: 'dark',
          },
        },
      ],
      quoteText: {
        defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Quote Rich Text Default</span></p>',
        mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Quote Rich Text Mobile</span></p>',
      },
      rating: 2,
      ratingColor: 'dark',
    },
    productCard: {
      image: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '28abeeec-f382-4954-bf8a-52959a5aff9b',
            name: 'IMG_1',
            endpoint: 'athleta',
            defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
          },
          altText: 'imageSlt',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Product Card Rich Text</span></p>',
      url: {
        label: 'Link title',
        value: '#',
      },
    },
  },
};
