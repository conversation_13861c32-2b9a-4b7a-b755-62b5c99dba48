'use client';
// @ts-ignore
import { Brands, useTheme, useEnabledFeatures } from '@ecom-next/core/react-stitch';
// @ts-ignore
import { GapTheme } from '@ecom-next/core/legacy/react-stitch/types/theme/brands/gap/types';
import React from 'react';
import { AspectRatioWrapper } from '../utilities';
import { useViewportIsLarge } from '../../../../hooks/index';
import { CMSMarketingCarousel } from '../../../subcomponents/CMSMarketingCarousel/index';
import { ShowHideWrapper } from '../../../subcomponents/ShowHideWrapper/index';
import { NonVimeoVideoControlColors } from '../../../types/amplience';
import { LinePaginationButton, LinePaginationContainer } from '../commonComponents';
import { SpotlightCarouselContentType } from '../types';
import { lazyLoadIfFirstFrameIsVideo } from '../../../subcomponents/CMSMarketingCarousel/helpers/lazyLoadIfFirstFrameIsVideo/index';
import CustomPlayPauseButton from '../../../subcomponents/PlayPauseButton/index';
import { MAX_FRAMES } from './constants';
import { useLinePaginationConfig } from './hooks';
import { getMediaAspectRatio, linePaginationCarouselCss, pausePlayButtonStyles } from './styles';
import { CarouselFrame } from './components/CarouselFrame';
import { isMobileXSmall } from './utils';

const SpotlightCarousel = ({
  desktopBannerSize,
  mobileBannerSize,
  carouselSettings,
  frames,
  layout = 'fullBleed',
  showHideBasedOnScreenSize,
}: SpotlightCarouselContentType) => {
  const theme: GapTheme = useTheme() as unknown as GapTheme;
  const isLarge = useViewportIsLarge();
  const enabledFeatures = useEnabledFeatures();

  const controlType = carouselSettings?.styling?.controlsIconsColor ?? 'primary';
  const controlColor: NonVimeoVideoControlColors = {
    fillColor: '#00000080',
    iconColor: '#FFFFFF',
    setMaxHeight: true,
  };

  if (controlType !== 'primary') {
    controlColor.fillColor = '#FFFFFF';
    controlColor.iconColor = '#2C2824';
    controlColor.setMaxHeight = true;
  }

  const hasFrames = carouselSettings && frames;
  const showFullBleedContent = hasFrames && layout === 'fullBleed';
  const autoPlayControl = carouselSettings?.videoControlSettings?.autoplay;

  const hasMoreThanOneFrame = frames && frames?.length > 1;

  const linePaginationConfig = useLinePaginationConfig(carouselSettings, frames);

  if (showFullBleedContent) {
    const isLinePagination = carouselSettings.styling.paginationStyle === 'line';
    const pagination = isLinePagination && !hasMoreThanOneFrame ? 'hide' : carouselSettings.styling.pagination;

    const hidePagination =
      carouselSettings.styling.pagination === 'hide' ||
      (isLarge && carouselSettings.styling.pagination === 'mobile') ||
      (!isLarge && carouselSettings.styling.pagination === 'desktop') ||
      !hasMoreThanOneFrame;

    const hideChevrons = !hidePagination || carouselSettings.styling.hideChevrons;
    const paginationColorType = carouselSettings?.styling.paginationColor;

    const enableLinePagination = isLinePagination && hasMoreThanOneFrame;

    const aspectRatio = getMediaAspectRatio({
      isLarge,
      mobileBannerSize,
      desktopBannerSize,
    });

    const framesList = frames.slice(0, MAX_FRAMES).map(frame => {
      /** TODO:
       * Gap can have maximum of three subframes, but it demand changes in styling, especially aspect ratio and width.
       * So, to start, let's render only the first subframe. In the proper card, work on split view and replace it to
       * Alternatively you can check CategoryBannerVariableHeightCarousel code to see how they handled with a frame with
       * split view.
       * File: src/CMS/components/CategoryBannerVariableHeight/styles.ts (line 16)
       */
      const frameToRender = frame?.subframes?.[0] ?? frame;
      const { contentJustification, text, verticalAlignment } = frameToRender;
      return (
        <CarouselFrame
          key={`spotlight-carousel-${text?.default?.replace(/\W/g, '')}-${verticalAlignment?.default}-${contentJustification?.default}`}
          autoPlayControl={autoPlayControl}
          carouselSettings={carouselSettings}
          desktopBannerSize={desktopBannerSize}
          frame={frameToRender}
          linePaginationConfig={linePaginationConfig}
          mobileBannerSize={mobileBannerSize}
        />
      );
    });

    const shouldUseAspectRatio = isLarge || !isMobileXSmall(mobileBannerSize);

    const linePaginationClassName = linePaginationConfig.enabled ? 'pagination-line' : '';

    const content =
      framesList.length < 2 ? (
        framesList
      ) : (
        <CMSMarketingCarousel
          appendDots={
            linePaginationConfig.enabled
              ? dots => (
                  <LinePaginationContainer brand={Brands.Gap} paginationAlignment={carouselSettings.styling.paginationAlignment}>
                    {dots}
                  </LinePaginationContainer>
                )
              : undefined
          }
          carouselSettings={{
            ...carouselSettings,
            slidesToShow: 1,
            styling: {
              ...carouselSettings.styling,
              hideChevrons,
              pagination,
            },
            origin: 'SpotlightCarousel',
            ...lazyLoadIfFirstFrameIsVideo(enabledFeatures, frames),
          }}
          className={`gap-spotlight-carousel ${linePaginationClassName}`}
          // @ts-ignore
          css={
            enableLinePagination
              ? linePaginationCarouselCss({
                  isLarge,
                  theme,
                  paginationColorType,
                })
              : {
                  '& .slick-dots': {
                    position: 'unset',
                    lineHeight: '0',
                  },
                }
          }
          customContainerStyles={{
            '.gap-spotlight-carousel .slick-list': {
              overflow: 'auto',
              overflowX: 'clip',
              overflowY: 'visible',
            },
            '& .slick-list': {
              overflow: 'initial !important',
            },
          }}
          customPaging={
            linePaginationConfig.enabled
              ? lineIndex => (
                  <LinePaginationButton
                    aria-hidden='false'
                    aria-label={`${lineIndex}`}
                    brand={Brands.Gap}
                    delay={carouselSettings.autoplay.delay}
                    speed={carouselSettings.animation.speed}
                  />
                )
              : undefined
          }
          customPlayPauseButton={
            <CustomPlayPauseButton
              className='gap-spotlight-carousel-play-pause-button'
              // @ts-ignore
              css={pausePlayButtonStyles(isLarge)}
              size={24}
              customShape='altCircle'
            />
          }
        >
          {framesList}
        </CMSMarketingCarousel>
      );

    return (
      <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={showHideBasedOnScreenSize}>
        {shouldUseAspectRatio ? <AspectRatioWrapper aspectRatio={aspectRatio}>{content}</AspectRatioWrapper> : content}
      </ShowHideWrapper>
    );
  }

  return null;
};

export default SpotlightCarousel;
