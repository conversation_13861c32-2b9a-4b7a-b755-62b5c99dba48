// @ts-nocheck
'use client';
import React from 'react';
import { useTheme } from '@ecom-next/core/react-stitch';
import { Meta } from '@storybook/react';
import SpotlightCarousel from '../index';
import README from '../README.mdx';
import {
  linePaginationFullBleedBannerSizeXLargeStoryData,
  linePaginationFullBleedBannerSizeLargeStoryData,
  linePaginationFullBleedBannerSizeMediumStoryData,
  linePaginationFullBleedBannerSizeSmallStoryData,
  linePaginationFullBleedBottomVerticalAlignPaginationAlignmentCenterStoryData,
  linePaginationFullBleedBottomVerticalAlignStoryData,
  linePaginationFullBleedMiddleVerticalAlignPaginationAlignmentCenterStoryData,
  linePaginationFullBleedMiddleVerticalAlignStoryData,
  linePaginationFullBleedTopVerticalAlignPaginationAlignmentCenterStoryData,
  linePaginationFullBleedTopVerticalAlignStoryData,
} from '../__fixtures__/story-data';
import { type SpotlightCarouselAllBrands } from '../types';
import { NotImplemented, NullJSX, UnsupportedByBrand } from '../../../../stories/story-helpers';
import { ComponentType, Story } from './types';

const meta: Meta<ComponentType> = {
  title: 'Common/JSON Components (Marketing)/Content-Types/SpotlightCarousel/FullBleed/LinePagination',
  parameters: {
    docs: {
      page: README,
    },
    layout: 'fullscreen',
  },
  tags: ['exclude'],
};

export default meta;

const StoryWrapper = (props: SpotlightCarouselAllBrands) => {
  const content = <SpotlightCarousel {...props} />;
  const { brand } = useTheme();
  return brand.includes('br') ? (
    <NotImplemented />
  ) : (
    <NullJSX content={content}>
      <UnsupportedByBrand />
    </NullJSX>
  );
};

export const PaginationAlignmentLeftTopVerticalAlign: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedTopVerticalAlignStoryData,
  },
  name: 'Pagination Alignment Left | Content Top Vertical Align',
};

export const PaginationAlignmentLeftMiddleVerticalAlign: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedMiddleVerticalAlignStoryData,
  },
  name: 'Pagination Alignment Left | Content Middle Vertical Align',
};

export const PaginationAlignmentLeftBottomVerticalAlign: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedBottomVerticalAlignStoryData,
  },
  name: 'Pagination Alignment Left | Content Bottom Vertical Align',
};

export const PaginationAlignmentCenterTopVerticalAlign: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedTopVerticalAlignPaginationAlignmentCenterStoryData,
  },
  name: 'Pagination Alignment Center | Content Top Vertical Align',
};

export const PaginationAlignmentCenterMiddleVerticalAlign: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedMiddleVerticalAlignPaginationAlignmentCenterStoryData,
  },
  name: 'Pagination Alignment Center | Content Middle Vertical Align',
};

export const PaginationAlignmentCenterBottomVerticalAlign: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedBottomVerticalAlignPaginationAlignmentCenterStoryData,
  },
  name: 'Pagination Alignment Center | Content Bottom Vertical Align',
};

export const BannerSizeXLarge: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedBannerSizeXLargeStoryData,
  },
};

export const BannerSizeLarge: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedBannerSizeLargeStoryData,
  },
};

export const BannerSizeMedium: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedBannerSizeMediumStoryData,
  },
};

export const BannerSizeSmall: Story = {
  render: props => <StoryWrapper {...props} />,
  args: {
    ...linePaginationFullBleedBannerSizeSmallStoryData,
  },
};
