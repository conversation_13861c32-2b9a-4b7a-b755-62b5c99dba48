// @ts-nocheck
'use client';
import { AdvanceImageExtensionValue, BaseContentType } from '../../../types/amplience';
import { type CMSMarketingCarouselProps } from '../../../subcomponents/CMSMarketingCarousel';
import { ShowHideBasedOnScreenSizeProps } from '../../../subcomponents/ShowHideWrapper/types';
import { CTADropdownItem } from '../../../subcomponents/CTAButton';
import { VerticalAlignmentProps, CtaProps } from '../../../global/types';
import { SpotlightCarouselSchema, UpperLowerContentAreaProps, Frame, CTAContent, Justification } from '../types';

export type MediaSize = 'xsmall' | 'small' | 'medium' | 'large';

type ONMediaContent = {
  image: Omit<SpotlightImage, 'imageSize'>;
};

interface ONFrame extends Omit<Frame, 'contentJustification' | 'mediaContent'> {
  contentJustification?: Justification;
  mediaContent: ONMediaContent;
  imageOverlay?: ImageOverlay;
}

interface ONCtaContent extends Omit<CTAContent, 'ctaDropdownList'> {
  ctaDropdownList?: CTADropdownItem[];
  mobileLayout: 'stacked' | 'linear';
}

export interface AboveBelowContentAreaProps extends Omit<UpperLowerContentAreaProps, 'cta' | 'ctaButtons' | 'contentPlacement'> {
  contentJustification: 'left' | 'middle' | 'right';
  contentPlacement?: 'left' | 'middle' | 'right'; // It's in the schema, but visually does nothing
  cta: ONCtaContent;
  mobileLayout?: 'stacked' | 'linear';
  rte?: string;
  showHideBasedOnScreenSize: ShowHideBasedOnScreenSizeProps;
}

export type ImageOverlay = {
  contentJustification: 'left' | 'middle' | 'right';
  contentPlacement?: ContentPlacement; // It's in the schema, but visually does nothing
  cta: ONCtaContent;
  detailsLink?: DetailsLink;
  handle: Handle;
  imageText?: string;
  mediaSize?: MediaSize;
  useGradientBackfill: boolean;
  useGradientBackfillFooter: boolean;
};
export interface ONSpotlightCarouselContentType extends BaseContentType<SpotlightCarouselSchema> {
  carouselSettings?: CMSMarketingCarouselProps['carouselSettings'];
  contentBlocks?: {
    belowImage: AboveBelowContentAreaProps;
  };
  frames?: ONFrame[];
  showHideBasedOnScreenSize?: ShowHideBasedOnScreenSizeProps;
  desktopBannerSize?: MediaSize;
  mobileBannerSize?: MediaSize;
}

export type SpotlightImage = {
  heroImage: AdvanceImageExtensionValue;
  mobileHeroImage?: AdvanceImageExtensionValue;
  imageSize: MediaSize;
  link?: CtaProps;
};

export type Handle = {
  text?: string;
  placement: 'left' | 'right';
};

export type DetailsLink = {
  label?: string;
  prefixLabel?: string;
  pemoleCode?: string;
  htmlModalUrl?: string;
  fontColor?: string;
};

type HorizontalAlignments = 'left' | 'middle' | 'right';
export type ContentPlacement = {
  horizontal: HorizontalAlignments;
  vertical: VerticalAlignmentProps;
};
