'use client';
// @ts-ignore: Cannot-find-module ts(2792)
import { CSSObject, styled, css } from '@ecom-next/core/react-stitch';
// @ts-ignore: Cannot-find-module ts(2792)
import BackgroundTypeContainer from '../../../subcomponents/BackgroundTypeContainer';
import { CarouselCSSProps, ContentContainerProps, ContentJustification } from '../types';
import { getAlignment } from '../utilities';
import { Backfill } from '../commonStyles';

export const FullBleedStyles = (isDesktop: boolean, contentJustification?: ContentJustification): CSSObject => {
  const overrideJustification = contentJustification?.mobileOverride?.[0]?.mobileContentJustification;
  const defaultJustification = contentJustification?.default;

  const justification = !isDesktop && overrideJustification ? overrideJustification : defaultJustification;

  const alignItems = getAlignment(justification);

  return {
    gap: '20px',
    padding: isDesktop ? '50px 60px' : '50px 30px',
    alignItems,
    textAlign: alignItems,
  };
};

export const InsetBackgroundContainer = styled(BackgroundTypeContainer)({
  display: 'flex !important',
  flexDirection: 'column',
  height: 'fit-content',
  width: '100%',
  padding: '40px 10px 10px',
  gap: '40px',
});

export const InsetContentContainer = styled.div<ContentContainerProps>(({ innerContentJustification, isLarge, isXLarge }) => {
  const defaultStyles: CSSObject = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'left',
    textAlign: 'left',
    gap: '20px',
    padding: isXLarge ? '115px' : '0 20px',
  };

  const overrideJustification = innerContentJustification?.mobileOverride?.[0]?.mobileContentJustification;
  const defaultJustification = innerContentJustification?.default;
  const justification = !isLarge && overrideJustification ? overrideJustification : defaultJustification;

  const alignItems = getAlignment(justification);

  return {
    ...defaultStyles,
    alignItems,
    textAlign: alignItems,
  };
});

export const InsetBackFill = styled(Backfill)(({ useGradientBackfill }) => ({
  height: '100px',
  bottom: '0',
  background: useGradientBackfill ? 'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 50%, rgba(0, 0, 0, 0.00) 100%)' : 'transparent',
}));

export const ctaButtonsStyles: CSSObject = {
  gap: '15px',
};

export const linePaginationCarouselCss = ({ isXLarge }: CarouselCSSProps) => css`
  &.pagination-line .slick-dots {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.pagination-line .slick-dots[data-alignment='left'] {
    left: ${isXLarge ? '20px' : '15px'};
    bottom: 0;
    top: 0;
    width: 3px;
  }

  &.pagination-line .slick-dots[data-alignment='center'] {
    right: 0;
    left: 0;
    bottom: ${isXLarge ? '20px' : '15px'};
    height: 3px;
  }

  & .at-spotlight-carousel-pagination-wrapper {
    flex: 0;
    text-wrap: nowrap;
    display: flex;
    gap: 10px;
  }

  &.pagination-line .slick-dots[data-alignment='left'] .at-spotlight-carousel-pagination-wrapper {
    transform: rotate(90deg);
  }

  &.pagination-line li {
    width: auto !important;
    height: auto !important;
    cursor: default !important;
  }

  & .slick-dots li {
    margin: 0 !important;
  }

  & .slick-dots li button {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  & .at-spotlight-carousel-pagination-line {
    position: relative;
    width: 55px;
    height: 1px;
    padding: 0;
    background-color: white;
  }

  & button.at-spotlight-carousel-button::before {
    content: none;
    display: none;
  }

  & .slick-active .at-spotlight-carousel-pagination-line .at-spotlight-carousel-pagination-progress {
    border-radius: 3px;
    height: 3px;
    left: 0;
    top: -1px;
    position: absolute;
    background-color: white;
    animation-name: at-spotlight-carousel-animation;
    animation-fill-mode: forwards;
    animation-timing-function: linear;
  }

  @keyframes at-spotlight-carousel-animation {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
`;
