// @ts-ignore
import { BreakpointContext, BreakpointProviderState, Size, LARGE } from '@ecom-next/core/breakpoint-provider';
// @ts-ignore
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';

import React from 'react';

import { renderHook } from 'test-utils';
import { CarouselSettings } from '../../../subcomponents/CMSMarketingCarousel/types';
import { videoFrames } from '../__fixtures__/gap-story-data';
import { useLinePaginationConfig, useMediaControlsConfig } from './hooks';

const WithProviders = (_size: Size): React.FC => {
  const Component: React.FC = props => (
    <StitchStyleProvider brand={Brands.Gap}>
      <BreakpointContext.Provider
        value={
          {
            greaterOrEqualTo: (size: Size) => size === _size,
          } as unknown as BreakpointProviderState
        }
      >
        {/* @ts-ignore */}
        {props.children}
      </BreakpointContext.Provider>
    </StitchStyleProvider>
  );
  Component.displayName = `WithProviders(${_size})`;
  return Component;
};

const baseConfig: Omit<CarouselSettings, 'styling'> = {
  animation: { ease: false },
  autoplay: { pauseOnHover: false },
  continuousLoop: false,
  type: 'clickThrough',
};

describe('SpotlightCarousel useLinePaginationConfig hook for GAP', () => {
  it('should match enabled true and justification right', () => {
    const { result } = renderHook(
      () =>
        useLinePaginationConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'right',
            },
          },
          videoFrames
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({
      enabled: true,
      justification: 'right',
    });
  });

  it('should match enabled false justification center', () => {
    const { result } = renderHook(
      () =>
        useLinePaginationConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'center',
            },
          },
          videoFrames
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({
      enabled: true,
      justification: 'center',
    });
  });
  it("should match enabled false if it's outside of the range", () => {
    const { result } = renderHook(
      () =>
        useLinePaginationConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'center',
            },
          },
          [videoFrames[0]]
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({ enabled: false });
  });
});
describe('SpotlightCarousel useMediaControlsConfig hook for GAP', () => {
  it('should match enabled true and justification right', () => {
    const { result } = renderHook(
      () =>
        useMediaControlsConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'right',
            },
            videoControlSettings: {
              autoplay: false,
              sequentialLoop: false,
              videoDisplaySettings: 'displayAlways',
              videoSoundIcons: true,
              audioVideoControlLocation: 'right',
              displayVideoControls: true,
            },
          },
          videoFrames[0].subframes![0]
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({
      enabled: true,
      justification: 'right',
    });
  });
  it('should match enabled true and justification left', () => {
    const { result } = renderHook(
      () =>
        useMediaControlsConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'right',
            },
            videoControlSettings: {
              autoplay: false,
              sequentialLoop: false,
              videoDisplaySettings: 'displayAlways',
              videoSoundIcons: true,
              audioVideoControlLocation: 'left',
              displayVideoControls: true,
            },
          },
          videoFrames[0].subframes![0]
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({
      enabled: true,
      justification: 'left',
    });
  });
  it('should match enabled false if displayVideoControls is false', () => {
    const { result } = renderHook(
      () =>
        useMediaControlsConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'right',
            },
            videoControlSettings: {
              autoplay: false,
              sequentialLoop: false,
              videoDisplaySettings: 'displayAlways',
              videoSoundIcons: true,
              audioVideoControlLocation: 'left',
              displayVideoControls: false,
            },
          },
          videoFrames[0].subframes![0]
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({ enabled: false });
  });
  it("should match enabled fase if it's vimeo", () => {
    const { result } = renderHook(
      () =>
        useMediaControlsConfig(
          {
            ...baseConfig,
            styling: {
              paginationStyle: 'line',
              pagination: 'desktopAndMobile',
              paginationAlignment: 'right',
            },
            videoControlSettings: {
              autoplay: false,
              sequentialLoop: false,
              videoDisplaySettings: 'displayAlways',
              videoSoundIcons: true,
              audioVideoControlLocation: 'left',
              displayVideoControls: true,
            },
          },
          videoFrames[1].subframes![0]
        ),
      // @ts-ignore
      {
        wrapper: WithProviders(LARGE),
      }
    );

    expect(result.current).toMatchObject({ enabled: false });
  });
});
