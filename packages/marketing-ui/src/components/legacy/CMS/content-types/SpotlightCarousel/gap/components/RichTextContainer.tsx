'use client';
/* eslint no-nested-ternary: 0 */
import React from 'react';
import { RichText } from '../../../../subcomponents/RichText/index';
import { Text } from '../../types';

interface RichTextContainerProps {
  isLarge: boolean;
  text: Text | undefined;
}

export const RichTextContainer: React.FC<RichTextContainerProps> = ({ text, isLarge }) =>
  text ? <RichText text={!isLarge && text.mobileOverride ? text.mobileOverride : text.default} /> : null;
