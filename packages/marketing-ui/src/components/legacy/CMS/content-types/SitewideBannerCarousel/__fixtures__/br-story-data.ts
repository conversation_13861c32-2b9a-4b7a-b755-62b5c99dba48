import { SitewideBannerCarouselContentType, SitewideBannerCarouselSlide } from '../types';

const banner1: SitewideBannerCarouselSlide = {
  background: {
    type: 'solid',
    color: '#000000',
  },
  mobileBackground: {
    type: 'solid',
  },
  desktopDetailsLayout: 'linear',
  carouselBannerStyling: {
    ctaButtonStyling: {
      buttonStyle: 'underline',
      buttonSize: 'small',
      buttonColor: 'primary',
    },
    detailsPrefixFontColor: 'secondary',
    detailsLinkFontColor: 'secondary',
    desktopImageOrIconSize: '24px',
    mobileImageOrIconSize: '14px',
  },
  countdownEnabled: false,
  /* Timer is out of the scope for BR */
  // timer: {
  //   endDate: "full",
  //   typography: {
  //     defaultTypography: {
  //       size: "body1",
  //       weight: "inherit",
  //       clockCapitalized: false,
  //     },
  //     mobileOverrideTypography: {
  //       size: "body1",
  //       weight: "inherit",
  //       clockCapitalized: false,
  //     },
  //   },
  //   format: "fullTimeFormat",
  //   location: "before",
  //   hideDays: false,
  // },
  mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFF">Lorem ipsum dolor. Ends 5/20.</span></p>',
  detailsLink: 'Details',
};

const banner2: SitewideBannerCarouselSlide = {
  ...banner1,
  mainRichText:
    '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-4" style="color:#FFF">Lorem ipsum dolor. Ends 5/20. Use Code: XXXXXXXXXX to Get 20% Off</span></p>',
  mobileRichTextArea1:
    '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-4" style="color:#FFF">Lorem ipsum dolor. Ends 5/20.</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-4" style="color:#FFF">Use Code: XXXXXXXXXX to Get 20% Off</span></p>',
};

export const comp1: SitewideBannerCarouselContentType = {
  _meta: {
    name: 'Comp 1',
    schema: 'https://cms.gap.com/schema/content/v1/sitewide-banner-carousel.json',
    deliveryId: '9410592e-861b-458b-a40f-98ab89506326',
  },
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: true,
    autoplay: {
      delay: 3000,
      pauseOnHover: false,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'secondary',
      pagination: 'hide',
      hideChevrons: false,
    },
  },
  /* Although they come in the schema, they're out of the scope for BR */
  // redpointExperimentRunning: false,
  // experimentRunning: false,
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
    mobileLayout: 'stacked',
  },
  carouselBanners: [{ ...banner1 }, { ...banner2 }],
};
