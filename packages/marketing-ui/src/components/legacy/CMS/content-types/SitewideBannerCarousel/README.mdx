# `SitewideBannerCarousel` Content-Type

## What is it?

- `SitewideBannerCarousel` (SWBC) is a narrow marketing banner used in sitewide slots that employs a carousel to cycle through multiple promos.
- It is currently available to all brands: `AT`, `BR`, `BRFS`, `Gap`, `GapFS` & `ON`.
- It is a `JSON`-configurable component that accepts `JSON` keys generated by our Content Management System, [Amplience](https://gapinc.app.amplience.net/). Note that this format differs from earlier JSON formats.

## Default Behavior

- `_meta` Configuration data from Amplience.
- `carouselBanners` Array of each carousel content e.g. _background_, _cta1_, _mainRichText_ and _detailsLink_.
- `carouselSettings` Setting to manage the carousel e.g. _autoplay_, _animation_ and _transition_.
- `webAppearance` Appearance settings not related only to the carousel e.g. _showHideBasedOnScreenSize_ and _mobileLayout_.

## API

For API information, see the [types.ts](./SitewideBannerCarousel/types.ts) file.

## Important Notes

These are the schema differences in `carouselBanners > carouselBannerStyling`:

### `ctaButtonStyling`

- Global schema uses it to render `subcomponents/CTAButton`, which under the hood renders `components/ComposableButton` and are conform to those properties and values.
- `BR` uses it to render `components/ComposableButtonBR` directly so it's conform to those variants, properties and values.

### `detailsPrefixFontColor` and `detailsLinkFontColor`

`AT`, `GAP` and `ON` use these properties as `string`, receiving color hexadecimals or values like:

```json
{
  "detailsPrefixFontColor": "#ABC123",
  "detailsLinkFontColor": "#ABC123"
},
```

`BR` is contraint to receive `primary` and `secondary` only, like:

```json
{
  "detailsPrefixFontColor": "secondary",
  "detailsLinkFontColor": "secondary"
},
```

This is important to mention to avoid forwarding it directly to CSS (although the type is still a `string`).

### Min Height

- In November 2024 for Athleta, we first adjusted the min height to 40px for carousel (2 or more banners) and 36px for single banner based on the brands request.
  Then the brand came back with a second enhancement request of making the minimum height of all Athleta SWBCs (multiple frames and a single frame) to be 32px on desktop and mobile.
  This change of Athleta SWBC having universal min height for all scenarios is in order to cover more edge cases for very small font size brand might use in the future.

## How to use it

\* This is a valid JSON for all brands but buttons aren't valid for `BR`, although this can't break `BR` variation due type guards.

```json
{
  "_meta": {
    "name": "Sitewide Banner Carousel [validation 4.25.22]",
    "schema": "https://cms.gap.com/schema/content/v1/sitewide-banner-carousel.json",
    "deliveryId": "aa5c7fee-7f09-4ae7-bea7-4d3b43c50e76"
  },
  "carouselBanners": [
    {
      "background": {
        "type": "solid",
        "color": "#00A9EB"
      },
      "carouselBannerStyling": {
        "ctaButtonStyling": {
          "buttonStyle": "chevron",
          "buttonColor": "light"
        },
        "desktopImageOrIconSize": "24px",
        "mobileImageOrIconSize": "14px",
        "desktopContentJustification": "center",
        "mobileContentJustification": "left"
      },
      "bannerLink": {
        "label": "gap.com",
        "value": "www.gap.com"
      },
      "cta1": {
        "label": "JOIN NOW",
        "value": "oldnavy.gap.com"
      },
      "cta2": {
        "label": "JOIN NOW",
        "value": "oldnavy.gap.com"
      },
      "mainRichText": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--subhead-3\" style=\"color:#FFFFFF\">Earning Points and Free Shipping on Orders of $50+</span></p>",
      "detailsLink": "Details",
      "detailsPrefix": "prefix",
      "mobileBackground": {
        "type": "image",
        "images": [
          {
            "image": {
              "_meta": {
                "schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"
              },
              "id": "62dc5230-f471-4073-b4e1-aff8d4ef03b4",
              "name": "SM222308_ISM_img_DESK",
              "endpoint": "gap",
              "defaultHost": "pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io"
            },
            "variations": [
              {
                "variation": "desktop"
              },
              {
                "variation": "mobile",
                "crop": {
                  "x": 36.00568199157715,
                  "y": 116.39772891998291,
                  "width": 201,
                  "height": 151,
                  "unit": "px"
                }
              }
            ],
            "fliph": false,
            "flipv": false,
            "enableChroma": false,
            "chromaQuality": 80
          }
        ]
      }
    },
    {
      "background": {
        "type": "image",
        "images": [
          {
            "image": {
              "_meta": {
                "schema": "http://bigcontent.io/cms/schema/v1/core#/definitions/image-link"
              },
              "id": "7cac9d7f-023e-46d3-83d9-111a15893961",
              "name": "SA834_Teen_ISM1b_image_1",
              "endpoint": "gap",
              "defaultHost": "pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io"
            },
            "variations": [
              {
                "variation": "desktop"
              },
              {
                "variation": "mobile",
                "crop": {
                  "x": 5.329597847165534,
                  "y": 1.203059775906751,
                  "width": 765.2728117913887,
                  "height": 250.50513484178003,
                  "unit": "px"
                }
              }
            ],
            "fliph": false,
            "flipv": false,
            "enableChroma": false,
            "chromaQuality": 80
          }
        ]
      },
      "carouselBannerStyling": {
        "ctaButtonStyling": {
          "buttonStyle": "border",
          "buttonColor": "dark"
        },
        "desktopImageOrIconSize": "24px",
        "mobileImageOrIconSize": "14px"
      },
      "mainRichText": "<p class=\"amp-cms--p\" style=\"text-align:left;\"><span class=\"amp-cms--body-1\" style=\"color:#FFFFFF\">Exclusivite en ligne justqu&#39;au rabais de 50%</span></p>",
      "bannerLink": {
        "label": "www.google.com",
        "value": "google"
      },
      "mobileBackground": {
        "type": "solid"
      },
      "cta1": {
        "label": "voir les details",
        "value": "www.gap.com"
      }
    }
  ],
  "carouselSettings": {
    "transition": "slide",
    "type": "clickThrough",
    "continuousLoop": false,
    "autoplay": {
      "delay": 3000,
      "pauseOnHover": false
    },
    "animation": {
      "speed": 500,
      "ease": false
    },
    "styling": {
      "controlsIconsColor": "primary",
      "pagination": "hide",
      "hideChevrons": false
    }
  },
  "webAppearance": {
    "showHideBasedOnScreenSize": "alwaysShow",
    "mobileLayout": "linear"
  }
}
```
