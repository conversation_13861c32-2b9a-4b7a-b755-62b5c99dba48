// @ts-nocheck
import { omit, pick } from 'lodash';
import { CMSMarketingCarouselProps } from '../../../subcomponents/CMSMarketingCarousel';
import { CtaProps } from '../../../subcomponents/CTAButton';
import { BackgroundTypeExtensionValue, MetaProps } from '../../../types/amplience';
import { CarouselBannerStylingProps, SitewideBannerCarouselContentType, SitewideBannerCarouselSlide, SitewideBannerCarouselWebAppearance } from '../types';
const athletaLogo = require('./assets/athleta-logo.svg').default?.src;
const backgroundImage = require('./assets/background-image.png').default?.src;

const meta: MetaProps<'https://cms.gap.com/schema/content/v1/sitewide-banner-carousel.json'> = {
  name: 'Sitewide Banner Carousel',
  schema: 'https://cms.gap.com/schema/content/v1/sitewide-banner-carousel.json',
  deliveryId: 'aa5c7fee-7f09-4ae7-bea7-4d3b43c50e76',
};

const background: BackgroundTypeExtensionValue = {
  type: 'solid',
  color: '#024A62',
};

const carouselBannerStyling: CarouselBannerStylingProps = {
  ctaButtonStyling: {
    buttonStyle: 'underline',
    buttonColor: 'white',
  },
  detailsLinkFontColor: '#FFFFFF',
  detailsPrefixFontColor: '#FFFFFF',
  desktopContentJustification: 'center',
  mobileContentJustification: 'center',
};

const cta1: CtaProps['ctaButton'] = {
  label: 'Shop Now',
  value: '#',
};

const cta2: CtaProps['ctaButton'] = {
  label: 'Shop Now2',
  value: '#',
};

const detailsPrefix = 'Ends 3/10.';
const detailsLink = '*Exclusions apply';

const carouselSettings: CMSMarketingCarouselProps['carouselSettings'] = {
  type: 'clickThrough',
  animation: {
    ease: true,
  },
  autoplay: {
    pauseOnHover: true,
  },
  continuousLoop: false,
  styling: {
    controlsIconsColor: 'primary',
    pagination: 'hide',
    hideChevrons: false,
  },
};

const webAppearanceStacked: SitewideBannerCarouselWebAppearance = {
  mobileLayout: 'stacked',
};

const webAppearanceLinear: SitewideBannerCarouselWebAppearance = {
  mobileLayout: 'linear',
};

const carouselBanners: SitewideBannerCarouselSlide[] = [
  {
    background,
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p>',
    cta1,
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsPrefix,
    detailsLink,
  },
  {
    background,
    desktopDetailsLayout: 'stacked',
    carouselBannerStyling: {
      ...carouselBannerStyling,
      desktopContentJustification: 'left',
    },
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    cta1,
    cta2,
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsPrefix,
    detailsLink,
  },
  {
    background,
    carouselBannerStyling: {
      ...carouselBannerStyling,
      mobileContentJustification: 'left',
    },
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsPrefix,
    detailsLink,
  },
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    imageIconOrLogo: [
      {
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        svgPath: athletaLogo,
      },
    ],
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    cta1,
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsPrefix,
    detailsLink,
  },
  {
    background: {
      type: 'image',
      images: [
        {
          variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
          svgPath: backgroundImage,
        },
      ],
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    cta1,
  },
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
      location: 'before',
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    cta1,
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsPrefix,
    detailsLink,
  },
];

const carouselSingleBanner: SitewideBannerCarouselSlide[] = [
  {
    background,
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p>',
    cta1,
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsLink: 'Details',
  },
];

export const clockCarouselBanners: SitewideBannerCarouselSlide[] = [
  // clock maincopy secondaryCopy cta1 detailsPrefix detailsLink (location: "before")
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
      location: 'before',
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    cta1,
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    detailsPrefix,
    detailsLink,
  },
  // maincopy clock secondaryCopy (location: "after")
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      location: 'after',
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
  },
  // maincopy clock secondaryCopy cta1 (location: "after")
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      location: 'after',
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    secondaryRichText:
      '<p class="amp-cms--p"><span class="amp-cms--body-2" style="color: #FFF">Code: <span style="color: #FFF; font-weight: 700">SHOPFIRST</span></span></p>',
    cta1,
  },
  // maincopy clock cta1 (location: "after")
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      location: 'after',
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
    cta1,
  },
  // clock maincopy (location: "before")
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      location: 'before',
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
  },
  // icon clock maincopy (location: "before")
  {
    background: {
      ...background,
      color: '#5A0528',
    },
    imageIconOrLogo: [
      {
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        svgPath: athletaLogo,
      },
    ],
    countdownEnabled: true,
    timer: {
      endDate: '2022-06-01T00:00:00',
      format: 'abbreviatedTimeFormat',
      hideDays: false,
      location: 'before',
      typography: {
        defaultTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
          clockCapitalized: true,
        },
        mobileOverrideTypography: {
          size: 'body2',
          weight: 'bold',
          color: '#FFFFFF',
        },
      },
    },
    carouselBannerStyling,
    mainRichText:
      '<p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--body-4" style="color: #FFF; text-transform: uppercase">This copy is in <span style="color: #F0FC5A">Small size</span></span></p><p class="amp-cms--p" style="text-align: center;"><span class="amp-cms--subhead-2" style="color: #FFF"></span></span></p>',
  },
];

const sitewideBannerCarouselChevronsBase: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners,
  carouselSettings,
};

const siteWideBannerCarouselAutoplayBase: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners,
  carouselSettings: {
    ...carouselSettings,
    type: 'autoplay',
  },
};

const siteWideSingleBannerCarouselAutoplayBase: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners: carouselSingleBanner,
  carouselSettings: {
    ...carouselSettings,
    type: 'autoplay',
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: true,
    },
  },
};

export const sitewideBannerCarouselMobileStackedWithChevrons: SitewideBannerCarouselContentType = {
  ...sitewideBannerCarouselChevronsBase,
  webAppearance: webAppearanceStacked,
};

export const sitewideBannerCarouselMobileLinearWithChevrons: SitewideBannerCarouselContentType = {
  ...sitewideBannerCarouselChevronsBase,
  webAppearance: webAppearanceLinear,
};

export const sitewideBannerCarouselMobileStackedWithAutoplay: SitewideBannerCarouselContentType = {
  ...siteWideBannerCarouselAutoplayBase,
  webAppearance: webAppearanceStacked,
};

export const sitewideBannerCarouselMobileLinearWithAutoplay: SitewideBannerCarouselContentType = {
  ...siteWideBannerCarouselAutoplayBase,
  webAppearance: webAppearanceLinear,
};

export const sitewideSingleBannerCarouselMobileStackedWithAutoplay: SitewideBannerCarouselContentType = {
  ...siteWideSingleBannerCarouselAutoplayBase,
  webAppearance: webAppearanceStacked,
};

export const sitewideSingleBannerCarouselMobileLinearWithAutoplay: SitewideBannerCarouselContentType = {
  ...siteWideSingleBannerCarouselAutoplayBase,
  webAppearance: webAppearanceLinear,
};

export const bannerLinkTestData: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners: [
    {
      background: { ...background, color: '#bfbfbf' },
      bannerLink: {
        label: 'Banana Republic',
        value: 'https://bananarepublic.gap.com/',
      },
      carouselBannerStyling: {
        ctaButtonStyling: {
          buttonColor: 'primary',
          buttonSize: 'large',
          buttonStyle: 'solid',
        },
        desktopContentJustification: 'center',
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
        mobileContentJustification: 'center',
      },
      countdownEnabled: false,
      desktopDetailsLayout: 'linear',
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Banner Link</span></p>',
      mobileBackground: { type: 'solid' },
      timer: {
        endDate: 'full',
        format: 'fullTimeFormat',
        hideDays: false,
        location: 'before',
        typography: {
          defaultTypography: {
            clockCapitalized: false,
            color: '000', // Not present in Amplience defaults
            size: 'body1',
            weight: 'default',
          },
          mobileOverrideTypography: {
            // clockCapitalized: false, // Present in Amplience defaults
            color: '000', // Not present in Amplience defaults
            size: 'body1',
            weight: 'default',
          },
        },
      },
    },
  ],
  carouselSettings: {
    ...carouselSettings,
    animation: { ease: false, speed: 500 },
    autoplay: { delay: 3000, pauseOnHover: false },
    transition: 'slide',
  },
  // experimentRunning: false, // Present in Amplience defaults
  // redpointExperimentRunning: false, // Present in Amplience defaults
  webAppearance: {
    ...webAppearanceLinear,
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};

// #region Backgrounds
export const backgroundSolidTestData: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners: [
    {
      background: { ...background, color: '#bfbfbf' },
      ...pick(bannerLinkTestData.carouselBanners[0], ['carouselBannerStyling', 'countdownEnabled', 'desktopDetailsLayout', 'mobileBackground', 'timer']),
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Background Solid</span></p>',
    },
  ],
  ...pick(bannerLinkTestData, ['carouselSettings', 'webAppearance']),
  // experimentRunning, redpointExperimentRunning...
};
export const backgroundSolidWithMobileOverrideArgs: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      background: { color: '#0466CA', type: 'solid' },
      mainRichText:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Color switch between mobile/desktop</span></p>',
      mobileBackground: { color: '#5CABF7', type: 'solid' },
    },
  ],
};
export const backgroundImageArgs: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners: [
    {
      background: {
        type: 'image',
        images: [
          {
            altText: 'A person',
            chromaQuality: 80,
            enableChroma: false,
            fliph: false,
            flipv: false,
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
              endpoint: 'bananarepublic',
              id: '0df9d11d-1cf0-4b19-b6a7-6971d35796da',
              mimeType: 'image/jpeg',
              name: 'BRFS240607_SITE_US_HP_06_IMG_XL',
            },
            variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
          },
        ],
      },
      ...pick(bannerLinkTestData.carouselBanners[0], ['carouselBannerStyling', 'countdownEnabled', 'desktopDetailsLayout', 'mobileBackground', 'timer']),
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Background Image</span></p>',
    },
  ],
  ...pick(bannerLinkTestData, ['carouselSettings', 'webAppearance']),
};
export const backgroundImageWithMobileOverrideArgs: SitewideBannerCarouselContentType = {
  ...backgroundImageArgs,
  carouselBanners: [
    {
      ...backgroundImageArgs.carouselBanners[0],
      mainRichText:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Image switches between mobile/desktop</span></p>',
      mobileBackground: {
        type: 'image',
        images: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '1869b97b-749a-4591-ad3d-712fb9cec08b',
              name: 'BRSP240529_SITE_USCA_HP_01_IMG_XL',
              endpoint: 'bananarepublic',
              defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
              mimeType: 'image/jpeg',
            },
            altText: 'A woman with a necklace in a chair',
            variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
    },
  ],
};
// #endregion

// #region Icons
export const icons1xTestData: SitewideBannerCarouselContentType = {
  _meta: meta,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      background: { ...background, color: '#666' },
      carouselBannerStyling: {
        ...backgroundSolidTestData.carouselBanners[0].carouselBannerStyling,
        desktopImageOrIconSize: '24px',
        mobileImageOrIconSize: '14px',
      },
      imageIconOrLogo: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '4a4148d5-db16-471f-a6b5-58db36d83a7a',
            name: 'BRWhiteDesktopLogo',
            endpoint: 'bananarepublic',
            defaultHost: '1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io',
            mimeType: 'image/png',
          },
          altText: 'BR logo',
          variations: [{ variation: 'desktop' }, { variation: 'mobile' }],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Icons Size 1x</span></p>',
    },
  ],
  ...pick(backgroundSolidTestData, ['carouselSettings', 'webAppearance']),
  // experimentRunning, redpointExperimentRunning...
};

export const icons2xTestData: SitewideBannerCarouselContentType = {
  ...icons1xTestData,
  carouselBanners: [
    {
      ...icons1xTestData.carouselBanners[0],
      carouselBannerStyling: {
        ...icons1xTestData.carouselBanners[0].carouselBannerStyling,
        desktopImageOrIconSize: '32px',
        mobileImageOrIconSize: '24px',
      },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Icons Size 2x</span></p>',
    },
  ],
};

export const icons3xTestData: SitewideBannerCarouselContentType = {
  ...icons1xTestData,
  carouselBanners: [
    {
      ...icons1xTestData.carouselBanners[0],
      carouselBannerStyling: {
        ...icons1xTestData.carouselBanners[0].carouselBannerStyling,
        desktopImageOrIconSize: '48px',
        mobileImageOrIconSize: '32px',
      },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Icons Size 3x</span></p>',
    },
  ],
};

export const icons4xTestData: SitewideBannerCarouselContentType = {
  ...icons1xTestData,
  carouselBanners: [
    {
      ...icons1xTestData.carouselBanners[0],
      carouselBannerStyling: {
        ...icons1xTestData.carouselBanners[0].carouselBannerStyling,
        desktopImageOrIconSize: '64px',
        mobileImageOrIconSize: '48px',
      },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Icons Size 4x</span></p>',
    },
  ],
};

export const icons5xTestData: SitewideBannerCarouselContentType = {
  ...icons1xTestData,
  carouselBanners: [
    {
      ...icons1xTestData.carouselBanners[0],
      carouselBannerStyling: {
        ...icons1xTestData.carouselBanners[0].carouselBannerStyling,
        desktopImageOrIconSize: '105px',
        mobileImageOrIconSize: '80px',
      },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Icons Size 5x</span></p>',
    },
  ],
};
// #endregion

export const cta1TestData: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      cta1: { label: 'CTA Button 1', value: 'https://bananarepublic.gap.com/' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">CTA 1</span></p>',
    },
  ],
};

export const cta2TestData: SitewideBannerCarouselContentType = {
  ...cta1TestData,
  carouselBanners: [
    {
      ...cta1TestData.carouselBanners[0],
      cta2: { label: 'CTA Button 2', value: 'https://www.gap.com/' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">CTA 2</span></p>',
    },
  ],
};

export const ctasBetweenTextareas: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Main Text Area</span></p>',
      secondaryRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Text Area 2</span></p>',
    },
  ],
};

// #region Arrow CTAs
export const ctasArrowPrimary: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: { buttonStyle: 'arrow', buttonColor: 'primary' },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasArrowSecondary: SitewideBannerCarouselContentType = {
  ...ctasArrowPrimary,
  carouselBanners: [
    {
      ...ctasArrowPrimary.carouselBanners[0],
      carouselBannerStyling: {
        ...ctasArrowPrimary.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: { buttonStyle: 'arrow', buttonColor: 'secondary' },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
// #endregion

// #region Outline CTAs
export const ctasOutlinePrimaryLarge: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'outline',
          buttonColor: 'primary',
          buttonSize: 'large',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasOutlinePrimarySmall: SitewideBannerCarouselContentType = {
  ...ctasOutlinePrimaryLarge,
  carouselBanners: [
    {
      ...ctasOutlinePrimaryLarge.carouselBanners[0],
      carouselBannerStyling: {
        ...ctasOutlinePrimaryLarge.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'outline',
          buttonColor: 'primary',
          buttonSize: 'small',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasOutlineSecondaryLarge: SitewideBannerCarouselContentType = {
  ...ctasOutlinePrimaryLarge,
  carouselBanners: [
    {
      ...ctasOutlinePrimaryLarge.carouselBanners[0],
      carouselBannerStyling: {
        ...ctasOutlinePrimaryLarge.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'outline',
          buttonColor: 'secondary',
          buttonSize: 'large',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasOutlineSecondarySmall: SitewideBannerCarouselContentType = {
  ...ctasOutlineSecondaryLarge,
  carouselBanners: [
    {
      ...ctasOutlineSecondaryLarge.carouselBanners[0],
      carouselBannerStyling: {
        ...ctasOutlineSecondaryLarge.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'outline',
          buttonColor: 'secondary',
          buttonSize: 'small',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
// #endregion

// #region Solid CTAs
export const ctasSolidPrimaryLarge: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'solid',
          buttonColor: 'primary',
          buttonSize: 'large',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasSolidPrimarySmall: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'solid',
          buttonColor: 'primary',
          buttonSize: 'small',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasSolidSecondaryLarge: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'solid',
          buttonColor: 'secondary',
          buttonSize: 'large',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
export const ctasSolidSecondarySmall: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: {
          buttonStyle: 'solid',
          buttonColor: 'secondary',
          buttonSize: 'small',
        },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
// #endregion

// #region Underline CTAs
export const ctasUnderlinePrimary: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: { buttonStyle: 'underline', buttonColor: 'primary' },
      },
    },
  ],
};
export const ctasUnderlineSecondary: SitewideBannerCarouselContentType = {
  ...cta2TestData,
  carouselBanners: [
    {
      ...cta2TestData.carouselBanners[0],
      carouselBannerStyling: {
        ...cta2TestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: { buttonStyle: 'underline', buttonColor: 'secondary' },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
};
// #endregion

export const textareaScenario1: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      carouselBannerStyling: {
        ...icons1xTestData.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: { buttonSize: 'small' },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
      cta1: { label: 'CTA Button 1', value: 'https://bananarepublic.gap.com/' },
      cta2: { label: 'CTA Button 2', value: 'https://www.gap.com/' },
      imageIconOrLogo: icons1xTestData.carouselBanners[0].imageIconOrLogo,
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Main text area</span></p>',
      mobileRichTextArea1: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Mobile Main Text Area</span></p>',
      mobileRichTextArea2:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Mobile Secondary Text Area</span></p>',
      secondaryRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Text area 2</span></p>',
    },
  ],
};

// #region Details Link
export const detailsLinkArgs: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      detailsLink: '*Details Link',
      detailsPrefix: 'Prefix',
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Main text area</span></p>',
    },
  ],
};
export const detailsLinkPemoleCodeArgs: SitewideBannerCarouselContentType = {
  ...detailsLinkArgs,
  carouselBanners: [
    {
      ...detailsLinkArgs.carouselBanners[0],
      pemoleCode: '773885', // should show a text with heading "40% Off + Extra 10% Off at Gap"
    },
  ],
};
export const detailsLinkHTMLModalURLArgs: SitewideBannerCarouselContentType = {
  ...detailsLinkArgs,
  carouselBanners: [
    {
      ...detailsLinkArgs.carouselBanners[0],
      pemoleCode: '123456', // invalid pemoleCode
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
    },
  ],
};
export const detailsLinkSpacedAsteriskArgs: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      detailsLink: '* Details Link',
      detailsPrefix: 'Prefix',
      mainRichText: detailsLinkArgs.carouselBanners[0].mainRichText,
    },
  ],
};
export const detailsLinkSecondaryColorArgs: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      carouselBannerStyling: {
        ...backgroundSolidTestData.carouselBanners[0].carouselBannerStyling,
        detailsLinkFontColor: 'secondary',
        detailsPrefixFontColor: 'secondary',
      },
      detailsLink: '*Details Link',
      detailsPrefix: 'Prefix',
      mainRichText: detailsLinkArgs.carouselBanners[0].mainRichText,
    },
  ],
};
export const detailsLinkPrefixOnlyArgs: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      detailsPrefix: 'Details Prefix',
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Details Prefix can\'t be shown ✅</span></p>',
    },
  ],
};
export const desktopDetailsLayoutNoneArgs: SitewideBannerCarouselContentType = {
  ...detailsLinkArgs,
  carouselBanners: [{ ...omit(detailsLinkArgs.carouselBanners[0], 'desktopDetailsLayout') }],
};
export const desktopDetailsLayoutLinearArgs: SitewideBannerCarouselContentType = {
  ...detailsLinkArgs,
  carouselBanners: [{ ...detailsLinkArgs.carouselBanners[0], desktopDetailsLayout: 'linear' }],
};
export const desktopDetailsLayoutStackedArgs: SitewideBannerCarouselContentType = {
  ...detailsLinkArgs,
  carouselBanners: [{ ...detailsLinkArgs.carouselBanners[0], desktopDetailsLayout: 'stacked' }],
};
// #endregion

// #region Carousel Quantity
export const carouselTwoBannersArgs: SitewideBannerCarouselContentType = {
  ...backgroundSolidTestData,
  carouselBanners: [
    {
      ...backgroundSolidTestData.carouselBanners[0],
      background: { color: '#0466CA', type: 'solid' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Banner 1</span></p>',
    },
    {
      ...backgroundSolidTestData.carouselBanners[0],
      background: { color: '#D00000', type: 'solid' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Banner 2</span></p>',
    },
  ],
};
export const carouselFiveBannersArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselBanners: [
    { ...carouselTwoBannersArgs.carouselBanners[0] },
    { ...carouselTwoBannersArgs.carouselBanners[1] },
    {
      ...carouselTwoBannersArgs.carouselBanners[0],
      background: { color: '#3AB200', type: 'solid' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Banner 3</span></p>',
    },
    {
      ...carouselTwoBannersArgs.carouselBanners[0],
      background: { color: '#DBA61D', type: 'solid' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Banner 4</span></p>',
    },
    {
      ...carouselTwoBannersArgs.carouselBanners[0],
      background: { color: '#FF7807', type: 'solid' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Banner 5</span></p>',
    },
  ],
};
export const carouselSixBannersArgs: SitewideBannerCarouselContentType = {
  ...carouselFiveBannersArgs,
  carouselBanners: [
    { ...carouselFiveBannersArgs.carouselBanners[0] },
    { ...carouselFiveBannersArgs.carouselBanners[1] },
    { ...carouselFiveBannersArgs.carouselBanners[2] },
    { ...carouselFiveBannersArgs.carouselBanners[3] },
    { ...carouselFiveBannersArgs.carouselBanners[4] },
    {
      ...carouselTwoBannersArgs.carouselBanners[0],
      background: { color: '#808080', type: 'solid' },
      mainRichText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1" style="color:#FFF">Banner 6</span></p>',
    },
  ],
};
// #endregion

// #region Carousel Transitions
export const transitionFade: SitewideBannerCarouselContentType = {
  ...carouselFiveBannersArgs,
  carouselSettings: {
    ...carouselFiveBannersArgs.carouselSettings,
    transition: 'fade',
  },
};
export const transitionSlide: SitewideBannerCarouselContentType = {
  ...carouselFiveBannersArgs,
  carouselSettings: {
    ...carouselFiveBannersArgs.carouselSettings,
    transition: 'slide',
  },
};
// #endregion

// #region Carousel Types
export const autoplay: SitewideBannerCarouselContentType = {
  ...carouselFiveBannersArgs,
  carouselSettings: {
    ...carouselFiveBannersArgs.carouselSettings,
    type: 'autoplay',
    hidePlayPause: false
  },
};
export const clickThrough: SitewideBannerCarouselContentType = {
  ...carouselFiveBannersArgs,
  carouselSettings: {
    ...carouselFiveBannersArgs.carouselSettings,
    type: 'clickThrough',
  },
};
// #endregion

// #region Carousel Autoplay Settings
export const continuousLoopArgs: SitewideBannerCarouselContentType = {
  ...autoplay,
  carouselSettings: {
    ...autoplay.carouselSettings,
    autoplay: { delay: 500, pauseOnHover: false },
    continuousLoop: true,
  },
};
export const customDelayArgs: SitewideBannerCarouselContentType = {
  ...autoplay,
  carouselSettings: {
    ...autoplay.carouselSettings,
    autoplay: { delay: 0, pauseOnHover: false },
  },
};
export const customSpeedArgs: SitewideBannerCarouselContentType = {
  ...autoplay,
  carouselSettings: {
    ...autoplay.carouselSettings,
    animation: { ease: false, speed: 2000 },
    autoplay: { delay: 500, pauseOnHover: false },
    continuousLoop: true,
  },
};
export const easeArgs: SitewideBannerCarouselContentType = {
  ...autoplay,
  carouselSettings: {
    ...autoplay.carouselSettings,
    animation: { ease: true, speed: 1000 },
    autoplay: { delay: 500, pauseOnHover: false },
    continuousLoop: true,
  },
};
export const pauseOnHoverArgs: SitewideBannerCarouselContentType = {
  ...autoplay,
  carouselSettings: {
    ...autoplay.carouselSettings,
    autoplay: { delay: 500, pauseOnHover: true },
    continuousLoop: true,
  },
};
// #endregion

// #region Styling > Controls Icons Color
export const controlsIconsColorPrimaryArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselSettings: {
    ...carouselTwoBannersArgs.carouselSettings,
    styling: {
      ...carouselTwoBannersArgs.carouselSettings.styling,
      controlsIconsColor: 'primary',
    },
  },
};
export const controlsIconsColorSecondaryArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselSettings: {
    ...carouselTwoBannersArgs.carouselSettings,
    styling: {
      ...carouselTwoBannersArgs.carouselSettings.styling,
      controlsIconsColor: 'secondary',
    },
  },
};
// #endregion

// #region Styling > Pagination
export const paginationHideArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselSettings: {
    ...carouselTwoBannersArgs.carouselSettings,
    styling: {
      ...carouselTwoBannersArgs.carouselSettings.styling,
      pagination: 'hide',
    },
  },
};
export const paginationDesktopArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselSettings: {
    ...carouselTwoBannersArgs.carouselSettings,
    styling: {
      ...carouselTwoBannersArgs.carouselSettings.styling,
      pagination: 'desktop',
    },
  },
};
export const paginationMobileArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselSettings: {
    ...carouselTwoBannersArgs.carouselSettings,
    styling: {
      ...carouselTwoBannersArgs.carouselSettings.styling,
      pagination: 'mobile',
    },
  },
};
export const paginationDesktopAndMobileArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  carouselSettings: {
    ...carouselTwoBannersArgs.carouselSettings,
    styling: {
      ...carouselTwoBannersArgs.carouselSettings.styling,
      pagination: 'desktopAndMobile',
    },
  },
};
// #endregion

// #region Styling > Hide Chevrons
export const hideChevronsArgs: SitewideBannerCarouselContentType = {
  ...paginationDesktopAndMobileArgs,
  carouselSettings: {
    ...paginationDesktopAndMobileArgs.carouselSettings,
    styling: {
      ...paginationDesktopAndMobileArgs.carouselSettings.styling,
      hideChevrons: true,
    },
  },
};
// #endregion

// #region Appearance > Show/Hide Based on Screen Size
export const alwaysShowArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  webAppearance: {
    ...webAppearanceLinear,
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};
export const hideOnMobileArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  webAppearance: {
    ...webAppearanceLinear,
    showHideBasedOnScreenSize: 'hideOnMobile',
  },
};
export const hideOnDesktopArgs: SitewideBannerCarouselContentType = {
  ...carouselTwoBannersArgs,
  webAppearance: {
    ...webAppearanceLinear,
    showHideBasedOnScreenSize: 'hideOnDesktop',
  },
};
// #endregion

// #region Appearance > Mobile Layout
export const mobileLayoutNoneArgs: SitewideBannerCarouselContentType = {
  ...ctasBetweenTextareas,
  carouselBanners: [
    {
      ...ctasBetweenTextareas.carouselBanners[0],
      carouselBannerStyling: {
        ...ctasBetweenTextareas.carouselBanners[0].carouselBannerStyling,
        ctaButtonStyling: { buttonSize: 'small' },
        detailsLinkFontColor: 'primary',
        detailsPrefixFontColor: 'primary',
      },
    },
  ],
  webAppearance: { showHideBasedOnScreenSize: 'alwaysShow' },
};
export const mobileLayoutLinearArgs: SitewideBannerCarouselContentType = {
  ...mobileLayoutNoneArgs,
  webAppearance: {
    ...mobileLayoutNoneArgs.webAppearance,
    ...webAppearanceLinear,
  },
};
export const mobileLayoutStackedArgs: SitewideBannerCarouselContentType = {
  ...mobileLayoutNoneArgs,
  webAppearance: {
    ...mobileLayoutNoneArgs.webAppearance,
    ...webAppearanceStacked,
  },
};
// #endregion
