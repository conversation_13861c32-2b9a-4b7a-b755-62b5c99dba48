// @ts-nocheck
import React from 'react';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import { render, screen, act } from 'test-utils';
import { LARGE, Size, SMALL } from '@ecom-next/core/breakpoint-provider';
import { Brands } from '@ecom-next/core/react-stitch';
import {
  countdownBanner,
  sitewideSingleBannerCarouselBasicData,
  sitewideBannerContentJustification,
  sitewideBannerCarouselBasicData,
  carouselSettingsAutoplay,
  carouselSettingsHideChevrons,
} from '../__fixtures__/test-data';
import { LocalizationTestWrapper } from '../../../subcomponents/LocalizationTestWrapper';
import { CAROUSEL_ARROW_SIZE } from './constants';
import { clockCarouselBanners } from '../__fixtures__/story-data';
import { GapSitewideBannerCarousel, SitewideBannerCarouselProps } from '.';

describe('SitewideBannerCarousel - Gap', () => {
  const renderComponent = (props?: Partial<SitewideBannerCarouselProps>, breakpoint: Size = LARGE) =>
    render(
      <LocalizationTestWrapper>
        <GapSitewideBannerCarousel {...sitewideBannerCarouselBasicData} {...props} />
      </LocalizationTestWrapper>,
      {
        breakpoint,
        appState: {
          brandName: Brands.Gap,
        } as AppState,
      }
    );

  const renderSingleBannerComponent = (props?: Partial<SitewideBannerCarouselProps>, breakpoint: Size = LARGE) =>
    render(
      <LocalizationTestWrapper>
        <GapSitewideBannerCarousel {...sitewideSingleBannerCarouselBasicData} {...props} />
      </LocalizationTestWrapper>,
      {
        breakpoint,
        appState: {
          brandName: Brands.Gap,
        } as AppState,
      }
    );

  it('should match snapshot on desktop', () => {
    const result = renderComponent();
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('should match snapshot on mobile', () => {
    const result = renderComponent({}, SMALL);
    expect(result.asFragment()).toMatchSnapshot();
  });

  describe('Carousel controls', () => {
    it('should match snapshot when on autoplay', () => {
      const result = renderComponent({
        carouselSettings: carouselSettingsAutoplay,
      });
      expect(result.asFragment()).toMatchSnapshot();
    });

    it('should show play/pause button and hide arrows when on autoplay', () => {
      renderComponent({
        carouselSettings: carouselSettingsAutoplay,
      });
      expect(screen.getByLabelText('pause')).toBeInTheDocument();
      expect(screen.queryByLabelText('Next')).not.toBeInTheDocument();
    });

    it('should show arrows and hide play/pause buttons when on clickThrough', () => {
      renderComponent();
      expect(screen.getByLabelText('Next')).toBeInTheDocument();
      expect(screen.queryByLabelText('pause')).not.toBeInTheDocument();
    });

    it('should hide chevrons', () => {
      renderComponent({
        carouselSettings: carouselSettingsHideChevrons,
      });
      expect(screen.queryByLabelText('Next')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('pause')).not.toBeInTheDocument();
    });

    it('wrapper should have margin the size of arrows on hideChevrons false', () => {
      const { container } = renderComponent();
      expect(container.querySelector('.sitewide-banner-carousel-wrapper')).toHaveStyleRule('margin-inline', `${CAROUSEL_ARROW_SIZE}px`);
    });

    it('wrapper should not have margin the size of arrows on hideChevrons true', () => {
      const { container } = renderComponent({
        carouselSettings: carouselSettingsHideChevrons,
      });
      expect(container.querySelector('.sitewide-banner-carousel-wrapper')).toHaveStyleRule('margin-inline', '0');
    });

    it('wrapper should not have margin the size of arrows on hideChevrons true with a single banner set up', () => {
      const { container } = renderSingleBannerComponent({
        ...sitewideBannerCarouselBasicData.carouselBanners[0],
        carouselSettings: carouselSettingsAutoplay,
      });

      expect(container.querySelector('.sitewide-banner-carousel-wrapper')).toHaveStyleRule('margin-inline', '0');
    });

    it('wrapper should not have margin the size of arrows on autoplay', () => {
      const { container } = renderComponent({
        carouselSettings: carouselSettingsAutoplay,
      });
      expect(container.querySelector('.sitewide-banner-carousel-wrapper')).toHaveStyleRule('margin-inline', `0 ${CAROUSEL_ARROW_SIZE}px`);
    });
  });

  describe('Countdown timer', () => {
    it('should render the countdown timer when countdownEnabled is true and timer is provided', () => {
      renderComponent({
        carouselBanners: [countdownBanner],
      });

      const countdownElement = screen.getByTestId('timer');
      expect(countdownElement).toBeInTheDocument();
    });

    it('should not render the countdown timer when countdownEnabled is false', () => {
      const noCountdownBanner = {
        ...sitewideBannerCarouselBasicData.carouselBanners[0],
        countdownEnabled: false,
      };
      renderComponent({
        carouselBanners: [noCountdownBanner],
      });

      const countdownElement = screen.queryByTestId('timer');
      expect(countdownElement).not.toBeInTheDocument();
    });

    it('should render the countdown timer with correct margin when location of timer is before', () => {
      const { container } = renderComponent({
        carouselBanners: [clockCarouselBanners[0]],
      });
      expect(container).toMatchSnapshot();
    });

    it('should render the countdown timer with correct margin when location of timer is after', () => {
      const { container } = renderComponent({
        carouselBanners: [clockCarouselBanners[1]],
      });
      expect(container).toMatchSnapshot();
    });
  });

  describe('content justification', () => {
    it('should align content to center by default', () => {
      const { container } = renderComponent({
        carouselBanners: [sitewideBannerContentJustification(undefined, undefined)],
      });

      const wrapperElement = container.querySelector('.sitewide-banner-carousel-wrapper')?.firstChild;
      expect(wrapperElement).toHaveStyleRule('justify-content', 'center');
    });

    it('should align content to center for desktop', () => {
      const { container } = renderComponent({
        carouselBanners: [sitewideBannerContentJustification('center', undefined)],
      });

      const wrapperElement = container.querySelector('.sitewide-banner-carousel-wrapper')?.firstChild;
      expect(wrapperElement).toHaveStyleRule('justify-content', 'center');
    });

    it('should align content to center for mobile', () => {
      const { container } = renderComponent(
        {
          carouselBanners: [sitewideBannerContentJustification(undefined, 'center')],
        },
        SMALL
      );

      const wrapperElement = container.querySelector('.sitewide-banner-carousel-wrapper')?.firstChild;
      expect(wrapperElement).toHaveStyleRule('justify-content', 'center');
    });

    it('should align content to left for desktop', () => {
      const { container } = renderComponent({
        carouselBanners: [sitewideBannerContentJustification('left', undefined)],
      });

      const wrapperElement = container.querySelector('.sitewide-banner-carousel-wrapper')?.firstChild;
      expect(wrapperElement).toHaveStyleRule('justify-content', 'left');
    });

    it('should align content to left for mobile', () => {
      const { container } = renderComponent(
        {
          carouselBanners: [sitewideBannerContentJustification(undefined, 'left')],
        },
        SMALL
      );

      const wrapperElement = container.querySelector('.sitewide-banner-carousel-wrapper')?.firstChild;
      expect(wrapperElement).toHaveStyleRule('justify-content', 'left');
    });
  });
});
