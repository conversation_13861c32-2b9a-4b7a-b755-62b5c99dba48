// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FlexibleSpacer Snapshot tests props: {"largeVP": false, "stacked": false} 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  />
</DocumentFragment>
`;

exports[`FlexibleSpacer Snapshot tests props: {"largeVP": false, "stacked": true} 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

<div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
    />
  </div>
</DocumentFragment>
`;

exports[`FlexibleSpacer Snapshot tests props: {"largeVP": true, "stacked": false} 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  />
</DocumentFragment>
`;

exports[`FlexibleSpacer Snapshot tests props: {"largeVP": true, "stacked": true} 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  />
</DocumentFragment>
`;
