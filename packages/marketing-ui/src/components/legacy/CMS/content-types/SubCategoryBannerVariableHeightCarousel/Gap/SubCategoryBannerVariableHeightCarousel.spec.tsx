// @ts-nocheck
import React from 'react';
import { render, screen, act } from 'test-utils';
import { Size, SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { LocalizationTestWrapper } from '../../../subcomponents/LocalizationTestWrapper';
import { SubCategoryBannerVHCarouselProps } from '../../../components/SubCategoryBannerVHCarousel';
import { SubCategoryBannerVHCarouselPropsGap } from '../types';
import { scbvhCarouselBaseData } from '../__fixtures__/test-data';
import SubCategoryBannerVariableHeightCarousel from './SubCategoryBannerVariableHeightCarousel.gap';
import { StaticTextAndCta } from '../../../components/SubCategoryBannerVHCarousel/components/StaticTextAndCta';
import { SubCategoryBannerVHCarouselItem } from './SubCategoryBannerVHCarouselItem.gap';
import { CMSMarketingCarousel } from '../../../subcomponents/CMSMarketingCarousel';
import AspectRatioContainer from '../../../subcomponents/AspectRatioContainer';

jest.mock('../../../components/SubCategoryBannerVHCarousel/components/StaticTextAndCta', () => ({
  __esModule: true,
  StaticTextAndCta: jest.fn(jest.requireActual('../../../components/SubCategoryBannerVHCarousel/components/StaticTextAndCta').StaticTextAndCta),
}));

jest.mock('./SubCategoryBannerVHCarouselItem.gap', () => ({
  __esModule: true,
  SubCategoryBannerVHCarouselItem: jest.fn(jest.requireActual('./SubCategoryBannerVHCarouselItem.gap').SubCategoryBannerVHCarouselItem),
}));

jest.mock('../../../subcomponents/CMSMarketingCarousel', () => ({
  __esModule: true,
  CMSMarketingCarousel: jest.fn(jest.requireActual('../../../subcomponents/CMSMarketingCarousel').CMSMarketingCarousel),
}));

const getLastCalledWith = (fn: Function): any => (fn as unknown as jest.Mock).mock.calls.pop()[0];
const MockAspectRatioContainer = jest.fn();

const defaultProps: SubCategoryBannerVHCarouselPropsGap = scbvhCarouselBaseData;

const renderComponent = (props?: Partial<SubCategoryBannerVHCarouselPropsGap>, breakpoint: Size = XLARGE, brand: Brands = Brands.Gap) =>
  render(
    <LocalizationTestWrapper>
      <StitchStyleProvider brand={brand}>
        <SubCategoryBannerVariableHeightCarousel {...defaultProps} {...props} />
      </StitchStyleProvider>
    </LocalizationTestWrapper>,
    {
      breakpoint,
    }
  );

describe('SubCategoryBannerVariableHeightCarousel', () => {
  afterEach(() => {
    StaticTextAndCta as unknown as jest.Mock;
    SubCategoryBannerVHCarouselItem as unknown as jest.Mock;
    CMSMarketingCarousel as unknown as jest.Mock;
    jest.clearAllMocks();
  });

  it('should match snapshot', () => {
    const { container } = renderComponent();
    expect(container).toMatchSnapshot();
  });

  it('should render persistent below on mobile snapshot', () => {
    const { asFragment } = renderComponent({ contentConfiguration: 'persistent', mobileTextTreatment: 'below' }, SMALL);
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render persistent && below, play and pagination on mobile', () => {
    const { asFragment, container } = renderComponent(
      {
        contentConfiguration: 'persistent',
        mobileTextTreatment: 'below',
        carouselSettings: {
          transition: 'slide',
          type: 'autoplay',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },

          styling: {
            controlsIconsColor: 'primary',
            pagination: 'desktopAndMobile',
            hideChevrons: false,
          },
        },
      },
      SMALL
    );
    const playPauseButton = container.firstChild?.firstChild?.firstChild?.firstChild?.firstChild?.firstChild.firstChild.firstChild as HTMLElement;

    expect(asFragment()).toMatchSnapshot();
    expect(playPauseButton).toHaveStyleRules({
      width: '44px',
      height: '44px',
    });
  });

  it('should render content if frames length between 2 and 6', () => {
    renderComponent();
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('should not render carousel if frames length is out of range', () => {
    const { container } = renderComponent({ frames: [] });
    expect(container.textContent).toBe('');
  });

  describe('SCBVHCarouselWrapper', () => {
    describe('StaticFile', () => {
      it('should not render component if not persistent', () => {
        renderComponent({ contentConfiguration: 'unique' });
        expect(StaticTextAndCta).not.toHaveBeenCalled();
      });

      it('should render component if persistent', () => {
        renderComponent({
          contentConfiguration: 'persistent',
        });
        expect(StaticTextAndCta).toHaveBeenCalled();
      });
    });

    describe('CMSMarketingCarousel', () => {
      describe('SubCategoryBannerVHCarouselItem', () => {
        describe('on desktop', () => {
          it('should render component per frame', () => {
            renderComponent();
            expect(SubCategoryBannerVHCarouselItem).toHaveBeenCalledTimes(defaultProps.frames.length);
          });
        });
      });
    });
  });
});

describe('SubCategoryBannerVHCarousel Pagination', () => {
  it('should render pagination on the right when desktop', () => {
    const { container, asFragment } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'primary',
            pagination: 'desktop',
            hideChevrons: false,
          },
        },
      },
      XLARGE
    );

    const pagination = container.firstChild.firstChild.firstChild as HTMLElement;

    expect(pagination).toHaveStyleRule('text-align', 'right', {
      target: 'ul.slick-dots',
    });

    expect(pagination).toHaveStyleRule('bottom', '0', {
      target: 'ul.slick-dots',
    });

    expect(pagination).toHaveStyleRule('margin-bottom', '20px', {
      target: 'ul.slick-dots',
    });
    expect(asFragment()).toMatchSnapshot();
  });
  it('should render pagination center on mobile', () => {
    const { container } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'primary',
            pagination: 'mobile',
            hideChevrons: false,
          },
        },
      },
      SMALL
    );

    const pagination = container.firstChild.firstChild as HTMLElement;

    expect(pagination).not.toHaveStyleRule('text-align', 'right');

    expect(pagination).not.toHaveStyleRule('bottom', '0');

    expect(pagination).not.toHaveStyleRule('padding-bottom', '5px');
  });

  it('should render secondary color for carousel desktop', () => {
    const { asFragment } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'secondary',
            pagination: 'desktop',
            hideChevrons: false,
          },
        },
      },
      XLARGE
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('should render secondary color for carousel mobile', () => {
    const { asFragment } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'secondary',
            pagination: 'desktop',
            hideChevrons: false,
          },
        },
      },
      SMALL
    );

    expect(asFragment()).toMatchSnapshot();
  });
});

describe('return null if showCarouselViewPort not match size', () => {
  it('should not render on mobile', () => {
    const { container } = renderComponent(
      {
        webAppearance: {
          showHideBasedOnScreenSize: 'hideOnMobile',
        },
      },
      SMALL
    );

    expect(container.firstChild.firstChild.firstChild).not.toBeInTheDocument();
  });
  it('should not render on Desktop', () => {
    const { container } = renderComponent(
      {
        webAppearance: {
          showHideBasedOnScreenSize: 'hideOnDesktop',
        },
      },
      XLARGE
    );

    expect(container.firstChild.firstChild.firstChild).not.toBeInTheDocument();
  });
});

describe('banner size options', () => {
  describe('mobile', () => {
    it('should render banner size small on mobile', async () => {
      await act(async () => {
        renderComponent(
          {
            mobileBannerSize: 'small',
          },
          SMALL
        );
      });

      const imageContainer = screen.getAllByTestId('aspect-ratio-container')[0];
      expect(imageContainer.getAttribute('height')).toBe('100');
    });
    it('should render banner size medium on mobile', async () => {
      await act(async () => {
        renderComponent(
          {
            mobileBannerSize: 'medium',
          },
          SMALL
        );
      });

      const imageContainer = screen.getAllByTestId('aspect-ratio-container')[0];
      expect(imageContainer.getAttribute('height')).toBe('160');
    });
    it('should render banner size large on mobile', async () => {
      await act(async () => {
        renderComponent(
          {
            mobileBannerSize: 'large',
          },
          SMALL
        );
      });

      const imageContainer = screen.getAllByTestId('aspect-ratio-container')[0];
      expect(imageContainer.getAttribute('height')).toBe('200');
    });
  });

  describe('desktop', () => {
    it('should render banner size small on desktop', async () => {
      await act(async () => {
        renderComponent(
          {
            desktopBannerSize: 'small',
          },
          XLARGE
        );
      });

      const imageContainer = screen.getAllByTestId('aspect-ratio-container')[0];
      expect(imageContainer.getAttribute('height')).toBe('115');
    });
    it('should render banner size medium on desktop', async () => {
      await act(async () => {
        renderComponent(
          {
            desktopBannerSize: 'medium',
          },
          XLARGE
        );
      });

      const imageContainer = screen.getAllByTestId('aspect-ratio-container')[0];
      expect(imageContainer.getAttribute('height')).toBe('275');
    });
    it('should render banner size large on desktop', async () => {
      await act(async () => {
        renderComponent(
          {
            desktopBannerSize: 'large',
          },
          XLARGE
        );
      });

      const imageContainer = screen.getAllByTestId('aspect-ratio-container')[0];
      expect(imageContainer.getAttribute('height')).toBe('365');
    });
  });
});

describe('Aspect Ratio Image mobile', () => {
  const scbvhBaseData: SubCategoryBannerVHCarouselProps = {
    desktopBannerSize: 'medium',
    mobileBannerSize: 'medium',
    contentConfiguration: 'unique',
    mobileTextTreatment: 'below',
    carouselSettings: {
      transition: 'slide',
      type: 'clickThrough',
      continuousLoop: false,
      autoplay: {
        delay: 3000,
        pauseOnHover: false,
      },
      animation: {
        speed: 500,
        ease: false,
      },
      styling: {
        controlsIconsColor: 'primary',
        pagination: 'hide',
        hideChevrons: false,
      },
    },
    frames: [
      {
        backgroundImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: '0e1911e3-d193-472e-ac07-eb19491a084b',
              name: '210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL',
              endpoint: 'oldnavy',
              defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
            },
            altText: 'Female red shirt',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        mobileBackgroundImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'df89d5a2-1b33-4b88-9d0f-b4848c57f871',
              name: '210726_34-M2140_AugMon_JeansVIBanner_B_US_tile4',
              endpoint: 'oldnavy',
              defaultHost: '1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io',
            },
            altText: 'mobile image',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],

        ctaButton: {
          label: 'Buy now',
          value: 'page.not.found',
        },
        bannerLink: {
          value: 'page.not.found',
          label: 'page not found',
        },
        webAppearance: {
          desktop: {
            verticalPlacement: 'center',
            horizontalPlacement: 'center',
            textJustification: 'center',
            ctaVerticalPlacement: 'center',
            ctaHorizontalPlacement: 'center',
            ctaJustification: 'center',
            ctaButtonStyling: {
              buttonStyle: 'border',
              buttonColor: 'dark',
            },
          },
          mobile: {
            textJustification: 'center',
            ctaPlacement: 'center',
          },
        },
        text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6">Lorem Ipsum Dolor</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Sit amet, consectetur adipiscing</span></p>',
      },
    ],
    webAppearance: {
      showHideBasedOnScreenSize: 'alwaysShow',
    },
  };

  it('should render component medium size AspectRatioContainer on mobile', () => {
    jest.spyOn(AspectRatioContainer as any, 'render').mockImplementationOnce(MockAspectRatioContainer);
    const backgroundImage = scbvhBaseData.frames[0].mobileBackgroundImage || [];
    renderComponent(
      {
        ...scbvhBaseData,
      },
      SMALL
    );
    expect(MockAspectRatioContainer).toHaveBeenCalled();
    expect(getLastCalledWith(MockAspectRatioContainer)).toMatchObject({
      background: { type: 'image', images: backgroundImage },
      height: 160,
      width: 375,
    });
  });
});

describe('should render GapFactory', () => {
  it('should render GapFactory mobile snaps', () => {
    const { asFragment } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'primary',
            pagination: 'desktop',
            hideChevrons: false,
          },
        },
      },
      SMALL,
      Brands.GapFactoryStore
    );

    expect(asFragment()).toMatchSnapshot();
  });
  it('should render GapFactory desktop snaps', () => {
    const { asFragment } = renderComponent(
      {
        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'primary',
            pagination: 'desktop',
            hideChevrons: false,
          },
        },
      },
      XLARGE,
      Brands.GapFactoryStore
    );

    expect(asFragment()).toMatchSnapshot();
  });
});
