// @ts-nocheck
'use client';
import React from 'react';
import { Story } from '@storybook/react';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import README from '../README.mdx';
import { scbvhCarouselBaseData } from '../__fixtures__/test-data';
import SubCategoryBannerVHCarousel from '../index';
import { SubCategoryBannerVHCarouselProps } from '../../../components/SubCategoryBannerVHCarousel';
import { NullJSX, localizationPicker } from '../../../../stories/story-helpers';

const extendedViewports = {
  scbvhc: {
    name: 'Category Page (Product Grid)',
    styles: {
      height: '768px',
      width: '1000px',
    },
    type: 'desktop',
  },
};

export default {
  title: 'Common/JSON Components (Marketing)/content-types/SubCategoryBannerVariableHeightCarousel',
  argTypes: {
    locale: localizationPicker,
  },
  parameters: {
    layout: 'fullscreen',
    eyes: { include: false },
    docs: {
      page: README,
    },
    viewport: {
      viewports: { ...extendedViewports },
    },
  },
  tags: ['exclude'],
  decorators: [
    (Story: React.FC) => (
      <BreakpointProvider>
        <Story />
      </BreakpointProvider>
    ),
  ],
};

export const Playground: Story<SubCategoryBannerVHCarouselProps> = props => {
  const content = <SubCategoryBannerVHCarousel {...props} />;

  return (
    <NullJSX content={content}>
      <p>Unsupported by brand</p>
    </NullJSX>
  );
};

Playground.argTypes = {
  desktopBannerSize: {
    control: {
      type: 'radio',
      options: ['medium', 'large'],
    },
  },
  mobileBannerSize: {
    control: { type: 'radio', options: ['medium', 'large'] },
  },
  contentConfiguration: {
    control: { type: 'radio', options: ['unique', 'persistent'] },
  },
  mobileTextTreatment: {
    control: { type: 'radio', options: ['below', 'on'] },
  },
};

Playground.args = { ...scbvhCarouselBaseData };
Playground.parameters = { viewport: { defaultViewport: 'scbvhc' } };
