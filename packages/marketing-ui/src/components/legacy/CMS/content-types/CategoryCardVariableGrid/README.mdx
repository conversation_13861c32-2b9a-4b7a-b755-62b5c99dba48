# Category Card Variable Grid

#### What is `CategoryCardVariableGrid`?

> Note: This content type is being put on hold. Athleta is our first priority for CMS work, and Athleta does not use this style of division pages. However, most of the work has been done to create a working unstyled content-type.

`CategoryCardVariableGrid` is a CMS content type that displays a grid of Category Cards for navigation. Non-CMS examples of this pattern can be found on the division pages of Gap, Gapfs, ON, BR, and BRFS. [Here is an example.](https://www.gap.com/browse/division.do?cid=1182426&mlink=39813,29086546,Megnav_Teen&clink=29086546&nav=meganav%3ATeen%3A%3A).

## Default Behavior

`CategoryCardVariableGrid`

## Component Props

Like all CMS Content Types, `CategoryCardVariableGrid` accepts JSON data generated in Amplience. It accepts an array of category cards containing images, styles, rich text and CTAs. It also accepts grid formats like `"2x3"` or `"4x1"` for desktop and mobile, while mobile will also accept `"carousel"`, which shows the cards as a scroll bar "carousel" layout.

For detailed prop info, see these files:

- [CategoryCardVariableGrid content-type](./src/CMS/content-types/CategoryCardVariableGrid/types.ts)
- [CategoryCardVariableGrid sub-components](./src/CMS/components/CategoryCardVariableGrid/types.ts)

## Limitations

This unfinished content type has:

- an AT storybook entry
- test coverage
- built-in card shapes selection
- parameters to enable brand-specific grid gaps
- an entry in cmsComponentRegistry.tsx

It does **not** have:

- a finalized schema
- any styles specific to brand
- realistic content in storybook

Some unaddressed callouts that need to wait as we prioritize AT in 2022:

- Link-wrapped cards still need to be reviewed for code quality and accessibility
- Long dropdown button text covers the icon in AT. This may just be a dropdown styles problem, not a CCVG problem.
