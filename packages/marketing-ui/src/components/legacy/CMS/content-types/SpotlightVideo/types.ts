// @ts-nocheck
'use client';
import { CTAVariant, CTAColor } from '../../subcomponents/CTAButton';
import { DefaultIconType, MobileIconType } from '../../types/amplience';
import { AdvanceImageExtensionValue, BackgroundTypeExtensionValue, BaseContentType, VimeoVideoPartialData } from '../../global/types';
import { SpotlightImageContent } from '../SpotlightImage/types';
import { ShowHideBasedOnScreenSizeProps } from '../../subcomponents/ShowHideWrapper/types';

export interface SpotlightVideoCta {
  cta: {
    label: string;
    value: string;
  };
  buttonStyle: {
    buttonStyle: CTAVariant;
    buttonColor: CTAColor;
    primaryHex?: string;
    secondaryHex?: string;
  };
}

export interface SpotlightVideoContent {
  contentJustification: 'left' | 'center' | 'right';
  mobileContentJustification: 'left' | 'center' | 'right';
  mobileVerticalAlignment: SpotlightImageContent['mobileVerticalAlignment'];
  verticalAlignment: SpotlightImageContent['verticalAlignment'];
  icon?: DefaultIconType;
  mobileIcon?: MobileIconType;
  spotlightText: {
    useGradientBackfill: boolean;
    useGradientOverride?: boolean;
    background?: Omit<BackgroundTypeExtensionValue, 'images' | 'color'>;
    defaultText?: string;
    mobileOverride?: string;
  };
  ctaButtons?: Array<SpotlightVideoCta>;
}

export interface SpotlightVideoImageType {
  mainImage: AdvanceImageExtensionValue;
  mobileImageOverride?: AdvanceImageExtensionValue;
}

export interface SpotlightVideoImageOverlaysProps {
  handle?: {
    placement: string;
    text?: string;
  };
  detailsLink?: {
    label: string;
    fontColor: string;
    prefixLabel?: string;
    pemoleCode?: string;
  };
  useGradientBackfill: boolean;
}

export interface MobileBackgroundHelper {
  mobileBackground: BackgroundTypeExtensionValue;
}
export interface SpotlightVideoContentType extends BaseContentType<SpotlightSchema> {
  general: {
    layout: 'inset' | 'fullBleed';
    background: BackgroundTypeExtensionValue;
    mobileBackground?: MobileBackgroundHelper[];
    showHideBasedOnScreenSize?: ShowHideBasedOnScreenSizeProps;
  };
  video: {
    vimeoVideo: VimeoVideoPartialData;
  };
  videoOverlays: SpotlightVideoImageOverlaysProps;
  content: SpotlightVideoContent;
}

export type SpotlightSchema = 'https://cms.gap.com/schema/content/v1/spotlight-video.json';
