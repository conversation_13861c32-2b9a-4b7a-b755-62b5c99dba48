// @ts-nocheck
import { SpotlightVariableHeightContentType } from '../types';

// Image section with single link CTA's
export const mainSectionUseCase4: SpotlightVariableHeightContentType = {
  _meta: {
    name: 'Spotlight Variable Height - Test',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json',
    deliveryId: '379da898-ff0e-43f1-841e-90a7e6d62c05',
  },
  image: {
    imageSize: 'large',
    heroImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '759dd7d6-ce44-4d6b-b84a-1ddaa282a423',
          name: 'Spotlight_Lg_1440x800',
          endpoint: 'oldnavy',
          defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  imageOverlay: {
    cta: {
      buttonStyle: {
        buttonStyle: 'underline',
        buttonColor: 'dark',
      },
      showHideBasedOnScreenSize: 'alwaysShow',
    },
    useGradientBackfill: false,
    contentPlacement: {
      horizontal: 'middle',
      vertical: 'middle',
    },
    contentJustification: 'middle',
    handle: {
      placement: 'left',
    },
    useGradientBackfillFooter: false,
  },
  contentBlocks: {
    aboveImage: {
      cta: {
        buttonStyle: {
          buttonStyle: 'outline',
          buttonColor: 'dark',
        },
      },
      contentPlacement: 'left',
      contentJustification: 'left',
    },
    belowImage: {
      cta: {
        buttonStyle: {
          buttonStyle: 'underline',
          buttonColor: 'dark',
        },
      },
      contentPlacement: 'middle',
      contentJustification: 'middle',
    },
  },
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};
