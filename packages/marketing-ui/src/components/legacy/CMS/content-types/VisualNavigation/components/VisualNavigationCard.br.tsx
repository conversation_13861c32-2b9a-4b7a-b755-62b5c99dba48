// @ts-nocheck
'use client';
import React from 'react';
import { styled, Theme } from '@ecom-next/core/react-stitch';
import { VisualNavigationCategoryCard } from '../VisualNavigation/types';
import { VisualNavigationCard as Card, VisualNavigationFooter, VisualNavigationDescriptionOverlay } from '../../../components/visual-navigation';
import { RichText } from '../../../subcomponents/RichText';
import { useThemeColors } from '../../../../hooks/useThemeColors';
import { formatUrl } from '../../../../helper';

/**
 * TODO: Explicitly using the #2b2b2b until it is adopted into the theme
 */
// export const OVERRIDDEN_B1_COLOR = "#2b2b2b";

const StyledCard = styled(Card)`
  & > div:nth-of-type(2) {
    border-top: 1px solid ${({ color }) => color};
  }

  &:not(:first-of-type) {
    & > div:nth-of-type(2) {
      border-left: 1px solid ${({ color }) => color};
    }
  }
`;

function getHeadingStateColor(
  colors: Theme['color'],
  { hovering = false, focused = false, selected = false }: { hovering?: boolean; focused?: boolean; selected?: boolean }
): string | undefined {
  if (!hovering && (focused || selected)) {
    return colors.wh;
  }

  return undefined;
}

interface VisualNavigationCardProps {
  className?: string;
  card: VisualNavigationCategoryCard;
  selected?: boolean;
  headingStyle?: 'primary' | 'secondary';
  imageAspectRatio: string;
  showHover?: boolean;
  isImageFixedHeight?: boolean;
  isImageFixedWidth?: boolean;
}

const VisualNavigationCard = (props: VisualNavigationCardProps) => {
  const { card, className, selected, imageAspectRatio, showHover = true, isImageFixedHeight, isImageFixedWidth } = props;
  const { heading, description, hoverOptions = [], image = [] } = card;
  const colors = useThemeColors();
  const [source] = image;
  const [hoverOption] = hoverOptions;
  const { image: images = [] } = hoverOption || {};
  const [hoverSource] = images;
  const scalableText = { enable: true, disableInfiniteScaling: true };
  const url = formatUrl(card.url?.value || '');

  return (
    <StyledCard
      className={className}
      color={colors.b1}
      hoverImage={hoverSource}
      image={source}
      imageAspectRatio={imageAspectRatio}
      isImageFixedHeight={isImageFixedHeight}
      isImageFixedWidth={isImageFixedWidth}
      overlay={hoverOption}
      selected={selected}
      url={url}
      zoom={hoverOption?.zoom}
    >
      {({ isHovering, focused }) => {
        const isHovered = !focused && isHovering && showHover;
        return (
          <VisualNavigationFooter
            css={{
              boxSizing: 'border-box',
              position: 'relative',
              width: '100%',
              minHeight: 44,
              backgroundColor: focused ? colors.b1 : colors.wh,
              color: getHeadingStateColor(colors, {
                focused,
                hovering: isHovered,
                selected,
              }),
            }}
          >
            {heading && (
              <RichText
                color={focused ? 'inherit' : undefined}
                css={{
                  opacity: isHovered ? 0.65 : 1,
                  boxSizing: 'border-box',
                  padding: '11px 15px 9px',
                  flex: 1,
                  display: 'flex',
                  alignItems: 'center',
                  '> *': {
                    width: '100%',
                  },
                }}
                scalableText={scalableText}
                text={heading}
              />
            )}
            {description && (
              <VisualNavigationDescriptionOverlay
                aria-hidden={!isHovering || focused}
                bgColor='#FFFFFF'
                css={{
                  borderTop: `1px solid ${colors.b1}`,
                  width: '100%',
                }}
                paddingBlock={15}
                paddingInline={15}
                show={isHovered}
              >
                <RichText scalableText={scalableText} text={description} />
              </VisualNavigationDescriptionOverlay>
            )}
          </VisualNavigationFooter>
        );
      }}
    </StyledCard>
  );
};

export default VisualNavigationCard;
