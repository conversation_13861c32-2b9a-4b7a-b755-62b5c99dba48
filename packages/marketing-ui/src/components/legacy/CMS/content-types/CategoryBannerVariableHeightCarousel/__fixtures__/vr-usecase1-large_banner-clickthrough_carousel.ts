// @ts-nocheck
import { CategoryBannerVHCarouselProps } from '../../../components/CategoryBannerVHCarousel';
const background1 = require('../../../../assets/background-1.png').default?.src;
const glance = require('../../../../assets/woman-glancing-the-view.png').default?.src;

// Large Banner Size with clickThrough carousel + content left justification
export const categoryBannerVariableHeightCarouselUseCase1: CategoryBannerVHCarouselProps = {
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 3000,
      pauseOnHover: false,
    },
    animation: {
      speed: 500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
    },
  },
  frames: [
    {
      tiles: [
        {
          backgroundImage: [
            {
              svgPath: background1,
              altText: 'background 1',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
          ctaButton: {
            label: 'SHOP NOW',
            value: 'https://www.gap.com',
          },
          webAppearance: {
            desktop: {
              verticalPlacement: 'start',
              horizontalPlacement: 'start',
              textJustification: 'start',
              ctaVerticalPlacement: 'start',
              ctaHorizontalPlacement: 'left',
              ctaJustification: 'start',
              ctaButtonStyling: {
                buttonStyle: 'underline',
                buttonColor: 'light',
              },
            },
            mobile: {
              textJustification: 'center',
              ctaPlacement: 'center',
            },
          },
          text: '<p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--headline-3 " style= "color:#FFFFFF ">Lorem ipsum dolor sit</span></p><p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--subhead-1 " style= "color:#FFFFFF ">dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore.</span></p>',
        },
      ],
    },
    {
      tiles: [
        {
          webAppearance: {
            desktop: {
              verticalPlacement: 'start',
              horizontalPlacement: 'start',
              textJustification: 'start',
              ctaVerticalPlacement: 'start',
              ctaHorizontalPlacement: 'left',
              ctaJustification: 'start',
              ctaButtonStyling: {
                buttonStyle: 'underline',
                buttonColor: 'light',
              },
            },
            mobile: {
              textJustification: 'center',
              ctaPlacement: 'center',
            },
          },
          backgroundImage: [
            {
              svgPath: glance,
              altText: 'Glance',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
          text: '<p class="amp-cms--p " style= "text-align:left; "><span class= "amp-cms--headline-3 " style= "color:#FFF ">Lorem ipsum dolor sit</span></p><p class= "amp-cms--p " style= "text-align:left; "><span class= "amp-cms--subhead-1 " style= "color:#FFF ">dolor sit amet consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore.</span></p>',
          ctaButton: {
            label: 'SHOP ALL',
            value: 'https://www.google.com',
          },
        },
      ],
    },
  ],
  contentConfiguration: false,
  desktopBannerSize: 'large',
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  mobileBannerSize: 'large',
  mobileTextTreatment: 'on',
};
