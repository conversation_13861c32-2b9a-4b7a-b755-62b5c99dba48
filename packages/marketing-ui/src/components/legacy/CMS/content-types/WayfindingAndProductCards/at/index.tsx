// @ts-nocheck
'use client';
import React from 'react';
import { useViewportIsLarge } from '../../../../hooks';
import { ProductCards, ProductCardsContainer, ProductCardsTopArea } from '../../../components/product-cards';
import { ShowHideWrapper } from '../../../subcomponents/ShowHideWrapper';
import { WayfindingAndProductCardsProps } from '../types';
import { CTA, scalableText, WayfindingAndProductCardsContainer } from '../utilities';

const AthletaWayfindingAndProductCards = (props: WayfindingAndProductCardsProps) => {
  const { general, content, recommendedContent } = props;
  const { mobileLayout, categoryButtonColor } = general;
  const { topText, cta, categories, carouselSettings } = content;
  const isDesktop = useViewportIsLarge();
  const desktopLayout = categories.length > 4 ? 'carousel' : 'basic';
  const variant = isDesktop ? desktopLayout : mobileLayout;

  const headlineText = isDesktop
    ? recommendedContent?.headlineOverride.headlineOverrideDesktop || topText?.defaultText
    : recommendedContent?.headlineOverride.headlineOverrideMobile || topText?.mobileOverride;

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={general.showHideBasedOnScreenSize}>
      <ProductCardsContainer {...general}>
        <WayfindingAndProductCardsContainer isDesktop={isDesktop}>
          <ProductCardsTopArea cta={cta} scalableText={scalableText} topText={{ ...topText, defaultText: headlineText }} />
          <ProductCards carouselSettings={carouselSettings} categories={categories} categoryButtonColor={categoryButtonColor} variant={variant} />
          <CTA cta={cta} isDesktop={isDesktop} />
        </WayfindingAndProductCardsContainer>
      </ProductCardsContainer>
    </ShowHideWrapper>
  );
};

export default AthletaWayfindingAndProductCards;
