// @ts-nocheck
import { ProductDetailsPageContentProps } from '../types';

export const productDetailsPageUseCase17: ProductDetailsPageContentProps = {
  defaultImage: {
    desktopSize: undefined,
    mobileSize: '4/5',
    splitViewImages: { desktopSize: undefined, mobileSize: '4/5' },
  },
  _meta: {
    name: 'PDP',
    schema: 'https://cms.gap.com/schema/content/v1/product-detail-page.json',
    deliveryId: '0',
  },
  general: {
    desktopLayout: '4/1',
    mobileLayout: '9/16',
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  content: {
    background: {
      type: 'gradient',
      gradient: {
        from: '#FF962E',
        to: '#8A4500',
      },
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#FFFF00',
        },
      },
    ],
    contentJustification: {
      defaultContentJustification: 'center',
      mobileContentJustification: [
        {
          contentJustification: 'left',
        },
      ],
    },
    horizontalPosition: 'right',
    verticalPosition: 'bottom',
    icon: {
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'c45867d4-98ee-4c1e-89e6-82efb1a493c9',
            name: 'ATHLETA_logo@2x',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: 'Default Icon',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      iconSize: '24px',
    },
    mobileIcon: {
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: '15587c86-1e59-46b9-852d-d318dd80b190',
            name: 'Gap_BOPIS_bag-icon',
            endpoint: 'gap',
            defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
          },
          altText: 'Mobile Icon',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
      iconSize: '80px',
    },
    iconLibrary: [
      {
        id: '331fd776-e00b-4c2c-ae50-e9fe19ce4508',
        name: 'BREATHABLE_dark',
        label: 'Breathable',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark',
      },
      {
        id: '3e82a0ef-07d2-4fff-82f7-15077b4ced7f',
        name: 'WRINKLE-RESISTANT_dark',
        label: 'Wrinkle-Resistant',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark',
      },
      {
        id: '5c4690be-dbb8-4f83-8bc4-72a62b91897a',
        name: 'ALL-AROUND-STRETCH_dark',
        label: 'All-Around Stretch',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark',
      },
    ],
    textArea: [
      {
        text: '<p class="amp-cms--p"><span class="amp-cms--eyebrow-1">Introducing</span></p><p class="amp-cms--p"><span class="amp-cms--headline-3">THE ULTIMATE II TIGHT</span></p><p class="amp-cms--p"><span class="amp-cms--body-1">Our #1 sweat ready style just got a major upgrade.</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" />',
        mobileText: [
          {
            mobileText:
              '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">Mobile Introducing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5">THE ULTIMATE II TIGHT</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
          },
        ],
      },
    ],
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'chevron',
          buttonColor: 'dark',
        },
        cta: {
          label: 'Shop The Ultimate Collection',
          value: 'VALUE',
        },
      },
    ],
  },
  media: {
    carousel: [
      {
        frames: [
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'b37e8c2f-369a-4173-a1c8-a7ae69b26d03',
                  name: 'ATG_DP_XL_Testing2',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: '',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            mobileBackgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '575bd9fc-88de-433f-acd1-307d0f4d853c',
                  name: '100_540x960_00',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: '',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '66f963df-2279-4b63-8b77-b9e73e85b812',
                  name: 'SU22_D1_ATG_Shorts&Skorts_S',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: '',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
          {
            backgroundImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '5fbc9f69-124e-4e51-80ed-ba372c8aabbd',
                  name: 'spotlight_ATG_image_xl@2x',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: '',
                variations: [
                  {
                    variation: 'desktop',
                    crop: {
                      x: 1009.009009009009,
                      y: 1118.172,
                      width: 1790.990990990991,
                      height: 457.828,
                      unit: 'px',
                    },
                  },
                  {
                    variation: 'mobile',
                    crop: {
                      x: 0,
                      y: 349.084,
                      width: 1021.6216216216217,
                      height: 1226.916,
                      unit: 'px',
                    },
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
          },
        ],

        carouselSettings: {
          transition: 'slide',
          type: 'clickThrough',
          continuousLoop: false,
          autoplay: {
            delay: 3000,
            pauseOnHover: false,
          },
          animation: {
            speed: 500,
            ease: false,
          },
          styling: {
            controlsIconsColor: 'primary',
            pagination: 'mobile',
            hideChevronsDesktop: true,
            hideChevronsMobile: false,
          },
        },
      },
    ],
    useGradientBackfill: false,
  },
  aspectRatio: '4/1',
};
