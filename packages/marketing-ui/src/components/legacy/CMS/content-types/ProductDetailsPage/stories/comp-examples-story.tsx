// @ts-nocheck
'use client';
import { Locale, LocalizationProvider, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { Meta, StoryFn } from '@storybook/react';
import React from 'react';
import { translations } from '../../../../helper/localTRO';
import ProductDetailsPage from '..';
import {
  productDetailsPageUseCaseA,
  productDetailsPageUseCaseB,
  productDetailsPageUseCaseBForON,
  productDetailsPageUseCaseCd,
  productDetailsPageUseCaseCdForON,
  productDetailsPageUseCaseCm,
} from '../__fixtures__/vr-pdp-usecases-abc';
import {
  productDetailsPageUseCaseDd,
  productDetailsPageUseCaseDdForON,
  productDetailsPageUseCaseDm,
  productDetailsPageUseCaseDmForON,
} from '../__fixtures__/vr-pdp-usecases-d';
import {
  productDetailsPageUseCaseEd,
  productDetailsPageUseCaseEdForON,
  productDetailsPageUseCaseEm,
  productDetailsPageUseCaseEmForON,
  productDetailsPageUseCaseFd,
} from '../__fixtures__/vr-pdp-usecases-ef';
import {
  productDetailsPageUseCaseGd,
  productDetailsPageUseCaseHd,
  productDetailsPageUseCaseHdForON,
  productDetailsPageUseCaseHm,
  productDetailsPageUseCaseHmForON,
} from '../__fixtures__/vr-pdp-usecases-gh';
import { productDetailsPageUseCaseI, productDetailsPageUseCaseJ } from '../__fixtures__/vr-pdp-usecases-ij';
import { productDetailsPageSplitViewMediaBlockPositionRightForON, productDetailsPageSplitViewMediaBlockPositionLeftForON } from '../__fixtures__/test-data';
import README from '../README.mdx';

const locale: Locale = 'en-US';

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/ProductDetailsPage',
  parameters: {
    docs: { page: README },
    knobs: {
      disable: true,
    },
    layout: 'fullscreen',
  },
  tags: ['!visual:check', 'exclude-br', 'exclude-gap'],
  decorators: [
    (StoryFn: StoryFn) => (
      <LocalizationProvider locale={locale} translations={translations[normalizeLocale(locale)].translation}>
        <div>
          <h1>Deprecated</h1>
        </div>
        <StoryFn />
      </LocalizationProvider>
    ),
  ],
} as Meta<typeof ProductDetailsPage>;

export const DeprecatedVisualRegressionTests: StoryFn = ({ brand }) => (
  <>
    <div>
      <div style={{ fontSize: 25 }}>Product Details Page - Split View - Image MediaSC - Visual Regression (AT Only)</div>
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case A - Desktop and Mobile: Center-Justified, Icon, RTE, Icons Group, CTA, Solid Background, Media - Image, 50/50 Desktop Layout
      </div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageUseCaseA} />
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>Case C - Mobile Only: Right-Justified, Icon, RTE, Icons Group, CTA, Solid Background, Media - Image</div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageUseCaseCm} />
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case F - Desktop Only: Left-Justified, Icon, RTE, NO Icons Group, CTA, Image Background, Media - Image With 2:1 Aspect Ratio, 50/50 Desktop Layout
      </div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageUseCaseFd} />
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case G - Desktop Only: Right-Justified, Icon, RTE, NO Icons Group, CTA, Image Background, Media - Image With 2:1 Aspect Ratio, 50/50 Desktop Layout
      </div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageUseCaseGd} />
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case I - Desktop Only and Case B - Mobile Only: Left-Justified, Icon, RTE, Icons Group, CTA, Solid Background, Media - Image, 33/67 Layout
      </div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageUseCaseI} />
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case J - Desktop Only and case G - Mobile Only: Right-Justified, NO con, RTE, NO Icons Group, CTA, Solid Background, Media - Image, 33/67 Layout
      </div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageUseCaseJ} />
      <div style={{ paddingBottom: 30 }} />
    </div>
    <div>
      <div style={{ fontSize: 25 }}>Product Details Page - Split View Video - Visual Regression (AT | ON)</div>
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case E - Desktop Only: Left-Justified, NO Icon, RTE, NO Icons Group, CTA, Gradient Background, Media - Video, 50/50 Desktop Layout and for Old Navy
        brand the Mobile Layout with 4/3 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseEd} /> : <ProductDetailsPage {...productDetailsPageUseCaseEdForON} />}
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case H - Desktop Only: Center-Justified, NO Icon, RTE, NO Icons Group, CTA, Solid Background, Media - Video 33/67 Layout and for Old Navy brand the
        Mobile Layout with 2/1 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseHd} /> : <ProductDetailsPage {...productDetailsPageUseCaseHdForON} />}
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case H - Mobile Only: Left-Justified, NO Icon, RTE, NO Icons Group, CTA, Gradient Background, Media - Video with enhanced 4/3 aspect ratio, 50/50
        Desktop Layout and for Old Navy brand the Mobile Layout with 4/5 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseHm} /> : <ProductDetailsPage {...productDetailsPageUseCaseHmForON} />}
      <div style={{ paddingBottom: 30 }} />
    </div>
    <div>
      <div style={{ fontSize: 25 }}>Product Details Page - Split View - Carousel - Visual Regression (AT | ON)</div>
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case B - Desktop Only: Left-Justified, Icon, RTE, Icons Group, CTA, Solid Background, Media - Carousel With Autoplay, 50/50 Desktop Layout and for Old
        Navy brand the Mobile Layout with 4/3 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseB} /> : <ProductDetailsPage {...productDetailsPageUseCaseBForON} />}
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case C - Desktop Only: Right-Justified, Icon, RTE, Icons Group, CTA, Solid Background, Media - Carousel With Autoplay, 50/50 Desktop Layout and for Old
        Navy brand the Mobile Layout with 4/5 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseCd} /> : <ProductDetailsPage {...productDetailsPageUseCaseCdForON} />}
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case D - Desktop Only And Case F - Mobile Only: Center-Justified, NO Icon, RTE, NO Icons Group, CTA, Solid Background, Media - Carousel With Chevrons,
        50/50 Desktop Layout and for Old Navy brand the Mobile Layout with 2/1 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseDd} /> : <ProductDetailsPage {...productDetailsPageUseCaseDdForON} />}
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case D - Mobile Only: Center-Justified, Icon, RTE, NO Icons Group, CTA, Solid Background, Media - Carousel With Autoplay and for Old Navy brand the
        Mobile Layout with 4/3 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseDm} /> : <ProductDetailsPage {...productDetailsPageUseCaseDmForON} />}
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>
        Case E - Mobile Only: Right-Justified, Icon, RTE, NO Icons Group, CTA, Solid Background, Media - Carousel With Autoplay and for Old Navy brand the
        Mobile Layout with 4/3 aspect ratio
      </div>
      <div style={{ paddingBottom: 30 }} />
      {brand === 'at' ? <ProductDetailsPage {...productDetailsPageUseCaseEm} /> : <ProductDetailsPage {...productDetailsPageUseCaseEmForON} />}
      <div style={{ paddingBottom: 30 }} />
    </div>
    <div>
      <div style={{ fontSize: 25 }}>
        Product Details Page - Split View - Media Block Positioning Left or Right for Desktop - Visual Regression (Old Navy Only)
      </div>
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>Split View 50/50 Desktop Layout with Media Block on Right and Mobile Layout with 4/5 aspect ratio</div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageSplitViewMediaBlockPositionRightForON} />
      <div style={{ paddingBottom: 30 }} />
      <div style={{ fontSize: 20 }}>Split View 50/50 Desktop Layout with Media Block on Left and Mobile Layout with 2/1 aspect ratio</div>
      <div style={{ paddingBottom: 30 }} />
      <ProductDetailsPage {...productDetailsPageSplitViewMediaBlockPositionLeftForON} />
    </div>
  </>
);
