# PDP - Product Details Page Marketing Banner/Tile

## Description

The `ProductDetailsPage` content-type displays banner-like interactive content. It can display content either at a full view or a split view. It will accept media in the form of an image, carousel or video content.

For mobile viewports this secondary content will fall below the main content and display as a column.

The difference between `ProductDetailsPage` and `FlexibleBannerHP` is that the first has limited scaling (1400 px) while the second one has unlimited scaling.

## API

To view documentation about the API for `ProductDetailsPage`, see: [ProductDetailsPage types.ts](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/content-types/ProductDetailsPage/types.ts) and [PDPContent props](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/components/product-details-page/types.ts)

For more information on layout and behaviors, see: [FlexibleBanner](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/components/flexible-banner/README.md)
