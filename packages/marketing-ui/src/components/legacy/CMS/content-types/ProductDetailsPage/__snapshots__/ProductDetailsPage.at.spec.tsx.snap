// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AT ProductDetailsPage renders PDP correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 4/1;
  overflow: hidden;
}

.emotion-3 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-4 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 4/1;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-5 {
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-6 {
  pointerevents: auto;
}

.emotion-7 {
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: min(1.3888888888888888vw, 19.444444444444443px) min(2.7777777777777777vw, 38.888888888888886px);
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: initial;
  padding: 0px;
  gap: min(1.3888888888888888vw, 19.444444444444443px);
}

.emotion-9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-10 {
  height: auto;
  max-width: 100%;
}

.emotion-11 {
  min-height: min(
              1.6666666666666667vw, 
              23.333333333333332px
            );
  max-height: min(
              1.6666666666666667vw, 
              23.333333333333332px
            );
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: min(0.034722222222222224vw, 0.4861111111111111px);
  font-weight: 500;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 17.5px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0625vw, 0.875px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.04861111111111111vw, 0.6805555555555555px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: min(0.034722222222222224vw, 0.4861111111111111px);
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 16.52777777777778px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.11805555555555555vw, 1.6527777777777777px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.75;
  letter-spacing: min(0.1111111111111111vw, 1.5555555555555556px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.056944444444444436vw, 0.7972222222222222px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 58.333333333333336px));
  line-height: 1;
  letter-spacing: min(0.16666666666666666vw, 2.333333333333333px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.888888888888889vw, 54.44444444444444px));
  line-height: 1;
  letter-spacing: min(0.19444444444444445vw, 2.722222222222222px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.4722222222222223vw, 48.61111111111111px));
  line-height: 1;
  letter-spacing: min(0.1736111111111111vw, 2.4305555555555554px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.7777777777777777vw, 38.888888888888886px));
  line-height: 1;
  letter-spacing: min(0.19444444444444445vw, 2.722222222222222px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.5vw, 35px));
  line-height: 1;
  letter-spacing: min(0.25vw, 3.5px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.9444444444444444vw, 27.22222222222222px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.1048611111111111vw, 1.4680555555555554px);
  text-transform: none;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.3888888888888888vw, 19.444444444444443px));
  line-height: 1.2;
  letter-spacing: min(0.09027777777777779vw, 1.2638888888888888px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(8.61111111111111vw, 120.55555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.833333333333333vw, 81.66666666666667px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.361111111111111vw, 33.05555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 97.22222222222221px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.6666666666666667vw, 23.333333333333332px));
  line-height: 1.5;
  letter-spacing: min(0.08333333333333333vw, 1.1666666666666665px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.25vw, 17.5px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.025vw, 0.35px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.04861111111111111vw, 0.6805555555555555px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: min(2.083333333333333vw, 29.166666666666668px);
  column-gap: min(2.083333333333333vw, 29.166666666666668px);
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-16 {
  height: 3.4722222222222223vw;
  max-height: 48.611111111111114px;
}

.emotion-17 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2777777777777777;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25>span span {
  height: calc(18px * 0.7133333333333334);
}

.emotion-25 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.5rem * 0.72);
}

.emotion-25 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25:hover,
.emotion-25:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-25:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-26 {
  box-sizing: border-box;
}

.emotion-27 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-27 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <article
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="product-card-image"
          >
            <img
              alt="image showing a person"
              class="emotion-3"
              src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Nav4?fmt=webp"
            />
          </div>
        </section>
        <div
          class="emotion-4"
          height="0"
          width="0"
        >
          <div
            class="emotion-5"
            data-testid="gradient-wrapper"
          >
            <div
              class="emotion-6"
            >
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                >
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                      data-testid="advance-image-container-test-id"
                    >
                      <img
                        alt="Default Icon"
                        class="emotion-11"
                        maxminheight="min(
            1.6666666666666667vw, 
            23.333333333333332px
          )"
                        src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATHLETA_logo@2x?fmt=auto"
                      />
                    </div>
                  </div>
                  <div
                    style="text-align: center;"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                          >
                            Introducing
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                        >
                          <span
                            class="amp-cms--headline-3"
                          >
                            THE ULTIMATE II TIGHT
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Our #1 sweat ready style just got a major upgrade.
                          </span>
                        </p>
                        <hr
                          aria-hidden="true"
                          style="display:block;border:0;height:8px;margin:0;background:transparent;"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-14"
                    >
                      <div
                        class="emotion-15"
                      >
                        <img
                          alt=""
                          class="emotion-16"
                          src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                        />
                        <span
                          class="emotion-17"
                        >
                          Breathable
                        </span>
                      </div>
                      <div
                        class="emotion-15"
                      >
                        <img
                          alt=""
                          class="emotion-16"
                          src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                        />
                        <span
                          class="emotion-17"
                        >
                          Wrinkle-Resistant
                        </span>
                      </div>
                      <div
                        class="emotion-15"
                      >
                        <img
                          alt=""
                          class="emotion-16"
                          src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                        />
                        <span
                          class="emotion-17"
                        >
                          All-Around Stretch
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <a
                      class="emotion-25"
                      color="dark"
                      href="/VALUE"
                    >
                      <span
                        class="emotion-26"
                      >
                        Shop The Ultimate Collection
                        <span
                          aria-hidden="true"
                          class="emotion-27"
                        />
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</DocumentFragment>
`;
