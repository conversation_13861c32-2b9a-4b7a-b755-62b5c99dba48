// @ts-nocheck
import { ProductDetailsPageContentProps } from '../types';

// Background video, content center justified, left positioned + Mobile: video override, middle positioned,text override
export const pdpFullViewNonVimeoVideoAT: ProductDetailsPageContentProps = {
  _meta: {
    name: 'Product Detail Page - VR4',
    schema: 'https://cms.gap.com/schema/content/v1/product-detail-page.json',
    deliveryId: '55378621-3c69-4b5d-8993-ed16005ca311',
  },
  general: {
    desktopLayout: '4/1',
    mobileLayout: '4/5',
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  defaultImage: {
    splitViewImages: {},
  },
  media: {
    useGradientBackfill: false,
    video: [
      {
        nonVimeoVideo: {
          displayVideoControls: true,
          controlsIconsColor: 'secondary',
          autoplay: false,
          nonVimeoVideoControlsDisplaySettings: 'displayAlways',
          nonVimeovideoSoundIcons: true,
          desktop: {
            fallbackImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'c1ccb5e5-6b17-4db9-a888-82c2ab700d9e',
                  name: 'ATH23_216_PresDay_HPSpotlight_BG_XL',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            url: 'https://cdn.static.amplience.net/oldnavyprod/_vid/94reissue_vi_banner_hero_desktop usca/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/f8f375ae-aaf6-4f98-a8a6-c57c990fb13f.mp4',
          },
          mobile: {
            fallbackImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '28abeeec-f382-4954-bf8a-52959a5aff9b',
                  name: 'IMG_1',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            url: 'https://cdn.static.amplience.net/oldnavyprod/_vid/94reissue hp mobile_uscdaeng_1/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/3351662f-4951-423f-b90d-fdafc8a28724.mp4',
          },
        },
        videoPlaybackBehavior: {
          singleLoop: true,
          sequentialLoop: false,
        },
        linkWrapper: {
          label: 'link-wrapper',
          value: '/linkwrapper',
        },
      },
    ],
  },
  content: {
    background: {
      type: 'gradient',
      gradient: {
        from: '#D8DE17',
        to: '#17DEA2',
      },
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#D3FA10',
        },
      },
    ],
    icon: {
      iconSize: '32px',
    },
    mobileIcon: {
      iconSize: '24px',
    },
    textArea: [
      {
        text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-3" style="text-transform:uppercase;color:#FCC">Gap-hosted Video</span></p><p class="amp-cms--p" style="text-align:left;"></p>',
        mobileText: [
          {
            mobileText:
              '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-3" style="color:#FCC">INTRODUCING</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="color:#FCC">Non-vimeo video </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="color:#FFFFFF">TIGHT</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
          },
        ],
      },
    ],
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'solid',
          buttonColor: 'light',
        },
        cta: {
          label: 'Shop The Ultimate Collection',
          value: 'https://www.gap.com',
        },
      },
    ],
    contentJustification: {
      defaultContentJustification: 'center',
      mobileContentJustification: [
        {
          contentJustification: 'center',
        },
      ],
    },
    horizontalPosition: 'left',
    verticalPosition: 'middle',
  },
};

export const pdpFullViewImageAT: ProductDetailsPageContentProps = {
  _meta: {
    name: 'Product Detail Page - VR1',
    schema: 'https://cms.gap.com/schema/content/v1/product-detail-page.json',
    deliveryId: '408967a3-6efc-43d6-8b63-29fb992d47bb',
  },
  general: {
    desktopLayout: '4/1',
    mobileLayout: '9/16',
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  defaultImage: {
    splitViewImages: {},
  },
  media: {
    useGradientBackfill: true,
    background: [
      {
        defaultBackground: {
          type: 'image',
          images: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: 'f4825ff1-123b-4221-9bdd-7801f1593b07',
                name: '1',
                endpoint: 'athleta',
                defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
              },
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
        },
        mobileBackground: [
          {
            mobileBackground: {
              type: 'image',
              images: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: 'bee2d3bd-0952-4ca9-8894-bae93c527506',
                    name: 'ATG_Spot_S1',
                    endpoint: 'athleta',
                    defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                  },
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
          },
        ],
        linkWrapper: {
          label: 'image-link-wrapper',
          value: 'https://www.gap-image.com',
        },
      },
    ],
  },
  content: {
    background: {
      type: 'gradient',
      gradient: {
        from: '#D8DE17',
        to: '#17DEA2',
      },
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#D3FA10',
        },
      },
    ],
    icon: {
      iconSize: '32px',
    },
    mobileIcon: {
      iconSize: '24px',
    },
    textArea: [
      {
        text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-3" style="text-transform:uppercase;color:#FFFFFF">The Ultimate II </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-3" style="text-transform:uppercase;color:#FFFFFF">Tight</span></p>',
        mobileText: [
          {
            mobileText:
              '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-3" style="color:#FFFFFF">INTRODUCING</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="color:#FFFFFF">THE ULTIMATE II </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="color:#FFFFFF">TIGHT</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
          },
        ],
      },
    ],
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'solid',
          buttonColor: 'light',
        },
        cta: {
          label: 'Shop The Ultimate Collection',
          value: 'https://www.gap.com',
        },
      },
    ],
    contentJustification: {
      defaultContentJustification: 'center',
      mobileContentJustification: [
        {
          contentJustification: 'center',
        },
      ],
    },
    horizontalPosition: 'center',
    verticalPosition: 'middle',
  },
};

export const pdpSplitViewGradientBGNonVimeoVideoAT: ProductDetailsPageContentProps = {
  _meta: {
    name: 'Product Detail Page',
    schema: 'https://cms.gap.com/schema/content/v1/product-detail-page.json',
    deliveryId: 'de3aa96e-9db0-4161-9e67-8d5b2c84735f',
  },
  general: {
    desktopLayout: '2/1',
    mobileLayout: '3/4',
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  defaultImage: {
    splitViewImages: {},
  },
  media: {
    useGradientBackfill: false,
    video: [
      {
        nonVimeoVideo: {
          displayVideoControls: true,
          controlsIconsColor: 'secondary',
          autoplay: false,
          nonVimeoVideoControlsDisplaySettings: 'displayAlways',
          nonVimeovideoSoundIcons: true,
          desktop: {
            fallbackImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: 'c1ccb5e5-6b17-4db9-a888-82c2ab700d9e',
                  name: 'ATH23_216_PresDay_HPSpotlight_BG_XL',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            url: 'https://cdn.static.amplience.net/oldnavyprod/_vid/94reissue_vi_banner_hero_desktop usca/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/f8f375ae-aaf6-4f98-a8a6-c57c990fb13f.mp4',
          },
          mobile: {
            fallbackImage: [
              {
                image: {
                  _meta: {
                    schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                  },
                  id: '28abeeec-f382-4954-bf8a-52959a5aff9b',
                  name: 'IMG_1',
                  endpoint: 'athleta',
                  defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                },
                altText: 'img',
                variations: [
                  {
                    variation: 'desktop',
                  },
                  {
                    variation: 'mobile',
                  },
                ],
                fliph: false,
                flipv: false,
                enableChroma: false,
                chromaQuality: 80,
              },
            ],
            url: 'https://cdn.static.amplience.net/oldnavyprod/_vid/94reissue hp mobile_uscdaeng_1/c03d59fd-0f86-4fe7-bbca-d250c9aea5b6/video/3351662f-4951-423f-b90d-fdafc8a28724.mp4',
          },
        },
        videoPlaybackBehavior: {
          singleLoop: true,
          sequentialLoop: false,
        },
        linkWrapper: {
          label: 'non-video-link-wrapper',
          value: 'https://www.gap-non-vimeo.com',
        },
      },
    ],
  },
  content: {
    background: {
      type: 'gradient',
      gradient: {
        from: '#F6F6F6',
        to: '#AACEEB',
      },
    },
    contentJustification: {
      defaultContentJustification: 'left',
      mobileContentJustification: [
        {
          contentJustification: 'left',
        },
      ],
    },
    horizontalPosition: 'center',
    verticalPosition: 'middle',
    icon: {
      iconSize: '24px',
    },
    mobileIcon: {
      iconSize: '32px',
    },
    textArea: [
      {
        text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--eyebrow-2">introducing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="font-weight:700">The Ultimate ii tight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
        mobileText: [
          {
            mobileText:
              '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--eyebrow-3">introducing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-1" style="font-weight:700">The Ultimate II tight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
          },
        ],
      },
    ],
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'border',
          buttonColor: 'light',
        },
        cta: {
          label: 'Shop The Ultimate Collection',
          value: '#',
        },
      },
    ],
    mobileBackground: [
      {
        mobileBackground: {
          type: 'gradient',
          gradient: {
            from: '#FFFAFA',
            to: '#FFC3CC',
          },
        },
      },
    ],
  },
};

export const pdpSplitViewSolidBGAnfImageAT: ProductDetailsPageContentProps = {
  _meta: {
    name: 'Product Detail Page',
    schema: 'https://cms.gap.com/schema/content/v1/product-detail-page.json',
    deliveryId: '53e88751-8b62-4329-8b6b-7882bd68a804',
  },
  general: {
    desktopLayout: '2/1',
    mobileLayout: '3/4',
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  defaultImage: {
    splitViewImages: {},
  },
  media: {
    useGradientBackfill: false,
    background: [
      {
        defaultBackground: {
          type: 'image',
          images: [
            {
              image: {
                _meta: {
                  schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                },
                id: '0cb34aa1-6a93-4168-be84-177cbef2bbb3',
                name: 'MD_S_FR_Test_XL',
                endpoint: 'athleta',
                defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
              },
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
              enableChroma: false,
              chromaQuality: 80,
            },
          ],
        },
        mobileBackground: [
          {
            mobileBackground: {
              type: 'image',
              images: [
                {
                  image: {
                    _meta: {
                      schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
                    },
                    id: 'bee2d3bd-0952-4ca9-8894-bae93c527506',
                    name: 'ATG_Spot_S1',
                    endpoint: 'athleta',
                    defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
                  },
                  altText: '#',
                  variations: [
                    {
                      variation: 'desktop',
                    },
                    {
                      variation: 'mobile',
                    },
                  ],
                  fliph: false,
                  flipv: false,
                  enableChroma: false,
                  chromaQuality: 80,
                },
              ],
            },
          },
        ],
        linkWrapper: {
          label: 'image-link-wrapper',
          value: 'https://www.gap-image.com',
        },
      },
    ],
  },
  content: {
    background: {
      type: 'solid',
      color: '#f7f9fa',
    },
    contentJustification: {
      defaultContentJustification: 'center',
    },
    horizontalPosition: 'center',
    verticalPosition: 'middle',
    icon: {
      iconSize: '24px',
      icon: [
        {
          image: {
            _meta: {
              schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
            },
            id: 'd608fb2b-ef42-46e1-8343-0bf307422442',
            name: 'athleta_logo_1',
            endpoint: 'athleta',
            defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
          },
          altText: '#',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
      ],
    },
    mobileIcon: {
      iconSize: '14px',
    },
    textArea: [
      {
        text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--eyebrow-2">introducing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5">The Ultimate ii tight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.</span></p>',
        mobileText: [
          {
            mobileText:
              '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2" style="font-weight:700">introducing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4">The Ultimate II tight</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Our #1 sweat ready style just got a major upgrade.</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3">Lorem ipsum dolor sit amet.</span></p>',
          },
        ],
      },
    ],
    iconLibrary: [
      {
        id: '331fd776-e00b-4c2c-ae50-e9fe19ce4508',
        name: 'BREATHABLE_dark',
        label: 'Breathable',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark',
      },
      {
        id: '3e82a0ef-07d2-4fff-82f7-15077b4ced7f',
        name: 'WRINKLE-RESISTANT_dark',
        label: 'Wrinkle-Resistant',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark',
      },
      {
        id: '5c4690be-dbb8-4f83-8bc4-72a62b91897a',
        name: 'ALL-AROUND-STRETCH_dark',
        label: 'All-Around Stretch',
        staticUrl: 'https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark',
      },
    ],
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'chevron',
          buttonColor: 'dark',
        },
        cta: {
          label: 'Shop The Ultimate Collection',
          value: '#',
        },
      },
    ],
  },
};
