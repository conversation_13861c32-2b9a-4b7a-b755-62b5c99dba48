import { aboveContentCtaDropdownList, belowContentCtaDropdownList, overlayCtaDropdownListData } from '../../SpotlightVariableHeight/__fixtures__/test-data';
import { SpotlightVariableHeightVideoContentType } from '../types';

export const defaultData: SpotlightVariableHeightVideoContentType = {
  _meta: {
    name: 'Spotlight Variable Height Video - ON',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight-variable-height-video.json',
  },
  video: {
    nonVimeoVideo: {
      desktop: {
        fallbackImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'd74feb17-1443-457d-9de2-c6046c349252',
              name: 'FA23_D3_Banner_NA_IMG1_XL',
              endpoint: 'oldnavy',
              defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
            },
            altText: '',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
      },
      mobile: {
        url: 'https://oldnavy.gap.com/Asset_Archive/videocampaign/SVH_Video_375x375.mp4',
        fallbackImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'df01efa3-63ff-4f06-b777-7a64a07414cd',
              name: '230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL',
              endpoint: 'oldnavy',
              defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
            },
            altText: 'Mobile image',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      nonVimeoVideoControlsDisplaySettings: 'displayAlways',
      nonVimeovideoSoundIcons: true,
    },
    videoSize: 'large',
    videoControls: true,
    link: {
      label: 'Voluptatem voluptas',
      value: '#3',
    },
  },
  videoOverlay: {
    cta: {
      ctaDropdownList: overlayCtaDropdownListData,
      buttonStyle: {
        buttonStyle: 'underline',
        buttonColor: 'dark',
      },
      showHideBasedOnScreenSize: 'alwaysShow',
      mobileLayout: 'linear',
    },
    contentPlacement: {
      horizontal: 'middle',
      vertical: 'middle',
    },
    contentJustification: 'right',
    videoText: '<p class="amp-cms--p"><span class="amp-cms--headline-4" style="color:#FFFFFF">Video Text 2<span></p>',
    mobileOverride: '<p class="amp-cms--p"><span class="amp-cms--headline-4" style="color:#FFFFFF">Video Overlay - Mobile Override Text</span></p>',
    handle: {
      text: '<p style="color:#FFFFFF">Handle Text</p>',
      placement: 'left',
      mobileOverride: '<p class="amp-cms--p"><span style="color:#FFFFFF">Video Overlay - Mobile Override Handle Text</span></p>',
    },
    detailsLink: {
      label: 'Details Link',
      prefixLabel: 'Prefix Label',
      fontColor: '#FFFFFF',
      pemoleCode: '987483',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
    },
    useGradientBackfill: true,
    useGradientBackfillFooter: true,
  },
  contentBlocks: {
    aboveVideo: {
      cta: {
        buttonStyle: {
          buttonStyle: 'underline',
          buttonColor: 'dark',
        },
        ctaDropdownList: aboveContentCtaDropdownList,
        showHideBasedOnScreenSize: 'alwaysShow',
        mobileLayout: 'linear',
      },
      contentPlacement: 'left',
      contentJustification: 'left',
      rte:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-1">Above' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Above section body</span></p>',
      mobileOverride:
        '<p class="amp-cms--p" style="text-align:left;"><span>Above' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Above section body</span></p>',
      background: {
        type: 'solid',
        color: 'rgb(203, 214, 230)',
      },
    },
    belowVideo: {
      cta: {
        ctaDropdownList: belowContentCtaDropdownList,
        buttonStyle: { buttonStyle: 'underline', buttonColor: 'dark' },
        showHideBasedOnScreenSize: 'alwaysShow',
        mobileLayout: 'linear',
      },
      contentPlacement: 'middle',
      contentJustification: 'middle',
      rte:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-1">Below' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Below section body</span></p>',
      mobileOverride:
        '<p class="amp-cms--p" style="text-align:left;"><span>Below' +
        ' section - Mobile Override Text</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Below section body - mobile override text</span></p>',
      background: {
        type: 'gradient',
        gradient: {
          from: '#DDDDFF',
          to: '#3333FF',
        },
      },
    },
  },
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};

export const gapHostedData: SpotlightVariableHeightVideoContentType = {
  _meta: {
    name: 'Spotlight Variable Height Video - ON',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight-variable-height-video.json',
  },
  video: {
    nonVimeoVideo: {
      desktop: {
        fallbackImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'd74feb17-1443-457d-9de2-c6046c349252',
              name: 'FA23_D3_Banner_NA_IMG1_XL',
              endpoint: 'oldnavy',
              defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
            },
            altText: '',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        url: 'https://onol.wip.prod.gaptecholapps.com/Asset_Archive/videocampaign/SVH_Video_1440x800.mp4',
      },
      mobile: {
        url: 'https://oldnavy.gap.com/Asset_Archive/videocampaign/SVH_Video_375x375.mp4',
        fallbackImage: [
          {
            image: {
              _meta: {
                schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
              },
              id: 'df01efa3-63ff-4f06-b777-7a64a07414cd',
              name: '230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL',
              endpoint: 'oldnavy',
              defaultHost: 'fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io',
            },
            altText: 'Mobile image',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
      },
      nonVimeoVideoControlsDisplaySettings: 'displayAlways',
      nonVimeovideoSoundIcons: true,
      controlsIconsColor: 'secondary',
    },
    videoControls: true,
    videoSize: 'large',
    link: {
      label: 'Voluptatem voluptas',
      value: '#3',
    },
  },
  videoOverlay: {
    cta: {
      ctaDropdownList: overlayCtaDropdownListData,
      buttonStyle: {
        buttonStyle: 'underline',
        buttonColor: 'dark',
      },
      showHideBasedOnScreenSize: 'alwaysShow',
      mobileLayout: 'linear',
    },
    contentPlacement: {
      horizontal: 'middle',
      vertical: 'middle',
    },
    contentJustification: 'middle',
    videoText: '<p class="amp-cms--p"><span class="amp-cms--headline-4" style="color:#FFFFFF">Video Text 1</span></p>',
    mobileOverride: '<p class="amp-cms--p"><span class="amp-cms--headline-4" style="color:#FFFFFF">Video Overlay - Mobile Override Text</span></p>',
    handle: {
      text: '<p style="color:#FFFFFF">Handle Text</p>',
      placement: 'left',
      mobileOverride: '<p class="amp-cms--p"><span style="color:#FFFFFF">Video Overlay - Mobile Override Handle Text</span></p>',
    },
    detailsLink: {
      label: 'Details Link',
      prefixLabel: 'Prefix Label',
      fontColor: '#FFFFFF',
      pemoleCode: '987483',
      htmlModalUrl: 'https://cdn.c1.amplience.net/c/oldnavyprod/rest_details_070823',
    },
    useGradientBackfill: true,
    useGradientBackfillFooter: true,
  },
  contentBlocks: {
    aboveVideo: {
      cta: {
        buttonStyle: {
          buttonStyle: 'underline',
          buttonColor: 'dark',
        },
        showHideBasedOnScreenSize: 'alwaysShow',
      },
      contentPlacement: 'left',
      contentJustification: 'left',
      rte:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-1">Above' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Above section body</span></p>',
      mobileOverride:
        '<p class="amp-cms--p" style="text-align:left;"><span>Above video' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Above video mobile override text</span></p>',
      background: {
        type: 'solid',
        color: 'rgb(203, 214, 230)',
      },
    },
    belowVideo: {
      cta: {
        ctaDropdownList: belowContentCtaDropdownList,
        buttonStyle: { buttonStyle: 'underline', buttonColor: 'dark' },
        showHideBasedOnScreenSize: 'alwaysShow',
      },
      contentPlacement: 'left',
      contentJustification: 'left',
      rte:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-1">Below' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Below section body</span></p>',
      mobileOverride:
        '<p class="amp-cms--p" style="text-align:left;"><span>Below video' +
        ' section</span></p><p' +
        ' class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Below video mobile override text</span></p>',
      background: {
        type: 'gradient',
        gradient: {
          from: '#DDDDFF',
          to: '#3333FF',
        },
      },
    },
  },
  webAppearance: {
    showHideBasedOnScreenSize: 'alwaysShow',
  },
};
