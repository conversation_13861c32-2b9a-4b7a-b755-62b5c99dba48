import React from 'react';
import { render, screen, fireEvent, cleanup } from 'test-utils';
import { visualNavigationCarouselData as defaultProps } from '../__fixtures__/test-data';
import VisualNavigationCard from './VisualNavigationCard.br';

const firstCard = defaultProps.categoryCards![0];

describe("BR's VisualNavigationCard", () => {
  const renderComponent = (selected = false) => {
    return render(
      <VisualNavigationCard
        key={`category-card-${firstCard.heading}`}
        card={firstCard}
        className='category-card'
        headingStyle='secondary'
        imageAspectRatio='1:1'
        isImageFixedHeight
        isImageFixedWidth
        selected={selected}
      />
    );
  };

  it('should apply the expected styles for hover', () => {
    const { container } = renderComponent();
    const card1 = container.querySelector('a.category-card') as Element;

    const hr1 = screen.queryByTestId('category-card-bottom-border');
    const computedStylesHr1 = window.getComputedStyle(hr1);
    expect(computedStylesHr1['border-bottom']).toBe('1px solid transparent');
    expect(computedStylesHr1['background-color']).toBe('transparent');

    fireEvent.mouseOver(card1);
    const hr2 = screen.queryByTestId('category-card-bottom-border') as Element;
    const computedStylesHr2 = window.getComputedStyle(hr2);
    expect(computedStylesHr2['background-color']).toBe('transparent');
    expect(computedStylesHr2['border-bottom']).toBe('1px solid #000000');
  });

  it('should apply the expected styles for focus', () => {
    const { container } = renderComponent(false);
    const card1 = container.querySelector('a.category-card') as Element;

    const hr1 = screen.queryByTestId('category-card-bottom-border');
    const computedStylesHr1 = window.getComputedStyle(hr1);
    expect(computedStylesHr1['border-bottom']).toBe('1px solid transparent');
    expect(computedStylesHr1['background-color']).toBe('transparent');

    fireEvent.focus(card1);
    const hr2 = screen.queryByTestId('category-card-bottom-border') as Element;
    const computedStylesHr2 = window.getComputedStyle(hr2);
    expect(computedStylesHr2['background-color']).toBe('transparent');
    expect(computedStylesHr2['border-bottom']).toBe('1px solid #000000');
  });

  it('should apply the expected styles for active/selection', () => {
    renderComponent(false);
    const hr1 = screen.queryByTestId('category-card-bottom-border');
    const computedStylesHr1 = window.getComputedStyle(hr1);
    expect(computedStylesHr1['border-bottom']).toBe('1px solid transparent');
    expect(computedStylesHr1['background-color']).toBe('transparent');

    cleanup();

    renderComponent(true);
    const hr2 = screen.queryByTestId('category-card-bottom-border') as Element;
    const computedStylesHr2 = window.getComputedStyle(hr2);
    expect(computedStylesHr2['background-color']).toBe('transparent');
    expect(computedStylesHr2['border-bottom']).toBe('1px solid #000000');
  });
});
