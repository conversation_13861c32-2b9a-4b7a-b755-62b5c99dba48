// @ts-nocheck
import { SMALL, LARGE, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { Brands } from '@ecom-next/core/react-stitch';
import { screen } from '@testing-library/react';
import { visualNavigationSizeToggleTabbedCarouselData } from '../__fixtures__/test-data-tabbed';
import VisualNavigationSizeToggleCarousel from './SizeToggle.on';
import { setupRender } from '../../../../../test-helpers';

const renderComponent = setupRender(VisualNavigationSizeToggleCarousel);

describe('VisualNavigationSizeToggleCarousel', () => {
  describe('in large viewports - Desktop', () => {
    it('should match snapshots', () => {
      const { container } = renderComponent(visualNavigationSizeToggleTabbedCarouselData, Brands.OldNavy, LARGE);
      expect(container).toMatchSnapshot();
    });
  });

  describe('Title', () => {
    it('should be optional', () => {
      const headlineText = visualNavigationSizeToggleTabbedCarouselData.headline!;
      const noHeadline = {
        ...visualNavigationSizeToggleTabbedCarouselData,
        headline: undefined,
      };
      renderComponent(noHeadline, Brands.OldNavy, LARGE);
      expect(screen.queryByText(headlineText)).not.toBeInTheDocument();
    });

    it('should show headline', () => {
      renderComponent(visualNavigationSizeToggleTabbedCarouselData, Brands.OldNavy, LARGE);
      expect(screen.getByText('Lorem ipsum dolor')).toBeInTheDocument();
      expect(screen.getByText('sit amet consectetur adipiscing elit sed do eiusmod.')).toBeInTheDocument();
    });
  });

  describe('in extra large viewports - extra large desktops', () => {
    it('should match snapshots for extra large viewports', () => {
      const { container } = renderComponent(visualNavigationSizeToggleTabbedCarouselData, Brands.OldNavy, XLARGE);
      expect(container).toMatchSnapshot();
    });
  });

  describe('in small viewports - mobile', () => {
    it('should match snapshots for mobile', () => {
      const { container } = renderComponent(visualNavigationSizeToggleTabbedCarouselData, Brands.OldNavy, SMALL);
      expect(container).toMatchSnapshot();
    });
  });
});
