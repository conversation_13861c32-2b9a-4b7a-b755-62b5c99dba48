// @ts-nocheck
import React from 'react';
import { axe } from 'jest-axe';
import { render, act } from 'test-utils';
import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { overlapping3ImageBannerData } from './__fixtures__/test-data';
import Overlapping3ImageBanner, { Overlapping3ImageBannerProps } from './Overlapping3ImageBanner.at';

const DEFAULT_FONT_COLOR = '#000000';

const dataWithOutLogo: Overlapping3ImageBannerProps = {
  ...overlapping3ImageBannerData,
  webAppearance: {
    ...overlapping3ImageBannerData.webAppearance,
    athletaGirlLogo: 'noLogo',
  },
};

const { webAppearance }: Overlapping3ImageBannerProps = overlapping3ImageBannerData;

const {
  headlineSubheadingFontColorMobile,
  headlineSubheadingFontColorDesktop,
  eyebrowSubcopyFontColorDesktop,
  eyebrowSubcopyFontColorMobile,
  detailsLinkFontColorDesktop,
  detailsLinkFontColorMobile,
  ...webAppearanceWithOutFontColors
}: Overlapping3ImageBannerProps['webAppearance'] = webAppearance;

const dataWithOutFontColors: Overlapping3ImageBannerProps = {
  ...overlapping3ImageBannerData,
  webAppearance: webAppearanceWithOutFontColors,
};

describe('Overlapping 3 Image Banner', () => {
  describe('should match snapshots:', () => {
    it('in mobile view', () => {
      const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
        breakpoint: SMALL,
      });
      expect(container).toMatchSnapshot();
    });
    it('in desktop view', () => {
      const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
        breakpoint: LARGE,
      });
      expect(container).toMatchSnapshot();
    });
  });

  describe('data with out optionals', () => {
    describe(`headline should be rendered and match the font color with ${DEFAULT_FONT_COLOR}`, () => {
      it('in mobile view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...dataWithOutFontColors} />, {
          breakpoint: SMALL,
        });
        const headline = getByText(overlapping3ImageBannerData.headline);
        expect(headline).toHaveStyle({
          color: DEFAULT_FONT_COLOR,
        });
      });
      it('in desktop view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...dataWithOutFontColors} />, {
          breakpoint: LARGE,
        });
        const headline = getByText(overlapping3ImageBannerData.headline);
        expect(headline).toHaveStyle({
          color: DEFAULT_FONT_COLOR,
        });
      });
    });

    describe('subhead should be rendered', () => {
      it('in mobile view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(getByText(overlapping3ImageBannerData.subhead!)).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(getByText(overlapping3ImageBannerData.subhead!)).toBeInTheDocument();
      });
    });

    describe('athletaGirlLogo should be rendered', () => {
      it('in mobile view', () => {
        const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(container.querySelector('svg')).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(container.querySelector('svg')).toBeInTheDocument();
      });
    });

    describe("athletaGirlLogo should be rendered for 'noLogo'", () => {
      it('in mobile view', () => {
        const { container } = render(<Overlapping3ImageBanner {...dataWithOutLogo} />, {
          breakpoint: SMALL,
        });
        expect(container.querySelector('svg')).not.toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { container } = render(<Overlapping3ImageBanner {...dataWithOutLogo} />, {
          breakpoint: LARGE,
        });
        expect(container.querySelector('svg')).not.toBeInTheDocument();
      });
    });
  });

  describe('data with optionals', () => {
    describe(`headline color should be  ${DEFAULT_FONT_COLOR}`, () => {
      it('in mobile view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(getByText(overlapping3ImageBannerData.headline)).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(getByText(overlapping3ImageBannerData.headline)).toBeInTheDocument();
      });
    });

    describe('headline divider should be rendered', () => {
      it('in mobile view', () => {
        const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(container.querySelector('hr')).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(container.querySelector('hr')).toBeInTheDocument();
      });
    });

    describe('eyebrow should be rendered', () => {
      it('in mobile view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(getByText(overlapping3ImageBannerData.eyebrow!)).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(getByText(overlapping3ImageBannerData.eyebrow!)).toBeInTheDocument();
      });
    });

    describe('subhead should be rendered', () => {
      it('in mobile view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(getByText(overlapping3ImageBannerData.subhead!)).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { getByText } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(getByText(overlapping3ImageBannerData.subhead!)).toBeInTheDocument();
      });
    });

    describe('athletaGirlLogo should be rendered', () => {
      it('in mobile view', () => {
        const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: SMALL,
        });
        expect(container.querySelector('svg')).toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
          breakpoint: LARGE,
        });
        expect(container.querySelector('svg')).toBeInTheDocument();
      });
    });

    describe("athletaGirlLogo should be rendered for 'noLogo'", () => {
      it('in mobile view', () => {
        const { container } = render(<Overlapping3ImageBanner {...dataWithOutLogo} />, {
          breakpoint: SMALL,
        });
        expect(container.querySelector('svg')).not.toBeInTheDocument();
      });
      it('in desktop view', () => {
        const { container } = render(<Overlapping3ImageBanner {...dataWithOutLogo} />, {
          breakpoint: LARGE,
        });
        expect(container.querySelector('svg')).not.toBeInTheDocument();
      });
    });
  });

  describe('should not have a11y violations:', () => {
    it('in mobile view', async () => {
      const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
        breakpoint: SMALL,
      });
      expect(await axe(container)).toHaveNoViolations();
    });
    it('in desktop view', async () => {
      const { container } = render(<Overlapping3ImageBanner {...overlapping3ImageBannerData} />, {
        breakpoint: LARGE,
      });
      expect(await axe(container)).toHaveNoViolations();
    });
  });
});
