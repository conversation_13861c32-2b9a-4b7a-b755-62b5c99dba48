// @ts-nocheck
'use client';
import React from 'react';
import { useTheme } from '@ecom-next/core/react-stitch';
import { Overlapping3ImageBannerContentType } from './types';
import { getDetailsContent } from '../../subcomponents/Details';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
import { AthletaGirlLogo } from '../../subcomponents/Logos/AthletaGirlLogo';
import {
  Eyebrow,
  Headline,
  HeadlineDivider,
  SubCopy,
  SubHeading,
  DynamicTextWidthContainer,
  defaultLeftContainerWidth,
  useTypographyElementWidth,
  withGridItemStyles,
  BackgroundImage,
} from '../../components/OverlappingImageBanner';

import { EncodingPOIOptions } from '../../helpers/ImageEncoder';
import { ComponentMaxWidth } from '../../subcomponents/ComponentMaxWidth';
import {
  Banner,
  BannerLink,
  BannerWith3Images,
  DesktopContainer,
  MobileContainer,
  LogoWrapper,
  MobileDiv,
  RightTextContainer,
  DetailsLink,
  StyledDiv,
  DetailsLinkWrapper,
} from '../../components/OverlappingImageBanner/components/styledComponents';

export type Overlapping3ImageBannerProps = Overlapping3ImageBannerContentType;

const AthletaOverlapping3ImageBanner = ({
  headline,
  subhead,
  eyebrow,
  subcopy,
  images,
  bannerLink,
  detailsLink,
  pemoleCode,
  htmlModalUrl,
  webAppearance,
}: Overlapping3ImageBannerProps): JSX.Element | null => {
  const theme = useTheme();
  const DEFAULT_FONT_COLOR = theme.color.bk;
  const {
    shorterImage,
    secondImagePosition,
    secondImagePlacement,
    headlineDivider,
    headlineSubheadingFontColorDesktop = DEFAULT_FONT_COLOR,
    headlineSubheadingFontColorMobile = DEFAULT_FONT_COLOR,
    eyebrowSubcopyFontColorDesktop = DEFAULT_FONT_COLOR,
    eyebrowSubcopyFontColorMobile = DEFAULT_FONT_COLOR,
    detailsLinkFontColorDesktop = DEFAULT_FONT_COLOR,
    detailsLinkFontColorMobile = DEFAULT_FONT_COLOR,
    athletaGirlLogo,
    desktopImageOrIconSize = '24px',
    mobileImageOrIconSize = '14px',
  } = webAppearance;
  const firstImageData = images[0];
  const secondImageData = images[1];
  const thirdImageData = images[2];

  const isMobile = !useViewportIsLarge();
  const defaultWidth = defaultLeftContainerWidth(isMobile);
  const [headlineRef, headlineWidth] = useTypographyElementWidth(isMobile, headline);

  const FirstBanner = withGridItemStyles(
    Banner,
    {
      selectedImage: 'first',
      isShorter: shorterImage === 'first-third',
      isOverlapEnabled: secondImagePlacement === 'behind',
      position: secondImagePosition,
    },
    3
  );

  const SecondBanner = withGridItemStyles(
    Banner,
    {
      selectedImage: 'second',
      isShorter: shorterImage === 'second',
      isOverlapEnabled: secondImagePlacement === 'front',
      position: secondImagePosition,
    },
    3
  );

  const ThirdBanner = withGridItemStyles(
    Banner,
    {
      selectedImage: 'third',
      isShorter: shorterImage === 'first-third',
      isOverlapEnabled: secondImagePlacement === 'behind',
      position: secondImagePosition,
    },
    3
  );

  const opts: EncodingPOIOptions = {
    aspect: '1:2',
    height: 400,
  };

  const detailsContent = getDetailsContent(theme.brand, pemoleCode, htmlModalUrl);

  const desktopView = (
    <ComponentMaxWidth>
      <StyledDiv>
        <DesktopContainer>
          {bannerLink?.value && <BannerLink aria-label={bannerLink.label || headline} href={bannerLink.value} target='_self' />}
          <BannerWith3Images>
            <DynamicTextWidthContainer dynamicWidth={headlineWidth || defaultWidth} minWidthPercentage={defaultWidth}>
              {athletaGirlLogo !== 'noLogo' && (
                <LogoWrapper>
                  <AthletaGirlLogo color={athletaGirlLogo} height={desktopImageOrIconSize} />
                </LogoWrapper>
              )}
              <Headline ref={headlineRef} color={headlineSubheadingFontColorDesktop} text={headline} />
              {headlineDivider && <HeadlineDivider color={headlineSubheadingFontColorDesktop} />}
              {subhead && <SubHeading color={headlineSubheadingFontColorDesktop} text={subhead} />}
            </DynamicTextWidthContainer>
            <FirstBanner>
              <BackgroundImage {...firstImageData} opts={opts} />
            </FirstBanner>
            <SecondBanner>
              <BackgroundImage {...secondImageData} opts={opts} />
            </SecondBanner>
            <ThirdBanner>
              <BackgroundImage {...thirdImageData} opts={opts} />
            </ThirdBanner>
            <RightTextContainer>
              {eyebrow && <Eyebrow color={eyebrowSubcopyFontColorDesktop} text={eyebrow} />}
              {subcopy && <SubCopy color={eyebrowSubcopyFontColorDesktop} text={subcopy} />}
            </RightTextContainer>
          </BannerWith3Images>
        </DesktopContainer>

        <>
          {detailsLink && (
            <DetailsLinkWrapper>
              <DetailsLink color={detailsLinkFontColorDesktop} label={detailsLink} value={detailsContent} />
            </DetailsLinkWrapper>
          )}
        </>
      </StyledDiv>
    </ComponentMaxWidth>
  );

  const mobileView = (
    <ComponentMaxWidth>
      <StyledDiv>
        <MobileContainer>
          {bannerLink?.value && <BannerLink aria-label={bannerLink.label || headline} href={bannerLink.value} target='_self' />}
          <MobileDiv>
            <BackgroundImage {...firstImageData} opts={opts}>
              <DynamicTextWidthContainer dynamicWidth={headlineWidth || defaultWidth} minWidthPercentage={defaultWidth}>
                {athletaGirlLogo !== 'noLogo' && (
                  <LogoWrapper>
                    <AthletaGirlLogo color={athletaGirlLogo} height={mobileImageOrIconSize} />
                  </LogoWrapper>
                )}
                <Headline ref={headlineRef} color={headlineSubheadingFontColorMobile} text={headline} />
                {headlineDivider && <HeadlineDivider color={headlineSubheadingFontColorMobile} />}
                {subhead && <SubHeading color={headlineSubheadingFontColorMobile} text={subhead} />}
              </DynamicTextWidthContainer>
              <RightTextContainer>
                {eyebrow && <Eyebrow color={eyebrowSubcopyFontColorMobile} text={eyebrow} />}
                {subcopy && <SubCopy color={eyebrowSubcopyFontColorMobile} text={subcopy} />}
              </RightTextContainer>
            </BackgroundImage>
          </MobileDiv>
        </MobileContainer>
        <>
          {detailsLink && (
            <DetailsLinkWrapper>
              <DetailsLink color={detailsLinkFontColorMobile} label={detailsLink} value={detailsContent} />
            </DetailsLinkWrapper>
          )}
        </>
      </StyledDiv>
    </ComponentMaxWidth>
  );

  if (isMobile) {
    return mobileView;
  }

  return desktopView;
};

export default AthletaOverlapping3ImageBanner;
