# Category Cards (CMS Configurable)

- What is `CategoryCards`?
  - It is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience. Note that this format differs from earlier JSON formats.

`CategoryCards` is intended for use on the OldNavy homepage.

## Limitations

`CategoryCards` is currently only styled for OldNavy.

### Old Navy 2024 CTA Redesign

As of November 2024, Old Navy decided to restyle their `CTAButton` and `CTADropdown` subcomponents. `CategoryCards` must set the
"on-cta-redesign-2024" feature flag to "true" _and_ the opt-in `OnCtaRedesign2024Context` to `{enabled: true}` in order to use the new Old Navy 2024 cta redesign styles.

## Default Behavior

- Its background applies to the entire background container and can be one of the following types: color (Hex), gradient, or image.
  - There is an optional mobile background override. If populated, this background is shown on mobile viewport only.
- this content type has product cards
  - the product cards have the ability to use a cta
    - the cta is a dropdown
    - it accepts a "dark" or "light" theme that sets text color, defaults to "dark"
  - the product cards have the ability to have an RTE
  - the product cards can have links
- there are 4 variants of this content-type
  - two desktop variants
    - basic variant
      - max of 4 cards
    - carousel variant
      - min of 5, max of 10 cards
      - slick carousel with arrows
      - accepts authored carousel settings from Amplience JSON
  - two mobile variants
    - exposed variant
      - max of 10 cards
      - cards are stacked in rows of 2 cards
    - slider variant
      - max of 10 cards
      - sliding slick carousel
- The `desktopScalingPoint` for the Category Cards content type is set to 1440px, as per UX request. Please note, this is different from the 1280px default scale point for Typography.

If the prop `showHideBasedOnScreenSize` is not in the json, then the component will default to use `alwaysShow` as the value for `showHideBasedOnScreenSize`. If the prop `showHideBasedOnScreenSize` is in the json, the component will use the value provided.

| prop                      | Default & Options |
| ------------------------- | ----------------- |
| showHideBasedOnScreenSize | alwaysShow        |
|                           | hideOnMobile      |
|                           | hideOnDesktop     |
