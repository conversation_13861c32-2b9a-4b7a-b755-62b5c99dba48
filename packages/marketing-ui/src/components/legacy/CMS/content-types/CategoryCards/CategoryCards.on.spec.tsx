// @ts-nocheck
import { LARGE, SMALL, Size } from '@ecom-next/core/breakpoint-provider';
import { render, act } from 'test-utils';
import React from 'react';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import CategoryCards from './CategoryCards.on';
import { categoryCardsDefaultData } from './__fixtures__/test-data';
import { CategoryCardsPropsContentType } from './types';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';

const mockUseEnabledFeatures = jest.fn();
jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  useEnabledFeatures: () => mockUseEnabledFeatures(),
}));

const renderComponent = (props: CategoryCardsPropsContentType, brand: Brands, breakpoint: Size = LARGE) =>
  render(
    <StitchStyleProvider brand={brand}>
      <CategoryCards {...props} />
    </StitchStyleProvider>,
    { breakpoint }
  );

describe('Category Cards - Product Cards', () => {
  describe('should match snapshots for Old Navy', () => {
    it("when 'on-cta-redesign-2024' feature flag is enabled", () => {
      const { container } = render(
        <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
          <StitchStyleProvider brand={Brands.OldNavy} enabledFeatures={{ 'on-cta-redesign-2024': true }}>
            <CategoryCards {...categoryCardsDefaultData} />
          </StitchStyleProvider>
        </OnCtaRedesign2024Context.Provider>
      );
      expect(container).toMatchSnapshot();
    });
    it('desktop', () => {
      const props = categoryCardsDefaultData;
      const { container } = renderComponent(props, Brands.OldNavy, LARGE);
      expect(container).toMatchSnapshot();
    });
    it('mobileExposed', () => {
      const props = categoryCardsDefaultData;
      const { container } = renderComponent(props, Brands.OldNavy, SMALL);
      expect(container).toMatchSnapshot();
    });
    it('mobile scroll', () => {
      const props: typeof categoryCardsDefaultData = JSON.parse(JSON.stringify(categoryCardsDefaultData));
      props.general.mobileLayout = 'scroll';
      const { container } = renderComponent(props, Brands.OldNavy, SMALL);
      expect(container).toMatchSnapshot();
    });
  });

  describe('renderProductCardCtas', () => {
    it('uses a separate layout for mobile exposed', () => {
      const props = categoryCardsDefaultData;
      const { getAllByTestId } = renderComponent(props, Brands.OldNavy, SMALL);
      expect(getAllByTestId('category-cta-container').length).toEqual(2);
    });
    it('use a separate layout for mobile scroll', () => {
      const props: typeof categoryCardsDefaultData = JSON.parse(JSON.stringify(categoryCardsDefaultData));
      props.general.mobileLayout = 'scroll';
      const { getAllByRole } = renderComponent(props, Brands.OldNavy, SMALL);
      const buttons = getAllByRole('button').filter(button =>
        categoryCardsDefaultData.content.categories.some(category => button.textContent === category.ctaDropdown?.label)
      );
      const links = getAllByRole('link').filter(link =>
        categoryCardsDefaultData.content.categories.some(category => link.textContent === category.ctaDropdown?.ctaDropdown[0].label)
      );
      expect(buttons.length).toBe(1);
      expect(links.length).toBe(3);
    });
    it('uses a default layout for desktop', () => {
      const props = categoryCardsDefaultData;
      const { queryAllByTestId } = renderComponent(props, Brands.OldNavy, LARGE);
      expect(queryAllByTestId('category-cta-container')).toEqual([]);
    });
  });
});
