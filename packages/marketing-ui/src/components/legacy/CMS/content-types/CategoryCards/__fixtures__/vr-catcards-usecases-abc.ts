// @ts-nocheck
import { CategoryCardsPropsContentType } from '../types';
const onAd = require('../../../../assets/on-ad.png').default?.src;
const jeans = require('../../../../assets/boyfriend-jeans.jpg').default?.src;
const glance = require('../../../../assets/woman-glancing-the-view.png').default?.src;

export const categoryCardsUseCaseA: CategoryCardsPropsContentType = {
  _meta: {
    name: 'Category Cards VR1',
    schema: 'https://cms.gap.com/schema/content/v1/category-cards.json',
    deliveryId: '5a144791-a543-4c9b-8d78-572705c8de9a',
  },
  general: {
    background: {
      type: 'solid',
      color: '#EBEBEB',
    },
    mobileLayout: 'exposed',
    showHideBasedOnScreenSize: 'alwaysShow',
    categoryButtonColor: 'dark',
  },
  content: {
    topText: {
      defaultText:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-2">Headline Goes Here</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor</span></p>',
      mobileOverride:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-3">Headline Goes Here</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-2">Lorem ipsum dolor sit amet, consectetur</span></p>',
    },
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'solid',
          buttonColor: 'dark',
        },
        cta: {
          label: 'SHOP CATEGORY',
          value: '#',
        },
        mobileCtaLayout: 'linear',
      },
    ],
    categories: [
      {
        image: [
          {
            svgPath: jeans,
            altText: 'Jeans',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
          },
        ],
        ctaDropdown: {
          ctaDropdown: [
            {
              label: 'ONE',
              value: '#',
            },
            {
              label: 'TWO',
              value: '#',
            },
          ],
          label: 'SHOP CATEGORY 1',
        },
        url: {
          label: 'card1',
          value: '#',
        },
        rte: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-1" style="font-weight:700">Category Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4">$10</span></p>',
      },
      {
        image: [
          {
            svgPath: glance,
            altText: 'Glance',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        ctaDropdown: {
          ctaDropdown: [
            {
              label: 'ONE',
              value: '#',
            },
            {
              label: 'TWO',
              value: '#',
            },
          ],
          label: 'SHOP CATEGORY 2',
        },
        url: {
          label: 'card2',
          value: '#',
        },
        rte: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-1" style="font-weight:700">Category Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4">$10</span></p>',
      },
    ],
    carouselSettings: {
      transition: 'slide',
      type: 'clickThrough',
      continuousLoop: false,
      autoplay: {
        delay: 3000,
        pauseOnHover: false,
      },
      animation: {
        speed: 50,
        ease: false,
      },
      styling: {
        controlsIconsColor: 'primary',
        pagination: 'hide',
        hideChevrons: false,
      },
    },
  },
  instanceName: 'Test',
};

export const categoryCardsUseCaseB: CategoryCardsPropsContentType = {
  _meta: {
    name: 'Category Cards VR2',
    schema: 'https://cms.gap.com/schema/content/v1/category-cards.json',
    deliveryId: 'aa23e032-41c5-452f-99b1-1eea641bf3ea',
  },
  general: {
    background: {
      type: 'solid',
      color: '#EBEBEB',
    },
    mobileLayout: 'scroll',
    showHideBasedOnScreenSize: 'alwaysShow',
    categoryButtonColor: 'dark',
  },
  content: {
    topText: {
      defaultText:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-3">Headline Goes Here</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor</span></p>',
      mobileOverride:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5" style="font-weight:700">Headline Goes Here</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-2">Lorem ipsum dolor sit amet, consectetur</span></p>',
    },
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'underline',
          buttonColor: 'dark',
        },
        cta: {
          label: 'SHOP CATEGORY',
          value: '#',
        },
        mobileCtaLayout: 'linear',
      },
    ],
    categories: [
      {
        image: [
          {
            svgPath: jeans,
            altText: 'Jeans',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
          },
        ],
        ctaDropdown: {
          ctaDropdown: [
            {
              label: 'ONE',
              value: '#',
            },
            {
              label: 'TWO',
              value: '#',
            },
          ],
          label: 'SHOP CATEGORY 1',
        },
        url: {
          label: 'card1',
          value: '#',
        },
        rte: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-7">Category Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4" style="font-weight:700">$10</span></p>',
      },
      {
        image: [
          {
            svgPath: glance,
            altText: 'Glance',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        ctaDropdown: {
          ctaDropdown: [
            {
              label: 'ONE',
              value: '#',
            },
            {
              label: 'TWO',
              value: '#',
            },
          ],
          label: 'SHOP CATEGORY 2',
        },
        url: {
          label: 'card2',
          value: '#',
        },
        rte: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-7">Category Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-7"></span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-4">$10</span></p>',
      },
    ],
    carouselSettings: {
      transition: 'slide',
      type: 'clickThrough',
      continuousLoop: false,
      autoplay: {
        delay: 3000,
        pauseOnHover: false,
      },
      animation: {
        speed: 50,
        ease: false,
      },
      styling: {
        controlsIconsColor: 'primary',
        pagination: 'hide',
        hideChevrons: false,
      },
    },
  },
  instanceName: 'Test',
};

export const categoryCardsUseCaseC: CategoryCardsPropsContentType = {
  _meta: {
    name: 'Category Cards VR3',
    schema: 'https://cms.gap.com/schema/content/v1/category-cards.json',
    deliveryId: 'e70bce1b-d65d-43ac-a869-d19911fe721f',
  },
  general: {
    background: {
      type: 'solid',
      color: '#EBEBEB',
    },
    mobileLayout: 'scroll',
    showHideBasedOnScreenSize: 'alwaysShow',
    categoryButtonColor: 'dark',
  },
  content: {
    topText: {
      defaultText:
        '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-5">Headline Goes Here</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--subhead-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor</span></p>',
    },
    cta: [
      {
        buttonStyle: {
          buttonStyle: 'solid',
          buttonColor: 'dark',
        },
        cta: {
          label: 'SHOP CATEGORY',
          value: '#',
        },
        mobileCtaLayout: 'linear',
      },
    ],
    categories: [
      {
        image: [
          {
            svgPath: onAd,
            altText: 'Old Navy Ad',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        ctaDropdown: {
          ctaDropdown: [
            {
              label: 'ONE',
              value: '#',
            },
            {
              label: 'TWO',
              value: '#',
            },
          ],
          label: 'SHOP CATEGORY 1',
        },
        url: {
          label: 'card1',
          value: '#',
        },
        rte: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-7" style="font-weight:700">Category Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">Lorem ipsum dolor sit amet, consectetur adipiscing</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-2">elit, sed do eiusmod tempor incididunt</span></p>',
      },
      {
        image: [
          {
            svgPath: glance,
            altText: 'Glance',
            variations: [
              {
                variation: 'desktop',
              },
              {
                variation: 'mobile',
              },
            ],
            fliph: false,
            flipv: false,
            enableChroma: false,
            chromaQuality: 80,
          },
        ],
        ctaDropdown: {
          ctaDropdown: [
            {
              label: 'ONE',
              value: '#',
            },
            {
              label: 'TWO',
              value: '#',
            },
          ],
          label: 'SHOP CATEGORY 2',
        },
        url: {
          label: 'card2',
          value: '#',
        },
        rte: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-7">Category Name</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt</span></p>',
      },
    ],
    carouselSettings: {
      transition: 'slide',
      type: 'clickThrough',
      continuousLoop: false,
      autoplay: {
        delay: 3000,
        pauseOnHover: false,
      },
      animation: {
        speed: 50,
        ease: false,
      },
      styling: {
        controlsIconsColor: 'primary',
        pagination: 'hide',
        hideChevrons: false,
      },
    },
  },
  instanceName: 'Test',
};
