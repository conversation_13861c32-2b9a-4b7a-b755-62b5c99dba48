:is([data-brand^='gap']) {
  & .mui_ism-banner-content,
  & .mui_ism__cms-video-component {
    position: relative;
  }

  & .mui_ism-full-image__container {
    width: 100%;
    height: 100%;

    &.with-min-height {
      /* mobile min height calc */
      @media (max-width: 1023px) {
        min-height: calc((4 / 3) * ((100vw - 48px) / 2) + 176px);
        /* mobile min height calc */
      }
    }

    &.single-column {
    }

    &.double-column {
    }

    &.flex-container {
      aspect-ratio: var(--ism--aspect-ratio, 1 / 1);
    }

    & .mui_ism-full-image__image-or-icon-content {
      width: 100%;
      grid-row: var(--ism--imageOrIcon-content-grid-row);

      &.mui_ism__cta-rte-bottom {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

      & .mui_ism-full-image__image-or-icon {
        display: block;
        width: 100%;
        padding: var(--ism--imageOrIcon-padding);
        text-align: var(--ism--imageOrIcon-text-align);
      }
    }

    & .mui_ism-full-image__banner-content {
      box-sizing: border-box;
      padding: var(--ism--padding, 15px);
      position: relative;
      height: 100%;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: var(--ism--grid-template-rows);
    }

    & .mui_ism-full-image__cta-container {
      align-self: var(--ism--cta--align-self);
      box-sizing: border-box;
      display: flex;
      flex-direction: var(--ism--cta-flex-direction, row);
      gap: 8px;
      grid-row: var(--ism--cta-grid-row);
      justify-content: var(--ism--cta-horizontal-alignment);
      max-height: var(--ism--cta--max-height);
      padding: var(--ism--cta-padding);
      width: 100%;
      z-index: 2;
    }

    & .mui_ism-full-image__details-link-button {
      display: inline-flex;
      position: relative;
      padding: 0;
      grid-row: var(--ism--details-link-grid-row);
      gap: 5px;
      align-items: var(--ism--details-link-vertical-cta-align);
      justify-content: var(--ism--details-link-justify-content);
      z-index: 2;
      pointer-events: all;
      text-underline-offset: 3.5px;

      & button {
        line-height: normal;
      }
    }
  }

  & .mui_ism-full-video__container {
    display: grid;
    grid-template-areas: 'stacked';
    width: 100%;
    height: 100%;

    &.with-min-height {
      /* mobile min height calc */
      @media (max-width: 1023px) {
        min-height: calc((4 / 3) * ((100vw - 48px) / 2) + 176px);
        /* mobile min height calc */
      }
    }

    &.single-column {
    }

    &.double-column {
    }

    &.grid-container {
      margin: 0;
      aspect-ratio: var(--ism--aspect-ratio, 1 / 1);
    }

    & .mui_ism-full-video__imageOrIcon-content {
      width: 100%;
      grid-row: var(--ism--imageOrIcon-content-grid-row);

      &.mui_ism__cta-rte-bottom {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

      & .mui_ism-full-video__image-or-icon {
        display: block;
        width: 100%;
        padding: var(--ism--imageOrIcon-padding);
        text-align: var(--ism--imageOrIcon-text-align);
      }
    }

    & .mui_ism-full-video__banner-content {
      box-sizing: border-box;
      padding: var(--ism--padding, 15px);
      position: relative;
      height: 100%;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: var(--ism--grid-template-rows);
      grid-area: stacked;
      aspect-ratio: var(--ism--container-aspect-ratio);
    }

    & .mui_ism-full-video__cta-container {
      box-sizing: border-box;
      display: flex;
      flex-direction: var(--ism--cta-flex-direction, row);
      gap: 8px;
      grid-row: var(--ism--cta-grid-row);
      justify-content: var(--ism--cta-horizontal-alignment);
      padding: var(--ism--cta-padding);
      width: 100%;
      z-index: 2;
    }

    & .mui_ism__video-component-wrapper {
      grid-area: stacked;

      & > div {
        position: relative;
      }

      & [data-testid='videocomponent-container'] > div {
        height: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
      }

      & .mui_ism__cms-video-component {
        &.mui_ism__pointer-events-none {
          pointer-events: none;
        }

        &.mui_ism__pointer-events-auto {
          pointer-events: auto;
        }

        & video {
          object-fit: cover;
        }
      }
    }

    & .mui_ism-full-video__details-link-button {
      display: inline-flex;
      position: relative;
      padding: 0;
      grid-row: var(--ism--details-link-grid-row);
      gap: 5px;
      align-items: var(--ism--details-link-vertical-cta-align);
      justify-content: var(--ism--details-link-justify-content);
      z-index: 2;
      pointer-events: all;
      text-underline-offset: 3.5px;
    }
  }

  /* partial image css */
  & .mui_ism-partial-image__container {
    width: 100%;
    height: 100%;

    &.flex-container {
      aspect-ratio: var(--ism--aspect-ratio, 1 / 1);
    }

    & .mui_ism-partial-image__image-or-icon-content {
      width: 100%;
      grid-row: var(--ism--imageOrIcon-content-grid-row);

      &.mui_ism__cta-rte-bottom {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

      & .mui_ism-partial-image__image-or-icon {
        display: block;
        width: 100%;
        padding: var(--ism--imageOrIcon-padding);
        text-align: var(--ism--imageOrIcon-text-align);
      }
    }

    & .mui_ism-partial-image__banner-content {
      box-sizing: border-box;
      padding: var(--ism--padding, 15px);
      position: relative;
      height: 100%;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
    }

    & .mui_ism-partial-image__cta-container {
      align-self: var(--ism--cta--align-self);
      box-sizing: border-box;
      display: flex;
      flex-direction: var(--ism--cta-flex-direction, row);
      gap: 8px;
      grid-row: var(--ism--cta-grid-row);
      justify-content: var(--ism--cta-horizontal-alignment);
      max-height: var(--ism--cta--max-height);
      padding: var(--ism--cta-padding);
      width: 100%;
      z-index: 2;
    }

    & .mui_ism-partial-image__details-link-button {
      display: inline-flex;
      position: relative;
      padding: 0;
      grid-row: var(--ism--details-link-grid-row);
      gap: 5px;
      align-items: var(--ism--details-link-vertical-cta-align, left);
      justify-content: var(--ism--details-link-justify-content, center);
      z-index: 2;
      pointer-events: all;
      text-underline-offset: 3.5px;

      & button {
        line-height: normal;
      }
    }
  }

  /* partial video css */
  & .mui_ism-partial-video__container {
    display: grid;
    grid-template-areas: 'stacked';
    width: 100%;
    height: 100%;

    &.grid-container {
      aspect-ratio: var(--ism--aspect-ratio, 1 / 1);
    }

    & .mui_ism-partial-video__imageOrIcon-content {
      width: 100%;
      grid-row: var(--ism--imageOrIcon-content-grid-row);

      &.mui_ism__cta-rte-bottom {
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
      }

      & .mui_ism-partial-video__image-or-icon {
        display: block;
        width: 100%;
        padding: var(--ism--imageOrIcon-padding);
        text-align: var(--ism--imageOrIcon-text-align);
      }
    }

    & .mui_ism-partial-video__banner-content {
      box-sizing: border-box;
      padding: var(--ism--padding, 15px);
      position: relative;
      height: 100%;
      width: 100%;
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: var(--ism--grid-template-rows);
      grid-area: stacked;
      aspect-ratio: var(--ism--container-aspect-ratio);
    }

    & .mui_ism-partial-video__cta-container {
      box-sizing: border-box;
      display: flex;
      flex-direction: var(--ism--cta-flex-direction, row);
      gap: 8px;
      grid-row: var(--ism--cta-grid-row);
      justify-content: var(--ism--cta-horizontal-alignment);
      padding: var(--ism--cta-padding);
      width: 100%;
      z-index: 2;
    }

    & .mui_ism__video-component-wrapper {
      grid-area: stacked;

      & > div {
        position: relative;
      }

      & [data-testid='videocomponent-container'] > div {
        height: 100%;
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
      }

      & .mui_ism__cms-video-component {
        &.mui_ism__pointer-events-none {
          pointer-events: none;
        }

        &.mui_ism__pointer-events-auto {
          pointer-events: auto;
        }

        & video {
          object-fit: cover;
        }
      }
    }

    & .mui_ism-partial-video__details-link-button {
      display: inline-flex;
      position: relative;
      padding: 0;
      grid-row: var(--ism--details-link-grid-row);
      gap: 5px;
      align-items: var(--ism--details-link-vertical-cta-align, left);
      justify-content: var(--ism--details-link-justify-content, center);
      z-index: 2;
      pointer-events: all;
      text-underline-offset: 3.5px;
    }
  }
}
