'use client';
//@ts-ignore
import { CSSObject, styled, useTheme, forBrands } from '@ecom-next/core/react-stitch';
import { DetailsLinkAlignment, VerticalCtaAlignment, VerticalTextAlignment } from './types';

const alignContentMap: Record<VerticalCtaAlignment | VerticalTextAlignment | DetailsLinkAlignment, string> = {
  left: 'flex-start',
  right: 'flex-end',
  center: 'center',
  middle: 'center',
  top: 'flex-start',
  bottom: 'end',
};

export const getDetailsButtonGridRow = (
  verticalCtaAlignment?: VerticalCtaAlignment | undefined,
  verticalTextAlignment?: VerticalTextAlignment | undefined,
  customGridRows?: { altMiddle?: number; default?: number; middle?: number }
): number => {
  const defaultGridRows = { middle: 6, altMiddle: 5, default: 7 };
  const gridRows = { ...defaultGridRows, ...customGridRows };

  return verticalCtaAlignment === 'middle' ? (verticalTextAlignment === 'middle' ? gridRows.middle : gridRows.altMiddle) : gridRows.default;
};
export const getDetailsButtonCtaAlignment = (verticalCtaAlignment: VerticalCtaAlignment | undefined): string =>
  verticalCtaAlignment === 'middle' ? alignContentMap['bottom'] : alignContentMap['center'];
export const getDetailsButtonJustifyContent = (detailsLinkLocation: DetailsLinkAlignment | undefined): string =>
  alignContentMap[detailsLinkLocation || 'right'];

export const ismGridSize = {
  mobile: 50,
  tablet: 33.3333,
  desktop: 25,
};

export const legacyISMGridSize = {
  mobile: 340,
  tablet: 430,
  desktop: 430,
};

export const ismProductGridHeight = {
  mobile: 'calc((4 / 3) * ((100vw - 96px) / 2)) + 189',
  tablet: 'calc((4 / 3) * ((100vw - 96px) / 3)) + 131',
  desktop: 'calc((4 / 3) * ((100vw - 176px) / 4)) + 131',
};

/**
 * @deprecated This function is deprecated and replaced by the following:
 * - getDetailsButtonGridRow
 * - getDetailsButtonCtaAlignment
 * - getDetailsButtonJustifyContent
 */
export const getGapDetailsButtonStyles = (
  detailsLinkLocation: DetailsLinkAlignment | undefined,
  verticalCtaAlignment?: VerticalCtaAlignment | undefined,
  verticalTextAlignment?: VerticalTextAlignment | undefined
): CSSObject => ({
  display: 'inline-flex',
  position: 'relative',
  padding: 0,
  gridRow: verticalCtaAlignment === 'middle' ? (verticalTextAlignment === 'middle' ? 6 : 5) : 7,
  gap: '5px',
  alignItems: verticalCtaAlignment === 'middle' ? 'end' : 'center',
  justifyContent: detailsLinkLocation === 'left' ? 'flex-start' : 'flex-end',
  zIndex: 2,
  pointerEvents: 'all',
  textUnderlineOffset: '3.5px',
});

export const DetailsButtonWrapperStyles: CSSObject = {
  display: 'flex',
  position: 'absolute',
  bottom: '8px',
  right: '8px',
  alignItems: 'baseline',
  zIndex: 2,
  gap: '5px',
};

export const ATDetailsButtonWrapperStyles: CSSObject = {
  position: 'absolute',
  width: 'max-content',
  bottom: '8px',
  left: '50%',
  transform: 'translate(-50%, 0)',
  zIndex: 2,
};

export const DetailsButtonWrapper = styled.div((): CSSObject => {
  const theme = useTheme();
  return forBrands(theme, {
    at: ATDetailsButtonWrapperStyles,
    default: DetailsButtonWrapperStyles,
  }) as CSSObject;
});

export const detailsBtnStyles = (isDesktopVP: boolean, isDouble: boolean, detailsPrefix?: string): CSSObject =>
  isDesktopVP || isDouble
    ? {
        right: 0,
        paddingLeft: detailsPrefix ? '5px' : '0px',
        position: 'relative',
        top: '12px',
      }
    : {
        display: 'block',
        position: 'absolute',
        bottom: '-5px',
        paddingLeft: '0px',
        paddingBottom: '12px',
      };

export const detailsPrefixBtnStyles = (isDesktopVP: boolean, isDouble?: boolean): CSSObject =>
  isDesktopVP || isDouble
    ? {
        position: 'relative',
        top: '12px',
        padding: '0px',
      }
    : {};
