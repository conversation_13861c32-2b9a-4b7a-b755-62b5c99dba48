// @ts-nocheck
import { SpotlightImageContentType } from '../types';

export const mobileOverrideText = 'Large Headline';
export const defaultText = 'Large Headline';

// Spotlight Image - Fullbleed - Desktop Center Aligned with <PERSON>le, details and both gradient backfills- Mobile Left Aligned with <PERSON><PERSON>, details and both gradient backfills
export const spotlightFullBleedFromComps3: SpotlightImageContentType = {
  _meta: {
    name: 'Spotlight- Full Bleed UAT',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight.json',
    deliveryId: '6a0591a2-31e4-4f54-ba9f-d3ea7756fe99',
  },
  general: {
    layout: 'fullBleed',
    background: {
      type: 'solid',
      color: '#EEDFE8',
    },
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '75cede42-696e-4148-ae51-7878a9071893',
          name: 'SU22_Drop2_NA_ISM_Shorts_XL_3',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  imageOverlays: {
    handle: {
      placement: 'left',
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Sara Smith, Yoga</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Wearing Size M</span></p>',
    },
    useGradientBackfill: true,
    detailsLink: {
      label: '*Details',
      prefixLabel: 'Exclusions apply',
      fontColor: '#FFFFFF',
    },
  },
  content: {
    contentJustification: 'center',
    verticalAlignment: 'middle',
    spotlightText: {
      useGradientBackfill: true,
      defaultText: `<p class="amp-cms--p" style="text-align: ${'center'};"><span class="amp-cms--headlineAlt-3" style="color:#FFFFFF">${defaultText}</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">eiusmod tempor incididunt ut labore.</span></p>`,

      mobileOverride: `<p class="amp-cms--p" style="text-align: ${'left'};"><span class="amp-cms--headlineAlt-3" style="color:#FFFFFF">${mobileOverrideText}</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">Lorem ipsum dolor sit amet</span></p> <p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">consectetur adipiscing elit, sed do </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">eiusmod tempor incididunt ut labore.</span></p>`,
    },
    mobileContentJustification: 'left',
    mobileVerticalAlignment: 'middle',
    ctaButtons: [
      {
        buttonStyle: {
          buttonStyle: 'border',
          buttonColor: 'dark',
        },
        cta: {
          label: 'Shop All',
          value: 'https://www.gap.athleta.com',
        },
      },
      {
        buttonStyle: {
          buttonStyle: 'border',
          buttonColor: 'dark',
        },
        cta: {
          label: 'Shop All',
          value: 'https://www.gap.athleta.com',
        },
      },
    ],
  },
};
// Spotlight Image - Fullbleed - Desktop right aligned with stacked handle and details and no gradient backfills- Mobile center Aligned heading with stacked handle and details and no gradient backfills
export const spotlightFullBleedFromComps4: SpotlightImageContentType = {
  _meta: {
    name: 'Spotlight- Full Bleed UAT',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight.json',
    deliveryId: '6a0591a2-31e4-4f54-ba9f-d3ea7756fe99',
  },
  general: {
    layout: 'fullBleed',
    background: {
      type: 'solid',
      color: '#D0EF0B',
    },
    showHideBasedOnScreenSize: 'alwaysShow',
  },
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'f3214f89-94c4-4bc8-a2ef-4e0440110e5a',
          name: 'HP_Test_Surf_XL',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: 'Background',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '9a1c8d9c-b0d8-4691-bb9f-0f533050d005',
          name: 'cq5dam.web.1280.1280',
          endpoint: 'athleta',
          defaultHost: '1d9xxafvh577y12766tt2karod.staging.bigcontent.io',
        },
        altText: 'Background',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  imageOverlays: {
    handle: {
      placement: 'right',
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Sara Smith, Yoga</span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-3" style="color:#FFFFFF">Wearing Size M</span></p>',
    },
    detailsLink: {
      prefixLabel: 'Exclusions apply',
      label: '*Details',
      fontColor: '#FFFFFF',
    },
    useGradientBackfill: false,
  },
  content: {
    contentJustification: 'right',
    verticalAlignment: 'middle',
    icon: {
      iconSize: '24px',
    },
    mobileContentJustification: 'center',
    mobileVerticalAlignment: 'middle',
    mobileIcon: {
      iconSize: '14px',
    },
    spotlightText: {
      useGradientBackfill: false,
      defaultText: `<p class="amp-cms--p" style="text-align: ${'center'};"><span class="amp-cms--headlineAlt-3" style="color:#FFFFFF">${defaultText}</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">eiusmod tempor incididunt ut labore.</span></p>`,
      mobileOverride: `<p class="amp-cms--p" style="text-align: ${'left'};"><span class="amp-cms--headlineAlt-3" style="color:#FFFFFF">${mobileOverrideText}</span></p><hr style="display:block;border:0;height:8px;margin:0;background:transparent;" aria-hidden="true" /><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">Lorem ipsum dolor sit amet</span></p> <p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">consectetur adipiscing elit, sed do </span></p><p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-4" style="color:#FFFFFF">eiusmod tempor incididunt ut labore.</span></p>`,
    },
    ctaButtons: [
      {
        buttonStyle: {
          buttonStyle: 'border',
          buttonColor: 'dark',
        },
        cta: {
          label: 'Shop All',
          value: 'https://www.gap.athleta.com',
        },
      },
    ],
  },
};
