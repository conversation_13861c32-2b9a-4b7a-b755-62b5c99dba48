// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { Brands, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import SpotlightImage from './index';
import { defaultText, mobileOverrideText, spotlightFullBleedData, spotlightImageFullbleedMinimumData, spotlightInsetData } from './__fixtures__/test-data';

describe('SpotlightImage Content Type', () => {
  describe('Inset variation snapshots for Athleta', () => {
    it('should match desktop', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightInsetData} />
        </StitchStyleProvider>,
        {
          breakpoint: XLARGE,
        }
      );

      expect(container).toMatchSnapshot();
    });

    it('should match mobile', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightInsetData} />
        </StitchStyleProvider>,
        {
          breakpoint: SMALL,
        }
      );

      expect(container).toMatchSnapshot();
    });

    it('should match fullbleed desktop with gradient backfills', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightFullBleedData} />
        </StitchStyleProvider>,
        {
          breakpoint: XLARGE,
        }
      );

      expect(container).toMatchSnapshot();
    });

    it('should match fullbleed desktop with minimal options', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightImageFullbleedMinimumData} />
        </StitchStyleProvider>,
        {
          breakpoint: XLARGE,
        }
      );

      expect(container).toMatchSnapshot();
    });

    it('should match fullbleed mobile with minimal options', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightImageFullbleedMinimumData} />
        </StitchStyleProvider>,
        {
          breakpoint: SMALL,
        }
      );

      expect(container).toMatchSnapshot();
    });

    it('should match fullbleed mobile', () => {
      const { container } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightFullBleedData} />
        </StitchStyleProvider>,
        {
          breakpoint: SMALL,
        }
      );

      expect(container).toMatchSnapshot();
    });
  });

  describe('Mobile overrides', () => {
    it('uses the Rich text override given the viewport is small and the overrides are present', () => {
      const { getByText } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightFullBleedData} />
        </StitchStyleProvider>,
        {
          breakpoint: SMALL,
        }
      );
      expect(getByText(mobileOverrideText)).toBeInTheDocument();
    });
    it('uses the default (desktop) Rich text if the viewport is small and no overrides are present', () => {
      const props = {
        ...spotlightInsetData,
        content: {
          ...spotlightInsetData.content,
          spotlightText: {
            ...spotlightInsetData.content.spotlightText,
            mobileOverride: undefined,
          },
        },
      };

      const { getByText } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...props} />
        </StitchStyleProvider>,
        {
          breakpoint: SMALL,
        }
      );
      expect(getByText(defaultText)).toBeInTheDocument();
    });
    it('uses the default text if the viewport is large and mobile overrides are present', () => {
      const { getByText } = render(
        <StitchStyleProvider brand={Brands.Athleta}>
          <SpotlightImage {...spotlightFullBleedData} />
        </StitchStyleProvider>,
        {
          breakpoint: XLARGE,
        }
      );
      expect(getByText(defaultText)).toBeInTheDocument();
    });
  });
});
