// @ts-nocheck
'use client';
import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { Carousel } from '@ecom-next/core/legacy/carousel';
import { CSSObject, styled, useTheme } from '@ecom-next/core/react-stitch';
import React, { PropsWithChildren, ReactNode, useContext } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { PromoCardList, PromoDrawerContainer } from '../../../components/promo-drawer';
import { BrPromoDrawerProps, PromoDrawerCard } from '../types';
import { useThemeColors } from '../../../../hooks';

const PromoCardsDesktop = styled.div(
  (): CSSObject => ({
    padding: 30,
    '.slick-slide': {
      marginRight: '16px',
    },
    '.slick-list': {
      justifyContent: 'center',
    },
    '.slick-slider': {
      padding: '0 22px',
    },
  })
);

const PromoCardsMobile = ({ children }: PropsWithChildren<ReactNode>) => {
  const MobileContainer = styled.div(
    (): CSSObject => ({
      display: 'flex',
      justifyContent: 'center',
    })
  );

  const MobileScrollerContainer = styled.div(
    (): CSSObject => ({
      margin: '0 16px',
      maxHeight: '100%',
      overflow: 'auto',
      padding: '16px 0',
    })
  );

  const PromoCardsContainerMobile = styled.div(
    (): CSSObject => ({
      gap: 8,
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'nowrap',
    })
  );

  return (
    <MobileContainer data-testid='scrollbar'>
      <MobileScrollerContainer>
        <PromoCardsContainerMobile data-testid='mobileCardsList'>{children}</PromoCardsContainerMobile>
      </MobileScrollerContainer>
    </MobileContainer>
  );
};

const PromoDrawerBananaRepublic = ({ promoCards = [] }: BrPromoDrawerProps) => {
  const { greaterOrEqualTo } = useContext(BreakpointContext);
  const isDesktop = greaterOrEqualTo(LARGE);
  const { localize } = useLocalize();
  const colors = useThemeColors();
  const theme = useTheme();

  /* TODO: FUI-4889 style carousel per specs and
   * test for keyboard & screenreader accessibility.
   * Had to have a basic carousel to ensure spacing
   * was correct for FUI-4888.
   * I also think UX wants no carousel arrows if
   * there is no overflow, ex: only 2 slides at 1440vw.
   */
  const carouselSettings = {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
    },
    infinite: false,
    nextArrowAlt: localize('cms.carousel.next'),
    prevArrowAlt: localize('cms.carousel.previous'),
    variableWidth: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    swipe: true,
  };

  const customDetailsButtonStyles: CSSObject = {
    textTransform: 'none',
    lineHeight: '133%',
    letterSpacing: '1.3px',
    textDecoration: 'none',
    padding: 0,
    borderBottom: `${colors.gray30} 1px solid`,
    marginLeft: '12px',
  };

  const buttonColor = theme.color.wh;
  const buttonFontColor = theme.color.bk;

  const tapToApplyStyles: CSSObject = {
    height: '32px',
    backgroundColor: buttonColor,
    border: `1px solid ${theme.color.gray20}`,
    color: buttonFontColor,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '8px 20px',
    gap: 10,
    '&:focus-visible, &:focus': {
      backgroundColor: buttonColor,
      border: `1px dashed ${theme.color.bk}`,
    },
    '&:has(svg)': {
      backgroundColor: buttonColor,
      color: buttonFontColor,
      '& > svg': {
        '& circle': {
          fill: buttonFontColor,
        },
        '& polygon': {
          fill: buttonColor,
        },
      },
      '&:hover': {
        backgroundColor: buttonColor,
        color: buttonFontColor,
      },
    },
    '&:hover': {
      backgroundColor: buttonColor,
      border: `1px solid ${theme.color.gray54}`,
      color: buttonFontColor,
    },
    '&:disabled': {
      border: `1px solid ${theme.color.gray20}`,
      color: theme.color.gray20,
    },
    zIndex: 1,
  };

  return (
    <div id='PromoDrawer'>
      <PromoDrawerContainer>
        {isDesktop ? (
          <PromoCardsDesktop data-testid='pd-carousel-desktop'>
            <Carousel {...carouselSettings}>
              {PromoCardList(promoCards as unknown as PromoDrawerCard[], true, {
                customDetailsButtonStyles,
                customTapToApplyStyles: tapToApplyStyles,
              })}
            </Carousel>
          </PromoCardsDesktop>
        ) : (
          <PromoCardsMobile data-testid='pd-carousel-mobile'>
            {PromoCardList(promoCards as unknown as PromoDrawerCard[], false, {
              customDetailsButtonStyles,
              customTapToApplyStyles: tapToApplyStyles,
            })}
          </PromoCardsMobile>
        )}
      </PromoDrawerContainer>
    </div>
  );
};

export default PromoDrawerBananaRepublic;
