# ComponentMaxWidth

- What is the `ComponentMaxWidth`?
  - `ComponentMaxWidth` is a helper for Amplience-configurable components. It allows for the ability to wrap components to specify the default max width.
- When should you use the component?
  - When you want to set the designated max width to a component on a certain page. Ie Homepage, CategoryPage, etc

## Default Behavior

- If the prop `componentType` is not in the json, then the component will default to use the type `categoryBanner` as the value for `componentType`. If the prop `componentType` is in the json, the component will use the value provided.
- If the value of the prop `disabled` is `false`, content will use the designated max width values
- If the value of the prop `disabled` is `true`, content will not use the designated max width values on the container
- If the value for `customStyles` is passed, then the custom styles will be added to the component.

## Properties

### `componentType`

- This value controls the set max widths. The max widths values are set as the following:

```
export const defaultComponentMaxWidths = {
  categoryBanner: 1280,
  categorySEO: {
    desktop: 1280,
    tablet: 768,
  },
  ebb: {
    gap: 1280,
    at: 1280,
    on: 1280,
    br: 1280,
  },
  subCategoryBanner: 1000,
  homepage: {
    gap: 1400,
    at: undefined, // scales indefinitely
    on: 1440,
  },
  categoryBottom: 1000,
  visualNavigation: 1280,
};
```

- As of 05/10/2024, the ebb values are the same as categoryBanner, but the slot is actually called ebb. So, a specific ebb section was added to be more future proof. Additionally, there is a rumor that brands may diverge soon.

### Scalepoint

A scalepoint is the "100% point" of a font size, line height, or spacing scale.
For example, 375 is currently the only mobile scalepoint.
If typography specs show a font size of 12px at 375 view width, and the font scales,
you would expect to see a font size smaller than 12px at 374 view width,
and larger than 12px at 376 view width.
Not to be confused with a max-width, which limits when the size is allowed to scale and when it must stop.
A scalepoint merely sets a standard for scaling.

## Example Usages in other components

```jsx
  <ComponentMaxWidth
    componentType={"subCategoryBanner"}
    customStyles={{background:'red'}}
  >
    <Component></Component>
  </ComponentMaxWidth>

  <ComponentMaxWidth
    componentType={"subCategoryBanner"}
    disabled={true}
  >
    <Component></Component>
  </ComponentMaxWidth>

  <ComponentMaxWidth
    componentType={"categorySEO"}
  >
    <Component></Component>
  </ComponentMaxWidth>
```

Wrap your component with the `ComponentMaxWidth` component and pass the specified componentType prop and it will correctly set the component's max width.
