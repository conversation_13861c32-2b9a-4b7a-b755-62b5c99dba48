// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import ImageOverlayCard, { CTAConfig } from '.';
import { imageOverlayCards } from '../../content-types/VisualNavigationCarouselV2/__fixtures__/withImageOverlay-data';

describe('ImageOverlayCard', () => {
  it('should render with props', () => {
    const { cta, description, desktopVerticalAlignment, heading, image } = imageOverlayCards[0];
    const buttonConfig: CTAConfig = { cta, size: 'xs' };
    const { container } = render(
      <ImageOverlayCard
        backgroundImage={image}
        ctaButton={buttonConfig}
        description={description}
        heading={heading}
        height={511.5}
        verticalAlignment={desktopVerticalAlignment}
        width={250}
      />
    );
    expect(container).toMatchSnapshot();
  });

  describe('in Mobile', () => {
    it('should match snapshots', () => {
      const { cta, description, heading, image, mobileVerticalAlignment } = imageOverlayCards[0];
      const buttonConfig: CTAConfig = { cta, size: 'xs' };
      const { container } = render(
        <ImageOverlayCard
          backgroundImage={image}
          ctaButton={buttonConfig}
          description={description}
          heading={heading}
          verticalAlignment={mobileVerticalAlignment}
        />
      );
      expect(container).toMatchSnapshot();
    });
  });
});
