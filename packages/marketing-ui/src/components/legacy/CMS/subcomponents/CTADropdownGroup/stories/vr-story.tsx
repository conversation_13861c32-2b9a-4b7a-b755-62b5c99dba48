// @ts-nocheck
'use client';
import React from 'react';
import { Meta, StoryFn } from '@storybook/react';
import README from '../../CTADropdown/README.mdx';
import CTADropdownGroup from '..';
import { defaultData } from '../__fixtures__';

const withBackground = (storyFn: () => JSX.Element) => <div css={{ padding: '10px', background: '#CCCCCC' }}>{storyFn()}</div>;

export default {
  title: 'Common/JSON Components (Marketing)/CMS/CTADropdownGroup',
  decorators: [withBackground],
  parameters: {
    docs: { page: README },
    knobs: { escapeHTML: false },
    sandbox: true,
  },
} as Meta<typeof CTADropdownGroup>;

export const VisualRegressionTestsForCTADropdownGroup: StoryFn = (): JSX.Element => (
  <div
    style={{
      fontSize: '20px',
    }}
  >
    <div style={{ fontSize: 25, fontWeight: 'bold', paddingBottom: 20 }}>Visual Regression - CTA Dropdown Group</div>
    <CTADropdownGroup {...defaultData} />
  </div>
);
