// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CTADropdownGroup should match snapshot on desktop 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-1 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2:hover,
.emotion-2:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-2:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-2>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-2>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-3 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 svg path {
  fill: currentColor;
}

.emotion-4 svg rect {
  fill: currentColor;
}

.emotion-6 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-7 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-7:last-child {
  border: none;
}

.emotion-8 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="ctaDropdownWrapper"
      >
        <button
          aria-expanded="false"
          class="emotion-2"
          color="dark"
        >
          <span
            class="emotion-3"
            data-id="cta-dropdown-label"
          >
            dummy
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 10.5 10.5"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                  fill="#003764"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="emotion-5"
        >
          <ul
            aria-hidden="true"
            class="emotion-6"
          >
            <li
              class="emotion-7"
            >
              <a
                breakpoint="desktop"
                class="emotion-8"
                href="/buy"
                target="_self"
              >
                buy
              </a>
            </li>
            <li
              class="emotion-7"
            >
              <a
                breakpoint="desktop"
                class="emotion-8"
                href="/sell"
                target="_self"
              >
                sell
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="ctaDropdownWrapper"
      >
        <button
          aria-expanded="false"
          class="emotion-2"
          color="dark"
        >
          <span
            class="emotion-3"
            data-id="cta-dropdown-label"
          >
            bloop
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 10.5 10.5"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                  fill="#003764"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="emotion-5"
        >
          <ul
            aria-hidden="true"
            class="emotion-6"
          >
            <li
              class="emotion-7"
            >
              <a
                breakpoint="desktop"
                class="emotion-8"
                href="/whee"
                target="_self"
              >
                whee
              </a>
            </li>
            <li
              class="emotion-7"
            >
              <a
                breakpoint="desktop"
                class="emotion-8"
                href="/whoo"
                target="_self"
              >
                whoo
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="ctaDropdownWrapper"
      >
        <button
          aria-expanded="false"
          class="emotion-2"
          color="dark"
        >
          <span
            class="emotion-3"
            data-id="cta-dropdown-label"
          >
            gap
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 10.5 10.5"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                  fill="#003764"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="emotion-5"
        >
          <ul
            aria-hidden="true"
            class="emotion-6"
          >
            <li
              class="emotion-7"
            >
              <a
                breakpoint="desktop"
                class="emotion-8"
                href="/athleta"
                target="_self"
              >
                athleta
              </a>
            </li>
            <li
              class="emotion-7"
            >
              <a
                breakpoint="desktop"
                class="emotion-8"
                href="/old-navy"
                target="_self"
              >
                old navy
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CTADropdownGroup should match snapshot on mobile 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 12px;
  -webkit-align-items: normal;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
}

.emotion-1 {
  position: relative;
}

.emotion-2 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-2:focus {
  outline: none;
}

.emotion-2>span {
  padding: 1px 0;
}

.emotion-2>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-2>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-3 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-4 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 svg path {
  fill: currentColor;
}

.emotion-4 svg rect {
  fill: currentColor;
}

.emotion-6 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-7 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-7:last-child {
  border: none;
}

.emotion-8 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="ctaDropdownWrapper"
      >
        <button
          aria-expanded="false"
          class="emotion-2"
          color="dark"
        >
          <span
            class="emotion-3"
            data-id="cta-dropdown-label"
          >
            dummy
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 10.5 10.5"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                  fill="#003764"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="emotion-5"
        >
          <ul
            aria-hidden="true"
            class="emotion-6"
          >
            <li
              class="emotion-7"
            >
              <a
                breakpoint="mobile"
                class="emotion-8"
                href="/buy"
                target="_self"
              >
                buy
              </a>
            </li>
            <li
              class="emotion-7"
            >
              <a
                breakpoint="mobile"
                class="emotion-8"
                href="/sell"
                target="_self"
              >
                sell
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="ctaDropdownWrapper"
      >
        <button
          aria-expanded="false"
          class="emotion-2"
          color="dark"
        >
          <span
            class="emotion-3"
            data-id="cta-dropdown-label"
          >
            bloop
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 10.5 10.5"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                  fill="#003764"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="emotion-5"
        >
          <ul
            aria-hidden="true"
            class="emotion-6"
          >
            <li
              class="emotion-7"
            >
              <a
                breakpoint="mobile"
                class="emotion-8"
                href="/whee"
                target="_self"
              >
                whee
              </a>
            </li>
            <li
              class="emotion-7"
            >
              <a
                breakpoint="mobile"
                class="emotion-8"
                href="/whoo"
                target="_self"
              >
                whoo
              </a>
            </li>
          </ul>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="ctaDropdownWrapper"
      >
        <button
          aria-expanded="false"
          class="emotion-2"
          color="dark"
        >
          <span
            class="emotion-3"
            data-id="cta-dropdown-label"
          >
            gap
            <span
              aria-hidden="true"
              class="emotion-4"
            >
              <svg
                viewBox="0 0 10.5 10.5"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                  fill="#003764"
                />
              </svg>
            </span>
          </span>
        </button>
        <div
          class="emotion-5"
        >
          <ul
            aria-hidden="true"
            class="emotion-6"
          >
            <li
              class="emotion-7"
            >
              <a
                breakpoint="mobile"
                class="emotion-8"
                href="/athleta"
                target="_self"
              >
                athleta
              </a>
            </li>
            <li
              class="emotion-7"
            >
              <a
                breakpoint="mobile"
                class="emotion-8"
                href="/old-navy"
                target="_self"
              >
                old navy
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;
