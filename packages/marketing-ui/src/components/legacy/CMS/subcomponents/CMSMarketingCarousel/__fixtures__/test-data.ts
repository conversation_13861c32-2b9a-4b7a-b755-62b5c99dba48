// @ts-nocheck
import { CMSMarketingCarouselProps } from '../types';
import { RichTextProps } from '../../../content-types/RichTextBanner/types';

export const defaultData: CMSMarketingCarouselProps = {
  carouselSettings: {
    transition: 'slide',
    type: 'autoplay',
    continuousLoop: true,
    autoplay: {
      pauseOnHover: true,
    },
    animation: {
      ease: false,
    },
    styling: {
      controlsIconsColor: 'secondary',
      pagination: 'mobile',
      hideChevrons: false,
      hideChevronsDesktop: false,
      hideChevronsMobile: false,
    },
  },
};

export const slideData: CMSMarketingCarouselProps = {
  carouselSettings: {
    transition: 'slide',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 3000,
      pauseOnHover: true,
    },
    animation: {
      speed: 1500,
      ease: false,
    },
    styling: {
      controlsIconsColor: 'secondary',
      pagination: 'mobile',
      hideChevrons: false,
    },
  },
};

export const fadeData: CMSMarketingCarouselProps = {
  carouselSettings: {
    transition: 'fade',
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 600,
      pauseOnHover: false,
    },
    animation: {
      speed: 2000,
      ease: true,
    },
    styling: {
      controlsIconsColor: 'secondary',
      pagination: 'hide',
      hideChevrons: true,
    },
  },
};

export const scrollData: CMSMarketingCarouselProps = {
  carouselSettings: {
    transition: 'slide',
    slidesToShow: 3,
    type: 'clickThrough',
    continuousLoop: false,
    autoplay: {
      delay: 600,
      pauseOnHover: false,
    },
    animation: {
      speed: 200,
      ease: true,
    },
    styling: {
      controlsIconsColor: 'primary',
      pagination: 'hide',
      hideChevrons: false,
    },
  },
};

const richTextMeta: RichTextProps['_meta'] = {
  schema: 'https://cms.gap.com/schema/content/v1/rich-text.json',
  deliveryId: '90b17bbc-a32f-4624-bddf-6eed4dbdde43',
};

export const richTextData: RichTextProps = {
  _meta: richTextMeta,
  richText: '<span class="amp-cms--headline-1 amp-cms--text-center amp-cms--avenir-next-regular">Rich Text 🥝 - SIGN UP FOR EMAIL & GET 20% OFF</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#77c1f7',
      to: '#04f3de',
    },
  },
  textPadding: false,
};

export const richTextData2: RichTextProps = {
  _meta: richTextMeta,
  richText: '<span class="amp-cms--headline-1 amp-cms--text-center amp-cms--avenir-next-regular">Rich Text 🍓 - SIGN UP FOR EMAIL & GET 25% OFF</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#77c1f7',
      to: '#08ed50',
    },
  },
  textPadding: false,
};

export const richTextData3: RichTextProps = {
  _meta: richTextMeta,
  richText: '<span class="amp-cms--subhead-1">Rich Text 3 🥒</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#0f0',
      to: '#FFFFFF',
    },
  },
  textPadding: false,
};

export const richTextData4: RichTextProps = {
  _meta: richTextMeta,
  richText: '<span class="amp-cms--subhead-1">Rich Text 4 🥭</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#f00',
      to: '#FFFFFF',
    },
  },
  textPadding: false,
};

export const richTextData5: RichTextProps = {
  _meta: richTextMeta,
  richText: '<span class="amp-cms--subhead-1">Rich Text 5 🫐</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#00F',
      to: '#FFFFFF',
    },
  },
  textPadding: false,
};

export const richTextData6: RichTextProps = {
  _meta: richTextMeta,
  richText: '<span class="amp-cms--subhead-1">Rich Text 6 🍇</span>',
  background: {
    type: 'gradient',
    gradient: {
      from: '#f0f',
      to: '#FFFFFF',
    },
  },
  textPadding: false,
};
