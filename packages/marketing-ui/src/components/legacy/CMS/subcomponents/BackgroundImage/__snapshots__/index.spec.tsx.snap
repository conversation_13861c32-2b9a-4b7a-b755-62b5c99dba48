// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Image Components should match snapshots 1`] = `
.emotion-0 {
  width: 100%;
  height: 100%;
  background-image: url(https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/ONLY_ISM_Pointelle_US?fmt=auto&h=1000);
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      aria-label="Image Alt Text"
      class="emotion-0"
      role="img"
    />
  </div>
</div>
`;

exports[`Image Components should render a div with background-image 1`] = `""`;
