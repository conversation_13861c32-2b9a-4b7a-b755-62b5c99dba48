// @ts-nocheck
'use client';
import React from 'react';
import { select, text, object, boolean } from '@storybook/addon-knobs';
import { Color, Size, Variant } from '../../../../components/ComposableButton/types';
import { AlignmentProps } from '../../../global/types';
import { CtaButton, CtaProps } from '../index';
import README from '../README.mdx';

const withBackground = (storyFn: () => JSX.Element) => <div css={{ padding: '10px' }}>{storyFn()}</div>;

const buttonProps: Pick<CtaProps, 'ctaButton' | 'alignment'> = {
  ctaButton: {
    label: 'Here is a button',
    value: 'test',
  },
  alignment: 'center',
};

const borderStyle: Pick<CtaProps, 'ctaButtonStyling'> = {
  ctaButtonStyling: {
    buttonColor: 'custom',
    buttonStyle: 'border',
    primaryHex: '#2C2B2B',
    secondaryHex: '#FFFFFF',
  },
};

const underlineStyle: Pick<CtaProps, 'ctaButtonStyling'> = {
  ctaButtonStyling: {
    buttonColor: 'dark',
    buttonStyle: 'underline',
  },
};

const solidStyle: Pick<CtaProps, 'ctaButtonStyling'> = {
  ctaButtonStyling: {
    buttonColor: 'custom',
    buttonStyle: 'solid',
    primaryHex: '#ffffff',
    secondaryHex: '#FFA590',
  },
};

const chevronStyle: Pick<CtaProps, 'ctaButtonStyling'> = {
  ctaButtonStyling: {
    buttonStyle: 'chevron',
    primaryHex: '',
    secondaryHex: '',
  },
};

const getButtonPropsBySize = (size: Size) => ({
  label: `I'm ${size}`,
  value: 'n/a',
});

export default {
  title: 'Common/JSON Components (Marketing)/CMS/Utilities/CTAButton',
  decorators: [withBackground],
  parameters: {
    docs: { page: README },
    sandbox: true,
  },
  tags: ['exclude'],
};

const styles: Variant[] = Object.values(Variant);
const buttonColors: Color[] = [Color.dark, Color.light, Color.custom];
const buttonAlignments: AlignmentProps[] = ['center', 'left', 'right'];
const ctaSizes: Size[] = Object.values(Size);

export const ButtonStyle = () => (
  <div>
    {styles.map(itemStyle => (
      <>
        <h2 css={{ fontWeight: 'bold', fontSize: 16 }}>{`${itemStyle} style`}</h2>
        <div
          css={{
            display: 'flex',
            justifyContent: 'space-around',
            background: '#CCCCCC',
            padding: 10,
          }}
        >
          {buttonColors.map(itemColor => (
            <CtaButton
              alignment={buttonProps.alignment}
              ctaButton={{ ...buttonProps.ctaButton, label: itemColor }}
              ctaButtonStyling={{
                buttonColor: itemColor,
                buttonStyle: itemStyle,
                primaryHex: itemColor === 'custom' ? '#FFA590' : '',
                secondaryHex: itemColor === 'custom' ? '#2C2B2B' : '',
              }}
            />
          ))}
        </div>
        <br />
        <br />
      </>
    ))}
  </div>
);

export const Selected = () => (
  <div>
    {styles.map(itemStyle => (
      <>
        <h2 css={{ fontWeight: 'bold', fontSize: 16 }}>{`${itemStyle} style`}</h2>
        <div
          css={{
            display: 'flex',
            justifyContent: 'space-around',
            background: '#CCCCCC',
            padding: 10,
          }}
        >
          {buttonColors.map(itemColor => (
            <CtaButton
              alignment={buttonProps.alignment}
              ctaButton={{ ...buttonProps.ctaButton, label: itemColor }}
              ctaButtonStyling={{
                buttonColor: itemColor,
                buttonStyle: itemStyle,
                primaryHex: itemColor === 'custom' ? '#FFA590' : '',
                secondaryHex: itemColor === 'custom' ? '#2C2B2B' : '',
              }}
              selected
            />
          ))}
        </div>
        <br />
        <br />
      </>
    ))}
  </div>
);

export const Configurable = () => {
  const buttonColor = select('Color', buttonColors, buttonColors[0]);
  const buttonStyle = select('Style', styles, styles[0]);
  const primaryHex = text('Text color (primaryHex):', '#FFA590');
  const secondaryHex = text('Background (secondaryHex)', '#2C2B2B');

  return (
    <div css={{ display: 'flex', flexDirection: 'column', background: '#CCCCCC' }}>
      <CtaButton
        alignment={select('Alignment', buttonAlignments, buttonAlignments[0])}
        ctaButton={{
          label: text('label', buttonProps.ctaButton.label),
          value: text('value', buttonProps.ctaButton.value),
        }}
        ctaButtonStyling={{ buttonColor, buttonStyle, primaryHex, secondaryHex }}
        ctaSize={select('ctaSize', ctaSizes, ctaSizes[0])}
        customCtaStyles={object('Custom Styles', {
          padding: 20,
          margin: 20,
        })}
        fixedWidth={boolean('fixedWidth', false)}
        fullWidth={boolean('fullWidth', false)}
        interactiveStyles={boolean('interactiveStyles', true)}
      />
    </div>
  );
};

export const CustomColor = () => {
  const buttonColor = select('Color', buttonColors, Color.custom);
  const buttonStyle = select('Variant', styles, Variant.solid);
  const primaryHex = text('Text color (primaryHex):', '#FFA590');
  const secondaryHex = text('Background (secondaryHex)', '#2C2B2B');

  return (
    <div style={{ backgroundColor: '#DDD', padding: 16 }}>
      <CtaButton
        alignment={buttonProps.alignment}
        ctaButton={buttonProps.ctaButton}
        ctaButtonStyling={{ buttonColor, buttonStyle, primaryHex, secondaryHex }}
        interactiveStyles
      />
    </div>
  );
};

export const BySize = () => (
  <div css={{ display: 'flex', justifyContent: 'space-between' }}>
    {ctaSizes.map((size: Size) => (
      <CtaButton alignment={buttonProps.alignment} ctaButton={getButtonPropsBySize(size)} ctaButtonStyling={solidStyle.ctaButtonStyling} ctaSize={size} />
    ))}
  </div>
);

export const Underline = () => (
  <div css={{ display: 'flex', flexDirection: 'column', gap: 15 }}>
    {ctaSizes.map((size: Size) => (
      <CtaButton
        alignment={buttonProps.alignment}
        ctaButton={{
          label: `Shop All (${size})`,
          value: `${size}`,
        }}
        ctaButtonStyling={underlineStyle.ctaButtonStyling}
        ctaSize={size}
      />
    ))}
  </div>
);

export const UnderlineWithTextWrap = () => (
  <CtaButton
    alignment={buttonProps.alignment}
    ctaButton={{
      label: `Here is a wrapped button`,
      value: `medium`,
    }}
    ctaButtonStyling={underlineStyle.ctaButtonStyling}
    customCtaStyles={{ width: 100 }}
    variant={Variant.underline}
  />
);

export const BorderWithTextWrap = () => (
  <CtaButton
    alignment={buttonProps.alignment}
    ctaButton={buttonProps.ctaButton}
    ctaButtonStyling={borderStyle.ctaButtonStyling}
    customCtaStyles={{ width: 100 }}
  />
);

export const ChevronWithTextWrap = () => (
  <CtaButton
    alignment={buttonProps.alignment}
    ctaButton={buttonProps.ctaButton}
    ctaButtonStyling={chevronStyle.ctaButtonStyling}
    customCtaStyles={{ width: 100 }}
  />
);
