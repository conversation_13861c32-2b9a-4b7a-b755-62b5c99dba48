// @ts-nocheck
'use client';
import React from 'react';
import { DesktopImageOrIconSizeType, MobileImageOrIconSizeType } from '../../ImageOrIcon/types';
import { useViewportIsLarge } from '../../../../hooks/useViewportIsLarge';
import { convertPxToVw } from '../helpers';
import { AT_ON_MAX_WIDTH_PX, MOBILE_MAX_WIDTH_PX } from '../../ComponentMaxWidth';

export type AthletaGirlLogoProps = {
  color: string;
  height: DesktopImageOrIconSizeType | MobileImageOrIconSizeType;
};

export const AthletaGirlLogo = ({ height, color }: AthletaGirlLogoProps): JSX.Element => {
  const isMobile = !useViewportIsLarge();
  const heightInVw = convertPxToVw(Number(height.replace('px', '')), isMobile ? MOBILE_MAX_WIDTH_PX : AT_ON_MAX_WIDTH_PX);
  return (
    <svg
      aria-labelledby='title'
      css={{
        maxHeight: height,
        height: heightInVw,
      }}
      viewBox='0 0 151 88'
      xmlns='http://www.w3.org/2000/svg'
    >
      <title id='title' lang='en'>
        Athleta Girl Logo
      </title>
      <path
        d='m1780.6 209.4 3.2 7.3h-6.6ZM1770 225h3.5l2.4-5.3h9.2l2.3 5.3h3.5l-10.3-23.1Zm34-18.6V225h-3.2v-18.6h-5v-3.1h13.1v3h-4.9Zm16.7 5.4h9.3v-8.5h3.3V225h-3.3V215h-9.3V225h-3.2v-21.7h3.2v8.5Zm26.6-8.5V222h6.3v3.1h-9.5v-21.7h3.2Zm27 3h-8.7v5.3h8.4v3h-8.4v7.3h8.7v3.1h-12v-21.7h12v3Zm15.8 0v18.7h-3.2v-18.6h-5v-3.1h13.1v3h-4.9Zm20.6 3 3.2 7.3h-6.6ZM1900 225h3.5l2.4-5.3h9.2l2.3 5.3h3.5l-10.3-23.1Zm-98.7 34.6h22.9v.4c0 9.6-2.8 17.4-8.2 22.7a25.8 25.8 0 0 1-19 7.2 24 24 0 0 1-18.8-7.7 30.2 30.2 0 0 1-8.3-21.2c0-8.7 3.5-16.3 8.1-21a29.4 29.4 0 0 1 20.9-8.3 29.7 29.7 0 0 1 14.5 3.5 25.3 25.3 0 0 1 9 8.4l-6.8 4.8a22.7 22.7 0 0 0-6.8-6.4 18.5 18.5 0 0 0-9.8-2.4 20 20 0 0 0-14.7 5.8 22.9 22.9 0 0 0-5.9 15.7 22.7 22.7 0 0 0 5.8 15.3c3.8 4 8.5 5.6 13.9 5.6a16.6 16.6 0 0 0 12.4-5 15.1 15.1 0 0 0 4.6-9.5h-13.8v-7.9Zm40.3-27v35h-8.4v-35h8.4Zm20.8 0c6.7 0 11 1 14.6 3.4 6 4 6.6 10.7 6.6 13.2 0 8-5 14-12.3 15.6l17.3 24h-10.3l-15.9-23h-1.5v23h-8.4v-56.1h9.9Zm-1.5 25.8h2.7c2.3 0 11.8-.2 11.8-9.1 0-8-7.4-8.7-11.5-8.7h-3v17.8Zm43.6-25.7v48h16.3v8.1h-24.8v-56.1h8.5Zm-67 40.7a8.3 8.3 0 1 0 8 8.2 8.2 8.2 0 0 0-8-8.2Zm4.3 10.4.6 2.7-5-4.9 2 4.5-1.2 2.4-.7-6.9-1.4 4.7-2.5 1 3.9-5.7-4 2.7-2.6-.8 6.6-1.9-4.9-.5-1.4-2.3 6.3 2.8-3.4-3.6.4-2.7 3 6.3-.3-5 2-1.7-1.7 6.7 2.9-4h2.6l-5.5 4 4.7-1.2 2.1 1.7-6.8-.5Z'
        data-name='Girl Logo Grey'
        fill={color}
        fillRule='evenodd'
        transform='translate(-1770 -202)'
      />
    </svg>
  );
};
