// @ts-nocheck
'use client';
import { useCallback } from 'react';
import { CSSObject, forBrands, styled } from '@ecom-next/core/react-stitch';
import { useViewportIsLarge } from '../../../hooks';

export const DetailsPrefixWrapper = styled.div(props => {
  const { theme } = props;
  const isLargeVP = useViewportIsLarge();
  const brandStyle = useCallback(
    (): CSSObject =>
      forBrands(theme, {
        on: {
          padding: isLargeVP ? 0 : '7px 10px 8px 0',
        },
        default: {
          padding: 0,
        },
      }) as CSSObject,
    [theme, isLargeVP]
  );
  return {
    display: 'inline-flex',
    ...brandStyle(),
  };
});
