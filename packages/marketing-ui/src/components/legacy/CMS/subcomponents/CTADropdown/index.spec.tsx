import { LARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import { <PERSON><PERSON>, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import userEvent from '@testing-library/user-event';
import * as React from 'react';
import { RenderOptions, act, fireEvent, renderAsync, screen } from 'test-utils';
import { ComposableButton } from '../../../components/ComposableButton/components/index';
import { OnCtaRedesign2024Context } from '../../../contexts/OnCtaRedesign2024Context';
import * as FormatUrlModule from '../../../helper/formatUrl/formatUrl';
import * as jumpToSectionModule from '../../../helper/jumpToSection';
import { setupRender } from '../../../test-helpers/index';
import { PRODUCT_GRID_CLASSNAME } from '../../constants';
import * as CTAButtonModule from '../CTAButton/index';
import * as DropdownButtonLabelModule from './components/DropdownButtonLabel/index';
import {
  ctaData,
  defaultData,
  fixedWidthBorderStyleDarkData,
  fixedWidthBorderStyleLightData,
  fixedWidthSolidStyleDarkData,
  fixedWidthSolidStyleLightData,
  naturalWidthBorderStyleDarkData,
  naturalWidthBorderStyleLightData,
  naturalWidthSolidStyleDarkData,
  naturalWidthSolidStyleLightData,
  naturalWidthUnderlineStyleDarkData,
} from './test-data';
import CTADropdown, { CTADropdownProps, DEFAULT_BUTTON_STYLE } from './index';

jest.mock('../../../helper/jumpToSection', () => {
  const orig = jest.requireActual('../../../helper/jumpToSection');
  return { ...orig, __esModule: true };
});
jest.mock('../../../helper/formatUrl/formatUrl', () => {
  const orig = jest.requireActual('../../../helper/formatUrl/formatUrl');
  return { ...orig, __esModule: true };
});
jest.mock('../CTAButton/index', () => {
  const orig = jest.requireActual('../CTAButton/index');
  return { ...orig, __esModule: true };
});
jest.mock('./components/DropdownButtonLabel/index', () => {
  const orig = jest.requireActual('./components/DropdownButtonLabel/index');
  return { ...orig, __esModule: true };
});

const _render = setupRender(CTADropdown, defaultData);
const _renderCTA = setupRender(CTADropdown, ctaData);

const getButtonElement = (container: HTMLElement) => container.querySelector('button') as HTMLButtonElement;
const getListContainer = (container: HTMLElement) => container.querySelector('ul');
const getFirstListItem = (container: HTMLElement) => container.querySelector('li a') as HTMLAnchorElement;

const mockUseEnabledFeatures = jest.fn();
jest.mock('@ecom-next/core/react-stitch', () => ({
  ...jest.requireActual('@ecom-next/core/react-stitch'),
  useEnabledFeatures: () => mockUseEnabledFeatures(),
}));

describe('CTADropdown', () => {
  describe('on desktop', () => {
    it('should match snapshot for Gap', () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      expect(container).toMatchSnapshot();
    });

    it('should render heading text', () => {
      _render({}, Brands.Gap, LARGE);
      expect(screen.getByRole('button', { name: /Shop Halloween styles long text/ })).toBeInTheDocument();
    });

    it('should render heading text aligned to the center', () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      expect(button).toHaveStyle({ 'text-align': 'left' });
    });

    it('should render DropdownButtonLabel with props', () => {
      const dropdownButtonLabelSpy = jest.spyOn(DropdownButtonLabelModule, 'default');
      _render({}, Brands.Gap, LARGE);
      expect(dropdownButtonLabelSpy).toHaveBeenCalledWith({ heading: defaultData.heading, isOpen: false }, {});
    });

    it('should render list item container hidden', () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const itemsContainer = getListContainer(container);
      expect(itemsContainer).toHaveStyle({ visibility: 'hidden' });
    });

    it('should show list item container on button click', async () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      await act(async () => {
        fireEvent.click(button);
      });
      expect(screen.getByRole('list')).toBeVisible();
    });

    it('should render DropdownButtonLabel isOpen true on button click', async () => {
      const dropdownButtonLabelSpy = jest.spyOn(DropdownButtonLabelModule, 'default');
      const { container } = _render({}, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      await act(async () => {
        fireEvent.click(button);
      });
      expect(dropdownButtonLabelSpy).toHaveBeenCalledWith({ heading: defaultData.heading, isOpen: true }, {});
    });

    it('should render list item container with absolute position', () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const itemsContainer = getListContainer(container);
      expect(itemsContainer).toHaveStyle({ position: 'absolute' });
    });

    it('should render list items', () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const itemsContainer = getListContainer(container);
      expect(itemsContainer?.children.length).toBe(defaultData.items.length);
    });

    it('should render list item content', () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const items = container.querySelectorAll('li a');
      defaultData.items.forEach((item, index) => {
        const node = items[index];
        expect(node).toHaveTextContent(item.label);
        expect(node).toHaveAttribute('href', item.value);
        expect(node).toHaveAttribute('target', '_self');
      });
    });

    it('should render button styles', () => {
      const BACKGROUND_COLOR = 'red';
      const { container } = _render({ buttonStyling: { backgroundColor: BACKGROUND_COLOR } }, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      expect(button).toHaveStyle({ 'background-color': BACKGROUND_COLOR });
    });

    it('should render list item container styles', () => {
      const BACKGROUND_COLOR = '#FFFFFF';
      const { container } = _render({ itemContainerStyles: { backgroundColor: BACKGROUND_COLOR } }, Brands.Gap, LARGE);
      const itemsContainer = getListContainer(container);
      // @ts-ignore
      expect(itemsContainer).toHaveStyleRule('background-color', BACKGROUND_COLOR);
    });

    it('should render item styles', () => {
      const BACKGROUND_COLOR = 'green';
      const { container } = _render({ itemStyle: { backgroundColor: BACKGROUND_COLOR } }, Brands.Gap, LARGE);
      const item = getFirstListItem(container);
      expect(item).toHaveStyle({ 'background-color': BACKGROUND_COLOR });
    });

    it('should render ctaButtonStyling on dropdown variant', () => {
      const ctaButtonStyling: CTAButtonModule.CtaButtonStylingProps = {
        buttonStyle: 'solid',
        buttonColor: 'custom',
        primaryHex: '#BB22BB',
        secondaryHex: '#22BB22',
      };
      const composableButtonSpy = jest.spyOn(ComposableButton as any, 'render');
      _render({ ctaButtonStyling }, Brands.Gap, LARGE);
      expect(composableButtonSpy).toHaveBeenCalledWith(expect.objectContaining({ variant: ctaButtonStyling.buttonStyle }), expect.anything());
    });

    it('should render ctaButtonStyling default button style on dropdown', () => {
      const ctaButtonStyling: CTAButtonModule.CtaButtonStylingProps = {
        buttonStyle: 'border',
        buttonColor: 'custom',
        primaryHex: '#BB22BB',
        secondaryHex: '#22BB22',
      };
      const composableButtonSpy = jest.spyOn(ComposableButton as any, 'render');
      _render({ ctaButtonStyling }, Brands.Gap, LARGE);
      expect(composableButtonSpy).toHaveBeenCalledWith(expect.objectContaining({ variant: DEFAULT_BUTTON_STYLE }), expect.anything());
    });

    it('should call formatUrl', () => {
      const jumpToSectionSpy = jest.spyOn(FormatUrlModule, 'formatUrl');
      _render({}, Brands.Gap, LARGE);
      expect(jumpToSectionSpy).toHaveBeenCalledTimes(defaultData.items.length);
    });

    it('should close if escape key is pressed', async () => {
      const user = userEvent.setup();
      const { container } = _render({}, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      await act(async () => {
        fireEvent.click(button);
      });
      expect(screen.getByRole('list')).toBeVisible();
      await user.keyboard('{Escape}');
      expect(screen.queryByRole('list')).not.toBeInTheDocument();
    });

    it('should close if mouse down is detected outside node', async () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      await act(async () => {
        fireEvent.click(button);
      });
      expect(screen.getByRole('list')).toBeVisible();
      const outsideNode = document.createElement('div');
      container.appendChild(outsideNode);
      fireEvent.mouseDown(outsideNode);
      expect(screen.queryByRole('list')).not.toBeInTheDocument();
    });

    it('should close if an item is clicked', async () => {
      const { container } = _render({}, Brands.Gap, LARGE);
      const button = getButtonElement(container);
      const itemsContainer = getListContainer(container);
      const item = getFirstListItem(container);
      await act(async () => {
        fireEvent.click(button);
      });
      expect(screen.getByRole('list')).toBeVisible();
      await act(async () => {
        fireEvent.click(item);
      });
      expect(itemsContainer).toHaveStyle({ visibility: 'hidden' });
    });

    it('should call jumpToSection on item click', async () => {
      const jumpToSectionSpy = jest.spyOn(jumpToSectionModule, 'jumpToSection');
      _render({}, Brands.Gap, LARGE);
      await act(async () => {
        fireEvent.click(screen.getByRole('button'));
      });
      await act(async () => {
        fireEvent.click(screen.getByRole('link', { name: 'Girls' }));
      });
      expect(jumpToSectionSpy).toHaveBeenCalledWith(defaultData.items[0].value.startsWith('#'), PRODUCT_GRID_CLASSNAME);
    });

    it('should match snapshot for cta button', () => {
      const { container } = _renderCTA({}, Brands.Gap, LARGE);
      expect(container).toMatchSnapshot();
    });

    it('should render cta button', () => {
      const spyCTAButton = jest.spyOn(CTAButtonModule, 'CtaButton');
      _renderCTA({}, Brands.Gap, LARGE);
      expect(spyCTAButton).toHaveBeenCalled();
    });

    it('should render cta button with default button style', () => {
      const ctaButtonStyling: CTAButtonModule.CtaButtonStylingProps = {
        buttonStyle: 'border',
        buttonColor: 'custom',
        primaryHex: '#BB22BB',
        secondaryHex: '#22BB22',
      };

      const spyCTAButton = jest.spyOn(CTAButtonModule, 'CtaButton');
      _renderCTA({ ctaButtonStyling }, Brands.Gap, LARGE);
      expect(spyCTAButton).toHaveBeenCalledWith(
        expect.objectContaining({
          ctaButtonStyling: {
            ...ctaButtonStyling,
            buttonStyle: DEFAULT_BUTTON_STYLE,
          },
        }),
        {}
      );
    });
  });

  describe('on mobile', () => {
    it('should match snapshot for Gap', () => {
      const { container } = _render({}, Brands.Gap, SMALL);
      expect(container).toMatchSnapshot();
    });
    it('should render list item container with relative position', () => {
      const { container } = _render({}, Brands.Gap, SMALL);
      const itemsContainer = getListContainer(container);
      expect(itemsContainer).toHaveStyle({ position: 'relative' });
    });
  });

  describe('accessibility', () => {
    it('should focus CTA Dropdown after closing dropdown list', async () => {
      const user = userEvent.setup();
      const { container } = _render({}, Brands.OldNavy, LARGE);
      const button = getButtonElement(container);

      await user.tab();
      expect(button).toHaveFocus();
      expect(screen.queryByRole('list')).not.toBeInTheDocument();

      await user.keyboard('{Enter}');
      expect(screen.getByRole('list')).toBeVisible();

      await user.tab();
      expect(screen.getByRole('link', { name: 'Girls' })).toHaveFocus();

      await user.keyboard('{Escape}');
      expect(screen.queryByRole('list')).not.toBeInTheDocument();
    });

    it('should NOT focus CTA Dropdown if esc key is pressed before focusing it', async () => {
      const user = userEvent.setup();
      const { container } = _render({}, Brands.OldNavy, LARGE);
      const button = getButtonElement(container);

      await user.keyboard('{Escape}');
      expect(button).not.toHaveFocus();
    });
  });

  describe('for Old Navy', () => {
    it('in desktop should match snapshot', () => {
      const { container } = _render({}, Brands.OldNavy, LARGE);
      expect(container).toMatchSnapshot();
    });

    it('in mobile should match snapshot', () => {
      const { container } = _render({}, Brands.OldNavy, SMALL);
      expect(container).toMatchSnapshot();
    });

    describe('when both on-cta-redesign-2024 feature flag and OnCtaRedesign2024Context are enabled', () => {
      beforeEach(() => {
        mockUseEnabledFeatures.mockReturnValue({
          'on-cta-redesign-2024': true,
        });
      });
      const renderOnCtaDropdown = (data?: CTADropdownProps, options?: RenderOptions<AppState>) => {
        const props = data || defaultData;
        return renderAsync(
          <OnCtaRedesign2024Context.Provider value={{ enable: true }}>
            <StitchStyleProvider brand={Brands.OldNavy}>
              <CTADropdown {...props} />
            </StitchStyleProvider>
          </OnCtaRedesign2024Context.Provider>,
          // @ts-ignore
          { appState: { brandName: Brands.OldNavy }, enabledFeatures: { 'on-cta-redesign-2024': true }, ...options }
        );
      };

      describe('in desktop', () => {
        describe('border style', () => {
          it('should match snapshot', () => {
            const fragment = renderOnCtaDropdown();
            expect(fragment).toMatchSnapshot();
          });

          // FIXME: jest is complaining that querySelectorAll is not a function
          it.skip('label and each menu item should have a height of 44px', () => {
            const container = renderOnCtaDropdown();
            expect(screen.getByRole('button')).toHaveStyle({ height: '44px' });
            const menuItem = container.querySelector('li') as HTMLLIElement;
            expect(menuItem).toHaveStyle({ height: '44px' });
          });

          // FIXME: jest is complaining that querySelectorAll is not a function
          it.skip('should have correct button styles', () => {
            const container = renderOnCtaDropdown();
            expect(screen.getByRole('button')).toHaveStyle({
              'font-weight': '700',
              'line-height': '14px',
              padding: '6px',
              'font-size': '14px',
            });

            const menuItem = container.querySelector('a');
            expect(menuItem).toHaveStyle({
              'font-weight': '700',
              'font-size': '12px',
              'line-height': '1.1666666666666667',
              padding: '6px',
            });
          });

          it('should have correct min and max width values', () => {
            renderOnCtaDropdown();
            expect(screen.getByTestId('ctaDropdownWrapper')).toHaveStyle({
              'min-width': '136px',
              'max-width': '255px',
            });
          });

          it('dark variation should match brand styling', () => {
            renderOnCtaDropdown(fixedWidthBorderStyleDarkData);
            expect(screen.getByRole('button')).toHaveStyle({
              'border-color': '#003764',
              color: '#003764',
              'background-color': '#FFFFFF',
            });
          });

          it('light variation should match brand styling', () => {
            _render(fixedWidthBorderStyleLightData, Brands.OldNavy, LARGE);

            expect(screen.getByRole('button')).toHaveStyle({
              'border-color': '#FFFFFF',
              color: '#FFFFFF',
              'background-color': '#003764',
            });
          });

          const borderStyleNaturalWidthData = [
            {
              theme: 'dark',
              data: naturalWidthBorderStyleDarkData,
            },
            {
              theme: 'light',
              data: naturalWidthBorderStyleLightData,
            },
          ];
          borderStyleNaturalWidthData.forEach(({ theme, data }) => {
            it(`should render cta dropdown with natural width for ${theme} style`, () => {
              renderOnCtaDropdown(data);

              expect(screen.getByTestId('ctaDropdownWrapper')).toHaveStyle({
                'min-width': '136px',
                'max-width': '400px',
                width: 'fit-content',
              });
            });
          });
        });

        describe('solid style', () => {
          const solidStyleNaturalWidthData = [
            {
              theme: 'dark',
              data: naturalWidthSolidStyleDarkData,
            },
            {
              theme: 'light',
              data: naturalWidthSolidStyleLightData,
            },
          ];

          solidStyleNaturalWidthData.forEach(({ theme, data }) => {
            it(`should render cta dropdown with natural width for ${theme} style`, () => {
              renderOnCtaDropdown(data);

              expect(screen.getByTestId('ctaDropdownWrapper')).toHaveStyle({
                'min-width': '136px',
                'max-width': '400px',
                width: 'fit-content',
              });
            });
          });

          it('dark variation should match brand styling', () => {
            renderOnCtaDropdown(fixedWidthSolidStyleDarkData);

            expect(screen.getByRole('button')).toHaveStyle({
              'border-color': '#003764',
              color: '#FFFFFF',
              'background-color': '#003764',
            });
          });

          it('light variation should match brand styling', () => {
            renderOnCtaDropdown(fixedWidthSolidStyleLightData);

            expect(screen.getByRole('button')).toHaveStyle({
              'border-color': '#FFFFFF',
              color: '#003764',
              'background-color': '#FFFFFF',
            });
          });
        });

        describe('natural width underline style', () => {
          it('should match snapshot', () => {
            const fragment = renderOnCtaDropdown(naturalWidthUnderlineStyleDarkData);
            expect(fragment).toMatchSnapshot();
          });

          it('should have correct min and max width values', () => {
            renderOnCtaDropdown(naturalWidthUnderlineStyleDarkData);
            expect(screen.getByTestId('ctaDropdownWrapper')).toHaveStyle({
              'min-width': '120px',
              'max-width': '400px',
            });
          });
        });
      });

      describe('in mobile', () => {
        describe('border style', () => {
          it('should match snapshot', () => {
            const fragment = renderOnCtaDropdown(defaultData, {
              // @ts-ignore
              breakpoint: SMALL,
            });
            expect(fragment).toMatchSnapshot();
          });

          it('should have correct min and max width values', () => {
            renderOnCtaDropdown(defaultData, {
              // @ts-ignore
              breakpoint: SMALL,
            });
            expect(screen.getByTestId('ctaDropdownWrapper')).toHaveStyle({
              'min-width': '136px',
              'max-width': '359px',
            });
          });

          // FIXME: jest is complaining that querySelectorAll is not a function
          it.skip('label and each menu item should have a height of 44px', () => {
            const container = renderOnCtaDropdown(defaultData, {
              // @ts-ignore
              breakpoint: SMALL,
            });
            expect(screen.getByRole('button')).toHaveStyle({ height: '44px' });

            const menuItem = container.querySelector('li') as HTMLLIElement;
            expect(menuItem).toHaveStyle({ height: '44px' });
          });

          // FIXME: jest is complaining that querySelectorAll is not a function
          it.skip('should have correct button styles', () => {
            const container = renderOnCtaDropdown(defaultData, {
              // @ts-ignore
              breakpoint: SMALL,
            });
            expect(screen.getByRole('button')).toHaveStyleRules({
              'font-weight': '700',
              'line-height': '14px',
              padding: '6px',
              'font-size': '14px',
            });

            const menuItem = container.querySelector('a');
            expect(menuItem).toHaveStyle({
              'font-weight': '700',
              'font-size': '12px',
              'line-height': '1.1666666666666667',
              padding: '6px',
            });
          });
        });

        describe('natural width underline style', () => {
          it('should match snapshot', () => {
            const fragment = renderOnCtaDropdown(naturalWidthUnderlineStyleDarkData, {
              // @ts-ignore
              breakpoint: SMALL,
            });
            expect(fragment).toMatchSnapshot();
          });
          it('should have correct min and max width values', () => {
            renderOnCtaDropdown(naturalWidthUnderlineStyleDarkData, {
              // @ts-ignore
              breakpoint: SMALL,
            });
            expect(screen.getByTestId('ctaDropdownWrapper')).toHaveStyle({
              'min-width': '120px',
              'max-width': '300px',
            });
          });
        });
      });
    });
  });
});
