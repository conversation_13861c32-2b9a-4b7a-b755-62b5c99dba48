// @ts-nocheck
'use client';
import React from 'react';
import IIIFImage from '../../components/IIIFImage';
import { AdvanceImageData } from '../../global/types';
import { AmplienceResponsiveImage } from '../AmplienceResponsiveImage';

interface ImageData {
  image: {
    _meta: {
      schema: string;
    };
    defaultHost: string;
    endpoint: string;
    name: string;
    id: string;
  };
  altText: string;
  variations: Array<{
    variation: string;
    poi?: {
      x: number;
      y: number;
    };
  }>;
  fliph: boolean;
  flipv: boolean;
  enableChroma: boolean;
  chromaQuality: number;
}

type Content = {
  _meta: {
    name: string;
    schema: 'https://cms.gap.com/schema/content/v1/advanced-image-poc.json';
    deliveryId: string;
  };
  imageAmplience?: Array<AdvanceImageData>;
  imageOL?: Array<ImageData>;
};

type ImageType = 'amplience' | 'iiif';

const getImageType = (content: Content): ImageType[] => {
  const types: ImageType[] = [];
  if (content.imageAmplience && content.imageAmplience.length > 0) {
    types.push('amplience');
  }
  if (content.imageOL && content.imageOL.length > 0) {
    types.push('iiif');
  }
  return types;
};

export type AdvancedImageProps = {
  content: Content;
  displaySize?: { width?: number; height?: number };
  aspectRatio?: string;
  useOrangeLogic?: boolean;
};

const AdvancedImage: React.FC<AdvancedImageProps> = ({ content, displaySize = { width: 0, height: 0 }, aspectRatio, useOrangeLogic = false }) => {
  const imageTypes = getImageType(content);

  return (
    <div>
      {useOrangeLogic &&
        imageTypes.includes('iiif') &&
        content.imageOL?.map(imageData => (
          <IIIFImage
            key={imageData.image.id}
            aspectRatio={aspectRatio}
            displaySize={displaySize}
            format='jpg'
            imageData={imageData}
            quality='default'
            region='full'
            rotation={0}
            size='max'
          />
        ))}

      {!useOrangeLogic &&
        imageTypes.includes('amplience') &&
        content.imageAmplience?.map(imageData => (
          <AmplienceResponsiveImage
            key={imageData.image?.id}
            aspectRatio={aspectRatio}
            displaySize={{
              width: displaySize.width || 0,
              height: displaySize.height || 0,
            }}
            imageData={imageData}
          />
        ))}
    </div>
  );
};

export default AdvancedImage;
