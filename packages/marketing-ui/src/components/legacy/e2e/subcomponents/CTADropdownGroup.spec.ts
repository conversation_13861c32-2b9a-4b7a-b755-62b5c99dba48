// @ts-nocheck
import { expect, test } from '@playwright/test';
import { getStoryUrl } from '../helpers';

test.describe('CTADropdownGroup', () => {
  const brands = ['at', 'gap', 'on', 'br'];

  brands.forEach(brand =>
    test.skip(`VR Tests for ${brand} content-type`, async ({ page }) => {
      await page.goto(
        getStoryUrl({
          id: 'common-json-components-marketing-cms-ctadropdowngroup--visual-regression-tests-for-cta-dropdown-group',
          brand,
          featureFlags: { 'gap-colors-2022': true, 'at-redesign-2023': true },
        })
      );
      await page.waitForLoadState('networkidle');

      await expect(page).toHaveScreenshot({
        fullPage: true,
        maxDiffPixelRatio: 0.5,
      });
    })
  );
});
