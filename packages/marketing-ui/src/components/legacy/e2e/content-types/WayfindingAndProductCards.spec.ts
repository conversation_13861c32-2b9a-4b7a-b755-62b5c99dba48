// @ts-nocheck
import { test, expect } from '@playwright/test';
import { getStoryUrl, DESKTOP_CHROME, DESKTOP_SAFARI, IPHONE_SAFARI, ANDROID_CHROME } from '../helpers';

test.describe('WayfindingAndProductCards', () => {
  const brand = 'at';
  const featureFlags = {
    'at-redesign-2023': true,
    'at-redesign-2024': true,
  };

  test.skip(`mobile exposed for ${brand}`, async ({ page }, testInfo) => {
    test.skip(
      testInfo.project.name === DESKTOP_CHROME || testInfo.project.name === DESKTOP_SAFARI || testInfo.project.name === IPHONE_SAFARI,
      'Test skipped on desktop and Safari mobile(inconsistent rendering)'
    );

    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'exposed',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });

  test.skip(`desktop responsive for ${brand} with three cards`, async ({ page }, testInfo) => {
    test.skip(testInfo.project.name === IPHONE_SAFARI || testInfo.project.name === ANDROID_CHROME, 'Test skipped on mobile');
    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'visual-regression-tests-for-wayfinding-cards-desktop-three-cards',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });
});

test.describe('WayfindingAndProductCards', () => {
  const brand = 'br';
  const featureFlags = { 'br-colors-2023': true };

  test.skip(`mobile exposed for ${brand}`, async ({ page }, testInfo) => {
    test.skip(testInfo.project.name === DESKTOP_CHROME || testInfo.project.name === DESKTOP_SAFARI, 'Test skipped on desktop');
    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'exposed-br',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });

  test.skip(`desktop responsive for ${brand} with three cards`, async ({ page }, testInfo) => {
    test.skip(testInfo.project.name === IPHONE_SAFARI || testInfo.project.name === ANDROID_CHROME, 'Test skipped on mobile');
    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'visual-regression-tests-for-wayfinding-cards-desktop-three-cards',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });

  test.skip(`mobile scroll for ${brand}`, async ({ page }, testInfo) => {
    test.skip(testInfo.project.name === DESKTOP_CHROME || testInfo.project.name === DESKTOP_SAFARI, 'Test skipped on desktop');
    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'scroll-br',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
      maxDiffPixelRatio: 0.2,
    });
  });
});

test.describe('WayfindingAndProductCards', () => {
  const brand = 'gap';
  const featureFlags = {
    'gap-colors-2022': true,
    'gap-redesign-2024': true,
    'gap-button-redesign-2024': true,
  };

  test.skip(`mobile exposed for ${brand}`, async ({ page }, testInfo) => {
    test.skip(testInfo.project.name === DESKTOP_CHROME || testInfo.project.name === DESKTOP_SAFARI, 'Test skipped on desktop');

    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'exposed',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });

  test.skip(`desktop responsive for ${brand} with three cards`, async ({ page }, testInfo) => {
    test.skip(
      testInfo.project.name === IPHONE_SAFARI || testInfo.project.name === ANDROID_CHROME || testInfo.project.name === DESKTOP_SAFARI,
      'Test skipped on mobile and Safari desktop(inconsistent rendering)'
    );
    await page.goto(
      getStoryUrl({
        brand,
        featureFlags,
        story: {
          subCategory: 'content-types',
          component: 'wayfindingandproductcards',
          storyName: 'visual-regression-tests-for-wayfinding-cards-desktop-three-cards',
        },
      })
    );

    await page.waitForLoadState('networkidle');
    await expect(page).toHaveScreenshot({
      fullPage: true,
      scale: 'css',
    });
  });
});
