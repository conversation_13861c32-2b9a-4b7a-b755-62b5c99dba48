// @ts-nocheck
import { expect, test } from '@playwright/test';
import { BrandInfo, defaultFlags, getStoryUrl } from '../helpers';

test.describe('PartnershipsAndBrandStories', () => {
  test.skip('"with redesign" in at', async ({ page }) => {
    const mergedFlags = { ...defaultFlags, ...{ 'at-redesign-2023': true } };

    const url = getStoryUrl({
      id: 'common-json-components-marketing-content-types-partnershipsandbrandstories--visual-regression-tests',
      featureFlags: mergedFlags,
      brand: 'at',
    });

    await page.goto(url);
    await page.waitForTimeout(10000);

    await expect(page).toHaveScreenshot({
      fullPage: true,
    });
  });
});
