// @ts-nocheck
import { expect, test } from '@playwright/test';
import { DESKTOP_CHROME, DESKTOP_SAFARI, getStoryUrl } from '../helpers';

test.describe('LeftNavTile', () => {
  test.slow();
  const brands = ['at', 'br', 'gap', 'on'];

  brands.forEach(brand =>
    test.skip(`VR Tests for ${brand} content-type`, async ({ page }, testInfo) => {
      test.skip(testInfo.project.name !== DESKTOP_CHROME && testInfo.project.name !== DESKTOP_SAFARI, 'Test Skipped. Unsupported viewport.');
      const responsePromise = page.waitForResponse(response => /\.(bigcontent.io|media.amplience.net|png)/.test.skip(response.url()));
      await page.goto(
        getStoryUrl({
          brand,
          featureFlags: {},
          story: {
            subCategory: 'content-types',
            component: 'leftnavtile',
            storyName: 'visual-regression-tests',
          },
        })
      );

      const text = page.getByText('way to earn points').first();
      await expect(text).toBeVisible();
      await responsePromise;
      await page.waitForTimeout(3000);

      await expect(page).toHaveScreenshot({
        fullPage: true,
        maxDiffPixelRatio: 0.1,
      });
    })
  );
});
