// @ts-nocheck
'use client';
export const generateCombinations = <Props extends Record<string, any>>(input: {
  [K in keyof Props]: Props[K][];
}): Array<Props> => {
  const keys = Object.keys(input) as (keyof Props)[];
  if (keys.length === 0) {
    return [{} as Props];
  }

  const [firstKey, ...restKeys] = keys;
  const firstValues = input[firstKey];
  const restCombinations = generateCombinations(
    Object.fromEntries(restKeys.map(key => [key, input[key]])) as {
      [K in keyof Props]: Props[K][];
    }
  );

  return firstValues.flatMap(firstValue =>
    restCombinations.map(restCombination => ({
      [firstKey]: firstValue,
      ...restCombination,
    }))
  );
};
