import { headlessApi } from '../../../utils/api/headlessApi';
import { getUserSessionsReq, revokeUserSessionsReq } from '../accountSecurityRequests';
import { AccountSecurityReqPayload } from '../types';

jest.mock('../../../utils/api/headlessApi', () => ({
  getCustomerId: jest.fn(),
  headlessApi: jest.fn(),
}));

describe('Account Security Requests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should make a request to get user sessions', async () => {
    const payload = {
      market: 'US',
      ecomApiBaseUrl: 'https://www.gap.com',
      accountSecurityDispatch: jest.fn(),
    } as AccountSecurityReqPayload;
    await getUserSessionsReq(payload);
    expect(headlessApi).toHaveBeenCalledTimes(1);
  });

  it('should make a request to revoke user sessions', async () => {
    const payload = {
      market: 'US',
      ecomApiBaseUrl: 'https://www.gap.com',
      accountSecurityDispatch: jest.fn(),
    } as AccountSecurityReqPayload;
    await revokeUserSessionsReq(payload);
    expect(headlessApi).toHaveBeenCalledTimes(1);
  });
});
