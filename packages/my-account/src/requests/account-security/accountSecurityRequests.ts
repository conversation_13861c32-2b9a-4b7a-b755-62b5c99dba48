import Cookies from 'js-cookie';
import { headlessApi } from '../../utils/api/headlessApi';
import { userSessions } from '../../context/account-security/types';
import { AccountSecurityReqPayload } from './types';

const stripPipes = (val: unknown): string => {
  return (typeof val === 'string' && val.replace(/\|/g, '')) || '';
};

export const getUserSessionsReq = async (payload: AccountSecurityReqPayload): Promise<void> => {
  const { ecomApiBaseUrl, accountSecurityDispatch } = payload;
  const customerId = stripPipes(Cookies.get('cam') ?? '');
  const requestUrl = `${ecomApiBaseUrl}/llsi_sessions/v1/${customerId}`;

  try {
    const response: userSessions = await headlessApi(requestUrl);
    accountSecurityDispatch({ type: 'GET_USER_SESSIONS_SUCCESS', payload: response });
  } catch (err) {
    accountSecurityDispatch({ type: 'GET_USER_SESSIONS_FAIL' });
  }
};

export const revokeUserSessionsReq = async (payload: AccountSecurityReqPayload): Promise<void> => {
  const { ecomApiBaseUrl, accountSecurityDispatch } = payload;
  const customerId = stripPipes(Cookies.get('cam') ?? '');
  const reqPayload = {
    external_customer_id: customerId,
  };
  const requestUrl = `${ecomApiBaseUrl}/llsi_sessions/v1/revocations/users`;
  try {
    await headlessApi(requestUrl, { method: 'post', body: JSON.stringify(reqPayload) });
    accountSecurityDispatch({ type: 'POST_REVOKE_USER_SESSIONS_SUCCESS' });
  } catch (err) {
    accountSecurityDispatch({ type: 'POST_REVOKE_USER_SESSIONS_FAIL' });
  }
};
