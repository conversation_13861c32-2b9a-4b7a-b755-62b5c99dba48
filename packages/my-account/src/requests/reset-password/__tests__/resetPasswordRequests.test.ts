import type { Locale } from '@ecom-next/utils/server';
import { xApi } from '../../../utils/api';
import {
  triggerResetPasswordGetReq,
  triggerResetPasswordPostReq,
  triggerForgotPasswordPostReq,
} from '../resetPasswordRequests';

jest.mock('../../../utils/api', () => ({
  xApi: jest.fn(),
}));

describe('Reset Password Requests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should make a request to get reset password link status', async () => {
    const payload = {
      prl: 'abc',
      resetPasswordDispatch: jest.fn(),
    };
    await triggerResetPasswordGetReq(payload);
    expect(xApi).toHaveBeenCalledTimes(1);
  });

  it('should make a request to post reset password', async () => {
    const payload = {
      externalCustomerId: 'abc',
      tempPassword: 'Example!2345',
      newPassword: 'Example!2345',
      brandAbbr: 'GP',
      locale: 'en_US' as Locale,
      resetPasswordDispatch: jest.fn(),
    };
    await triggerResetPasswordPostReq(payload);
    expect(xApi).toHaveBeenCalledTimes(1);
  });

  it('should make a request to post forgot password', async () => {
    const payload = {
      email: '<EMAIL>',
      brandAbbr: 'GP',
      locale: 'en_US' as Locale,
      referer: 'PROFILE',
      resetPasswordDispatch: jest.fn(),
    };
    await triggerForgotPasswordPostReq(payload);
    expect(xApi).toHaveBeenCalledTimes(1);
  });
});
