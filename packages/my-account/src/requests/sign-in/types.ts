import type { Dispatch } from 'react';
import type { BrandAbbr, Market, Locale } from '@ecom-next/utils/server';
import type { Action } from '../../context/sign-in/types';

export type AuthorizeReqPayload = {
  brandAbbr: BrandAbbr;
  ecomApiBaseUrl: string;
  locale: Locale;
  market: Market;
  signInDispatch: Dispatch<Action>;
  uiv?: string;
};

export type AuthorizeReqUrlPayload = {
  brandAbbr: BrandAbbr;
  ecomApiBaseUrl: string;
  locale: Locale;
  market: Market;
  uiv?: string;
};

export type VerifyEmailReqPayload = {
  email: string;
  signInDispatch: Dispatch<Action>;
  xAppName?: string;
};

export type CreateAccountReqPayload = {
  dateOfBirth?: string;
  emailAddress: string;
  firstName: string;
  isDateOfBirthEnabled?: boolean;
  isEZEnrollEnabled: boolean;
  lastName: string;
  marketCode: Market;
  password: string;
  phoneNumber: string;
  registrationBrand: string;
  signInDispatch: Dispatch<Action>;
  xAppName?: string;
};

export type UniqueIdReqPayload = {
  ecomApiBaseUrl: string;
  email: string;
  flowId: string;
  signInDispatch: Dispatch<Action>;
};

export type SignInPayload = {
  ecomApiBaseUrl: string;
  email: string;
  flowId: string;
  isLLSIEnabled: boolean;
  password: string;
  signInDispatch: Dispatch<Action>;
  thisIsMyDevice: boolean;
};

export type BarclaysPostInPayload = {
  brandCode: string;
  isAuthenticated: boolean;
  isRecognized: boolean;
  market: Market;
  preScreenId: string;
  retUrl: string;
  signInDispatch: Dispatch<Action>;
  siteCode: string;
  targetEnv: string;
  tealiumEvent: string | undefined;
};

export type BarclaysParams = {
  [key: string]: unknown;
};

export type BarclaysPostInUris = {
  prod: string;
  qa: string;
};

export type BarclaysAuthenticatedPayload = {
  payloadType: string;
  postUrl: string;
  response: {
    cardProviderInfo: {
      offerId: string;
    };
    encryptedCustomerInfo: string;
    encryptionInfo: {
      encryptionKeyId: string;
      keyVersion: string;
      sessionKey: string;
      signKeyVersion: string;
      signature: string;
      signatureKeyId: string;
    };
    memberId: string;
    preScreenInfo?: {
      preScreenId: string;
    };
    sessionId: string;
  };
  retUrl: string;
  siteCode: string | undefined;
  targetEnv: string;
};

export type BarclaysGuestPayload = {
  payloadType: string;
  postUrl: string;
  response: {
    SESSION: string;
    offerId: string;
    signature: string;
    signatureKeyId: string;
  };
  retUrl: string;
  siteCode: string | undefined;
  targetEnv: string;
};

export type PostInRedirectPayload = BarclaysAuthenticatedPayload | BarclaysGuestPayload;

export type GuestLoginReqPayload = {
  emailAddress: string;
  signInDispatch: Dispatch<Action>;
  xAppName?: string;
};

export type ResetPasswordOptionsReqPayload = {
  emailAddress: string;
  market: Market;
  signInDispatch: Dispatch<Action>;
};

export type SignOutPayload = {
  isAuthFail: boolean;
  isLLSIEnabled: boolean;
};

export type ResetPasswordReqPayload = {
  brandCode: string;
  emailAddress: string;
  isForceResetPassword?: boolean;
  locale: Locale;
  signInDispatch: Dispatch<Action>;
};

export type ResetPasswordSendCodeReqPayload = {
  emailAddress: string;
  locale: Locale;
  market: Market;
  mfaType: string;
  signInDispatch: Dispatch<Action>;
};

export type ResetPasswordVerifyCodeReqPayload = {
  code: string;
  deviceId: string;
  nonce: string;
  signInDispatch: Dispatch<Action>;
};

export type ResetPasswordFormReqPayload = {
  brandCode: string;
  cam: string;
  locale: Locale;
  newPassword: string;
  otpSession: string;
  signInDispatch: Dispatch<Action>;
};
