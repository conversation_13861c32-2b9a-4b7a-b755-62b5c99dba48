import Cookies from 'js-cookie';
import { ApiError } from '@ecom-next/utils/clientFetch';
import { headlessApi } from '../../utils/api/headlessApi';
import { Action } from '../../context/communication-preferences/types';
import * as ActionTypes from '../../constants/communication-preferences/communicationPreferencesActionTypes';
import type { textNotificationResponse } from '../../context/communication-preferences/types';
import { TextNotificationsReqPayload, SubscribeReqPayload } from './types';

const stripPipes = (val: unknown): string => {
  return (typeof val === 'string' && val.replace(/\|/g, '')) || '';
};

export const getCustomerId = () => {
  return stripPipes(Cookies.get('cam') ?? '');
};

export const triggerTextNotificationReq = async (payload: TextNotificationsReqPayload): Promise<void> => {
  const { body, ecomApiBaseUrl, communicationPreferencesDispatch } = payload;
  const requestUrl = `${ecomApiBaseUrl}/commerce/communication-preference/v2/subscriptions/sms`;
  (body as Record<string, unknown>).externalCustomerId = getCustomerId();
  try {
    const res = await headlessApi(requestUrl, {
      method: 'POST',
      body: JSON.stringify(body),
    });
    communicationPreferencesDispatch({ type: ActionTypes.COMM_PREF_TEXT_NOTIFICATION_SUCCESS, payload: res as textNotificationResponse });
  } catch (err) {
    communicationPreferencesDispatch({ type: ActionTypes.COMM_PREF_TEXT_NOTIFICATION_FAIL, payload: err as ApiError });
  }
};

export const triggerSubscribePostReq = async (payload: SubscribeReqPayload): Promise<void> => {
  const { emailSubscribeBody, emailPreferencesBody, ecomApiBaseUrl, communicationPreferencesDispatch } = payload;
  try {
    await headlessApi<Extract<Action, { type: typeof ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_SUCCESS }>>(
      `${ecomApiBaseUrl}/commerce/communication-preference/v2/preferences/email`,
      {
        method: 'POST',
        body: JSON.stringify(emailPreferencesBody),
      }
    );
    try {
      (emailSubscribeBody as Record<string, unknown>).externalCustomerId = getCustomerId();
      const res = await headlessApi<Extract<Action, { type: typeof ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_SUCCESS }>>(
        `${ecomApiBaseUrl}/commerce/communication-preference/v2/subscriptions/email`,
        {
          method: 'POST',
          body: JSON.stringify(emailSubscribeBody),
        }
      );
      communicationPreferencesDispatch({ type: ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_SUCCESS, payload: res });
    } catch (err) {
      communicationPreferencesDispatch({ type: ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_FAIL, payload: err as ApiError });
    }
  } catch (err) {
    communicationPreferencesDispatch({ type: ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_FAIL, payload: err as ApiError });
  }
};

export const triggerUnsubscribePostReq = async (payload: SubscribeReqPayload): Promise<void> => {
  const { body, ecomApiBaseUrl, communicationPreferencesDispatch } = payload;
  const requestUrl = `${ecomApiBaseUrl}/commerce/communication-preference/v2/unsubscriptions/email`;
  (body as Record<string, unknown>).externalCustomerId = getCustomerId();
  try {
    const res = await headlessApi<Extract<Action, { type: typeof ActionTypes.COMM_PREFS_UNSUBSCRIBE_REQ_SUCCESS }>>(requestUrl, {
      method: 'POST',
      body: JSON.stringify(body),
    });
    communicationPreferencesDispatch({ type: ActionTypes.COMM_PREFS_UNSUBSCRIBE_REQ_SUCCESS, payload: res });
  } catch (err) {
    communicationPreferencesDispatch({ type: ActionTypes.COMM_PREFS_UNSUBSCRIBE_REQ_FAIL, payload: err as ApiError });
  }
};
