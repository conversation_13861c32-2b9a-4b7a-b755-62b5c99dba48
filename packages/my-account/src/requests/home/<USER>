import { ApiError } from '@ecom-next/utils/clientFetch';
import { xApi } from '../../utils/api';
import * as ActionTypes from '../../constants/home/<USER>';
import type { mergeResponse } from '../../context/home/<USER>';
import { BdssMergeReqPayload } from './types';

export const triggerBdssMergeReq = async (payload: BdssMergeReqPayload): Promise<void> => {
  const { homeDispatch, body } = payload;
  try {
    const headers = new Headers();
    headers.set('x-app-name', 'profileUI');

    const response = await xApi('/account-self-service/merge', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body),
    });
    homeDispatch({ type: ActionTypes.BDSS_MERGE_ACCOUNTS_SUCCESS, payload: response as mergeResponse });
  } catch (err) {
    homeDispatch({ type: ActionTypes.BDSS_MERGE_ACCOUNTS_FAIL, payload: err as ApiError });
  }
};
