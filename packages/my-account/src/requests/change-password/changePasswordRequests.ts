import { ApiError } from '@ecom-next/utils/clientFetch';
import { xApi } from '../../utils/api';
import { Action } from '../../context/change-password/types';
import * as ActionTypes from '../../constants/change-password/changePasswordActionTypes';

type Payload = {
  body: {
    brandCode: string;
    locale: string;
    newPassword: string;
    oldPassword: string;
  };
  changePasswordDispatch: React.Dispatch<Action>;
};

export const triggerChangePasswordReq = async (payload: Payload): Promise<void> => {
  const { body, changePasswordDispatch } = payload;
  const headers = new Headers();
  headers.set('x-app-name', 'profileUI');
  try {
    const res = await xApi<Extract<Action, { type: typeof ActionTypes.CHANGE_PASSWORD_REQ_SUCCESS }>>('/change-password', {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });
    changePasswordDispatch({ type: ActionTypes.CHANGE_PASSWORD_REQ_SUCCESS, payload: res });
  } catch (err) {
    changePasswordDispatch({ type: ActionTypes.CHANGE_PASSWORD_REQ_FAIL, payload: err as ApiError });
  }
};
