type BrandAbbr = 'gp' | 'on' | 'br' | 'at' | 'gpfs' | 'brfs';
type Market = 'us' | 'ca';
type BrandMap = {
  readonly [j in Market]: {
    readonly [k in BrandAbbr]: {
      terms: string;
    };
  };
};

export const BRANDS: BrandMap = {
  us: {
    at: {
      terms: 'https://athleta.gap.com/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
    br: {
      terms: 'https://bananarepublic.gap.com/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
    brfs: {
      terms: 'https://bananarepublicfactory.gapfactory.com/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
    gp: {
      terms: 'https://www.gap.com/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
    gpfs: {
      terms: 'https://www.gapfactory.com/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
    on: {
      terms: 'https://oldnavy.gap.com/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
  },
  ca: {
    at: {
      terms: 'https://www.gapcanada.ca/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions-ca.html',
    },
    br: {
      terms: 'https://www.gapcanada.ca/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions-ca.html',
    },
    gp: {
      terms: 'https://www.gapcanada.ca/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions-ca.html',
    },
    on: {
      terms: 'https://www.gapcanada.ca/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions-ca.html',
    },
    gpfs: {
      terms: 'https://www.gapfactory.ca/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
    brfs: {
      terms: 'https://bananarepublicfactory.gapfactory.ca/Asset_Archive/AllBrands/Loyalty/terms/terms-conditions.html',
    },
  },
};
