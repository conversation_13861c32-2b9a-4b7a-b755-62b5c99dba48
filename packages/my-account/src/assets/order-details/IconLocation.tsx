export type IconLocationProps = {
  alt?: string;
  className?: string;
};

export const IconLocation = (props: IconLocationProps) => {
  return (
    <svg
      width='12px'
      height='14px'
      viewBox='0 0 12 14'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      className={props?.className || ''}
    >
      <title>{props?.alt || ''}</title>
      <g id='Checkout-and-Bag-BOPIS-Expansion-Phase-1' stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
        <g id='Shopping-Bag-Module-' transform='translate(-64.000000, -180.000000)'>
          <g id='Module-/-Collapsed-Shopping-Bag' transform='translate(47.000000, 123.000000)'>
            <g id='Product' transform='translate(15.000000, 52.000000)'>
              <g id='Aton-/-Icon-/-16-/-Pin' transform='translate(0.000000, 4.000000)'>
                <g id='icon_pin_24'>
                  <rect id='bounding-box' x='0' y='0' width='16' height='16'></rect>
                  <path
                    d='M8.11519072,14.5632234 L8.11978298,14.5687143 C8.11824798,14.5668446 8.11671758,14.5650142 8.11519074,14.5632234 Z M7.99998683,14.4254759 L12.4169997,9.14415479 C13.122175,8.30387591 13.5,7.31769748 13.5,6.29859712 C13.5,3.66946844 11.0404231,1.5 8,1.5 C4.95957685,1.5 2.5,3.66946844 2.5,6.29859712 C2.5,7.31769748 2.87782495,8.30387591 3.58354176,9.14480108 L7.99998683,14.4254759 Z'
                    id='icon_pin'
                    stroke='#000000'
                    fillRule='nonzero'
                  ></path>
                  <path
                    d='M7.72067511,8.4678176 L7.72612415,8.46899387 C7.91259246,8.51033538 8.09646431,8.51033538 8.29842046,8.46581613 C9.08780612,8.31681849 9.62503569,7.51953874 9.47414037,6.70224708 C9.36983318,6.1194785 8.9076567,5.66435993 8.28838166,5.53196192 C7.48346561,5.36289804 6.70694998,5.88813108 6.5319646,6.71738819 C6.36281013,7.5190126 6.88883088,8.29309775 7.72067511,8.4678176 Z'
                    id='Path'
                    stroke='#000000'
                    fill='#FFFFFF'
                    fillRule='nonzero'
                  ></path>
                </g>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  );
};
