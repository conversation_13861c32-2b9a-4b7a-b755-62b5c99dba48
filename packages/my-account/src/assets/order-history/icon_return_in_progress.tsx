export type IconReturnInProgressProps = {
  alt?: string;
  className?: string;
};

export const IconReturnInProgress = (props: IconReturnInProgressProps) => {
  return (
    <svg
      data-testid='IconReturnInProgress'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={props?.className || ''}
    >
      <title>{props?.alt || ''}</title>
      <path
        d='M24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12ZM2.04 12C2.04 17.5008 6.49924 21.96 12 21.96C17.5008 21.96 21.96 17.5008 21.96 12C21.96 6.49924 17.5008 2.04 12 2.04C6.49924 2.04 2.04 6.49924 2.04 12Z'
        fill='#666666'
      />
      <path
        d='M12 0C14.3734 2.83022e-08 16.6935 0.703788 18.6668 2.02236C20.6402 3.34094 22.1783 5.21509 23.0866 7.4078C23.9948 9.60051 24.2324 12.0133 23.7694 14.3411C23.3064 16.6689 22.1635 18.8071 20.4853 20.4853C18.8071 22.1635 16.6689 23.3064 14.3411 23.7694C12.0133 24.2324 9.60051 23.9948 7.4078 23.0866C5.21509 22.1783 3.34094 20.6402 2.02236 18.6668C0.703788 16.6935 -5.66044e-08 14.3734 0 12H2.04C2.04 13.9699 2.62414 15.8956 3.71856 17.5335C4.81298 19.1714 6.36852 20.448 8.18847 21.2018C10.0084 21.9557 12.011 22.1529 13.9431 21.7686C15.8752 21.3843 17.6499 20.4357 19.0428 19.0428C20.4357 17.6499 21.3843 15.8752 21.7686 13.9431C22.1529 12.011 21.9557 10.0084 21.2018 8.18847C20.448 6.36852 19.1714 4.81298 17.5335 3.71856C15.8956 2.62414 13.9699 2.04 12 2.04V0Z'
        fill='#666666'
      />
      <path d='M16.7146 16.16V13.1086C16.7146 12.004 15.8192 11.1086 14.7146 11.1086H8.16602' stroke='#666666' strokeWidth='2' />
      <path d='M10.1086 8L7 11.1086L10.1086 14.2171' stroke='#666666' strokeWidth='2' />
    </svg>
  );
};
