export type IconStatusUnavailableProps = {
  alt?: string;
  className?: string;
};

export const IconStatusUnavailable = (props: IconStatusUnavailableProps) => {
  return (
    <svg
      data-testid='IconStatusUnavailable'
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={props?.className || ''}
    >
      <title>{props?.alt || ''}</title>
      <path
        d='M24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0C18.6274 0 24 5.37258 24 12ZM2.04 12C2.04 17.5008 6.49924 21.96 12 21.96C17.5008 21.96 21.96 17.5008 21.96 12C21.96 6.49924 17.5008 2.04 12 2.04C6.49924 2.04 2.04 6.49924 2.04 12Z'
        fill='#BEC1C2'
      />
    </svg>
  );
};
