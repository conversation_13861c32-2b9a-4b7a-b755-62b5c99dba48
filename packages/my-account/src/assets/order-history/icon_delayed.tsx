export type IconDelayedProps = {
  alt?: string;
  className?: string;
};

export const IconDelayed = (props: IconDelayedProps) => {
  return (
    <svg data-testid='IconDelayed' width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg' className={props?.className || ''}>
      <title>{props?.alt || ''}</title>
      <path
        d='M24 12C24 14.3734 23.2962 16.6935 21.9776 18.6668C20.6591 20.6402 18.7849 22.1783 16.5922 23.0866C14.3995 23.9948 11.9867 24.2324 9.65892 23.7694C7.33114 23.3064 5.19295 22.1635 3.51472 20.4853C1.83649 18.8071 0.693599 16.6689 0.230577 14.3411C-0.232446 12.0133 0.00519403 9.60051 0.913446 7.4078C1.8217 5.21509 3.35977 3.34094 5.33316 2.02236C7.30655 0.703788 9.62662 -2.83022e-08 12 0V2.04C10.0301 2.04 8.10443 2.62414 6.46652 3.71856C4.82861 4.81298 3.55201 6.36852 2.79816 8.18847C2.04431 10.0084 1.84707 12.011 2.23138 13.9431C2.61569 15.8752 3.56429 17.6499 4.95722 19.0428C6.35015 20.4357 8.12485 21.3843 10.0569 21.7686C11.989 22.1529 13.9916 21.9557 15.8115 21.2018C17.6315 20.448 19.187 19.1714 20.2814 17.5335C21.3759 15.8956 21.96 13.9699 21.96 12H24Z'
        fill='#BEC1C2'
      />
      <path
        d='M24 12C24 13.5759 23.6896 15.1363 23.0866 16.5922C22.4835 18.0481 21.5996 19.371 20.4853 20.4853C19.371 21.5996 18.0481 22.4835 16.5922 23.0866C15.1363 23.6896 13.5758 24 12 24C10.4241 24 8.86369 23.6896 7.40778 23.0865C5.95187 22.4835 4.629 21.5996 3.5147 20.4853C2.4004 19.371 1.51649 18.0481 0.913435 16.5922C0.310381 15.1363 -4.27059e-06 13.5758 0 12L2.04 12C2.04 13.3079 2.29762 14.6031 2.79815 15.8115C3.29869 17.0199 4.03233 18.1179 4.9572 19.0428C5.88207 19.9676 6.98005 20.7013 8.18846 21.2018C9.39686 21.7024 10.692 21.96 12 21.96C13.308 21.96 14.6031 21.7024 15.8115 21.2018C17.0199 20.7013 18.1179 19.9677 19.0428 19.0428C19.9677 18.1179 20.7013 17.0199 21.2018 15.8115C21.7024 14.6031 21.96 13.308 21.96 12H24Z'
        fill='#0B2C63'
      />
      <path d='M12 5V12H19' stroke='#0B2C63' strokeWidth='2' />
      <path
        d='M13.8033 0.136266C15.4595 0.388004 17.0446 0.983716 18.4566 1.88506L17.359 3.6046C16.187 2.85648 14.8713 2.36204 13.4967 2.1531L13.8033 0.136266Z'
        fill='#0B2C63'
      />
      <path
        d='M19.8088 2.88835C20.8116 3.74776 21.6657 4.7668 22.3365 5.90439L20.5793 6.94065C20.0225 5.99645 19.3136 5.15064 18.4813 4.43733L19.8088 2.88835Z'
        fill='#0B2C63'
      />
      <path
        d='M23.1675 7.60824C23.4551 8.33974 23.6703 9.09775 23.8097 9.87133L21.802 10.2332C21.6863 9.59113 21.5078 8.96199 21.269 8.35484L23.1675 7.60824Z'
        fill='#0B2C63'
      />
    </svg>
  );
};
