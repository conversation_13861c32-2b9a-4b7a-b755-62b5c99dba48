'use client';
import React, { createContext, useReducer, ReactNode } from 'react';
import * as ActionTypes from '../../constants/communication-preferences/communicationPreferencesActionTypes';
import type { Action, State, ContextProps } from './types';

const initialState: State = {
  textNotificationsPostReq: {
    isSuccess: false,
    isFail: false,
  },
  subscribePostReq: {
    isFail: false,
    isSuccess: false,
  },
  unsubscribePostReq: {
    isFail: false,
    isSuccess: false,
  },
};

const CommunicationPreferencesContext = createContext<ContextProps | undefined>(undefined);
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case ActionTypes.COMM_PREF_TEXT_NOTIFICATION_SUCCESS: {
      return {
        ...state,
        textNotificationsPostReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ActionTypes.COMM_PREF_TEXT_NOTIFICATION_FAIL: {
      return {
        ...state,
        textNotificationsPostReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.COMM_PREF_TEXT_NOTIFICATION_RESET: {
      return {
        ...state,
        textNotificationsPostReq: {
          isSuccess: false,
          isFail: false,
        },
      };
    }
    case ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_SUCCESS:
      return {
        ...state,
        subscribePostReq: {
          isFail: false,
          isSuccess: true,
        },
      };
    case ActionTypes.COMM_PREFS_SUBSCRIBE_REQ_FAIL:
      return {
        ...state,
        subscribePostReq: {
          isFail: true,
          isSuccess: false,
        },
      };
    case ActionTypes.RESET_COMM_PREFS_SUBSCRIBE_REQ:
      return {
        ...state,
        subscribePostReq: {
          isFail: false,
          isSuccess: false,
        },
      };
    case ActionTypes.COMM_PREFS_UNSUBSCRIBE_REQ_SUCCESS:
      return {
        ...state,
        unsubscribePostReq: {
          isFail: false,
          isSuccess: true,
        },
      };
    case ActionTypes.COMM_PREFS_UNSUBSCRIBE_REQ_FAIL:
      return {
        ...state,
        unsubscribePostReq: {
          isFail: true,
          isSuccess: false,
        },
      };
    case ActionTypes.RESET_COMM_PREFS_UNSUBSCRIBE_REQ:
      return {
        ...state,
        unsubscribePostReq: {
          isFail: false,
          isSuccess: false,
        },
      };
    default:
      throw new Error(`Unknown action: ${action}`);
  }
};

const CommunicationPreferencesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [communicationPreferencesState, communicationPreferencesDispatch] = useReducer(reducer, initialState);
  return (
    <CommunicationPreferencesContext.Provider value={{ communicationPreferencesState, communicationPreferencesDispatch }}>
      {children}
    </CommunicationPreferencesContext.Provider>
  );
};

export { CommunicationPreferencesProvider, CommunicationPreferencesContext };
