'use client';
import React, { createContext, useReducer, ReactNode } from 'react';
import {
  GET_ADDRESSES_SUCCESS,
  GET_ADDRESSES_FAIL,
  ADD_ADDRESSES_SUCCESS,
  ADD_ADDRESSES_FAIL,
  EDIT_ADDRESSES_SUCCESS,
  EDIT_ADDRESSES_FAIL,
  DELETE_ADDRESSES_SUCCESS,
  DELETE_ADDRESSES_FAIL,
  DEFAULT_ADDRESSES_SUCCESS,
  DEFAULT_ADDRESSES_FAIL,
  AVS_ADDRESSES_SUCCESS,
  AVS_ADDRESSES_FAIL,
  RESET_SHIPPING,
  RESET_DELETE_ADDRESS_REQ,
  RESET_AVS_ADDRESS_REQ,
} from '../../constants/shipping-addresses/shippingAddressesActionTypes';
import type { Action, State, ContextProps } from './types';
import { processGetAddressResponse, avsTransform } from './reducerUtil';

const initialState: State = {
  addresses: [],
  suggestedAddresses: [],
  isAvsMatch: false,
  getAddressesReq: {
    isFail: false,
    isSuccess: false,
  },
  addAddressesReq: {
    isSuccess: false,
    isFail: false,
  },
  editAddressesReq: {
    isSuccess: false,
    isFail: false,
  },
  deleteAddressesReq: {
    isSuccess: false,
    isFail: false,
  },
  defaultAddressesReq: {
    isSuccess: false,
    isFail: false,
  },
  avsAddressesReq: {
    isSuccess: false,
    isFail: false,
  },
};

const ShippingContext = createContext<ContextProps | undefined>(undefined);
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case GET_ADDRESSES_SUCCESS: {
      return {
        ...state,
        addresses: processGetAddressResponse(action.payload, action?.useMockData),
        getAddressesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case GET_ADDRESSES_FAIL: {
      return {
        ...state,
        addresses: [],
        getAddressesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ADD_ADDRESSES_SUCCESS: {
      return {
        ...state,
        addAddressesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ADD_ADDRESSES_FAIL: {
      return {
        ...state,
        addAddressesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case EDIT_ADDRESSES_SUCCESS: {
      return {
        ...state,
        editAddressesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case EDIT_ADDRESSES_FAIL: {
      return {
        ...state,
        editAddressesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case DELETE_ADDRESSES_SUCCESS: {
      return {
        ...state,
        deleteAddressesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case DELETE_ADDRESSES_FAIL: {
      return {
        ...state,
        deleteAddressesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case AVS_ADDRESSES_SUCCESS: {
      const formatedAvsResponse = avsTransform(action.payload.res, action.payload.body);
      return {
        ...state,
        suggestedAddresses: formatedAvsResponse?.suggestedAddresses,
        isAvsMatch: formatedAvsResponse?.matchAccuracy === 'MATCH_FOUND' || false,
        avsAddressesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case AVS_ADDRESSES_FAIL: {
      return {
        ...state,
        avsAddressesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case DEFAULT_ADDRESSES_SUCCESS: {
      return {
        ...state,
        defaultAddressesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case DEFAULT_ADDRESSES_FAIL: {
      return {
        ...state,
        defaultAddressesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case RESET_SHIPPING: {
      return {
        ...state,
        getAddressesReq: {
          isFail: false,
          isSuccess: false,
        },
        addAddressesReq: {
          isSuccess: false,
          isFail: false,
        },
        editAddressesReq: {
          isSuccess: false,
          isFail: false,
        },
        deleteAddressesReq: {
          isSuccess: false,
          isFail: false,
        },
        defaultAddressesReq: {
          isSuccess: false,
          isFail: false,
        },
      };
    }
    case RESET_DELETE_ADDRESS_REQ: {
      return {
        ...state,
        deleteAddressesReq: {
          isSuccess: false,
          isFail: false,
        },
      };
    }
    case RESET_AVS_ADDRESS_REQ: {
      return {
        ...state,
        avsAddressesReq: {
          isSuccess: false,
          isFail: false,
        },
        isAvsMatch: false,
        suggestedAddresses: [],
      };
    }

    default:
      throw new Error(`Unknown action: ${action}`);
  }
};

const ShippingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [shippingState, shippingDispatch] = useReducer(reducer, initialState);
  return <ShippingContext.Provider value={{ shippingState, shippingDispatch }}>{children}</ShippingContext.Provider>;
};

export { ShippingProvider, ShippingContext };
