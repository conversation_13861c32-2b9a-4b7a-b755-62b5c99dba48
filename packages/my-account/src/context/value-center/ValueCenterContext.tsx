'use client';
import React, { createContext, useReducer, ReactNode } from 'react';
import {
  RESET_VALUE_CENTER_STATE,
  GET_VALUE_CENTER_MEMBERSHIP_SUCCESS,
  GET_VALUE_CENTER_MEMBERSHIP_FAIL,
  GET_VALUE_CENTER_POINTS_ACTIVITY_SUCCESS,
  GET_VALUE_CENTER_POINTS_ACTIVITY_FAIL,
  GET_VALUE_CENTER_EARN_AND_REDEEM_SUCCESS,
  GET_VALUE_CENTER_EARN_AND_REDEEM_FAIL,
  POST_VALUE_CENTER_DONATION_SUCCESS,
  POST_VALUE_CENTER_DONATION_FAIL,
  POST_VALUE_CENTER_CONVERT_POINTS_SUCCESS,
  POST_VALUE_CENTER_CONVERT_POINTS_FAIL,
  POST_VALUE_CENTER_APPLY_REWARDS_SUCCESS,
  POST_VALUE_CENTER_APPLY_REWARDS_FAIL,
  POST_VALUE_CENTER_REMOVE_APPLIED_REWARDS_SUCCESS,
  POST_VALUE_CENTER_REMOVE_APPLIED_REWARDS_FAIL,
} from '../../constants/value-center/valueCenterActionTypes';
import * as utils from '../../utils/value-center/valueCenterUtils';
import type { Action, State, ContextProps } from './types';

const initialState: State = {
  membership: {
    customerDetail: {
      callFail: false,
    },
    tierData: {
      callFail: false,
    },
  },
  trackPoints: {
    points: [],
  },
  earnAndRedeem: {
    charities: {
      callFail: false,
      charityConfiguration: {
        conversionRate: 0,
        incrementalValue: 0,
        maxPointsPerTransaction: 0,
        minDollarsForDonation: 0,
        minPointsForDonation: 0,
      },
      charityList: [],
    },
    quarterlyRewards: {
      callFail: false,
    },
    loyaltyOffers: {
      callFail: false,
      list: [],
    },
    rewardsStatus: {
      callFail: false,
      list: [],
    },
  },
  getMembershipReq: {
    isSuccess: false,
    isFail: false,
    initialCallMade: false,
    isGetUserRewardsFail: false,
    isGetTierDataFail: false,
  },
  getPointsActivityReq: {
    isSuccess: false,
    isFail: false,
    initialCallMade: false,
  },
  getEarnAndRedeemReq: {
    isSuccess: false,
    isFail: false,
    initialCallMade: false,
    isGetCharitiesFail: false,
    isGetQuarterlyRewardsFail: false,
    isGetLoyaltyOffersFail: false,
    isGetRewardsStatusFail: false,
  },
  postDonationReq: {
    isSuccess: false,
    isFail: false,
  },
  postConvertPointsReq: {
    isSuccess: false,
    isFail: false,
    promoCode: '',
    rewardRedeemptionType: '',
    points: 0,
    errorAlreadyConverted: false,
  },
  postApplyRewardsReq: {
    isSuccess: false,
    isFail: false,
    promoCode: '',
  },
  postRemoveAppliedRewardsReq: {
    isSuccess: false,
    isFail: false,
    promoCode: '',
  },
};

const ValueCenterContext = createContext<ContextProps | undefined>(undefined);
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case RESET_VALUE_CENTER_STATE: {
      return initialState;
    }
    case GET_VALUE_CENTER_MEMBERSHIP_SUCCESS: {
      const newCustomerDetail = action?.payload?.response?.customerDetail ? action.payload.response.customerDetail : state.membership.customerDetail;
      const newTierData = action?.payload?.response?.tierData ? action.payload.response.tierData : state.membership.tierData;
      const newIsGetUserRewardsFail =
        typeof action?.payload?.response?.customerDetail?.callFail !== 'undefined' ? !!action.payload.response.customerDetail.callFail : false;
      const newIsGetTierDataFail = typeof action?.payload?.response?.tierData?.callFail !== 'undefined' ? !!action.payload.response.tierData.callFail : false;
      return {
        ...state,
        membership: {
          customerDetail: newCustomerDetail,
          tierData: newTierData,
        },
        getMembershipReq: {
          isSuccess: true,
          isFail: false,
          initialCallMade: true,
          isGetUserRewardsFail: newIsGetUserRewardsFail,
          isGetTierDataFail: newIsGetTierDataFail,
        },
      };
    }
    case GET_VALUE_CENTER_MEMBERSHIP_FAIL: {
      return {
        ...state,
        getMembershipReq: {
          isSuccess: false,
          isFail: true,
          initialCallMade: true,
          isGetUserRewardsFail: true,
          isGetTierDataFail: true,
        },
      };
    }
    case GET_VALUE_CENTER_POINTS_ACTIVITY_SUCCESS: {
      const newPoints = action?.payload?.response?.points ? action.payload.response.points : state.trackPoints.points;
      return {
        ...state,
        trackPoints: {
          points: newPoints,
        },
        getPointsActivityReq: {
          isSuccess: true,
          isFail: false,
          initialCallMade: true,
        },
      };
    }
    case GET_VALUE_CENTER_POINTS_ACTIVITY_FAIL: {
      return {
        ...state,
        getPointsActivityReq: {
          isSuccess: false,
          isFail: true,
          initialCallMade: true,
        },
      };
    }
    case GET_VALUE_CENTER_EARN_AND_REDEEM_SUCCESS: {
      const newCharities = action?.payload?.response?.charities ? action.payload.response.charities : state.earnAndRedeem.charities;
      const newQuarterlyRewards = action?.payload?.response?.quarterlyRewards ? action.payload.response.quarterlyRewards : state.earnAndRedeem.quarterlyRewards;
      const newLoyaltyOffers = action?.payload?.response?.loyaltyOffers ? action.payload.response.loyaltyOffers : state.earnAndRedeem.loyaltyOffers;
      const newRewardsStatus = action?.payload?.response?.rewardsStatus ? action.payload.response.rewardsStatus : state.earnAndRedeem.rewardsStatus;
      const newIsGetCharitiesFail =
        typeof action?.payload?.response?.charities?.callFail !== 'undefined' ? !!action.payload.response.charities.callFail : false;
      const newIsGetQuarterlyRewardsFail =
        typeof action?.payload?.response?.quarterlyRewards?.callFail !== 'undefined' ? !!action.payload.response.quarterlyRewards.callFail : false;
      const newIsGetLoyaltyOffersFail =
        typeof action?.payload?.response?.loyaltyOffers?.callFail !== 'undefined' ? !!action.payload.response.loyaltyOffers.callFail : false;
      const newIsGetRewardsStatusFail =
        typeof action?.payload?.response?.rewardsStatus?.callFail !== 'undefined' ? !!action.payload.response.rewardsStatus.callFail : false;
      return {
        ...state,
        earnAndRedeem: {
          charities: newCharities,
          quarterlyRewards: newQuarterlyRewards,
          loyaltyOffers: newLoyaltyOffers,
          rewardsStatus: newRewardsStatus,
        },
        getEarnAndRedeemReq: {
          isSuccess: true,
          isFail: false,
          initialCallMade: true,
          isGetCharitiesFail: newIsGetCharitiesFail,
          isGetQuarterlyRewardsFail: newIsGetQuarterlyRewardsFail,
          isGetLoyaltyOffersFail: newIsGetLoyaltyOffersFail,
          isGetRewardsStatusFail: newIsGetRewardsStatusFail,
        },
      };
    }
    case GET_VALUE_CENTER_EARN_AND_REDEEM_FAIL: {
      return {
        ...state,
        getEarnAndRedeemReq: {
          isSuccess: false,
          isFail: true,
          initialCallMade: true,
          isGetCharitiesFail: true,
          isGetQuarterlyRewardsFail: true,
          isGetLoyaltyOffersFail: true,
          isGetRewardsStatusFail: true,
        },
      };
    }
    case POST_VALUE_CENTER_DONATION_SUCCESS: {
      return {
        ...state,
        postDonationReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case POST_VALUE_CENTER_DONATION_FAIL: {
      return {
        ...state,
        postDonationReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case POST_VALUE_CENTER_CONVERT_POINTS_SUCCESS: {
      const newPoints = action?.payload?.response?.addedPoints ? Number(action.payload.response.addedPoints) : 0;
      return {
        ...state,
        postConvertPointsReq: {
          isSuccess: true,
          isFail: false,
          promoCode: action?.payload?.body?.promotionCode || '',
          rewardRedeemptionType: action?.payload?.body?.rewardRedeemptionType || '',
          points: newPoints,
          errorAlreadyConverted: false,
        },
      };
    }
    case POST_VALUE_CENTER_CONVERT_POINTS_FAIL: {
      return {
        ...state,
        postConvertPointsReq: {
          isSuccess: false,
          isFail: true,
          promoCode: action?.payload?.body?.promotionCode || '',
          rewardRedeemptionType: action?.payload?.body?.rewardRedeemptionType || '',
          points: 0,
          errorAlreadyConverted: action?.payload?.response?.status === 422,
        },
      };
    }
    case POST_VALUE_CENTER_APPLY_REWARDS_SUCCESS: {
      const newState = {
        ...state,
        postApplyRewardsReq: {
          isSuccess: true,
          isFail: false,
          promoCode: action?.payload?.response?.promoCode || '',
        },
      };
      if (action?.payload?.response?.status === 'SUCCESS' && action?.payload?.response?.promoCode) {
        newState.earnAndRedeem.rewardsStatus.list = [...state.earnAndRedeem.rewardsStatus.list, action.payload.response.promoCode];
      }
      return newState;
    }
    case POST_VALUE_CENTER_APPLY_REWARDS_FAIL: {
      return {
        ...state,
        postApplyRewardsReq: {
          isSuccess: false,
          isFail: true,
          promoCode: action?.payload?.body?.promoCode || '',
        },
      };
    }
    case POST_VALUE_CENTER_REMOVE_APPLIED_REWARDS_SUCCESS: {
      const newState = {
        ...state,
        postRemoveAppliedRewardsReq: {
          isSuccess: true,
          isFail: false,
          promoCode: action?.payload?.response?.promoCode || '',
        },
      };
      if (action?.payload?.response?.status === 'SUCCESS' && action?.payload?.response?.promoCode) {
        newState.earnAndRedeem.rewardsStatus.list = utils.removeItemAll(state.earnAndRedeem.rewardsStatus.list, action.payload.response.promoCode);
      }
      return newState;
    }
    case POST_VALUE_CENTER_REMOVE_APPLIED_REWARDS_FAIL: {
      return {
        ...state,
        postRemoveAppliedRewardsReq: {
          isSuccess: false,
          isFail: true,
          promoCode: action?.payload?.body?.promoCode || '',
        },
      };
    }
    default:
      throw new Error(`Unknown action: ${action}`);
  }
};

const ValueCenterProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [valueCenterState, valueCenterDispatch] = useReducer(reducer, initialState);
  return <ValueCenterContext.Provider value={{ valueCenterState, valueCenterDispatch }}>{children}</ValueCenterContext.Provider>;
};

export { ValueCenterProvider, ValueCenterContext };
