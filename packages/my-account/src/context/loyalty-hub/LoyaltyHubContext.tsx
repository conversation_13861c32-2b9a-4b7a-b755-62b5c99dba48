'use client';
import { isEqual } from 'lodash';
import { usePathname } from 'next/navigation';
import React, { createContext, useReducer, ReactNode, useEffect } from 'react';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import * as ActionTypes from '../../constants/loyalty-hub/loyaltyHubActionTypes';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import { isObject } from '../../utils/shared';
import hatImg from '../../assets/rewards-marketplace/hat.png'; // temporary placeholder img until API is ready
import type { State, Action, ContextProps } from './types';
import { isBirthdayMonth, extractCustomerOffers, extractBounceBackOffers } from './reducerUtil';

// temporary reward id for the first item in the rewards array until API is ready
const idAtIdxZero = 'a75f7e3d-b8ff-461b-9077-4ddee6bcae5e';

// temporary reward id generator until API is ready
const generateUniqueId = (): string => {
  return typeof crypto?.randomUUID === 'function' ? crypto.randomUUID() : (Math.floor(Math.random() * 1_000_000_000) + 123).toString();
};

const initialState: State = {
  user: {
    dob: '',
    tier: '',
    points: 0,
    nextTier: '',
    loyaltyId: '',
    phoneNumber: '',
    amountSpent: 0,
    rewardsBalance: '',
    isCardHolder: false,
    isBirthdayMonth: false,
    amountToSpendToNextTier: 0,
    pointsToSpendToNextTier: 0,
  },
  offers: {
    customerOffers: [],
    bounceBackOffers: [],
  },
  rewards: Array.from({ length: 16 }, () => ({
    rewardId: '',
    rewardName: '',
    pointsValue: 0,
    imageListPageUrl: '',
    badges: [],
  })),
  pointsActivity: [],
  loyaltyHubLandingReq: {
    isSuccess: false,
    isFail: false,
  },
  loyaltyHubMemberPerksReq: {
    isSuccess: false,
    isFail: false,
  },
  loyaltyHubMyMembershipReq: {
    isSuccess: false,
    isFail: false,
  },
  loyaltyHubRewardsMarketplaceReq: {
    isSuccess: false,
    isFail: false,
  },
};

const LoyaltyHubContext = createContext<ContextProps | undefined>(undefined);
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case ActionTypes.POST_LOYALTY_HUB_LANDING_SUCCESS: {
      return {
        ...state,
        loyaltyHubLandingReq: {
          isSuccess: true,
          isFail: false,
        },
        user: {
          ...state.user,
          dob: action.payload?.customer_info?.date_of_birth || '',
          tier: action.payload?.customer_info?.customer_tier || '',
          points: action.payload?.tier_progress_info?.current_points || 0,
          nextTier: action.payload?.tier_progress_info?.next_tier || '',
          loyaltyId: action.payload?.loyalty_id || '',
          phoneNumber: action.payload?.customer_info?.phone_number || '',
          amountSpent: action.payload?.tier_progress_info?.current_spend_amount || 0,
          isCardHolder: action.payload?.customer_info?.is_card_holder === 'Y',
          isBirthdayMonth: isBirthdayMonth(action.payload?.customer_info?.date_of_birth || ''),
          amountToSpendToNextTier: action.payload?.tier_progress_info?.goal_spend_amount || 0,
          pointsToSpendToNextTier: action.payload?.tier_progress_info?.goal_points || 0,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_LANDING_FAIL: {
      return {
        ...state,
        loyaltyHubLandingReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MEMBER_PERKS_SUCCESS: {
      return {
        ...state,
        loyaltyHubMemberPerksReq: {
          isSuccess: true,
          isFail: false,
        },
        user: {
          ...state.user,
          dob: action.payload?.customer_info?.date_of_birth || '',
          tier: action.payload?.customer_info?.customer_tier || '',
          points: action.payload?.tier_progress_info?.current_points || 0,
          nextTier: action.payload?.tier_progress_info?.next_tier || '',
          loyaltyId: action.payload?.loyalty_id || '',
          phoneNumber: action.payload?.customer_info?.phone_number || '',
          amountSpent: action.payload?.tier_progress_info?.current_spend_amount || 0,
          isCardHolder: action.payload?.customer_info?.is_card_holder === 'Y',
          isBirthdayMonth: isBirthdayMonth(action.payload?.customer_info?.date_of_birth || ''),
          amountToSpendToNextTier: action.payload?.tier_progress_info?.goal_spend_amount || 0,
        },
        offers: {
          customerOffers: extractCustomerOffers(
            Array.isArray(action.payload?.customer_offers) ? action.payload?.customer_offers[0]?.offer_value : [],
            action.payload.locale
          ),
          bounceBackOffers: extractBounceBackOffers(
            Array.isArray(action.payload?.bounceback_offers) ? action.payload?.bounceback_offers : [],
            action.payload.locale,
            action.payload.market
          ),
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MEMBER_PERKS_FAIL: {
      return {
        ...state,
        loyaltyHubMyMembershipReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MY_MEMBERSHIP_SUCCESS: {
      return {
        ...state,
        loyaltyHubMyMembershipReq: {
          isSuccess: true,
          isFail: false,
        },
        user: {
          ...state.user,
          dob: action.payload?.customer_info?.date_of_birth || '',
          tier: action.payload?.customer_info?.customer_tier || '',
          points: action.payload?.tier_progress_info?.current_points || 0,
          nextTier: action.payload?.tier_progress_info?.next_tier || '',
          loyaltyId: action.payload?.loyalty_id || '',
          phoneNumber: action.payload?.customer_info?.phone_number || '',
          amountSpent: action.payload?.tier_progress_info?.current_spend_amount || 0,
          isCardHolder: action.payload?.customer_info?.is_card_holder === 'Y',
          isBirthdayMonth: isBirthdayMonth(action.payload?.customer_info?.date_of_birth || ''),
          amountToSpendToNextTier: action.payload?.tier_progress_info?.goal_spend_amount || 0,
        },
        pointsActivity:
          action.payload?.points_activity?.map(activity => ({
            date: activity?.transaction_date,
            status: activity?.points[0]?.status || '',
            points: activity?.points[0]?.value || 0,
            transactionId: activity?.transaction_number,
            transactionDescription: activity?.transaction_desc,
          })) || [],
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MY_MEMBERSHIP_FAIL: {
      return {
        ...state,
        loyaltyHubMemberPerksReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_REWARDS_MARKETPLACE_SUCCESS: {
      const mockRewards = new Array(16).fill(null).map(() => ({
        id: generateUniqueId(),
        name: 'Gap Logo Baseball Hat in Union Blue',
        points_value: 100,
      }));
      const possibleValues = [50, 100, 200, 250];
      return {
        ...state,
        loyaltyHubRewardsMarketplaceReq: {
          isSuccess: true,
          isFail: false,
        },
        user: {
          ...state.user,
          dob: action.payload?.customer_info?.date_of_birth || '',
          tier: action.payload?.customer_info?.customer_tier || '',
          points: action.payload?.tier_progress_info?.current_points || 0,
          nextTier: action.payload?.tier_progress_info?.next_tier || '',
          loyaltyId: action.payload?.loyalty_id || '',
          phoneNumber: action.payload?.customer_info?.phone_number || '',
          amountSpent: action.payload?.tier_progress_info?.current_spend_amount || 0,
          isCardHolder: action.payload?.customer_info?.is_card_holder === 'Y',
          isBirthdayMonth: isBirthdayMonth(action.payload?.customer_info?.date_of_birth || ''),
          amountToSpendToNextTier: action.payload?.tier_progress_info?.goal_spend_amount || 0,
        },
        // Use mock data for rewards until API is ready
        rewards: mockRewards.map((_elem, idx) => {
          return {
            rewardId: idx === 0 ? idAtIdxZero : generateUniqueId(),
            rewardName: 'Gap Logo Baseball Hat in Union Blue',
            pointsValue: possibleValues[Math.floor(Math.random() * possibleValues.length)],
            imageListPageUrl: hatImg.src, // Use the temporary image until API is ready
            badges: [],
          };
        }),
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_REWARDS_MARKETPLACE_FAIL: {
      return {
        ...state,
        loyaltyHubRewardsMarketplaceReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.RESET_LOYALTY_HUB_REQS: {
      const isMyMembership = action.payload === 'myMembership';
      const isMemberPerks = action.payload === 'memberPerks';
      const isRewardsMarketplace = action.payload === 'rewardsMarketplace';
      if (isMyMembership) {
        return {
          ...state,
          loyaltyHubMyMembershipReq: {
            isSuccess: false,
            isFail: false,
          },
        };
      } else if (isMemberPerks) {
        return {
          ...state,
          loyaltyHubMemberPerksReq: {
            isSuccess: false,
            isFail: false,
          },
        };
      } else if (isRewardsMarketplace) {
        return {
          ...state,
          loyaltyHubRewardsMarketplaceReq: {
            isSuccess: false,
            isFail: false,
          },
        };
      }
      return {
        ...state,
        loyaltyHubLandingReq: {
          isSuccess: false,
          isFail: false,
        },
      };
    }
    case ActionTypes.RESET_LOYALTY_HUB_STATE: {
      return {
        ...initialState,
      };
    }
    default:
      throw new Error(`Unknown action: ${action}`);
  }
};

const LoyaltyHubProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const pathname = usePathname();
  const { market, targetEnv } = usePageContext();
  const featureFlags = useFeatureFlags();
  const [loyaltyHubState, loyaltyHubDispatch] = useReducer(reducer, initialState);

  useEffect(() => {
    const isLoyaltyHubRoute = typeof pathname === 'string' && pathname.startsWith('/my-account/loyalty-hub');
    const isLoyaltyHubEnabled = targetEnv === 'stage' && featureFlags && isObject(featureFlags) && featureFlags['profile-ui-loyalty-hub'];
    const isStateModified = !isEqual(loyaltyHubState, initialState);
    if (isLoyaltyHubEnabled && !isLoyaltyHubRoute && market === 'us' && isStateModified) {
      loyaltyHubDispatch({ type: ActionTypes.RESET_LOYALTY_HUB_STATE });
    }
  }, [pathname]);

  return <LoyaltyHubContext.Provider value={{ loyaltyHubState, loyaltyHubDispatch }}>{children}</LoyaltyHubContext.Provider>;
};

export { LoyaltyHubProvider, LoyaltyHubContext };
