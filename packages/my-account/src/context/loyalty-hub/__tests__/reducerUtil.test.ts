import { isBirthdayMonth, isExpiringSoon } from '../reducerUtil';

describe('isExpiringSoon', () => {
  const fixedDate = new Date('2023-06-15T12:00:00Z');
  beforeAll(() => {
    jest.useFakeTimers({ now: fixedDate });
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('returns true when the expiration date is within 30 days from now', () => {
    expect(isExpiringSoon('2023-07-10T12:00:00Z')).toBe(true);
  });

  it('returns false when the expiration date is more than 30 days from now', () => {
    expect(isExpiringSoon('2023-08-20T12:00:00Z')).toBe(false);
  });

  it('returns false when the expiration date is in the past', () => {
    expect(isExpiringSoon('2023-05-10T12:00:00Z')).toBe(false);
  });

  it('returns false when the expiration date is today', () => {
    expect(isExpiringSoon('2023-06-15T12:00:00Z')).toBe(false);
  });

  it('returns false when an invalid date is supplied', () => {
    expect(isExpiringSoon('invalid-date')).toBe(false);
  });

  it('returns true when the expiration date is exactly 30 days from now', () => {
    expect(isExpiringSoon('2023-07-15T12:00:00Z')).toBe(true);
  });

  it('returns false when the expiration date is just over 30 days from now', () => {
    expect(isExpiringSoon('2023-07-15T12:00:01Z')).toBe(false);
  });
});

describe('isBirthdayMonth', () => {
  const fixedDate = new Date('2023-06-15T12:00:00Z');

  beforeAll(() => {
    jest.useFakeTimers({ now: fixedDate });
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('returns true when the provided birthday month matches the current month', () => {
    expect(isBirthdayMonth('06-19')).toBe(true);
  });

  it('returns false when the provided birthday month does not match the current month', () => {
    expect(isBirthdayMonth('04-19')).toBe(false);
  });

  it('returns false when an empty string is supplied', () => {
    expect(isBirthdayMonth('')).toBe(false);
  });

  it('returns false for an improperly formatted date', () => {
    expect(isBirthdayMonth('invalid-format')).toBe(false);
  });
});
