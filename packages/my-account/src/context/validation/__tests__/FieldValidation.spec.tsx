import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { LocalizationProvider } from '@ecom-next/sitewide/localization-provider';
import { kebabCase } from 'lodash';
import { FieldValidation } from '../FieldValidation';
import { FieldValidationConfig } from '../types';
import { FormValidation } from '../FormValidation';

const mapPropsToDataAtts = (props: { [k: string]: unknown }) =>
  Object.keys(props).reduce(
    (all, key) => ({
      ...all,
      [`data-${kebabCase(key)}`]: typeof props[key] !== 'function' ? String(props[key] ?? '') : '',
    }),
    {}
  );

describe('FieldValidation', () => {
  const renderFieldValidation = (fieldValidationProps?: Partial<FieldValidationConfig>) =>
    render(
      <LocalizationProvider locale='en_US' translations={{ invalid: 'Field is invalid' }} market='us'>
        <FormValidation onSubmit={jest.fn()}>
          <FieldValidation name='test' {...fieldValidationProps}>
            {({ onChange, onInput, ...props }) => (
              <button {...mapPropsToDataAtts(props)} onClick={onChange ?? onInput} data-oninput-compatible={onInput !== undefined} />
            )}
          </FieldValidation>
        </FormValidation>
      </LocalizationProvider>
    );

  describe('rendering', () => {
    it('should pass the required attributes', () => {
      const { getByRole } = renderFieldValidation();
      const domElement = getByRole('button').outerHTML;
      expect(domElement).toContain('data-name="test"');
      expect(domElement).toContain('data-error-message=""');
      expect(domElement).toContain('data-has-error="false"');
    });
  });

  describe('validation', () => {
    it('should validate a custom function', () => {
      const { getByRole } = renderFieldValidation({
        validation: value => (value && String(value).length > 2 ? '' : 'invalid'),
        disableAfterBlurValidation: true,
      });

      expect(getByRole('button')).toBeInTheDocument();

      fireEvent.click(getByRole('button'), { target: { value: 'a' } });
      const domElement1 = getByRole('button').outerHTML;
      expect(domElement1).toContain('data-name="test"');
      expect(domElement1).toContain('data-error-message="Field is invalid"');
      expect(domElement1).toContain('data-has-error="true"');

      fireEvent.click(getByRole('button'), { target: { value: 'something' } });
      const domElement2 = getByRole('button').outerHTML;
      expect(domElement2).toContain('data-name="test"');
      expect(domElement2).toContain('data-error-message=""');
      expect(domElement2).toContain('data-has-error="false"');
    });
  });

  describe('handlers', () => {
    it('should trigger onInput when the field is not compatible with onInput events', () => {
      const { getByRole } = renderFieldValidation({ type: 'text' });
      const button = getByRole('button');
      expect(button).toHaveAttribute('data-oninput-compatible', 'true');
    });

    it('should trigger onChange when the field is not compatible with onInput events', () => {
      const { getByRole } = renderFieldValidation({ type: 'tel' });
      const button = getByRole('button');
      expect(button).toHaveAttribute('data-oninput-compatible', 'true');
    });
  });
});
