import { createContext, useContext } from 'react';
import { FormField, FormValidationContextType } from './types';

const initialState: FormValidationContextType = {
  loading: false,
  formData: {},
  setFormData: () => void 0,
  setFormValue: () => void 0,
  setFormValues: () => void 0,
  setLoading: () => void 0,
  resetForm: () => void 0,
  resetFormErrors: () => void 0,
  resetFormExceptFieldsError: () => void 0,
  resetRequiredFields: () => void 0,
  setRequiredField: () => void 0,
  setFormValidation: () => void 0,
  submitForm: async () => void 0,
  setOnSubmit: async () => void 0,
  validateForm: () => void 0,
  setValidateFormOn: () => void 0,
  hasError: false,
  isClean: false,
  allRequiredFieldsComplete: true,
  _validateFormOn: 0,
};

export const FormValidationContext = createContext<FormValidationContextType>(initialState);

export const useFormValidation = <T extends object>() =>
  useContext(FormValidationContext) as FormValidationContextType & {
    formData: symbol extends keyof T ? never : { [K in keyof T]: FormField<T[K]> };
  };
