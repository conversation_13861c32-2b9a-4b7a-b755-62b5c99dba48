import { render, screen } from '@testing-library/react';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { OrderStatusGroup, OrderStatusGroupProps } from '../OrderStatusGroup';

const initProps = {
  orderStatus: 'shipped',
  itemCount: 1,
  deliveryDate: '2020-09-30T12:13:34-0400',
  sellerId: '',
  storeName: '',
  trackingInfo: [
    {
      trackingNumber: '',
      shippingCarrier: '',
      trackingLink: '#',
    },
  ],
  productItems: [],
};

const renderComponent = (props: OrderStatusGroupProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <BreakpointProvider>
      <LocalizationProvider locale='en-US' translations={translations} market='us'>
        <OrderStatusGroup {...props} />
      </LocalizationProvider>
    </BreakpointProvider>
  );
};

describe('OrderStatusGroup component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders OrderStatusGroup component properly', () => {
    renderComponent(initProps);
    expect(screen.queryByText('1 item', { exact: false })).toBeInTheDocument();
  });
});
