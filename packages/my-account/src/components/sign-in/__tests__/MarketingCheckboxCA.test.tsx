import '@testing-library/jest-dom';
import { render, screen, fireEvent, waitFor } from 'test-utils';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { SignInProvider } from '../../../context/sign-in/SignInContext';
import { MarketingCheckboxCA } from '../MarketingCheckboxCA';

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-CA' translations={translations} market='ca'>
      <SignInProvider>
        <MarketingCheckboxCA brand='gap' marketingOptIn={false} toggleMarketingCheckBox={jest.fn()} />
      </SignInProvider>
    </LocalizationProvider>
  );
};

describe('MarketingCheckBoxCA', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should render Marketing Checkbox', async () => {
    renderComponent();
    expect(screen.getByText(/Check the box below to sign up for marketing emails and get/i)).toBeInTheDocument();
  });

  it('should call toggle function when user clicks checkbox', () => {
    renderComponent();
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);
    waitFor(() => expect(jest.fn()).toHaveBeenCalledTimes(1));
  });
});
