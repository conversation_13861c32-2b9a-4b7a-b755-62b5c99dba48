import '@testing-library/jest-dom';
import datalayer from '@mfe/data-layer';
import { render, screen, fireEvent, waitFor } from 'test-utils';
import { useSearchParams } from 'next/navigation';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { SignInProvider } from '../../../context/sign-in/SignInContext';
import { useSignIn } from '../../../hooks/useSignIn';
import * as requests from '../../../requests/sign-in/signInRequests';
import { Triage } from '../Triage';
import { CreateAccount } from '../CreateAccount';

type Props = {
  isAuthenticated: boolean;
  isBarclaysPostIn: boolean | undefined;
  queryParams: Record<string, string>;
};

const mockReload = jest.fn();
const mockDispatch = jest.fn();
const mockRouterReplace = jest.fn();

jest.mock('../../../hooks/useFeatureFlags', () => ({
  useFeatureFlags: jest.fn(),
}));

jest.mock('js-cookie', () => ({
  get: jest.fn(() => 'acctdob-on'),
}));

jest.mock('../../../hooks/useSignIn');

jest.mock('../../../utils/api', () => ({
  pingApi: jest.fn(),
}));

jest.mock('../../../requests/sign-in/signInRequests', () => ({
  triggerCreateAccountReq: jest.fn(),
  triggerCreateAccountPingReq: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

jest.mock('next/navigation', () => ({
  ...jest.requireActual('next/navigation'),
  usePathname: jest.fn(() => '/my-account/sign-in'),
  useSearchParams: jest.fn().mockReturnValue(new URLSearchParams()),
  useRouter: () => {
    return {
      replace: mockRouterReplace,
    };
  },
}));

const props: Props = {
  queryParams: {},
  isAuthenticated: false,
  isBarclaysPostIn: false,
} as const;

const TEST_EMAIL = '<EMAIL>' as const;
const password = 'Example!2345' as const;
const FIRST_NAME = 'Test' as const;
const LAST_NAME = 'Account' as const;
const SIGN_IN_PATH = '/my-account/sign-in' as const;
const ORIGIN = 'https://secure-www.gap.com' as const;
const TEST_URL = `${ORIGIN}${SIGN_IN_PATH}` as const;
const DOB = '01/01' as const;

const initialState = {
  flowId: 'XMSNNFM',
  signInDispatch: mockDispatch,
  signInState: {
    user: {
      email: TEST_EMAIL,
      isRegistered: false,
    },
    resetPassword: {
      cam: '',
      nonce: '',
      deviceId: '',
      sessionId: '',
    },
    authorizeReq: {
      isFail: false,
      isSuccess: false,
    },
    verifyEmailReq: {
      isFail: false,
      isSuccess: false,
    },
    createAccountReq: {
      isSuccess: false,
      isFail: false,
    },
    createAccountPingReq: {
      isSuccess: false,
      isFail: false,
    },
    uniqueIdReq: {
      isFalse: false,
      isSuccess: true,
    },
    signInReq: {
      isFail: false,
      isSuccess: false,
    },
    guestLoginReq: {
      isSuccess: false,
      isFail: false,
    },
    barclaysPostInReq: {
      isFail: false,
      isGuestFail: false,
      isAuthFail: false,
    },
    resetPasswordReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordOptionsReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordSendCodeReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordVerifyCodeReq: {
      isSuccess: false,
      isFail: false,
      isServiceError: false,
      isInvalidCode: false,
      isExpiredCode: false,
    },
    resetPasswordFormReq: {
      isSuccess: false,
      isFail: false,
      isAlreadyUsed: false,
    },
  },
};

const renderComponent = (modifiedProps?: Props) => {
  const createAccountProps = modifiedProps ?? props;
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <SignInProvider>
        <Triage {...createAccountProps}>
          <CreateAccount {...createAccountProps} startBarclaysApp={jest.fn()} />
        </Triage>
      </SignInProvider>
    </LocalizationProvider>
  );
};

describe('CreateAccount', () => {
  beforeEach(() => {
    global.window = Object.create(window);
    Object.defineProperty(window, 'location', {
      value: {
        href: TEST_URL,
        search: '',
        pathname: '/my-account/sign-in',
        reload: mockReload,
      },
      writable: true,
    });
    (usePageContext as jest.Mock).mockReturnValue({
      brandAbbr: 'gp',
      market: 'us',
      locale: 'en_US',
      ecomApiBaseUrl: 'https://api.gap.com',
    });
    (useSignIn as jest.Mock).mockReturnValue({ ...initialState });
  });

  afterEach(() => {
    jest.restoreAllMocks();
    jest.clearAllMocks();
  });

  it('should render Create Account', async () => {
    renderComponent();
    expect(screen.getByText('Become a Rewards Member')).toBeInTheDocument();
  });

  it('should render barclays create account text when isBarclaysPostIn is true', async () => {
    const searchParams = new Map();
    searchParams.set('creditOffer', 'barclays');
    searchParams.set('sitecode', 'GPSSUNIFTD');
    searchParams.set('mlink', '55278,********,UNIFOOTER_GGR_CARD_ACQ');
    (useSearchParams as jest.Mock).mockReturnValue(searchParams);
    const modifiedProps = {
      ...props,
      isBarclaysPostIn: true,
    };
    renderComponent(modifiedProps);
    const partialLegalText = 'By creating an account, you’re joining the Gap Inc. Rewards Program';
    expect(screen.getByText(new RegExp(partialLegalText))).toBeInTheDocument();
    expect(screen.getByText('Create Account')).toBeInTheDocument();
    expect(screen.getByText('credit card application')).toBeInTheDocument();
    expect(screen.getByText('Setting up an account will help you get the most out of your credit card.')).toBeInTheDocument();
  });

  it('should render Create Account and apply for card button when isBarclaysPostIn is true', () => {
    const searchParams = new Map();
    searchParams.set('creditOffer', 'barclays');
    searchParams.set('sitecode', 'GPSSUNIFTD');
    searchParams.set('mlink', '55278,********,UNIFOOTER_GGR_CARD_ACQ');
    (useSearchParams as jest.Mock).mockReturnValue(searchParams);
    const modifiedProps = {
      ...props,
      isBarclaysPostIn: true,
    };
    renderComponent(modifiedProps);
    expect(screen.getByText('Create Account and Apply for Card')).toBeInTheDocument();
  });

  it('should reload the page when clicking use another email address', () => {
    const spy = jest.spyOn(window.location, 'reload');
    renderComponent();
    const useAnotherEmail = screen.getByText('Use another email address');
    fireEvent.click(useAnotherEmail);
    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('renders Password Panel properly', () => {
    renderComponent();
    expect(screen.getByText(/Your password must include/i)).toBeInTheDocument();
    expect(screen.getByText(/8 to 24 characters/i)).toBeInTheDocument();
    expect(screen.getByText(/A number/i)).toBeInTheDocument();
    expect(screen.getByText(/A lowercase letter/i)).toBeInTheDocument();
    expect(screen.getByText(/A special character/i)).toBeInTheDocument();
    expect(screen.getByText(/An uppercase letter/i)).toBeInTheDocument();
  });

  it('renders Button with correct text', () => {
    renderComponent();
    waitFor(() => {
      expect(screen.getByText('CREATE ACCOUNT')).toBeInTheDocument();
    });
  });

  it('should render Guest Sign In', async () => {
    const modifiedProps = {
      queryParams: {
        targetURL: 'https://secure-www.stage.gaptechol.com/checkout',
      },
      isBarclaysPostIn: false,
      isAuthenticated: false,
    } as const;
    renderComponent(modifiedProps);
    expect(screen.getByText('OR')).toBeInTheDocument();
  });

  it('renders continue as guest button', () => {
    const modifiedProps = {
      queryParams: {
        targetURL: 'https://secure-www.stage.gaptechol.com/checkout',
      },
      isBarclaysPostIn: false,
      isAuthenticated: false,
    } as const;
    renderComponent(modifiedProps);
    waitFor(() => {
      expect(screen.getByText('Continue As Guest')).toBeInTheDocument();
    });
  });

  it('should call createAccountReq on submit', async () => {
    renderComponent();
    const firstNameInput = screen.getByTestId('create-account-firstname-input');
    const lastNameInput = screen.getByTestId('create-account-lastname-input');
    const passwordInput = screen.getByTestId('create-account-password-input');
    const createAccountBtn = screen.getByTestId('create-account-btn');

    await waitFor(() => {
      fireEvent.change(firstNameInput, { target: { value: FIRST_NAME } });
      fireEvent.change(lastNameInput, { target: { value: LAST_NAME } });
      fireEvent.change(passwordInput, { target: { value: password } });
      fireEvent.click(createAccountBtn);
    });
    expect(requests.triggerCreateAccountReq).toHaveBeenCalledTimes(1);
  });

  it('should not show DOB input if flag on and the market is not US', () => {
    (usePageContext as jest.Mock).mockReturnValue({ brandAbbr: 'gp', market: 'ca', locale: 'en_CA', ecomApiBaseUrl: 'https://api.gap.ca' });

    renderComponent();
    expect(screen.queryByTestId('create-account-dob-input')).not.toBeInTheDocument();
  });

  it('should show error in DOB on blue if the value is not in the format MM/DD - incomplete DOB', async () => {
    renderComponent();
    const dobInput = screen.getByTestId('create-account-dob-input');

    await waitFor(() => {
      fireEvent.change(dobInput, { target: { value: '011' } });
      fireEvent.blur(dobInput);
    });
    expect(dobInput).toHaveValue('01/1');
    expect(screen.getByText('Enter a valid date of birth (MM/DD).')).toBeInTheDocument();
  });

  it('should show error in DOB on blue if the value is not in the format MM/DD - wrong month format', async () => {
    renderComponent();
    const dobInput = screen.getByTestId('create-account-dob-input');

    await waitFor(() => {
      fireEvent.change(dobInput, { target: { value: '2020' } });
      fireEvent.blur(dobInput);
    });

    expect(dobInput).toHaveValue('20/20');
    expect(screen.getByText('Enter a valid birth month (MM).')).toBeInTheDocument();
  });

  it('should show error in DOB on blue if the value is not in the format MM/DD - wrong day format', async () => {
    renderComponent();
    const dobInput = screen.getByTestId('create-account-dob-input');

    await waitFor(() => {
      fireEvent.change(dobInput, { target: { value: '2032' } });
      fireEvent.blur(dobInput);
    });

    expect(dobInput).toHaveValue('20/32');
    expect(screen.getByText('Enter a valid date of birth (MM/DD).')).toBeInTheDocument();
  });

  it('should call createAccountReq on submit with DOB', async () => {
    renderComponent();
    const firstNameInput = screen.getByTestId('create-account-firstname-input');
    const lastNameInput = screen.getByTestId('create-account-lastname-input');
    const passwordInput = screen.getByTestId('create-account-password-input');
    const dobInput = screen.getByTestId('create-account-dob-input');
    const createAccountBtn = screen.getByTestId('create-account-btn');

    await waitFor(() => {
      fireEvent.change(firstNameInput, { target: { value: FIRST_NAME } });
      fireEvent.change(lastNameInput, { target: { value: LAST_NAME } });
      fireEvent.change(passwordInput, { target: { value: password } });
      fireEvent.change(dobInput, { target: { value: DOB } });
      fireEvent.click(createAccountBtn);
    });
    expect(requests.triggerCreateAccountReq).toHaveBeenCalledWith({
      dateOfBirth: '0101',
      emailAddress: '<EMAIL>',
      firstName: 'Test',
      isDateOfBirthEnabled: true,
      isEZEnrollEnabled: true,
      lastName: 'Account',
      marketCode: 'us',
      password: password,
      phoneNumber: '',
      registrationBrand: 'gp',
      signInDispatch: mockDispatch,
    });
  });

  it('should call createAccountReq on submit with empty DOB since DOB is an optional field', async () => {
    renderComponent();
    const firstNameInput = screen.getByTestId('create-account-firstname-input');
    const lastNameInput = screen.getByTestId('create-account-lastname-input');
    const passwordInput = screen.getByTestId('create-account-password-input');
    const dobInput = screen.getByTestId('create-account-dob-input');
    const createAccountBtn = screen.getByTestId('create-account-btn');

    await waitFor(() => {
      fireEvent.change(firstNameInput, { target: { value: FIRST_NAME } });
      fireEvent.change(lastNameInput, { target: { value: LAST_NAME } });
      fireEvent.change(passwordInput, { target: { value: password } });
      fireEvent.change(dobInput, { target: { value: '' } });
      fireEvent.click(createAccountBtn);
    });
    expect(requests.triggerCreateAccountReq).toHaveBeenCalledWith({
      dateOfBirth: '',
      emailAddress: '<EMAIL>',
      firstName: 'Test',
      isDateOfBirthEnabled: true,
      isEZEnrollEnabled: true,
      lastName: 'Account',
      marketCode: 'us',
      password: password,
      phoneNumber: '',
      registrationBrand: 'gp',
      signInDispatch: mockDispatch,
    });
  });

  it('should call createAccountPingReq when createAccountReq is successful', async () => {
    const state = { ...initialState };
    state.signInState.createAccountReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(requests.triggerCreateAccountPingReq).toHaveBeenCalledTimes(1);
  });

  it('should fire a tealium link event mtl_views on load of create account', () => {
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    renderComponent(props);
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
  });

  it('should fire a tealium link event on click of create account', () => {
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    renderComponent(props);
    const createAccountBtn = screen.getByTestId('create-account-btn');
    fireEvent.click(createAccountBtn);
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(3);
  });

  it('should fire a barclays tealium link event on click of create account when isBarclaysPostIn is true', () => {
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    const modifiedProps = {
      ...props,
      isBarclaysPostIn: true,
    };
    renderComponent(modifiedProps);
    const createAccountBtn = screen.getByTestId('create-account-btn');
    fireEvent.click(createAccountBtn);
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(3);
  });
});
