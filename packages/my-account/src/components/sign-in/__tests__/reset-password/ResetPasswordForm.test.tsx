import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import datalayer from '@mfe/data-layer';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { useSignIn } from '../../../../hooks/useSignIn';
import { ResetPasswordForm } from '../../reset-password/ResetPasswordForm';
import { SignInProvider } from '../../../../context/sign-in/SignInContext';
import * as requests from '../../../../requests/sign-in/signInRequests';

const setSignInView = jest.fn();
const mockDispatch = jest.fn();

jest.mock('../../../../hooks/useSignIn');

jest.mock('../../../../requests/sign-in/signInRequests', () => ({
  triggerResetPasswordFormReq: jest.fn(),
}));

jest.mock('@sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn().mockReturnValue({
    brandAbbr: 'gp',
    market: 'us',
    locale: 'en_US',
  }),
}));

const TEST_EMAIL = '<EMAIL>' as const;
const MASKED_PHONE = '******9999' as string;

const initialState = {
  flowId: 'XMSNNFM',
  signInDispatch: mockDispatch,
  signInState: {
    user: {
      email: TEST_EMAIL,
      maskedPhoneNumber: MASKED_PHONE,
      isRegistered: false,
    },
    resetPassword: {
      cam: '',
      nonce: '',
      deviceId: '',
      sessionId: '',
    },
    authorizeReq: {
      isFail: false,
      isSuccess: false,
    },
    verifyEmailReq: {
      isFail: false,
      isSuccess: false,
    },
    createAccountReq: {
      isSuccess: false,
      isFail: false,
    },
    createAccountPingReq: {
      isSuccess: false,
      isFail: false,
    },
    uniqueIdReq: {
      isFalse: false,
      isSuccess: true,
    },
    signInReq: {
      isFail: false,
      isSuccess: false,
      isAccountLocked: false,
    },
    guestLoginReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordOptionsReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordSendCodeReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordVerifyCodeReq: {
      isSuccess: false,
      isFail: false,
      isServiceError: false,
      isInvalidCode: false,
      isExpiredCode: false,
    },
    resetPasswordFormReq: {
      isSuccess: false,
      isFail: false,
      isAlreadyUsed: false,
    },
  },
};

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <SignInProvider>
        <ResetPasswordForm setSignInView={setSignInView} />
      </SignInProvider>
    </LocalizationProvider>
  );
};

describe('ResetPasswordForm', () => {
  beforeEach(() => {
    (useSignIn as jest.Mock).mockReturnValue(initialState);
    jest.restoreAllMocks();
    jest.clearAllMocks();
  });

  it('renders reset password form', () => {
    renderComponent();
    expect(screen.getByText(/Reset your Password?/i)).toBeInTheDocument();
  });

  it('renders new password input', () => {
    renderComponent();
    expect(screen.getByText(/New Password/i)).toBeInTheDocument();
  });

  it('renders Password Panel correctly', () => {
    renderComponent();
    expect(screen.getByText(/Your password must include/i)).toBeInTheDocument();
    expect(screen.getByText(/8 to 24 characters/i)).toBeInTheDocument();
    expect(screen.getByText(/A number/i)).toBeInTheDocument();
    expect(screen.getByText(/A lowercase letter/i)).toBeInTheDocument();
    expect(screen.getByText(/A special character/i)).toBeInTheDocument();
    expect(screen.getByText(/An uppercase letter/i)).toBeInTheDocument();
  });

  it('renders confirm password input', () => {
    renderComponent();
    expect(screen.getByText(/Confirm Password/i)).toBeInTheDocument();
  });

  it('renders save password button', () => {
    renderComponent();
    expect(screen.getByText(/Save Password/i)).toBeInTheDocument();
  });

  it('should not invoke triggerResetPasswordFormReq and renders the expected error when password and confirm password do not match', async () => {
    renderComponent();
    const newPasswordInput = screen.getByTestId('rp-new-password-input');
    const confirmPasswordInput = screen.getByTestId('rp-confirm-password-input');
    const savePasswordBtn = screen.getByTestId('reset-password-form-btn');
    await waitFor(async () => {
      userEvent.type(newPasswordInput, 'Test12357!');
      await userEvent.tab();
      userEvent.type(confirmPasswordInput, 'Test12358!');
      await userEvent.tab();
      fireEvent.click(savePasswordBtn);
    });
    expect(screen.getByText(/Password criteria are not met./i)).toBeInTheDocument();
    expect(requests.triggerResetPasswordFormReq).not.toHaveBeenCalled();
  });

  it('should invoke triggerResetPasswordFormReq when passwords are valid and match', async () => {
    renderComponent();
    const newPasswordInput = screen.getByTestId('rp-new-password-input');
    const confirmPasswordInput = screen.getByTestId('rp-confirm-password-input');
    const savePasswordBtn = screen.getByTestId('reset-password-form-btn');
    await waitFor(async () => {
      fireEvent.change(newPasswordInput, { target: { value: 'Test12357!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Test12357!' } });
      fireEvent.click(savePasswordBtn);
    });
    expect(requests.triggerResetPasswordFormReq).toHaveBeenCalledTimes(1);
  });

  it('renders the expected error when reset password form req returns already used error', async () => {
    const state = { ...initialState };
    state.signInState.resetPasswordFormReq.isFail = true;
    state.signInState.resetPasswordFormReq.isAlreadyUsed = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/This password has already been used. Please try again with a new password./i)).toBeInTheDocument();
  });

  it('renders the expected error when reset password form req returns service error', async () => {
    const state = { ...initialState };
    state.signInState.resetPasswordFormReq.isFail = true;
    state.signInState.resetPasswordFormReq.isAlreadyUsed = false;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Something went wrong. Please try again/i)).toBeInTheDocument();
  });

  it('should invoke setSignInView on reset password form req success', async () => {
    const state = { ...initialState };
    state.signInState.resetPasswordFormReq.isFail = false;
    state.signInState.resetPasswordFormReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(setSignInView).toHaveBeenCalledTimes(1);
  });

  it('should fire tealium link event when reset password form req success', async () => {
    const state = { ...initialState };
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    state.signInState.resetPasswordFormReq.isFail = false;
    state.signInState.resetPasswordFormReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
  });

  it('should fire tealium link event when reset password form req fail', async () => {
    const state = { ...initialState };
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    state.signInState.resetPasswordFormReq.isFail = true;
    state.signInState.resetPasswordFormReq.isSuccess = false;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
  });
});
