import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import datalayer from '@mfe/data-layer';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { ResetPassword } from '../../reset-password/ResetPassword';
import { SignInProvider } from '../../../../context/sign-in/SignInContext';
import { useSignIn } from '../../../../hooks/useSignIn';
import * as requests from '../../../../requests/sign-in/signInRequests';

const mockDispatch = jest.fn();
const mockReload = jest.fn();
const setSignInView = jest.fn();
const returnToSignIn = jest.fn();
jest.mock('../../../../hooks/useSignIn');

jest.mock('../../../../requests/sign-in/signInRequests', () => ({
  triggerResetPasswordReq: jest.fn(),
  triggerResetPasswordOptionsReq: jest.fn(),
  triggerResetPasswordSendCodeReq: jest.fn(),
  triggerResetPasswordVerifyCodeReq: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: () => ({
    brandAbbr: 'gp',
    market: 'us',
    locale: 'en_US',
    ecomApiBaseUrl: 'https://api.gap.com',
  }),
}));

const TEST_EMAIL = '<EMAIL>' as const;
const MASKED_PHONE = '******9999' as string;
const SIGN_IN_PATH = '/my-account/sign-in' as const;
const ORIGIN = 'https://secure-www.gap.com' as const;
const TEST_URL = `${ORIGIN}${SIGN_IN_PATH}` as const;

const initialState = {
  flowId: 'XMSNNFM',
  signInDispatch: mockDispatch,
  signInState: {
    user: {
      email: TEST_EMAIL,
      maskedPhoneNumber: MASKED_PHONE,
      isRegistered: false,
    },
    resetPassword: {
      cam: '',
      nonce: '',
      deviceId: '',
      sessionId: '',
    },
    authorizeReq: {
      isFail: false,
      isSuccess: false,
    },
    verifyEmailReq: {
      isFail: false,
      isSuccess: false,
    },
    createAccountReq: {
      isSuccess: false,
      isFail: false,
    },
    createAccountPingReq: {
      isSuccess: false,
      isFail: false,
    },
    uniqueIdReq: {
      isFalse: false,
      isSuccess: true,
    },
    signInReq: {
      isFail: false,
      isSuccess: false,
      isAccountLocked: false,
    },
    guestLoginReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordOptionsReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordSendCodeReq: {
      isSuccess: false,
      isFail: false,
    },
    resetPasswordVerifyCodeReq: {
      isSuccess: false,
      isFail: false,
      isServiceError: false,
      isInvalidCode: false,
      isExpiredCode: false,
    },
    resetPasswordFormReq: {
      isSuccess: false,
      isFail: false,
      isAlreadyUsed: false,
    },
  },
};

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <SignInProvider>
        <ResetPassword setSignInView={setSignInView} returnToSignIn={returnToSignIn} />
      </SignInProvider>
    </LocalizationProvider>
  );
};

describe('ResetPassword', () => {
  beforeEach(() => {
    global.window = Object.create(window);
    Object.defineProperty(window, 'location', {
      value: {
        href: TEST_URL,
        search: '',
        pathname: '/my-account/sign-in',
        reload: mockReload,
      },
      writable: true,
    });
    (useSignIn as jest.Mock).mockReturnValue({ ...initialState });
  });

  afterEach(() => {
    jest.restoreAllMocks();
    jest.clearAllMocks();
  });

  it('should render Reset Password options view onload and make options request', () => {
    const state = { ...initialState };
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Forgot your password?/i)).toBeInTheDocument();
    expect(screen.getByText(/We’ll send you a link or one-time code to reset your password./i)).toBeInTheDocument();
    expect(requests.triggerResetPasswordOptionsReq).toHaveBeenCalledTimes(1);
  });

  it('renders both send code by text and send link by email only when phone is present', () => {
    const state = { ...initialState };
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Send Code By Text/i)).toBeInTheDocument();
    expect(screen.getByText(/Send Link To Email/i)).toBeInTheDocument();
  });

  it('should fire tealium link event when choosing sms', async () => {
    const state = { ...initialState };
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    const smsButton = screen.getByText(/Send Code By Text/i);
    fireEvent.click(smsButton);
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
  });

  it('should fire tealium link event when choosing email', async () => {
    const state = { ...initialState };
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    const emailButton = screen.getByText(/Send Link To Email/i);
    fireEvent.click(emailButton);
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
  });

  it('should fire tealium view event when resetPasswordOptionsReq is success', () => {
    const state = { ...initialState };
    const tealiumViewEventSpy = jest.spyOn(datalayer, 'view');
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(tealiumViewEventSpy).toHaveBeenCalledTimes(1);
  });

  it('renders only send link by email when phone is not present', () => {
    const state = { ...initialState };
    state.signInState.user.maskedPhoneNumber = '';
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.queryByText(/Send Code By Text/i)).not.toBeInTheDocument();
    expect(screen.getByText(/Send Email/i)).toBeInTheDocument();
    expect(screen.getByText(/We’ll send you a link or one-time code to reset your password./i)).toBeInTheDocument();
  });

  it('should invoke triggerResetPasswordSendCodeReq when choosing sms', async () => {
    const state = { ...initialState };
    state.signInState.user.maskedPhoneNumber = MASKED_PHONE;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    (requests.triggerResetPasswordSendCodeReq as jest.Mock).mockResolvedValueOnce({
      status: 200,
    });
    renderComponent();
    expect(screen.getByText(/Send Code By Text/i)).toBeInTheDocument();
    expect(screen.getByText(/Send Link To Email/i)).toBeInTheDocument();
    expect(screen.getByText(/We’ll send you a link or one-time code to reset your password./i)).toBeInTheDocument();
    await waitFor(() => {
      screen.getByText(/Send Code By Text/i).click();
    });
    expect(requests.triggerResetPasswordSendCodeReq).toHaveBeenCalledTimes(1);
  });

  it('should invoke triggerResetPasswordSendCodeReq when choosing sms', async () => {
    const state = { ...initialState };
    state.signInState.user.maskedPhoneNumber = MASKED_PHONE;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    (requests.triggerResetPasswordSendCodeReq as jest.Mock).mockResolvedValueOnce({
      status: 200,
    });
    renderComponent();
    expect(screen.getByText(/Send Code By Text/i)).toBeInTheDocument();
    expect(screen.getByText(/Send Link To Email/i)).toBeInTheDocument();
    expect(screen.getByText(/We’ll send you a link or one-time code to reset your password./i)).toBeInTheDocument();
    await waitFor(() => {
      screen.getByText(/Send Code By Text/i).click();
    });
    expect(requests.triggerResetPasswordSendCodeReq).toHaveBeenCalledTimes(1);
  });

  it('should render the expected error when triggerResetPasswordSendCodeReq fails', async () => {
    const state = { ...initialState };
    state.signInState.user.maskedPhoneNumber = MASKED_PHONE;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    state.signInState.resetPasswordSendCodeReq.isFail = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    (requests.triggerResetPasswordSendCodeReq as jest.Mock).mockRejectedValueOnce({
      status: 500,
    });
    renderComponent();
    expect(screen.getByText(/Unfortunately your password could not be reset. Please re-verify your identity in order to continue./i)).toBeInTheDocument();
  });

  it('should invoke triggerResetPasswordReq when resend link is clicked', async () => {
    const state = { ...initialState };
    state.signInState.resetPasswordReq.isSuccess = true;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Resend Link/i)).toBeInTheDocument();
    await waitFor(() => {
      screen.getByText(/Resend Link/i).click();
    });
    expect(requests.triggerResetPasswordReq).toHaveBeenCalledTimes(1);
  });

  it('should fire tealium view event when resetPasswordReq is success', async () => {
    const state = { ...initialState };
    const tealiumViewEventSpy = jest.spyOn(datalayer, 'view');
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    state.signInState.resetPasswordReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Resend Link/i)).toBeInTheDocument();
    expect(tealiumViewEventSpy).toHaveBeenCalledTimes(2);
  });

  it('should call window.location.reload when return to sign-in is clicked', async () => {
    const state = { ...initialState };
    state.signInState.resetPasswordReq.isSuccess = true;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Return to Sign In/i)).toBeInTheDocument();
    await waitFor(() => {
      screen.getByText(/Return to Sign In/i).click();
    });
    expect(mockReload).toHaveBeenCalledTimes(1);
  });

  it('should render the correct error inline on send reset password request fail for email case', async () => {
    const state = { ...initialState };
    state.signInState.resetPasswordReq.isFail = true;
    state.signInState.resetPasswordReq.isSuccess = false;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Forgot your password?/i)).toBeInTheDocument();
    expect(screen.getByText(/Unfortunately your password could not be reset. Please re-verify your identity in order to continue./i)).toBeInTheDocument();
  });

  it('should render account locked error when isAccountLocked case is true', async () => {
    const state = { ...initialState };
    state.signInState.signInReq.isFail = true;
    state.signInState.resetPasswordReq.isFail = false;
    state.signInState.resetPasswordReq.isSuccess = false;
    state.signInState.signInReq.isAccountLocked = true;
    state.signInState.user.maskedPhoneNumber = MASKED_PHONE;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/Your account has been temporarily locked/i)).toBeInTheDocument();
  });

  it('should fire tealium view event when resetPasswordSendCodeReq is success', () => {
    const state = { ...initialState };
    const tealiumViewEventSpy = jest.spyOn(datalayer, 'view');
    state.signInState.signInReq.isFail = false;
    state.signInState.signInReq.isAccountLocked = false;
    state.signInState.resetPasswordOptionsReq.isSuccess = true;
    state.signInState.resetPasswordSendCodeReq.isSuccess = true;
    (useSignIn as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(tealiumViewEventSpy).toHaveBeenCalledTimes(2);
  });
});
