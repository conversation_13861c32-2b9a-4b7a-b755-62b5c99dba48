import { Button } from '@ecom-next/core/migration/button';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useSignIn } from '../../hooks/useSignIn';
import { triggerGuestLoginReq } from '../../requests/sign-in/signInRequests';
import { fireTealiumLinkTag } from '../../utils/tealium/tealiumUtil';

type Props = {
  isCreateAccountReqInFlight?: boolean;
  isGuestSignInReqInFlight?: boolean;
  isSignInReqInFlight?: boolean;
  setIsGuestSignInReqInFlight: (arg: boolean) => void;
};

export const GuestSignIn = (props: Props): JSX.Element => {
  const { localize } = useLocalize();
  const { signInState, signInDispatch } = useSignIn();
  const { brandAbbr, brandCode } = usePageContext();
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;
  const { isGuestSignInReqInFlight, setIsGuestSignInReqInFlight, isSignInReqInFlight, isCreateAccountReqInFlight } = props;

  const {
    user: { email },
  } = signInState;

  const onGuestSignIn = (event?: React.FormEvent<HTMLFormElement>): void => {
    event?.preventDefault();
    if (!!email && !isGuestSignInReqInFlight && !isSignInReqInFlight && !isCreateAccountReqInFlight) {
      setIsGuestSignInReqInFlight(true);
      fireTealiumLinkTag({
        page: 'Sign In',
        eventName: 'continue_as_guest',
        brandCode,
        brandTealium,
        recognitionStatus: 'unrecognized',
      });
      const payload = {
        emailAddress: email,
        signInDispatch,
      };
      triggerGuestLoginReq(payload);
    }
  };

  return (
    <div data-testid='guest-signin'>
      <div className='inline-flex w-full items-center justify-center'>
        <hr className='my-6 w-full border bg-[#CCCCCC]' />
        <span className='absolute left-1/2 -translate-x-1/2 bg-white px-3 text-base/[19.2px] font-black'>
          {localize('accountLogin.guestLogin.dividerText')}
        </span>
      </div>
      <form onSubmit={onGuestSignIn} noValidate>
        <div>
          <div className='relative mx-auto w-[343px] pb-6'>
            <Button
              className='whitespace-nowrap'
              kind='secondary'
              type='submit'
              fullWidth={true}
              data-testid='continue-guest-btn'
              loadingAnimationStatus={isGuestSignInReqInFlight}
            >
              {localize('accountLogin.guestLogin.continueAsGuestButton')}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};
