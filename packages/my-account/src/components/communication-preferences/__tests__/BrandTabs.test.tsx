import React from 'react';
import { cleanup, render, fireEvent } from '@testing-library/react';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import type { Market } from '@mfe/brand-info';
import BrandTabs, { BrandTabsProps } from '../BrandTabs';

const renderComponent = (props: BrandTabsProps, market: Market) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return (
    <LocalizationProvider locale='en-US' translations={translations} market={market}>
      <BrandTabs {...props} />
    </LocalizationProvider>
  );
};

describe('BrandTabs component', () => {
  afterEach(cleanup);

  it('renders correct navigation tabs for specialty brands', () => {
    const props = {
      baseBrand: 'gp',
      selectedBrand: 'gp',
      onClickSelectedBrand: jest.fn(),
      market: 'us',
    };

    const { getByText } = render(renderComponent(props, 'us'));
    expect(getByText('Gap')).toBeInTheDocument();
    expect(getByText('Old Navy')).toBeInTheDocument();
    expect(getByText('Banana Republic')).toBeInTheDocument();
    expect(getByText('Athleta')).toBeInTheDocument();
  });

  it('renders with selected brand properly', () => {
    const props = {
      baseBrand: '',
      selectedBrand: 'gp',
      onClickSelectedBrand: jest.fn(),
      market: 'us',
    };

    const { getByText } = render(renderComponent(props, 'us'));
    expect(getByText('Gap Factory')).toBeInTheDocument();
    expect(getByText('Banana Republic Factory')).toBeInTheDocument();
  });

  it('renders with different market', () => {
    const props = {
      baseBrand: '',
      selectedBrand: '',
      onClickSelectedBrand: jest.fn(),
      market: 'ca',
    };

    const { getByText } = render(renderComponent(props, 'ca'));
    expect(getByText('Banana Republic Factory')).toBeInTheDocument();
  });

  it('renders with unselected brand properly', () => {
    const props = {
      baseBrand: 'gp',
      selectedBrand: '',
      onClickSelectedBrand: jest.fn(),
      market: 'ca',
    };

    const { getByText } = render(renderComponent(props, 'us'));
    expect(getByText('Gap')).toBeInTheDocument();
    expect(getByText('Old Navy')).toBeInTheDocument();
    expect(getByText('Banana Republic')).toBeInTheDocument();
    expect(getByText('Athleta')).toBeInTheDocument();
  });

  it('calls onClick prop when clicked', () => {
    const props = {
      baseBrand: 'gp',
      selectedBrand: 'at',
      onClickSelectedBrand: jest.fn(),
      market: 'us',
    };

    const { getByText } = render(renderComponent(props, 'us'));
    const buttonElement = getByText('Athleta');
    fireEvent.click(buttonElement);
    expect(props.onClickSelectedBrand).toHaveBeenCalledTimes(1);
    expect(props.onClickSelectedBrand).toHaveBeenCalledWith(props.selectedBrand);
  });

  it('renders correct navigation tabs for factory brands CA', () => {
    const props = {
      baseBrand: 'gpfs',
      selectedBrand: 'gpfs',
      onClickSelectedBrand: jest.fn(),
      market: 'ca',
    };

    const { getByText } = render(renderComponent(props, 'ca'));
    expect(getByText('Gap Factory')).toBeInTheDocument();
    expect(getByText('Banana Republic Factory')).toBeInTheDocument();
  });
});
