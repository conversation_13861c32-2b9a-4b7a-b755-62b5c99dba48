import React, { useState, useEffect } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Button } from '@ecom-next/core/migration/button';
import { Radio } from '@ecom-next/core/legacy/radio';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { EmailInput } from '../common/legacy/EmailInput';
import { validateEmail } from '../../utils/validation/email/emailValidation';
import { SuccessNotification, ErrorNotification } from '../common/legacy/Notifications';
import { useCommunicationPreferences } from '../../hooks/useCommunicationPreferences';
import { triggerUnsubscribePostReq } from '../../requests/communication-preferences/communicationPreferencesRequests';
import * as utils from '../../utils/communication-preferences/communicationPrefsUtil';
import { FormState } from './types';

type FactoryBrands = 'gpfs' | 'brfs';
type SpecialtyBrands = 'gp' | 'on' | 'br' | 'at';

export type UnsubscribeEmailFormProps = {
  selectedBrand: string;
};

const UnsubscribeEmailForm = (props: UnsubscribeEmailFormProps) => {
  const { selectedBrand } = props;
  const { localize } = useLocalize();
  const { communicationPreferencesState, communicationPreferencesDispatch } = useCommunicationPreferences();
  const { unsubscribePostReq } = communicationPreferencesState;
  const [showUnsubscribeMessage, setShowUnsubscribeMessage] = useState(true);
  const { market, locale, brandAbbr, ecomApiBaseUrl } = usePageContext();
  const [isReqInFlight, setIsReqInFlight] = useState(false);

  // setup state
  const [emailAddress, setEmailAddress] = useState<FormState>({ value: '', errorMessage: '', hasError: false });
  const [radioButtons, toggleRadioButtons] = useState({
    one: true,
    all: false,
  });

  const specialtyBrands: readonly SpecialtyBrands[] = ['gp', 'on', 'br', 'at'] as const;
  const factoryBrands: readonly FactoryBrands[] = ['gpfs', 'brfs'] as const;

  // setup params
  const brandFullName = utils.brandMapping[selectedBrand] || '';
  const radioSpanClasses =
    '[&_span]:cursor-pointer [&_span]:box-border [&_span]:inline-block [&_span]:align-middle [&_span]:text-[#666] [&_span]:text-sm [&_span]:not-italic [&_span]:font-normal [&_span]:leading-5 [&_span]:m-0 [&_span]:p-0 [&_span]:border-0 [&_span]:font-sourcesans';

  useEffect(() => {
    setEmailAddress({ value: '', errorMessage: '', hasError: false });
    toggleRadioButtons({
      one: true,
      all: false,
    });
    setShowUnsubscribeMessage(false);
  }, [selectedBrand]);

  const resetUnSubscribeReqState = (): void => {
    communicationPreferencesDispatch({ type: 'RESET_COMM_PREFS_UNSUBSCRIBE_REQ' });
  };

  const onEmailChange = (email: string): void => {
    setEmailAddress((prevState: FormState) => ({
      ...prevState,
      value: email,
    }));
  };

  const onEmailBlur = (email: string, errorMessage: string): void => {
    setEmailAddress(() => ({
      value: email,
      errorMessage: localize(errorMessage),
      hasError: !!errorMessage,
    }));
  };

  const onSubmit = (e: React.FormEvent): void => {
    e?.preventDefault();
    const email = emailAddress?.value;
    const trimmedEmail = typeof email === 'string' ? email.replace(/\s/g, '') : '';
    const errorMessage = validateEmail(trimmedEmail);
    setEmailAddress(() => ({
      value: trimmedEmail,
      errorMessage: localize(errorMessage),
      hasError: !!errorMessage,
    }));
    if (!isReqInFlight && trimmedEmail !== '' && errorMessage === '') {
      const brandList = [];
      const isUnsubscribeAll = radioButtons.all;
      if (isUnsubscribeAll) {
        const brands = specialtyBrands.includes(brandAbbr as SpecialtyBrands) ? specialtyBrands : factoryBrands;
        brands.forEach((brand: SpecialtyBrands[number]) => {
          brandList.push(brand?.toUpperCase());
        });
      } else {
        brandList.push(selectedBrand?.toUpperCase());
      }
      const payload = {
        body: {
          locale,
          emailAddress: trimmedEmail,
          brandList,
          market: market?.toUpperCase(),
          mainSource: 'profileui',
          subSource: 'communication-preferences',
        },
        ecomApiBaseUrl,
        communicationPreferencesDispatch,
      };
      resetUnSubscribeReqState();
      setIsReqInFlight(true);
      triggerUnsubscribePostReq(payload);
    }
  };

  useEffect(() => {
    if (unsubscribePostReq.isSuccess || unsubscribePostReq.isFail) {
      setIsReqInFlight(false);
      setEmailAddress({ value: '', errorMessage: '', hasError: false });
      toggleRadioButtons({
        one: true,
        all: false,
      });
      setShowUnsubscribeMessage(true);
    }
  }, [unsubscribePostReq.isSuccess, unsubscribePostReq.isFail]);

  return (
    <div className='mb-12 mt-9 flex flex-col border-t border-solid border-t-[#ccc] pt-9'>
      {unsubscribePostReq.isSuccess && showUnsubscribeMessage ? (
        <SuccessNotification className='[&_*]:font-sourcesans w-full' message={localize('communicationPreferences.unsubscribe.successNotification')} />
      ) : null}
      {unsubscribePostReq.isFail && showUnsubscribeMessage ? (
        <ErrorNotification className='[&_*]:font-sourcesans w-full' message={localize('communicationPreferences.unsubscribe.failNotification')} />
      ) : null}
      <h1 className='m-0 mb-4 box-border p-0 text-base font-bold uppercase not-italic leading-[1.563rem] text-[#333]'>
        {localize('communicationPreferences.unsubscribeButtonText')}
      </h1>
      <p className='m-0 box-border p-0 text-base font-normal not-italic leading-6 text-[#666]'>{localize('communicationPreferences.unsubscribeText')}</p>
      <form
        onSubmit={e => {
          e?.preventDefault();
        }}
        noValidate
      >
        <EmailInput
          name='comm-prefs-unsubscribe-email'
          className='fs-mask [&_*]:!font-sourcesans !font-sourcesans'
          label={localize('communicationPreferences.emailLabel')}
          autoComplete='username'
          onChange={onEmailChange}
          onBlur={onEmailBlur}
          hasError={emailAddress.hasError}
          errorMessage={emailAddress.errorMessage}
          value={emailAddress.value}
          id='comm-prefs-unsubscribe-email-hui'
        />
        <br />
        <Radio
          id='unsubscribe-one'
          name='unsubscribe-one'
          className={`mx-0 mb-6 mt-0 box-border block cursor-pointer text-[100%] font-normal ${radioSpanClasses}`}
          customBackgroundColor={radioButtons.one ? '#002554' : 'white'}
          checked={radioButtons.one}
          label={localize('communicationPreferences.unsubscribeOneText', { brand: brandFullName })}
          onChange={() =>
            toggleRadioButtons({
              one: true,
              all: false,
            })
          }
        />
        <Radio
          id='unsubscribe-all'
          name='unsubscribe-all'
          className={`mx-0 mb-6 mt-0 box-border block cursor-pointer text-[100%] font-normal ${radioSpanClasses}`}
          customBackgroundColor={radioButtons.all ? '#002554' : 'white'}
          checked={radioButtons.all}
          label={localize('communicationPreferences.unsubscribeAllText')}
          onChange={() =>
            toggleRadioButtons({
              one: false,
              all: true,
            })
          }
        />
        <Button
          kind='tertiary'
          data-testid='comm-prefs-unsubscribe-btn'
          className='[&_*]:!font-sourcesans !font-sourcesans hover:border-cb-textColor-medium hover:text-cb-textColor-medium m-0 mb-12 box-border block h-11 w-full items-center justify-center border-2 border-solid border-[#333] p-2 text-center align-middle text-base !font-bold uppercase not-italic leading-5 tracking-[normal] md:w-[220px]'
          onClick={onSubmit}
        >
          {localize('communicationPreferences.unsubscribeButtonText')}
        </Button>
      </form>
    </div>
  );
};

export default UnsubscribeEmailForm;
