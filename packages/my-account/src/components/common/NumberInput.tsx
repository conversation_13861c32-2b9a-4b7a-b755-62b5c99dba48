import React, { ChangeEvent, forwardRef } from 'react';
import TextInput from '@ecom-next/core/migration/text-input';

export type Props = {
  disabled?: boolean;
  errorMessage: string;
  fsTracking?: 'none' | 'mask' | 'exclude';
  hasError: boolean;
  id?: string;
  label: string;
  maxlength: number;
  name?: string;
  onBlur: (phone: string, errorMessage: string) => void;
  onChange?: (phone: string, errorMessage: string) => void;
  type?: string;
  value: string;
};

export const NumberInput = forwardRef<HTMLInputElement, Props>((props, ref) => {
  const { id, name, label, onChange, onBlur, maxlength, fsTracking = 'none', value, type = 'text', disabled, hasError, errorMessage } = props;

  const onNumberChange = (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value.trim();
    if (onChange) onChange(input, errorMessage);
  };

  const onNumberBlur = (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value.trim();
    if (onBlur) onBlur(input, errorMessage);
  };

  return (
    <TextInput
      ref={ref}
      data-testid={id}
      id={id}
      fsTracking={fsTracking}
      name={name}
      type={type}
      maxLength={maxlength}
      max={maxlength}
      required={true}
      crossBrand={true}
      disabled={disabled}
      errorMessage={errorMessage}
      hasError={hasError}
      onBlur={onNumberBlur}
      onChange={onNumberChange}
      label={label}
      value={value}
    />
  );
});

NumberInput.displayName = 'NumberInput';
