import './shimmer.css';

type Props = {
  className?: string;
  dataTestId?: string;
  height?: string;
  width?: string;
};

export const Shimmer = ({ width, height, className, dataTestId }: Props): JSX.Element => {
  return (
    <div className='m-0 mx-auto w-full max-w-[655px] border-none bg-white p-0 text-center text-black' data-testid={dataTestId}>
      <div className='flex flex-col items-center justify-center bg-white p-0 text-black'>
        <div className={`${width ?? ''} ${height ?? ''} ${className ?? ''} animate-[shimmer-pulse_0.7s_infinite_alternate_ease-in-out]`}></div>
      </div>
    </div>
  );
};
