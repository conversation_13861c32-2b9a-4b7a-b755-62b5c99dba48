import '@testing-library/jest-dom';
import { render, screen, fireEvent } from 'test-utils';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import React from 'react';
import { PasswordInput, Props } from '../PasswordInput';

const props = {
  id: 'optional-id',
  type: 'password',
  name: 'create-account-password',
  value: 'Jane',
  label: 'Create a password',
  placeholder: 'Password',
  onBlur: jest.fn(),
  onFocus: jest.fn(),
  onChange: jest.fn(),
  onChangeConfirm: jest.fn(),
  autoComplete: 'off',
  className: 'optional-className',
  hasError: false,
  errorMessage: '',
  isSignInForm: false,
  readOnly: false,
};

// TODO: This is a temporary type will update once the test getting run.
const renderComponent = (props: Props) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>

      <PasswordInput {...props} />

    </LocalizationProvider>
  );
};

describe('PasswordInput', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    renderComponent(props);
    expect(screen.getByText('Create a password')).toBeInTheDocument();
  });

  it('should call on change and on blur correctly for password input', () => {
    renderComponent(props);
    const passwordInput = screen.getByTestId('password-input');
    fireEvent.change(passwordInput, { target: { value: 'aVfnHFFF!2' } });
    fireEvent.blur(passwordInput);
    expect(props.onChange).toHaveBeenCalledTimes(1);
    expect(props.onBlur).toHaveBeenCalledTimes(1);
  });

  it('should call on change and on blur correctly for confirm password input', () => {
    const modifiedProps = {
      ...props,
      name: 'create-account-password-confirm',
    };
    const { container } = renderComponent(modifiedProps);
    const passwordConfirmInput = container.querySelector('[name="create-account-password-confirm"]') as HTMLInputElement;
    fireEvent.change(passwordConfirmInput, { target: { value: 'aVfnHFFF!2' } });
    fireEvent.blur(passwordConfirmInput);
    expect(props.onChange).toHaveBeenCalledTimes(0);
    expect(props.onChangeConfirm).toHaveBeenCalledTimes(1);
    expect(props.onBlur).toHaveBeenCalledTimes(1);
  });

  it('should have the expected password placeholder', async () => {
    renderComponent(props);
    const passwordInput = screen.getByTestId('password-input');
    expect(passwordInput).toHaveAttribute('placeholder', 'Password');
  });
});
