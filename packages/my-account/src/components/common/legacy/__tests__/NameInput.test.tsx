import * as React from 'react';
import '@testing-library/jest-dom';
import { render, cleanup, fireEvent, screen } from 'test-utils';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { NameInput, Props } from '../NameInput';

const props = {
  id: 'optional-id',
  value: 'Jane',
  label: 'Enter Email',
  onBlur: jest.fn(),
  onChange: jest.fn(),
  name: 'create-account-first-name',
  hasError: false,
  errorMessage: '',
};

const renderComponent = (props: Props) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);

  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <NameInput {...props} />
    </LocalizationProvider>
  );
};

describe('Name Input', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(cleanup);

  it('should invoke on change handler for each character in the first name field', () => {
    renderComponent(props);
    const nameInput = screen.getByTestId('optional-id');
    expect(nameInput?.getAttribute('name')).toEqual('create-account-first-name');
    fireEvent.change(nameInput, { target: { value: 'test' } });
    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it('should invoke on blur handler once when the first name field is blurred', () => {
    renderComponent(props);
    const nameInput = screen.getByTestId('optional-id');
    expect(nameInput?.getAttribute('name')).toEqual('create-account-first-name');
    fireEvent.change(nameInput, { target: { value: 'test' } });
    fireEvent.blur(nameInput);
    expect(props.onBlur).toHaveBeenCalledTimes(1);
    expect(props.onChange).toHaveBeenCalledTimes(1);
  });

  it('should invoke on change handler for full name', () => {
    const modifiedProps = { ...props, name: 'account-settings-full-name' };
    renderComponent(modifiedProps);
    const nameInput = screen.getByTestId('optional-id');
    expect(nameInput?.getAttribute('name')).toEqual('account-settings-full-name');
    fireEvent.change(nameInput, { target: { value: 'test' } });
    fireEvent.change(nameInput, { target: { value: 'test' } });
    expect(props.onChange).toHaveBeenCalledTimes(2);
  });

  it('should invoke on blur handler once for full name', () => {
    const modifiedProps = { ...props, name: 'account-settings-full-name' };
    renderComponent(modifiedProps);
    const nameInput = screen.getByTestId('optional-id');
    expect(nameInput?.getAttribute('name')).toEqual('account-settings-full-name');
    fireEvent.change(nameInput, { target: { value: 'test' } });
    fireEvent.change(nameInput, { target: { value: 'test' } });
    fireEvent.blur(nameInput);
    expect(props.onBlur).toHaveBeenCalledTimes(1);
    expect(props.onChange).toHaveBeenCalledTimes(2);
  });
});
