import { render, cleanup } from 'test-utils';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { ErrorNotification, SuccessSnackBar } from '../Notifications';

const renderComponent = (props: React.ReactElement) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      {props}
    </LocalizationProvider>
  );
};

describe('all notifications render ', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(cleanup);

  it('render error notifications correctly', () => {
    const { container } = renderComponent(<ErrorNotification message='Error notification' />);
    expect(container).toBeInTheDocument();
  });

  it('render success snackbar correctly', () => {
    const { container } = renderComponent(<SuccessSnackBar message='Success snackbar' />);
    expect(container).toBeInTheDocument();
  });
});
