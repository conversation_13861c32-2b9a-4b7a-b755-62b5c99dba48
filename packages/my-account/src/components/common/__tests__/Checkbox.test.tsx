import '@testing-library/jest-dom';
import { render, fireEvent } from 'test-utils';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { CoreCheckbox } from '../Checkbox';

const props = {
  defaultIsChecked: false,
  onFocus: jest.fn(),
  isChecked: false,
  errorText: '',
  labelText: 'Label',
};

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return (
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <CoreCheckbox {...props} />
    </LocalizationProvider>
  );
};

describe('CoreCheckbox', () => {
  beforeEach(() => {
    jest.restoreAllMocks();
  });

  test('renders correctly', () => {
    const { getByLabelText } = render(renderComponent());
    expect(getByLabelText(props.labelText)).toBeInTheDocument();
  });

  test('should toggle correctly', () => {
    const { getByLabelText } = render(renderComponent());
    expect(getByLabelText(props.labelText)).toBeInTheDocument();

    const checkbox = getByLabelText(props.labelText) as HTMLInputElement;
    expect(checkbox.checked).toBe(false);

    fireEvent.click(checkbox);

    expect(checkbox.checked).toBe(true);
  });
});
