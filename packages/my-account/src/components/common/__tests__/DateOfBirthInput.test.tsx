import React from 'react';
import { cleanup, fireEvent, render } from 'test-utils';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { DateOfBirthInput, DateOfBirthInputProps } from '../DateOfBirthInput';

const defaultProps = {
  id: 'optional-id',
  value: '10/10',
  label: 'Birthday MM/DD (optional)',
  testId: 'dob-input',
  name: 'create-account-dob',
  onBlur: jest.fn(),
  onChange: jest.fn(),
  onFocus: jest.fn(),
  brand: 'gap',
  locale: 'en_US',
  hasError: false,
  errorMessage: '',
};

const renderComponent = (props: DateOfBirthInputProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <DateOfBirthInput {...props} />
    </LocalizationProvider>
  );
};

describe('DateOfBirthInput', () => {
  afterEach(cleanup);
  it('renders correctly with given props', () => {
    const { container } = renderComponent(defaultProps);
    expect(container.firstChild).toBeInTheDocument();
  });

  it('calls onChange with formatted DOB', () => {
    const { getByTestId } = renderComponent(defaultProps);
    const input = getByTestId('dob-input');
    fireEvent.change(input, { target: { value: '1020' } });
    expect(defaultProps.onChange).toHaveBeenCalledWith('10/20', '');
  });

  it('calls onBlur with trimmed DOB and adding DOB formatting', () => {
    const { getByTestId } = renderComponent(defaultProps);
    const input = getByTestId('dob-input');
    fireEvent.blur(input, { target: { value: '1111  ' } });
    expect(defaultProps.onBlur).toHaveBeenCalledWith('11/11', '');
  });

  it('calls onFocus when input is focused', () => {
    const { getByTestId } = renderComponent(defaultProps);
    const input = getByTestId('dob-input');
    fireEvent.focus(input);
    expect(defaultProps.onFocus).toHaveBeenCalled();
  });
});
