import React, { forwardRef, FocusEvent, ChangeEvent, useState, useCallback } from 'react';
import { EyeMask, EyeUnmask } from '@ecom-next/core/migration/icons';
import TextInput from '@ecom-next/core/migration/text-input';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { noop } from '../../utils/form/formUtil';
import { getValidationResults, validatePassword, ValidationResult } from '../../utils/validation/password/passwordValidation';

export type Props = {
  autoComplete?: string;
  className?: string;
  disabled?: boolean;
  errorMessage?: string;
  hasError: boolean;
  id?: string;
  isSignInForm?: boolean;
  label?: string;
  name: string;
  onBlur?: (value: string, errorMessage: string) => void;
  onChange?: (value: string, errorMessage: string, validationReults: ValidationResult) => void;
  onChangeConfirm?: (payload: string) => void;
  onFocus?: () => void;
  placeholder?: string;
  supportingTxt?: string;
  testId?: string;
  type?: string;
  value?: string;
};

export const PasswordInput = forwardRef<HTMLInputElement, Props>((props, ref) => {
  const [isMasked, setIsMasked] = useState(true);
  const {
    name,
    hasError,
    label,
    disabled,
    errorMessage,
    onBlur = noop,
    onFocus = noop,
    onChange = noop,
    onChangeConfirm = noop,
    testId = 'password-input',
    isSignInForm = false,
    autoComplete,
    supportingTxt,
    ...rest
  } = props;
  const { localize } = useLocalize();

  const onPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value.trim();
    const errorMessage = localize(validatePassword(password)) || '';
    const passwordValidationResults = getValidationResults(password);
    onChange(password, errorMessage, passwordValidationResults);
  };

  const onPasswordBlur = (e: FocusEvent<HTMLInputElement>) => {
    const password = e.target.value.trim();
    const isCurrentPassword = name.includes('old');
    const errorMessage = localize(validatePassword(password, isSignInForm, isCurrentPassword)) || '';
    onBlur(password, errorMessage);
  };

  const onConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value.trim();
    onChangeConfirm(password);
  };

  const onConfirmPasswordBlur = (e: FocusEvent<HTMLInputElement>) => {
    const password = e.target.value.trim();
    const errorMessage = localize(validatePassword(password)) || '';
    onBlur(password, errorMessage);
  };

  const onChangeFn = name.includes('confirm') ? onConfirmPasswordChange : onPasswordChange;
  const onBlurFn = name.includes('confirm') ? onConfirmPasswordBlur : onPasswordBlur;
  const getIcon = useCallback(() => {
    const iconClass = 'absolute right-2 border-none bg-transparent top-[0.5625rem]';
    return isMasked ? (
      <button
        tabIndex={-1}
        type='button'
        className={iconClass}
        onClick={event => {
          event?.preventDefault();
          setIsMasked(false);
        }}
      >
        <EyeMask />
      </button>
    ) : (
      <button
        type='button'
        tabIndex={-1}
        className={iconClass}
        onClick={event => {
          event?.preventDefault();
          setIsMasked(true);
        }}
      >
        <EyeUnmask />
      </button>
    );
  }, [isMasked]);

  return (
    <div style={{ position: 'relative' }}>
      <TextInput
        ref={ref}
        name={name}
        title={name}
        type={isMasked ? 'password' : 'text'}
        label={label}
        maxLength={24}
        required={true}
        crossBrand={true}
        disabled={disabled}
        data-testid={testId}
        fsTracking='exclude'
        hasError={hasError}
        supportingTxt={supportingTxt}
        errorMessage={errorMessage}
        autoComplete={autoComplete || 'off'}
        onBlur={onBlurFn}
        onChange={onChangeFn}
        onFocus={onFocus}
        {...rest}
      />
      {getIcon()}
    </div>
  );
});

PasswordInput.displayName = 'PasswordInput';
