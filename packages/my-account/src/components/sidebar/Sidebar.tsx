'use client';
import { useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import cx from 'classnames';
import { getSidebarLinks } from '../../utils/sidebar/sidebarUtils';
import Breadcrumbs from '../breadcrumbs/Breadcrumbs';
import { RESET_ACCOUNT_SETTINGS_REQS } from '../../constants/account-settings/accountSettingsActionTypes';
import { useAccountSettings } from '../../hooks/useAccountSettings';
import { triggerGetAccountSettingsReq } from '../../requests/account-settings/accountSettingsRequests';
import { isObject } from '../../utils/layout/layoutUtil';
import { useFeatureFlags } from '../../hooks/useFeatureFlags';
import { NavSection } from './NavSection';
import { NavItem } from './NavItem';
import { BrandNames, Links, ReturnTypeKeys, Environment, ReturnType } from './types';

export const Sidebar = (): JSX.Element => {
  const pathName = usePathname();
  const { market, brandId, targetEnv: tEnv } = usePageContext();
  const { accountSettingsState, accountSettingsDispatch } = useAccountSettings();
  const featureFlags = useFeatureFlags();
  const isCanada = market === 'ca';
  const { user } = accountSettingsState;
  const brand = brandId.replace(/\s+/g, '') as BrandNames;
  const targetEnv = tEnv as Environment;
  const isStage = targetEnv === 'stage';
  const isLoyaltyHubEnabled = isStage && isObject(featureFlags) && featureFlags['profile-ui-loyalty-hub'];
  const { localize } = useLocalize();
  const accountLinks: ReturnType = getSidebarLinks({
    brand,
    market,
    targetEnv,
    isLoyaltyHubEnabled,
  });

  useEffect(() => {
    const isCanadaAccountSecurity = pathName?.includes('/my-account/account-security') && isCanada;
    if (!isCanadaAccountSecurity && !user?.firstName && !pathName?.includes('/my-account/name-and-email')) {
      triggerGetAccountSettingsReq({ accountSettingsDispatch, isLoyaltyUS: !isCanada, isLoyaltyCanada: isCanada });
    }
  }, [user, pathName]);

  useEffect(() => {
    const isAccountSettings = pathName?.includes('/my-account/name-and-email');
    if (!isAccountSettings && accountSettingsState.getAccountSettingsReq.isSuccess) {
      accountSettingsDispatch({ type: RESET_ACCOUNT_SETTINGS_REQS });
    }
  }, [accountSettingsState.getAccountSettingsReq.isSuccess]);

  if (pathName?.includes('/my-account/account-security') && isCanada) {
    return <></>;
  }

  return (
    <div
      id='sidebar-hui'
      className='max1220:min-w-[16.25rem] max1220:pl-[1.5rem] font-sourcesans hidden min-w-[17.25rem] border-r-2 border-[#f2f2f2] bg-white pl-[2rem] text-black md:block'
    >
      <Breadcrumbs />
      <Link href='/my-account/home' prefetch={true}>
        <div className='font-sourcesans mb-6 flex w-[210px] rounded bg-[#333] px-4 py-2 text-base font-normal leading-[1.28rem] tracking-[1px] text-white'>
          <div id='sidebar-desktop-name-hui'>
            <div className='font-sourcesans mr-1'>{localize('homeRedesign.sidebarNav.userCardText')}</div>
            <div className='fs-mask font-sourcesans'>{user?.firstName}</div>
          </div>
        </div>
      </Link>
      <div className='flex flex-col'>
        {Object.keys(accountLinks).map((navSection: string) => {
          const section = navSection as unknown as ReturnTypeKeys;
          const sectionData = accountLinks[section];
          if (!sectionData) return <></>;
          const { href, icon, title, links } = sectionData;
          const isGiftCards = title === 'home.myAccountNav.giftCards';

          if (!title) return <></>;

          if (isGiftCards && href) {
            return (
              <div key={title}>
                <a
                  href={href}
                  className={cx(
                    'mb-[0.4rem] flex rounded p-4 text-[0.8125rem] leading-[0.8rem] leading-[0.8rem] text-[#333] md:mb-1.5 md:items-center md:px-4 md:py-3',
                    {
                      'items-center rounded pb-3 hover:cursor-pointer hover:bg-[#dadada]': !links.length || isGiftCards,
                      'hover:cursor-auto': !href,
                      'bg-[#dadada]': !!(href && pathName.includes(href)),
                    }
                  )}
                  onClick={e => {
                    e.preventDefault();
                    window.location.href = href;
                  }}
                >
                  <div className='mr-3.5 h-auto w-[18px] leading-[0.8rem]'>
                    {icon && typeof icon === 'string' ? (
                      <img className='inline align-middle' src={icon} alt={localize(title)} />
                    ) : (
                      icon && <span className='inline align-middle'>{icon}</span>
                    )}
                  </div>
                  <div className='grow text-base font-semibold leading-[0.8rem] text-[#333] md:text-sm'>{localize(title)}</div>
                </a>
                {links.length > 0 && (
                  <div>
                    {links.map(
                      (navLink: Links) =>
                        navLink.title && (
                          <NavItem
                            key={navLink.title}
                            label={localize(navLink.title)}
                            href={navLink.href}
                            selected={!!(navLink.href && pathName.includes(navLink.href))}
                          />
                        )
                    )}
                  </div>
                )}
              </div>
            );
          }

          return (
            <NavSection
              key={title}
              icon={icon}
              label={localize(title)}
              href={href}
              selected={!!(href && pathName.includes(href) && !pathName.startsWith('/my-account/loyalty-hub/'))}
              isHoverEnabled={!!pathName.startsWith('/my-account/loyalty-hub/')}
            >
              {links && links.length
                ? links.map(
                    (navLink: Links) =>
                      navLink.title && (
                        <NavItem
                          key={navLink.title}
                          label={localize(navLink.title)}
                          href={navLink.href}
                          selected={!!(navLink.href && pathName.includes(navLink.href))}
                        />
                      )
                  )
                : ''}
            </NavSection>
          );
        })}
      </div>
    </div>
  );
};
