import React from 'react';
import Cookies from 'js-cookie';
import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { CertonaContext } from '@ecom-next/core/legacy/certona-provider';
import * as ProductRecsProvider from '@ecom-next/sitewide/product-recs-provider';
import { CertonaTile, CertonaData } from '../../../tile/certona/CertonaTile';

jest.mock('js-cookie', () => ({
  get: jest.fn(),
}));

jest.mock('@ecom-next/sitewide/product-recs-provider', () => ({
  useProductRecommendations: jest.fn(() => ({
    getAiRecommendations: jest.fn(() =>
      Promise.resolve({
        recommendationsData: {
          schemes: [
            {
              explanation: 'AI Recommendations',
              items: [
                {
                  CurrentPrice: '10.00',
                  DetailURL: '/browse/product.do?pid=795346012&rrec=true',
                  ID: '795346012',
                  ImageURL: '/webcontent/0057/796/433/cn57796433.jpg',
                  LightWeightImageURL: '/webcontent/0057/796/433/cn57796433.jpg',
                  OriginalPrice: '10.00',
                  Percentage: '',
                  ProductName: 'Organic Cotton VintageSoft T-Shirt',
                  PromotionDisplay: '',
                  Rating: '4.35',
                  ReviewCount: '3548',
                  instock: 'True',
                  isSalePrice: false,
                },
              ],
            },
          ],
        },
      })
    ),
  })),
}));

const validCertonaData = {
  schemes: [
    {
      scheme: 'gapaccounttwo1_rr',
      explanation: "We Think You'll Love These",
      display: 'yes',
      items: [
        {
          ID: '*********',
          ProductName: 'Baby Ribbed Button Sweater',
          ImageURL: 'https://secure-www.stage.gaptechol.com/webcontent/0027/187/198/cn27187198.jpg',
          LightWeightImageURL: 'https://secure-www.stage.gaptechol.com/webcontent/0027/187/196/cn27187196.jpg',
          DetailURL: '/browse/product.do?pid=*********&rrec=true',
          instock: 'False',
          OriginalPrice: '34.95',
          CurrentPrice: '34.95',
          MarketingFlag: 'Save More With GapCash! Or Extra 30% Off With Code YOURS',
          PromotionDisplay: '',
          Rating: '4.9',
          ReviewCount: '18',
        },
      ],
    },
  ],
};

const invalidCertonaData = {
  schemes: [
    {
      scheme: 'gapaccounttwo1_rr',
      explanation: "We Think You'll Love These",
      display: 'no',
      items: [
        {
          ID: '*********',
          ProductName: 'Baby Ribbed Button Sweater',
          ImageURL: 'https://secure-www.stage.gaptechol.com/webcontent/0027/187/198/cn27187198.jpg',
          LightWeightImageURL: 'https://secure-www.stage.gaptechol.com/webcontent/0027/187/196/cn27187196.jpg',
          DetailURL: '/browse/product.do?pid=*********&rrec=true',
          instock: 'False',
          OriginalPrice: '34.95',
          CurrentPrice: '34.95',
          MarketingFlag: 'Save More With GapCash! Or Extra 30% Off With Code YOURS',
          PromotionDisplay: '',
          Rating: '4.9',
          ReviewCount: '18',
        },
      ],
    },
  ],
};

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

(usePageContext as jest.Mock).mockReturnValue({
  market: 'US',
  brandAbbr: 'gp',
  brand: 'gp',
});

const renderComponent = (data: CertonaData) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <CertonaContext.Provider value={{ data }}>
        <CertonaTile />
      </CertonaContext.Provider>
    </LocalizationProvider>
  );
};

describe('Certona Tile component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('does not render component if certona api returns nothing', () => {
    renderComponent({});
    expect(screen.queryByText("We Think You'll Love These")).not.toBeInTheDocument();
  });

  it('does not renders component if schema.display is not "yes"', () => {
    renderComponent(invalidCertonaData);
    expect(screen.queryByText('Baby Ribbed Button Sweater')).not.toBeInTheDocument();
  });

  it('renders component if schema.display is "yes"', () => {
    const { container } = renderComponent(validCertonaData);
    expect(container.childElementCount).toBeGreaterThan(0);
    expect(screen.getByText("We Think You'll Love These")).toBeInTheDocument();
  });

  it('renders products name correctly', () => {
    const { getByText } = renderComponent(validCertonaData);
    expect(getByText('Baby Ribbed Button Sweater')).toBeTruthy();
  });

  it('renders explanation correctly', () => {
    const { getByText } = renderComponent(validCertonaData);
    expect(getByText("We Think You'll Love These")).toBeTruthy();
  });

  it('renders component with AI recommendations data when A/B experiment is on', async () => {
    (Cookies.get as jest.Mock).mockReturnValue('prfairec-b');
    const useProductRecommendationsMock = ProductRecsProvider.useProductRecommendations as jest.Mock;
    renderComponent(validCertonaData);
    const getAiRecommendationsMock = useProductRecommendationsMock?.mock?.results?.[0]?.value?.getAiRecommendations;
    expect(getAiRecommendationsMock).toHaveBeenCalled();
    expect(await screen.findByText('Organic Cotton VintageSoft T-Shirt')).toBeInTheDocument();
  });
});
