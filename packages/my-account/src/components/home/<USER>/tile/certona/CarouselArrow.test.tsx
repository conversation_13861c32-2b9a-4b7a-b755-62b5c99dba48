import { render, screen, fireEvent } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { CarouselArrow, CarouselArrowProps } from '../../../tile/certona/CarouselArrow';

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

(usePageContext as jest.Mock).mockReturnValue({
  market: 'US',
  brandAbbr: 'gp',
  brand: 'gp',
});

const defaultProps: CarouselArrowProps = {
  alt: 'prev',
  className: 'rotate-90',
  onClick: jest.fn(),
};

const renderComponent = (props: CarouselArrowProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <CarouselArrow {...props} />
    </LocalizationProvider>
  );
};

describe('CarouselArrow', () => {
  it('should render with the correct tabIndex', () => {
    renderComponent(defaultProps);
    const arrow = screen.getByRole('button');
    expect(arrow).toHaveAttribute('tabIndex', '0');
  });

  it('should call onClick when clicked', () => {
    renderComponent(defaultProps);
    const arrow = screen.getByRole('button');
    fireEvent.click(arrow);
    expect(defaultProps.onClick).toHaveBeenCalled();
  });

  it('should call onClick when Enter key is pressed', () => {
    renderComponent(defaultProps);
    const arrow = screen.getByRole('button');
    fireEvent.keyDown(arrow, { key: 'Enter' });
    expect(defaultProps.onClick).toHaveBeenCalled();
  });

  it('should render with the correct prev alt text', () => {
    renderComponent(defaultProps);
    const img = screen.getByAltText('prev');
    expect(img).toBeInTheDocument();
  });

  it('should render with the correct next alt text', () => {
    const props = { ...defaultProps, alt: 'next' };
    renderComponent(props);
    const img = screen.getByAltText('next');
    expect(img).toBeInTheDocument();
  });
});
