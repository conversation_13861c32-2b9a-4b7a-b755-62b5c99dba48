import React from 'react';
import datalayer from '@mfe/data-layer';
import { fireEvent, render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { BdssTile, bdssTileProps } from '../../../tile/bdss/BdssTile';
import { GlobalProvider } from '../../../../../context/global/GlobalContext';

const defaultProps: bdssTileProps = {
  secondaryEmailAddress: 't*******<EMAIL>',
  currentEmail: '<EMAIL>',
  dateOfBirth: '01/01/2000',
  phoneNumber: '**********',
};

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: () => ({
    brandAbbr: 'gp',
    market: 'us',
    locale: 'en_US',
    ecomApiBaseUrl: 'https://api.gap.com',
    brandCode: 1,
    brandId: 'gap',
    targetEnv: 'stage',
  }),
}));

jest.mock('../../../../../hooks/useHome', () => ({
  useHome: () => ({
    homeDispatch: jest.fn(),
    homeState: {
      cards: [],
      getRewardsCardsReq: {
        isSuccess: false,
        isFail: false,
      },
      postBdssMergeReq: {
        isSuccess: false,
        isFail: false,
        isSecondFailedAttempt: false,
        isThirdFailedAttempt: false,
        warningMessage: '',
        errorMessage: '',
      },
    },
  }),
}));

const renderComponent = (props: bdssTileProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <GlobalProvider isGuestView={false}>
      <LocalizationProvider locale='en_US' translations={translations} market='us'>
        <BdssTile {...props} />
      </LocalizationProvider>
    </GlobalProvider>
  );
};

describe('BdssTile tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render component correctly', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('You might be missing out')).toBeInTheDocument();
  });

  it('should render description correctly', async () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Looks like you have two Rewards accounts. Merge them to access all your benefits and points in one place.')).toBeInTheDocument();
  });

  it('should render thisAccount text correctly', async () => {
    renderComponent(defaultProps);
    expect(screen.getByText('This account')).toBeInTheDocument();
  });

  it('should render anotherAccount text correctly', async () => {
    renderComponent(defaultProps);
    expect(screen.getByText('is associated with another account')).toBeInTheDocument();
  });

  it('should render secondaryEmailAddress text correctly', async () => {
    renderComponent(defaultProps);
    expect(screen.getByText('t*******<EMAIL>')).toBeInTheDocument();
  });

  it('should open merge drawer popup on click of merge accounts button', async () => {
    renderComponent(defaultProps);
    expect(screen.getByText('CONTINUE TO MERGE')).toBeInTheDocument();
  });

  it('should fire a tealium link event on click of continue to merge button', () => {
    renderComponent(defaultProps);
    const tealiumLinkEventSpy = jest.spyOn(datalayer, 'link');
    const mergeButton = screen.getByText('CONTINUE TO MERGE');
    expect(mergeButton).toBeInTheDocument();
    fireEvent.click(mergeButton);
    expect(tealiumLinkEventSpy).toHaveBeenCalledTimes(1);
  });
});
