import React from 'react';
import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { RewardsTile, RewardsTileProps } from '../../../tile/my-points-rewards/RewardsTile';

const defaultProps: RewardsTileProps = {
  rewardsValue: '10',
};

const renderComponent = (props: RewardsTileProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <RewardsTile {...props} />
    </LocalizationProvider>
  );
};

describe('Rewards Tile component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render rewards point', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('$10')).toBeInTheDocument();
  });

  it('should render rewards text correctly', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('in rewards available.')).toBeInTheDocument();
  });

  it('should render rewards title sub-text correctly', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Rewards are redeemed in checkout.')).toBeInTheDocument();
  });

  it('should render total rewards value', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('$10')).toBeInTheDocument();
  });
});
