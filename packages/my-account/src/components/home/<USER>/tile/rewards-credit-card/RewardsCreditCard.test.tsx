import React from 'react';
import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { RewardsCreditCard, RewardsCreditCardProps } from '../../../tile/rewards-credit-card/RewardsCreditCard';

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

(usePageContext as jest.Mock).mockReturnValue({
  market: 'US',
  brandAbbr: 'gp',
});

const defaultProps: RewardsCreditCardProps = {
  href: '',
  isFetching: false,
  getCardInfohasError: false,
  cards: [],
};

const renderComponent = (props: RewardsCreditCardProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <RewardsCreditCard {...props} />
    </LocalizationProvider>
  );
};

describe('Rewards Credit Card component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('Should render Rewards Credit Cards', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Rewards Credit Cards')).toBeInTheDocument();
  });

  it('should render the Rewards Credit Cards component with the correct title and href', () => {
    renderComponent({ ...defaultProps, href: '/my-account/gap-cards' });
    const rewardsCreditCardsText = screen.getByText('Rewards Credit Cards');
    expect(rewardsCreditCardsText).toBeInTheDocument();
    expect(rewardsCreditCardsText.closest('a')).toHaveAttribute('href', '/my-account/gap-cards');
  });

  it('should display ApplyForCards component when there are no cards', () => {
    renderComponent({ ...defaultProps, cards: [] });
    expect(screen.getByText('Apply')).toBeInTheDocument();
  });

  it('should display loading skeleton when isFetching is true', () => {
    renderComponent({ ...defaultProps, isFetching: true });
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
  });

  it('should display error message when getCardInfohasError is true', () => {
    renderComponent({ ...defaultProps, getCardInfohasError: true });
    expect(screen.getByText('Your Rewards Credit Cards activity is not available at this time. Please try again later.')).toBeInTheDocument();
  });
});
