import { useLocalize } from '@sitewide/providers/localization';
import Link from 'next/link';

export type RewardsTileProps = {
  rewardsValue: string;
};

export const RewardsTile = (props: RewardsTileProps): JSX.Element => {
  const { rewardsValue } = props;
  const { localize } = useLocalize();

  return (
    <Link className='cursor-pointer' href='/my-account/value-center?target=EarnAndRedeem' prefetch={true}>
      <div className='m-0 flex items-center rounded-lg border border-solid border-[#d9d9d9] p-3'>
        <div className='mr-2 flex h-[2.81rem] w-[2.81rem] items-center justify-center rounded-[50%] bg-[#f2f2f2] text-[1.125rem] font-bold tracking-[1px] text-[#333]'>
          ${Math.floor(Number(rewardsValue))}
        </div>
        <div className='mb-1 flex flex-col items-start text-[0.875rem] text-[#333]'>
          <p className='font-bold'>{localize('homeRedesign.rewardsTileAvailableText')}</p>
          <p className='mt-0.5 font-[normal] text-[0.75rem] text-[#666]'>{localize('homeRedesign.rewardsTileRedeemText')}</p>
        </div>
      </div>
    </Link>
  );
};
