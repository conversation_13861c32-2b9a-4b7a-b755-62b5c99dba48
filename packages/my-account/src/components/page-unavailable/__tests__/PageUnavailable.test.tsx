import { render, screen, fireEvent } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { PageUnavailable } from '../PageUnavailable';
import { GlobalProvider } from '../../../context/global/GlobalContext';

const backMock = jest.fn();
const reloadMock = jest.fn();

const ORIGIN = 'https://secure-www.gap.com' as const;
const PAGE_UNAVAILABLE_PATH = '/my-account/page-unavailable' as const;
const TEST_URL = `${ORIGIN}${PAGE_UNAVAILABLE_PATH}` as const;

jest.mock('@sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn().mockReturnValue({
    brand: 'gap',
    locale: 'en_US',
  }),
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    back: backMock,
  }),
}));

const renderComponent = (isGuestView: boolean, hideErrorSubText: boolean = false) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <GlobalProvider isGuestView={isGuestView}>
      <LocalizationProvider locale='en-US' translations={translations} market='us'>
        <PageUnavailable hideErrorSubText={hideErrorSubText} />
      </LocalizationProvider>
    </GlobalProvider>
  );
};

describe('Page Unavailable', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllMocks();
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'gap',
      locale: 'en_US',
    });
    global.window = Object.create(window);
    Object.defineProperty(window, 'location', {
      value: {
        protocol: 'https:',
        host: 'secure-www.gap.com',
        href: TEST_URL,
        search: '',
        pathname: '/my-account/order-lookup',
        reload: reloadMock,
      },
      writable: true,
    });
  });

  it('should render without crashing for not authenticated case', () => {
    const isGuestView = false;
    renderComponent(isGuestView);
    expect(screen.getByText('Oh no! We hit a snag.')).toBeInTheDocument();
  });

  it('should render without crashing for authenticated case', () => {
    const isGuestView = true;
    renderComponent(isGuestView);
    expect(screen.getByText('Oh no! We hit a snag.')).toBeInTheDocument();
  });

  it('should render gap support number when brand is gap', () => {
    const isGuestView = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'gap',
      locale: 'en_US',
    });
    renderComponent(isGuestView);
    expect(screen.getByText('1-800-427-7895.')).toBeInTheDocument();
  });

  it('should render at support number when brand is at', () => {
    const isGuestView = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'at',
      locale: 'en_US',
    });
    renderComponent(isGuestView);
    expect(screen.getByText('1-877-328-4538.')).toBeInTheDocument();
  });

  it('should render on support number when brand is on', () => {
    const isGuestView = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'on',
      locale: 'en_US',
    });
    renderComponent(isGuestView);
    expect(screen.getByText('1-800-653-6289.')).toBeInTheDocument();
  });

  it('should render br support number when brand is br', () => {
    const isGuestView = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'br',
      locale: 'en_US',
    });
    renderComponent(isGuestView);
    expect(screen.getByText('1-888-277-8953.')).toBeInTheDocument();
  });

  it('should render gapfs support number when brand is gapfs', () => {
    const isGuestView = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'gapfs',
      locale: 'en_US',
    });
    renderComponent(isGuestView);
    expect(screen.getByText('1-844-437-6654.')).toBeInTheDocument();
  });

  it('should render brfs support number when brand is brfs', () => {
    const isGuestView = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'brfs',
      locale: 'en_US',
    });
    renderComponent(isGuestView);
    expect(screen.getByText('1-844-273-7746.')).toBeInTheDocument();
  });

  it('should not show support number when hideErrorSubText is true', () => {
    const isGuestView = false;
    const hideErrorSubText = true;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'brfs',
      locale: 'en_US',
    });
    renderComponent(isGuestView, hideErrorSubText);
    expect(screen.queryByText('1-844-273-7746.')).not.toBeInTheDocument();
  });

  it('should call location.reload() when hideErrorSubText is true', async () => {
    const isGuestView = false;
    const hideErrorSubText = true;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'brfs',
      locale: 'en_US',
    });
    renderComponent(isGuestView, hideErrorSubText);
    fireEvent.click(screen.getByTestId('pg-try-again-btn'));
    expect(backMock).toHaveBeenCalledTimes(0);
    expect(reloadMock).toHaveBeenCalledTimes(1);
  });

  it('should call router.back() when hideErrorSubText is false', async () => {
    const isGuestView = false;
    const hideErrorSubText = false;
    (usePageContext as jest.Mock).mockReturnValue({
      brand: 'brfs',
      locale: 'en_US',
    });
    renderComponent(isGuestView, hideErrorSubText);
    fireEvent.click(screen.getByTestId('pg-try-again-btn'));
    expect(backMock).toHaveBeenCalledTimes(1);
    expect(reloadMock).toHaveBeenCalledTimes(0);
  });
});
