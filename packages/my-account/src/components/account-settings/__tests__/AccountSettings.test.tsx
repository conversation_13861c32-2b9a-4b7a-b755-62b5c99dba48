import { render, screen } from 'test-utils';
import datalayer from '@mfe/data-layer';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { GlobalProvider } from '../../../context/global/GlobalContext';
import { AccountSettings } from '../AccountSettings';
import { AccountSettingsProvider } from '../../../context/account-settings/AccountSettingsContext';

const tealiumAddSpy = jest.spyOn(datalayer, 'add');
const tealiumViewSpy = jest.spyOn(datalayer, 'view');

jest.mock('@ecom-next/utils/clientFetch');

jest.mock('../../../hooks/useAccountSettings', () => ({
  useAccountSettings: () => ({
    accountSettingsDispatch: jest.fn(),
    accountSettingsState: {
      user: {
        email: '',
        firstName: '',
        lastName: '',
        phoneNumber: '',
        dateOfBirth: '',
        isLoyalty: false,
      },
      getAccountSettingsReq: {
        isFail: false,
        isSuccess: false,
      },
      updateAccountSettingsReq: {
        isFail: false,
        isSuccess: false,
        isPhoneUsedErr: false,
        isEmailUsedErr: false,
        isServiceError: false,
        isUnexpectedOtpErr: false,
        isInvalidSessionErr: false,
        isOtpSessionExpiredErr: false,
      },
      sendOTPReq: {
        isFail: false,
        isBotError: false,
        isSuccess: false,
      },
      otpCookies: {
        otpValidation: '',
        otpSession: '',
      },
      getOTPcookiesReq: {
        isFail: false,
        isSuccess: false,
      },
      verifyOTPReq: {
        isFail: false,
        isSuccess: false,
        isBotError: false,
        isInCorrectOTP: false,
        isServiceError: false,
        isOtpMaxAttemptsReached: false,
      },
    },
  }),
}));

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <GlobalProvider isGuestView={false}>
      <LocalizationProvider locale='en_US' translations={translations} market='us'>
        <AccountSettingsProvider>
          <AccountSettings />
        </AccountSettingsProvider>
      </LocalizationProvider>
    </GlobalProvider>
  );
};

describe('AccountSettings', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading onload', () => {
    renderComponent();
    expect(screen.getByText('loading...')).toBeInTheDocument();
    expect(tealiumAddSpy).toHaveBeenCalledTimes(1);
    expect(tealiumViewSpy).toHaveBeenCalledTimes(1);
  });
});
