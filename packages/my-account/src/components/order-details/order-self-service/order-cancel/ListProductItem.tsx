import React from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Checkbox } from '@ecom-next/core/legacy/checkbox';
import { Dropdown } from '@ecom-next/core/legacy/dropdown';
import { ProductImage } from '@ecom-next/core/legacy/product-image';
import * as utils from '../../../../utils/order-details/orderSelfServiceUtils';
import { BrandKey } from '../../types';

/* eslint-disable typescript-sort-keys/interface, typescript-sort-keys/string-enum */
export type ListProductItemProps = {
  index: number;
  total: number;
  product: {
    productImageURL: string;
    brandCode: string;
    productName: string;
    productNumber: string;
    productURL: string;
    color: string;
    size: string;
    unitPrice: number;
    promoCodes: string[];
    discount: number;
    quantity: number;
    finalPrice: number;
    finalSale: boolean;
    quantityToBeCancelled?: number;
  } | null;
  selected: boolean;
  reason: string;
  selectError: boolean;
  reasonError: boolean;
  enableSelectAll: boolean;
  onCheckboxChange: () => void;
  onDropMenuChange: (value: string) => void;
  onCancelQuantityChange: (value: number) => void;
};

export const ListProductItem = (props: ListProductItemProps) => {
  const { index, total, product, selected, reason, selectError, reasonError, enableSelectAll, onCheckboxChange, onDropMenuChange, onCancelQuantityChange } =
    props;
  const {
    productImageURL,
    brandCode,
    productName,
    productNumber,
    productURL,
    color,
    size,
    unitPrice,
    promoCodes,
    discount,
    quantity,
    finalPrice,
    quantityToBeCancelled,
  } = product!;
  const { localize, formatCurrency } = useLocalize();

  // setup variables
  let brand = brandCode || '';
  brand = brand === 'GP' ? 'GAP' : brand;
  brand = brand === 'GPFS' ? 'GAPFS' : brand;
  const hasProductImage = productImageURL && productImageURL !== '#';
  const actionsCSS = enableSelectAll ? 'justify-between' : 'justify-end';

  // setup elements
  const renderProductImage = (): JSX.Element => {
    return hasProductImage ? (
      <div className='[&_svg]:-bottom-1.5'>
        <ProductImage className='m-0 border-0 p-0' alt={productName} brand={brand as BrandKey} src={productImageURL} />
      </div>
    ) : (
      <></>
    );
  };
  const renderProductImageUnavailable = (): JSX.Element => {
    return !hasProductImage ? (
      <div className='m-0 box-border flex h-[7.5rem] w-full max-w-[5.625rem] flex-col justify-center border-0 bg-[#f1f1f2] p-0 text-center align-baseline text-xs font-normal'>
        {localize('orderDetails.productPanel.imageNotAvailable')}
      </div>
    ) : (
      <></>
    );
  };
  const renderProductColor = (): JSX.Element => {
    return color ? (
      <div className='flex flex-row leading-[1.3rem] text-[#333]'>
        <div className='basis-[25%] md:basis-[20%]'>{localize('orderDetails.productPanel.colorLabel')}:</div>
        <div className='basis-[75%] text-left md:basis-[80%]'>{color}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderProductSize = (): JSX.Element => {
    return size ? (
      <div className='flex flex-row leading-[1.3rem] text-[#333]'>
        <div className='basis-[25%] md:basis-[20%]'>{localize('orderDetails.productPanel.sizeLabel')}:</div>
        <div className='basis-[75%] text-left md:basis-[80%]'>{size}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderPromoCodes = (): JSX.Element => {
    return promoCodes && promoCodes.length > 0 && discount !== 0 ? (
      <div className='flex flex-row leading-[1.3rem] text-[#333]'>
        <div className='basis-[25%] md:basis-[20%]'>{localize('orderDetails.productPanel.promoLabel')}:</div>
        <div className='basis-[45%] text-left md:basis-[65%]'>{promoCodes.join(', ')}</div>
        <div className='basis-[30%] text-right font-bold text-[red] md:basis-[15%]'>{formatCurrency(discount)}</div>
      </div>
    ) : (
      <></>
    );
  };
  const renderCheckbox = (): JSX.Element => {
    return enableSelectAll ? (
      <Checkbox
        id={`checkbox-${index}`}
        labelText={localize('orderSelfService.cancelItem')}
        crossBrand={true}
        className='before:!border-[#002554] before:!bg-[#002554]'
        errorText={localize('orderSelfService.SelectItems')}
        hasError={selectError}
        isChecked={selected}
        onChange={onCheckboxChange}
      />
    ) : (
      <></>
    );
  };
  const renderQuantityMenu = (): JSX.Element => {
    return selected && quantity > 1 ? (
      <div className='m-0 mb-[1.125rem] box-border flex flex-row border-0 p-0 align-baseline'>
        <label className='box-border block cursor-pointer px-3 py-0 leading-8 text-[#333]'>{localize('orderSelfService.cancelQtyLabel')}</label>
        <Dropdown
          crossBrand={true}
          className='m-0 h-full min-w-[48px] border-0 px-1 py-0 align-baseline'
          placeholder=''
          options={utils.setupCancelQuantityOptions(quantity)}
          value={quantityToBeCancelled}
          onChange={value => onCancelQuantityChange(value as number)}
        />
      </div>
    ) : (
      <></>
    );
  };
  const renderReasonsMenu = (): JSX.Element => {
    return selected ? (
      <Dropdown
        crossBrand={true}
        className='mb-6'
        placeholder={localize('orderSelfService.reasonForCancel')}
        options={utils.setupMenuOptionsReasons(localize)}
        errorMessage={localize('orderSelfService.validOption')}
        hasError={reasonError}
        value={reason ? reason : localize('orderSelfService.reasonForCancel')}
        onChange={value => onDropMenuChange(value as string)}
      />
    ) : (
      <></>
    );
  };

  return (
    <div className='flex flex-col'>
      <p className='m-0'>{localize('orderSelfService.itemIndexTotal', { index, total })}</p>
      <div className='m-0 box-border flex flex-row flex-wrap border-0 px-0 py-4 align-baseline'>
        <div className='basis-[30%] pr-[0.8rem] md:basis-[20%]'>
          {renderProductImage()}
          {renderProductImageUnavailable()}
        </div>
        <div className='flex basis-[70%] flex-col md:basis-[80%]'>
          <a href={productURL} className='border-b-[none] font-bold leading-[1.2rem] text-black no-underline'>
            {productName}
          </a>
          <div className='m-0 box-border border-0 p-0 align-baseline text-xs leading-[1.2rem] text-[#666]'>#{productNumber}</div>
          <div className='flex flex-col'>
            {renderProductColor()}
            {renderProductSize()}
            <div className='flex flex-row leading-[1.3rem] text-[#333]'>
              <div className='basis-[25%] md:basis-[20%]'>{localize('orderDetails.productPanel.priceLabel')}:</div>
              <div className='basis-[75%] text-left md:basis-[80%]'>{formatCurrency(unitPrice)}</div>
            </div>
            {renderPromoCodes()}
            <div className='flex flex-row leading-[1.3rem] text-[#333]'>
              <div className='basis-[25%] md:basis-[20%]'>{localize('orderDetails.productPanel.quantityLabel')}</div>
              <div className='basis-[45%] text-left md:basis-[65%]'>{quantity}</div>
              <div className='basis-[30%] text-right font-bold md:basis-[15%]'>{formatCurrency(finalPrice)}</div>
            </div>
          </div>
        </div>
      </div>
      <div className={`flex flex-row [&_label]:text-[#333] ${actionsCSS}`}>
        {renderCheckbox()}
        {renderQuantityMenu()}
      </div>
      {renderReasonsMenu()}
    </div>
  );
};
