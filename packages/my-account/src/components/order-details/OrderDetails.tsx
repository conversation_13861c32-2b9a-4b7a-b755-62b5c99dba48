'use client';
import React, { useEffect, useState } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useGlobal } from '../../hooks/useGlobal';
import { useOrderDetails } from '../../hooks/useOrderDetails';
import { triggerOrderDetailsReq, triggerOrderCancelReq, triggerChangeShippingAddressReq } from '../../requests/order-details/orderDetailsRequests';
import { RESET_ORDER_DETAILS_STATE } from '../../constants/order-details/orderDetailsActionTypes';
import * as orderDetailsUtils from '../../utils/order-details/orderDetailsUtils';
import * as orderSelfServiceUtils from '../../utils/order-details/orderSelfServiceUtils';
import { fireTealiumViewTag } from '../../utils/tealium/tealiumUtil';
import { Loader } from '../common/loader/Loader';
import { ErrorNotification } from '../common/legacy/Notifications';
import { OrderSummary } from './order-summary/OrderSummary';
import { DeliverySummary } from './delivery-summary/DeliverySummary';
import { ChargesSummary } from './charges-summary/ChargesSummary';
import { Barcode } from './Barcode';
import { PackageOrder } from './package-order/PackageOrder';
import { Returns } from './returns/Returns';
import { OrderSelfServiceSummary } from './order-self-service/OrderSelfServiceSummary';
import { OrderCancelModal } from './order-self-service/order-cancel/OrderCancelModal';
import { OrderChangeShippingAddressModal } from './order-self-service/change-shipping-address/OrderChangeShippingAddressModal';

export const OrderDetails = ({ params }: { params: { orderNumber: string } }): JSX.Element => {
  const { localize } = useLocalize();
  const { orderNumber } = params;
  const { globalState, setNavigationType } = useGlobal();
  const { orderLookupRedirect, isGuestView = false, orderHistoryRedirect, navigationType } = globalState;
  const { market, ecomApiBaseUrl, targetEnv, brandCode, brandAbbr } = usePageContext();
  const { orderDetailsState, orderDetailsDispatch } = useOrderDetails();
  const { orderSummary, deliverySummary, chargesSummary, packageOrders, orderInfo, postOrderDetails, postOrderCancel, postChangeShippingAddress } =
    orderDetailsState;
  const [isReqInFlight, setIsReqInFlight] = useState<boolean>(true);
  const [remorseDateTime, setRemorseDateTime] = useState({ date: '', time: '' });
  const [inRemorsePeriod, setInRemorsePeriod] = useState(false);

  // setup variables
  const containerCSS = isGuestView ? 'box-border mx-auto my-4' : 'box-content m-0';
  const isOrderTypeStore = orderSummary?.orderType === 'STORE';
  const returnItemsBrands = orderSummary?.returnItemsBrands;
  const returnHeaderStatus = orderSummary?.returnHeaderStatus;
  const zipCode = deliverySummary?.shippingTo?.zipCode || '';
  const emailAddress = orderLookupRedirect?.email || '';
  const orderPurchaseType = orderHistoryRedirect?.orderNumber === orderNumber ? orderHistoryRedirect?.orderPurchaseType : '';
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;

  // toggle params
  const showReturns = Array.isArray(returnItemsBrands) && returnItemsBrands.length > 0 && returnHeaderStatus === 'returnAvailable';
  const showOrderSelfService = orderSummary?.isSignIn;
  const [showOrderCancelModal, toggleOrderCancelModal] = useState(false);
  const [showOrderChangeShippingAddressModal, toggleOrderChangeShippingAddressModal] = useState(false);

  // setup poll refresh to make interval order-details API calls in order to refresh the data after a success order-self-service API call
  const MAX_POLL_RETRY_COUNT = 4;
  const [pollRetryCount, setPollRetryCount] = useState(0);
  const [pollRefreshId, setPollRefreshId] = useState<NodeJS.Timeout | undefined>(undefined);
  const startPollRefresh = () => {
    setPollRetryCount(0);
    // clear any old polls - defensive check
    if (pollRefreshId) {
      clearInterval(pollRefreshId);
    }
    // start new poll
    const pollRefresh = setInterval(() => {
      setPollRetryCount(prevState => prevState + 1);
      // make order-details API call
      const payload = {
        ecomApiBaseUrl,
        emailAddress,
        isGuestView,
        marketCode: market?.toUpperCase(),
        orderDetailsDispatch,
        orderNumber,
        orderPurchaseType,
        targetEnv,
        useMockData: orderDetailsUtils.checkUseMockOrderTypesData(window),
      };
      triggerOrderDetailsReq(payload);
    }, 3000);
    setPollRefreshId(pollRefresh);
  };

  // check for max poll retry count
  useEffect(() => {
    if (pollRetryCount >= MAX_POLL_RETRY_COUNT) {
      clearInterval(pollRefreshId);
    }
  }, [pollRetryCount]);

  // on mount/first-load
  useEffect(() => {
    if (isGuestView && !emailAddress) {
      window.location.href = '/my-account/order-lookup';
      return undefined;
    }
    const eventType = navigationType === 'hard' ? 'add' : 'view';
    fireTealiumViewTag({ page: 'Order Details', brandCode, brandTealium, eventType });
    setIsReqInFlight(true);
    orderDetailsDispatch({ type: RESET_ORDER_DETAILS_STATE });
    if (navigationType === 'hard') {
      setNavigationType('soft');
    }
    const payload = {
      ecomApiBaseUrl,
      emailAddress,
      isGuestView,
      marketCode: market?.toUpperCase(),
      orderDetailsDispatch,
      orderNumber,
      orderPurchaseType,
      targetEnv,
      useMockData: orderDetailsUtils.checkUseMockOrderTypesData(window),
    };
    triggerOrderDetailsReq(payload);
    return () => clearInterval(pollRefreshId);
  }, []);

  useEffect(() => {
    if (postOrderDetails.isSuccess || postOrderDetails.isFail) {
      setIsReqInFlight(false);
    }
    // check if poll refresh has been set
    if (pollRefreshId && pollRetryCount > 0) {
      // stop conditions
      if (orderSelfServiceUtils.checkForAnyCancelOrderStatus(packageOrders)) {
        clearInterval(pollRefreshId);
      } else if (orderInfo.isAddressChanged) {
        clearInterval(pollRefreshId);
      }
    }
  }, [orderSummary, packageOrders, orderInfo, postOrderDetails]);

  // on mount/order-self-service
  useEffect(() => {
    // set remorse period timer
    let timer: NodeJS.Timeout | number = 0;
    const remorsePeriodCount = orderInfo?.remorseExpiryMinutes;
    if (orderSelfServiceUtils.checkRemorsePeriod(remorsePeriodCount)) {
      setInRemorsePeriod(true);
      const countdown = remorsePeriodCount * 60000;
      timer = setTimeout(() => {
        setInRemorsePeriod(false);
      }, countdown);
      if (remorseDateTime.date === '' || remorseDateTime.time === '') {
        setRemorseDateTime({
          date: orderSelfServiceUtils.getRemorsePeriodEndDateTime('date', remorsePeriodCount),
          time: orderSelfServiceUtils.getRemorsePeriodEndDateTime('time', remorsePeriodCount),
        });
      } else {
        // clear
        setRemorseDateTime({ date: '', time: '' });
      }
    } else {
      // clear
      setInRemorsePeriod(false);
      setRemorseDateTime({ date: '', time: '' });
    }
    return () => clearTimeout(timer);
  }, [orderInfo]);

  // setup package orders list
  const packageOrdersList = packageOrders
    ? packageOrders?.map((item, idx) => {
        const key = idx * 100;
        return <PackageOrder key={key} {...item} />;
      })
    : [];

  // setup elements
  const renderLoader = (): JSX.Element => {
    return isReqInFlight ? <Loader /> : <></>;
  };
  const renderBarcode = (): JSX.Element => {
    return isOrderTypeStore && orderNumber ? <Barcode orderNumber={orderNumber} /> : <></>;
  };
  const renderReturns = (): JSX.Element => {
    return showReturns ? <Returns orderFrom={orderSummary?.orderFrom} orderNumber={orderNumber} returnItemsBrands={returnItemsBrands} /> : <></>;
  };
  const renderOrderSelfServiceSummary = (): JSX.Element => {
    return showOrderSelfService ? (
      <OrderSelfServiceSummary
        orderSummary={orderSummary}
        deliverySummary={deliverySummary}
        packageOrders={packageOrders}
        orderInfo={orderInfo}
        inRemorsePeriod={inRemorsePeriod}
        toggleOrderCancelModal={() => toggleOrderCancelModal(true)}
        toggleOrderChangeShippingAddressModal={() => toggleOrderChangeShippingAddressModal(true)}
        postOrderCancel={postOrderCancel}
        postChangeShippingAddress={postChangeShippingAddress}
      />
    ) : (
      <></>
    );
  };
  const renderErrorMessage = (): JSX.Element => {
    return postOrderDetails.isFail ? (
      <ErrorNotification className='font-sourcesans mb-4 w-full' message={localize('orderDetails.globalMessageError')} />
    ) : (
      <></>
    );
  };
  const renderOrderCancelModal = (): JSX.Element => {
    return showOrderCancelModal ? (
      <OrderCancelModal
        packageOrders={packageOrders}
        orderInfo={orderInfo}
        isOpen={showOrderCancelModal}
        inRemorsePeriod={inRemorsePeriod}
        remorseDateTime={remorseDateTime}
        onClose={() => toggleOrderCancelModal(false)}
        onSubmit={requestBody => {
          triggerOrderCancelReq({ orderNumber, requestBody, orderDetailsDispatch, ecomApiBaseUrl });
        }}
        postOrderCancel={postOrderCancel}
        startPollRefresh={startPollRefresh}
      />
    ) : (
      <></>
    );
  };
  const renderOrderChangeShippingAddressModal = (): JSX.Element => {
    return showOrderChangeShippingAddressModal ? (
      <OrderChangeShippingAddressModal
        packageOrders={packageOrders}
        orderInfo={orderInfo}
        deliverySummary={deliverySummary}
        isOpen={showOrderChangeShippingAddressModal}
        inRemorsePeriod={inRemorsePeriod}
        remorseDateTime={remorseDateTime}
        onClose={() => toggleOrderChangeShippingAddressModal(false)}
        onSubmit={requestBody => {
          triggerChangeShippingAddressReq({ orderNumber, requestBody, orderDetailsDispatch, ecomApiBaseUrl });
        }}
        postChangeShippingAddress={postChangeShippingAddress}
        startPollRefresh={startPollRefresh}
      />
    ) : (
      <></>
    );
  };

  return (
    <div className={`flex w-auto max-w-[656px] flex-col border-0 p-0 align-baseline text-[100%] font-normal ${containerCSS}`}>
      {renderLoader()}
      {!isReqInFlight ? (
        <>
          <h1
            className={`m-0 mb-8 box-border border-0 p-0 indent-[1rem] align-baseline font-[inherit] text-[2rem] font-normal leading-10 text-[#333] md:indent-0`}
          >
            {localize('orderDetails.details')}
          </h1>
          {renderErrorMessage()}
          <div className='mb-4 flex w-full flex-col flex-wrap md:flex-row'>
            <OrderSummary {...orderSummary} zipCode={zipCode} />
            <DeliverySummary {...deliverySummary} />
            {renderBarcode()}
          </div>
          <div className='flex flex-col'>{packageOrdersList}</div>
          <div className='m-0 mb-4 box-border flex flex-col border-0 p-0 align-baseline'>
            <ChargesSummary {...chargesSummary} />
            {renderOrderSelfServiceSummary()}
            {renderReturns()}
          </div>
          {renderOrderCancelModal()}
          {renderOrderChangeShippingAddressModal()}
        </>
      ) : (
        <></>
      )}
    </div>
  );
};
