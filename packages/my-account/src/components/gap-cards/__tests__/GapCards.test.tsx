import React from 'react';
import { render, waitFor } from 'test-utils';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useGapCards } from '../../../hooks/useGapCards';
import { GapCards } from '../GapCards';
import { xApi } from '../../../utils/api';
import { firePeekInfoTealiumViewTag } from '../../../utils/tealium/tealiumUtil';
import { GlobalProvider } from '../../../context/global/GlobalContext';

jest.mock('../../../utils/tealium/tealiumUtil', () => ({
  firePeekInfoTealiumViewTag: jest.fn(),
}));

jest.mock('@sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(),
}));

(usePageContext as jest.Mock).mockReturnValue({
  market: 'US',
  brandAbbr: 'gp',
  brandCode: 1,
});

jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}));

jest.mock('../../../hooks/useGapCards', () => ({
  useGapCards: jest.fn().mockReturnValue({
    gapCardsState: {},
    gapCardsDispatch: jest.fn(),
  }),
}));

jest.mock('../../../utils/api', () => ({
  xApi: jest.fn(),
}));

jest.mock('next/navigation', () => {
  return {
    useSearchParams: jest.fn().mockReturnValue(new Map()),
  };
});

const defaultGapCardsState = {
  cards: [],
  enableQuickViewReq: {
    isFail: false,
    isSuccess: false,
  },
  getCardsReq: {
    isFail: false,
    isSuccess: false,
  },
  getPeekInfoReq: {
    isFail: false,
    isSuccess: false,
  },
  getUUIDReq: {
    isFail: false,
    isSuccess: false,
  },
  getUUIDValueReq: {
    isFail: false,
    isSuccess: false,
  },
  uuid: null,
};

describe('GapCards', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('should make a xapi call on load to get UUID value', () => {
    (usePageContext as jest.Mock).mockReturnValue({ market: 'us', brandAbbr: 'gp' });
    (useGapCards as jest.Mock).mockReturnValue({
      gapCardsState: {
        ...defaultGapCardsState,
      },
      gapCardsDispatch: jest.fn(),
    });

    (xApi as jest.Mock).mockResolvedValue({});

    render(
      <GlobalProvider isGuestView={false}>
        <GapCards />
      </GlobalProvider>
    );
    expect(xApi).toHaveBeenCalledWith('/get-gap-cards/uuid-value', { method: 'GET', isText: true });
    expect(firePeekInfoTealiumViewTag).toHaveBeenCalledWith({
      page: 'My Rewards Credit Cards',
      brandTealium: 'gp',
      eventName: '',
      eventType: 'add',
    });
  });

  it('should make a xapi call on load to get peek info', () => {
    (usePageContext as jest.Mock).mockReturnValue({ market: 'us', brandAbbr: 'gp' });
    (useGapCards as jest.Mock).mockReturnValue({
      gapCardsState: {
        ...defaultGapCardsState,
        uuid: '1234',
      },
      gapCardsDispatch: jest.fn(),
    });

    (xApi as jest.Mock).mockResolvedValue({});

    render(
      <GlobalProvider isGuestView={false}>
        <GapCards />
      </GlobalProvider>
    );
    expect(xApi).toHaveBeenCalledWith('/get-gap-cards/uuid-value', { method: 'GET', isText: true });
    waitFor(() => {
      expect(xApi).toHaveBeenCalledWith('/get-gap-cards/peek-info', { method: 'GET' });
    });
  });
  it('should make a xapi call on load to get peek info', () => {
    (usePageContext as jest.Mock).mockReturnValue({ market: 'us', brandAbbr: 'gp' });
    (useGapCards as jest.Mock).mockReturnValue({
      gapCardsState: {
        ...defaultGapCardsState,
        uuid: '',
      },
      gapCardsDispatch: jest.fn(),
    });

    (xApi as jest.Mock).mockResolvedValue({});

    render(
      <GlobalProvider isGuestView={false}>
        <GapCards />
      </GlobalProvider>
    );
    expect(xApi).toHaveBeenCalledWith('/get-gap-cards/uuid-value', { method: 'GET', isText: true });
    waitFor(() => {
      expect(xApi).toHaveBeenCalledWith('/get-gap-cards', { method: 'POST' });
    });
  });

  it('should trigger 2 tealuim even peek info call success', () => {
    (usePageContext as jest.Mock).mockReturnValue({ market: 'us', brandAbbr: 'gp' });
    (useGapCards as jest.Mock).mockReturnValue({
      gapCardsState: {
        ...defaultGapCardsState,
        getPeekInfoReq: {
          isSuccess: true,
          isFail: false,
        },
        uuid: '',
      },
      gapCardsDispatch: jest.fn(),
    });

    render(
      <GlobalProvider isGuestView={false}>
        <GapCards />
      </GlobalProvider>
    );
    expect(firePeekInfoTealiumViewTag).toHaveBeenCalledTimes(2);
  });
  it('should trigger 2 tealuim even peek info call fail', () => {
    (usePageContext as jest.Mock).mockReturnValue({ market: 'us', brandAbbr: 'gp' });
    (useGapCards as jest.Mock).mockReturnValue({
      gapCardsState: {
        ...defaultGapCardsState,
        getPeekInfoReq: {
          isSuccess: false,
          isFail: true,
        },
        uuid: '',
      },
      gapCardsDispatch: jest.fn(),
    });

    render(
      <GlobalProvider isGuestView={false}>
        <GapCards />
      </GlobalProvider>
    );
    expect(firePeekInfoTealiumViewTag).toHaveBeenCalledTimes(2);
  });
});
