import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { manageAndPayBarclays } from '../../utils/gap-cards/linksUtils';
import { fireBarclaysTealiumLinkTag } from '../../utils/tealium/tealiumUtil';

export type UnknownCardProps = {
  brand: string;
  targetEnv: string | undefined;
};
export const UnknownCard = (props: UnknownCardProps): JSX.Element => {
  const { brand, targetEnv } = props;
  const { localize } = useLocalize();
  const currentEnv = targetEnv === 'stage' ? 'stage' : 'prod';
  const { brandAbbr, brandCode } = usePageContext();
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;
  return (
    <div className='border-b border-[#d8d9d6] pb-8'>
      <p className='mb-8'>{localize('barclaysServicing.labels.Select')}</p>
      <a
        className='cursor-pointer text-base text-[#0466ca] hover:underline'
        href={manageAndPayBarclays[currentEnv][brand]}
        onClick={() => fireBarclaysTealiumLinkTag({ page: 'Credit Card Servicing', eventName: 'manage_pay', brandCode, brandTealium })}
      >
        {localize('barclaysServicing.labels.manageAndPay')}
      </a>
    </div>
  );
};
