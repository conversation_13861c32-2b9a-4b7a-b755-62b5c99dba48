import React from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Modal } from '@ecom-next/core/legacy/modal';
import IconPayItForwardBlack from '../../../../assets/value-center/icon_pay_it_forward_black.svg';
import * as utils from '../../../../utils/value-center/valueCenterUtils';
import type { CharityType } from '../../../../context/value-center/types';
import { OrganizationMenuItem } from './OrganizationMenuItem';

export type OrganizationModalProps = {
  charityList: CharityType[];
  isOpen: boolean;
  toggleOrganizationModal: (value: boolean) => void;
};

export const OrganizationModal = (props: OrganizationModalProps): JSX.Element => {
  const { charityList, isOpen, toggleOrganizationModal } = props;
  const { localize } = useLocalize();

  // setup organization options
  const organizationItems =
    charityList
      ?.filter(item => utils.checkValidCharityItem(item))
      ?.map((item, index) => {
        const key = index * 100;
        return (
          <OrganizationMenuItem
            key={key}
            brand={item?.brand}
            brandFullName={item?.brandFullName}
            charityCorporation={item?.charityCorporation}
            charityLogoLocation={item?.charityLogoLocation}
            description={item?.description}
            showDescription={true}
            title={item?.title}
            inModal={true}
          />
        );
      }) || [];

  return (
    <Modal
      isOpen={isOpen}
      className='[&_*]:!font-sourcesans'
      crossBrand={true}
      title={localize('valueCenter.earnRedeem.organizationModal.header')}
      closeButtonAriaLabel={localize('accessibility.ModalCloseButton')}
      onClose={() => toggleOrganizationModal(false)}
    >
      <div className='flex flex-col p-4'>
        <div className='mx-0 my-4 box-border flex flex-row justify-center border-0 p-0 text-center align-baseline'>
          <img className='mr-2 max-w-full' src={IconPayItForwardBlack.src} alt={localize('valueCenter.earnRedeem.organizationModal.handsAltText')} />
          <h3 className='m-0 box-border self-center p-0 text-center text-xl font-bold'>{localize('valueCenter.earnRedeem.organizationModal.title')}</h3>
        </div>
        <p className='m-0 mb-4 p-0 text-[#666]'>{localize('valueCenter.earnRedeem.organizationModal.description')}</p>
        <hr className='m-0 mb-8 box-border h-0.5 w-full border border-solid border-[#ccc] p-0 text-[#C9C9C9]' />
        <div className='flex flex-col'>{organizationItems}</div>
      </div>
    </Modal>
  );
};
