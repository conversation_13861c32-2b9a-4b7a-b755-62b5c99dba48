import React, { useState, useEffect } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useFeatureFlags } from '../../../../hooks/useFeatureFlags';
import { useValueCenter } from '../../../../hooks/useValueCenter';
import { fireValueCenterLinkTag, fireTargetedOffersTealiumLinkTag } from '../../../../utils/tealium/tealiumUtil';
import { EarnAndRedeemLoading } from '../EarnAndRedeemLoading';
import { SpecialOffer } from '../SpecialOffer';
import { BarclaysPointsTile } from './BarclaysPointsTile';
import { ConvertPointsForm } from './ConvertPointsForm';
import { ExpiredRewardTile } from './ExpiredRewardTile';
import { BirthdayBonusTile } from './BirthdayBonusTile';
import { FamilyOfBrands } from './FamilyOfBrands';

export type EarnMorePointsProps = {
  activetabSection: string;
  isReqInFlight: boolean;
  setIsReqInFlight: (value: boolean) => void;
};

export const EarnMorePoints = React.forwardRef<HTMLDivElement, EarnMorePointsProps>((props, ref): JSX.Element => {
  const { isReqInFlight, setIsReqInFlight, activetabSection } = props;
  const { localize } = useLocalize();
  const { market, brandAbbr, brandCode } = usePageContext();
  const featureFlags = useFeatureFlags();
  const { valueCenterState } = useValueCenter();
  const { membership, getMembershipReq, getEarnAndRedeemReq, earnAndRedeem } = valueCenterState;
  const [isSpecialOfferEventFired, setIsSpecialOfferEventFired] = useState(false);

  // setup variables
  const isCanada = market === 'ca';
  const tier = membership?.tierData?.currentTier?.toUpperCase() || 'CORE';
  const isCardHolder = membership?.customerDetail?.isCardHolder || false;
  const isMonthOfBirth = membership?.customerDetail?.isMonthOfBirth || false;
  const DOB = membership?.customerDetail?.DOB || '';
  const shoppedBrands = earnAndRedeem?.quarterlyRewards?.shoppedBrands || [];
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;

  useEffect(() => {
    if (!earnAndRedeem.loyaltyOffers?.callFail) {
      const isSpecialOffersPresent = earnAndRedeem.loyaltyOffers.list.length > 0;
      const tealiumPayload = {
        brandCode,
        brandTealium,
        eventName: 'value-center-click',
        value_center_interaction: 'Earn & Redeem',
        specialOffersPresent: isSpecialOffersPresent,
      };
      fireValueCenterLinkTag(tealiumPayload);
    }
  }, [earnAndRedeem.loyaltyOffers]);

  useEffect(() => {
    if (!isSpecialOfferEventFired && earnAndRedeem?.rewardsStatus?.list) {
      setIsSpecialOfferEventFired(true);
      fireTargetedOffersTealiumLinkTag({
        brandCode,
        brandTealium,
        page: 'LoyaltyValueCenter',
        eventName: 'spl-offer-toggle-cta',
      });
    }
  }, [earnAndRedeem, isSpecialOfferEventFired]);
  const isTargetedOffersEnabled = !!featureFlags?.['profile-ui-is-targeted-offers-enabled'];
  const isShowDateRangeOnBirthdayBonusPromoEnabled = !!featureFlags?.['profile-ui-is-show-date-range-on-birthday-bonus-promo-enabled'];

  // setup refs
  const refFamilyOfBrands = React.useRef<HTMLDivElement | null>(null);
  const refBirthdayBonus = React.useRef<HTMLDivElement | null>(null);
  const refConvertPoints = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    // check for auto-scroll
    switch (activetabSection) {
      case 'familyOfBrands':
        isCanada && refFamilyOfBrands?.current?.scrollIntoView({ behavior: 'smooth' });
        break;
      case 'birthdayBonus':
        isCanada && refBirthdayBonus?.current?.scrollIntoView({ behavior: 'smooth' });
        break;
      case 'convertPoints':
        refConvertPoints?.current?.scrollIntoView({ behavior: 'smooth' });
        break;
      default:
        break;
    }
  }, [activetabSection]);

  // setup expired rewards
  const expiredItems =
    membership?.customerDetail?.rewards
      ?.filter(reward => reward?.isExpired && reward?.type === 'cash')
      ?.map((item, index) => {
        const key = index * 100;
        return (
          <ExpiredRewardTile
            key={key}
            brandCode={item?.brandCode}
            expirationDate={item?.expirationDate}
            isReqInFlight={isReqInFlight}
            points={item?.points || 0}
            promotionCode={item?.promotionCode}
            setIsReqInFlight={setIsReqInFlight}
            title={item?.title}
          />
        );
      }) || [];

  // setup special offers
  const max = 10;
  const offerItems =
    earnAndRedeem?.loyaltyOffers?.list
      ?.filter(promo => !promo.isBirthdayBonusPromo)
      ?.slice(0, max)
      ?.map((item, index) => {
        const key = index * 100;
        return (
          <SpecialOffer
            key={key}
            awardsPercentage={item?.awardsPercentage}
            barCode={item?.barCode}
            currentBrand={brandAbbr}
            endDate={item?.endDate}
            isBirthdayBonusPromo={item?.isBirthdayBonusPromo}
            isReqInFlight={isReqInFlight}
            isShowDateRangeOnBirthdayBonusPromoEnabled={isShowDateRangeOnBirthdayBonusPromoEnabled}
            isTargetedOffersEnabled={isTargetedOffersEnabled}
            legalText={item?.legalText}
            promoBrand={item?.promoBrand}
            promoCode={item?.promoCode}
            promoDescription={item?.promoDescription}
            promoId={item?.promoId}
            promoName={item?.promoName}
            rewardsStatus={earnAndRedeem?.rewardsStatus?.list}
            setIsReqInFlight={setIsReqInFlight}
            startDate={item?.startDate}
          />
        );
      }) || [];

  // setup elements
  const renderLoader = (): JSX.Element => {
    return isReqInFlight ? <EarnAndRedeemLoading /> : <></>;
  };
  const renderBirthdayBonusTile = (): JSX.Element => {
    return isCanada ? <BirthdayBonusTile DOB={DOB} isMonthOfBirth={isMonthOfBirth} ref={refBirthdayBonus} /> : <></>;
  };
  const renderFamilyOfBrands = (): JSX.Element => {
    return isCanada && !getMembershipReq?.isGetTierDataFail && !getEarnAndRedeemReq?.isGetQuarterlyRewardsFail ? (
      <FamilyOfBrands shoppedBrands={shoppedBrands} market={market} tier={tier} ref={refFamilyOfBrands} />
    ) : (
      <></>
    );
  };
  const renderExpiredRewardTiles = (): JSX.Element[] => {
    return !isReqInFlight && !getMembershipReq?.isGetUserRewardsFail && !getEarnAndRedeemReq?.isGetRewardsStatusFail ? expiredItems : [];
  };
  const renderSpecialOffers = (): JSX.Element[] => {
    return !isReqInFlight && !getEarnAndRedeemReq?.isGetLoyaltyOffersFail && !getEarnAndRedeemReq?.isGetRewardsStatusFail ? offerItems : [];
  };
  const renderBarclaysPointsTile = (): JSX.Element => {
    return !isCanada && !getMembershipReq?.isGetUserRewardsFail && !getMembershipReq?.isGetTierDataFail && !isCardHolder ? (
      <BarclaysPointsTile tier={tier} />
    ) : (
      <></>
    );
  };

  return (
    <div
      className='mx-auto mb-4 mt-0 box-border flex w-full max-w-[655px] flex-col border-[none] bg-white p-4 align-baseline text-black'
      data-testid='earn-more-points'
      ref={ref}
    >
      <div className='mb-4'>
        <h1 className='m-0 box-border p-0 text-[1.625rem] font-normal text-black'>{localize('valueCenter.earnRedeem.earnMorePoints.title')}</h1>
      </div>
      <div className='flex flex-col'>
        {renderLoader()}
        {renderBirthdayBonusTile()}
        {renderExpiredRewardTiles()}
        {renderSpecialOffers()}
        {renderFamilyOfBrands()}
        <ConvertPointsForm isReqInFlight={isReqInFlight} setIsReqInFlight={setIsReqInFlight} ref={refConvertPoints} />
        {renderBarclaysPointsTile()}
      </div>
    </div>
  );
});

EarnMorePoints.displayName = 'EarnMorePoints';
