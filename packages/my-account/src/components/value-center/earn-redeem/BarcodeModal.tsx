import React, { useEffect, useRef } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Modal } from '@ecom-next/core/legacy/modal';
import bwipjs from 'bwip-js/browser';
import FamilyAllBrands from '../../../assets/value-center/familyAllBrands.svg';
import * as utils from '../../../utils/value-center/valueCenterUtils';

export type BarcodeModalProps = {
  amount?: number;
  awardsPercentage?: number;
  barCode?: string;
  expirationDate?: string;
  isOpen: boolean;
  isShowDateRangeOnBirthdayBonusPromoEnabled?: boolean;
  promoBrand?: string;
  promoCode?: string;
  promoDescription?: string;
  toggleBarcodeModal: (value: boolean) => void;
  type: string;
};

export const BarcodeModal = (props: BarcodeModalProps): JSX.Element => {
  const {
    amount = 0,
    awardsPercentage = 0,
    barCode = '',
    expirationDate = '',
    isOpen,
    isShowDateRangeOnBirthdayBonusPromoEnabled = false,
    promoBrand = '',
    promoCode = '',
    promoDescription = '',
    toggleBarcodeModal,
    type,
  } = props;
  const { localize, formatCurrency } = useLocalize();
  const barcodeCanvas = useRef(null);

  // setup variables
  const isOffer = type === 'offer';
  const isBirthday = type === 'birthday';
  const showExpireDate = !isBirthday || (isBirthday && isShowDateRangeOnBirthdayBonusPromoEnabled);
  const modalTitle = isBirthday ? localize('valueCenter.earnRedeem.birthdayBonusPromo.modal.header') : '';
  const totalCash = formatCurrency(amount, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
  const headerClasses = isBirthday ? 'text-xl leading-[1.5625rem] m-4' : 'box-border text-[#333] text-3xl text-center mb-5 m-0 p-0';
  const headerText = utils.setupBarcodeModalHeaderText(localize, type, {
    totalCash,
    awardsPercentage,
    promoBrand,
    promoDescription,
  });

  useEffect(() => {
    if (isOpen && barCode) {
      try {
        // create barcode image
        bwipjs.toCanvas(barcodeCanvas.current!, {
          bcid: 'pdf417',
          text: barCode,
          scale: 2,
          height: 12,
          includetext: true,
          textxalign: 'center',
        });
      } catch (error) {
        // need to log error
      }
    }
  }, [isOpen]);

  // setup elements
  const renderFamilyAllBrandsImage = (): JSX.Element => {
    return isOffer ? (
      <div className='flex text-center'>
        <img
          className='max655:max-w-[300px] mx-auto mb-5 mt-0 h-auto w-full max-w-[400px]'
          src={FamilyAllBrands.src}
          alt={localize('valueCenter.earnRedeem.barcodeModal.altText')}
        />
      </div>
    ) : (
      <></>
    );
  };
  const renderBirthdayDescription = (): JSX.Element => {
    return isBirthday ? (
      <p className='mx-4 my-0 text-left leading-[1.2rem] text-[#666]'>{localize('valueCenter.earnRedeem.birthdayBonusPromo.modal.description')}</p>
    ) : (
      <></>
    );
  };
  const renderDefaultDescription = (): JSX.Element => {
    return !isBirthday ? (
      <>
        <p className='text-center leading-[1.2rem] text-[#666]'>{localize('valueCenter.earnRedeem.barcodeModal.description1')}</p>
        <p className='text-center leading-[1.2rem] text-[#666]'>{localize('valueCenter.earnRedeem.barcodeModal.description2')}</p>
      </>
    ) : (
      <></>
    );
  };
  const renderExpireDate = (): JSX.Element => {
    return showExpireDate ? (
      <span className='text-center leading-[1.2rem] text-[#666]'>{localize('valueCenter.earnRedeem.barcodeModal.expires', { date: expirationDate })}</span>
    ) : (
      <></>
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      className='[&_*]:!font-sourcesans'
      crossBrand={true}
      title={modalTitle}
      closeButtonAriaLabel={localize('accessibility.ModalCloseButton')}
      onClose={() => toggleBarcodeModal(false)}
    >
      <div className='flex flex-col'>
        <h3 className={headerClasses}>{headerText}</h3>
        {renderFamilyAllBrandsImage()}
        {renderBirthdayDescription()}
        {renderDefaultDescription()}
        <div className='mx-0 mb-10 mt-5 flex flex-col'>
          {renderExpireDate()}
          <div className='mx-0 my-2 text-center'>
            <canvas ref={barcodeCanvas} className='m-auto' />
          </div>
          <span className='text-center leading-[1.2rem] text-[#666]'>{promoCode}</span>
        </div>
      </div>
    </Modal>
  );
};
