import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { TableItem } from './TableItem';

export const Earn = () => {
  const { localize } = useLocalize();
  const { market } = usePageContext();
  const isCanada = market === 'ca';

  return (
    <div className='max655:w-auto max655:divide-none m-4 mb-8 box-border w-full max-w-[623px] flex-col divide-y divide-[#d4d4d4] rounded shadow-[0_1px_4px_rgba(0,0,0,0.25)]'>
      <h1 className='max655:text-center m-4 text-left font-bold'>{localize('valueCenter.membership.earn.header')}</h1>
      {!isCanada && (
        <TableItem
          text={localize('valueCenter.membership.earn.copytext1')}
          checkmarkTwo={{ type: 'star', count: 1 }}
          checkmarkThree={{ type: 'star', count: 1 }}
          orText={localize('valueCenter.membership.earn.or')}
        />
      )}
      <TableItem
        text={localize('valueCenter.membership.earn.copytext2')}
        checkmarkOne={{ type: 'dot', count: 1 }}
        checkmarkTwo={{ type: 'dot', count: 1 }}
        checkmarkThree={{ type: 'dot', count: 1 }}
      />
      {!isCanada && (
        <TableItem
          text={localize('valueCenter.membership.earn.copytext3')}
          checkmarkTwo={{ type: 'star', count: 1 }}
          checkmarkThree={{ type: 'star', count: 1 }}
        />
      )}
      <TableItem
        text={localize('valueCenter.membership.earn.copytext4')}
        checkmarkOne={{ type: 'dot', count: 1 }}
        checkmarkTwo={{ type: 'dot', count: 1 }}
        checkmarkThree={{ type: 'dot', count: 1 }}
      />
    </div>
  );
};
