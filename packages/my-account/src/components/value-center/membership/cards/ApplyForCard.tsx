import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { BarclaysTile } from './BarclaysTile';

const DISPLAY_NAME_MAP: Readonly<Record<string, string>> = {
  gp: 'Gap',
  br: 'Banana Republic',
  on: 'Old Navy',
  at: 'Athleta',
  gpfs: 'Gap',
  brfs: 'Banana Republic',
} as const;

type ApplyForCardProps = {
  isCardHolder: boolean;
};

export const ApplyForCard = (props: ApplyForCardProps) => {
  const { isCardHolder } = props;
  const { localize } = useLocalize();
  const { market, brandAbbr } = usePageContext();
  const isCanada = market === 'ca';

  if (isCardHolder || isCanada) {
    return <></>;
  }

  return (
    <div className='max655:px-4 mb-8 box-border w-full max-w-[623px] flex-col'>
      <BarclaysTile
        header={localize('valueCenter.membership.addCard.header')}
        desc={localize('valueCenter.membership.addCard.copytext')}
        hasImage={false}
        buttonText={localize('valueCenter.membership.addCard.buttonText')}
      />
      <div className='mt-12'>
        <BarclaysTile
          header={localize('valueCenter.membership.earnPoints.header')}
          desc={localize('valueCenter.membership.earnPoints.copytext', { brand: DISPLAY_NAME_MAP[brandAbbr] })}
          hasImage={true}
          isBarclaysLink={true}
          buttonText={localize('valueCenter.membership.earnPoints.buttonText')}
        />
      </div>
    </div>
  );
};
