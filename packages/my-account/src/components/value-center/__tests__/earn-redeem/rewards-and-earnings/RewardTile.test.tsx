import { render, screen } from '@testing-library/react';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { ValueCenterProvider } from '../../../../../context/value-center/ValueCenterContext';
import { RewardTile, RewardTileProps } from '../../../earn-redeem/rewards-and-earnings/RewardTile';

const mockData = {
  amount: 10,
  barCode: 'barcode123',
  cashType: 'gapCash',
  currentBrand: 'on',
  expirationDate: '09/02/2030',
  expiresSoon: false,
  isReqInFlight: false,
  legalTerms: 'legal test',
  promotionCode: 'promo123',
  rewardsStatus: [],
  setIsReqInFlight: jest.fn(),
  startDate: '09/02/2024',
  type: 'cash',
};

const renderComponent = (props: RewardTileProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <StitchStyleProvider brand='gap'>
        <ValueCenterProvider>
          <RewardTile {...props} />
        </ValueCenterProvider>
      </StitchStyleProvider>
    </LocalizationProvider>
  );
};

describe('RewardTile component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders RewardTile component properly', () => {
    renderComponent(mockData);
    expect(screen.getByText('GapCash')).toBeInTheDocument();
    expect(screen.getByText('Expires Sep 2, 2030')).toBeInTheDocument();
  });

  it('renders Style Cash Reward Tile', () => {
    const modifiedData = { ...mockData, cashType: 'styleCash' };
    renderComponent(modifiedData);
    expect(screen.getByText('BR Factory StyleCash')).toBeInTheDocument();
    expect(screen.getByText('Expires Sep 2, 2030')).toBeInTheDocument();
  });

  it('renders Super Cash Reward Tile', () => {
    const modifiedData = { ...mockData, cashType: 'superCash' };
    renderComponent(modifiedData);
    expect(screen.getByText('Old Navy SuperCash')).toBeInTheDocument();
    expect(screen.getByText('Expires Sep 2, 2030')).toBeInTheDocument();
  });
});
