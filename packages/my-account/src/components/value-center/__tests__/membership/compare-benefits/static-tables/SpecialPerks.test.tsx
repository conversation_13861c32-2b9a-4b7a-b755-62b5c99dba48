import { render, screen } from 'test-utils';
import { LocalizationProvider } from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { SpecialPerks } from '../../../../membership/compare-benefits/static-tables/SpecialPerks';

const initProps = {
  onSetActiveTab: jest.fn(),
};

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <SpecialPerks {...initProps} />
    </LocalizationProvider>
  );
};

describe('SpecialPerks', () => {
  it('renders correctly', () => {
    renderComponent();
    expect(screen.getByText('SPECIAL PERKS')).toBeInTheDocument();
    expect(screen.getByText('Free fast shipping (on orders $50 and more)')).toBeInTheDocument();
    expect(screen.getByText('2-3 DAYS')).toBeInTheDocument();
    expect(screen.getByText('No Annual Fee & $0 Fraud Liability Protection**')).toBeInTheDocument();
    expect(screen.getByText('Members only exclusive offers')).toBeInTheDocument();
    expect(screen.getByText('Early access')).toBeInTheDocument();
  });
});
