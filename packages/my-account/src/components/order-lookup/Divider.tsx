import React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';

export const Divider = (): JSX.Element => {
  const { localize } = useLocalize();
  return (
    <div className='my-10 flex'>
      <hr className='mt-2.5 h-0.5 flex-grow border-none bg-[#cbcaca]' />
      <div className='font-sourcesans mx-5 h-6 text-center leading-6 text-[#333]'>{localize('orDivider.orLabel')}</div>
      <hr className='mt-2.5 h-0.5 flex-grow border-none bg-[#cbcaca]' />
    </div>
  );
};
