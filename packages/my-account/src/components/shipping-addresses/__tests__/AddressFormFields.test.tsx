import { render, screen, fireEvent } from '@testing-library/react';
import LocalizationProvider from '@sitewide/providers/localization';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { AddressFormFields, AddressFormFieldsProps } from '../AddressFormFields';

const defaultProps = {
  fullName: { value: '', hasError: false, errorMessage: '' },
  addressLine1: { value: '', hasError: false, errorMessage: '' },
  addressLine2: { value: '', hasError: false, errorMessage: '' },
  city: { value: '', hasError: false, errorMessage: '' },
  dayPhone: { value: '', hasError: false, errorMessage: '' },
  state: { value: '', hasError: false, errorMessage: '' },
  zipCode: { value: '', hasError: false, errorMessage: '' },
  onAddressChange: jest.fn(),
  onAddressFieldBlur: jest.fn(),
  handleAddressSelect: jest.fn(),
  market: 'us',
};

jest.mock('@sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn().mockReturnValue({
    brand: 'gap',
    market: 'us',
    locale: 'en_US',
  }),
}));

const renderComponent = (props: AddressFormFieldsProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <StitchStyleProvider brand={'gap'}>
        <AddressFormFields {...props} />
      </StitchStyleProvider>
    </LocalizationProvider>
  );
};

describe('AddressFormFields', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    const google = {
      maps: {
        places: {
          Autocomplete: jest.fn().mockImplementation(() => ({
            addListener: jest.fn(),
          })),
        },
      },
    };
    // @ts-ignore
    window.google = google;
  });

  it('renders correctly', () => {
    renderComponent(defaultProps);
    expect(screen.getByTestId('addressForm')).toBeInTheDocument();
  });

  it('should call onChange and onBlur', () => {
    renderComponent(defaultProps);
    const fields = screen.getAllByRole('textbox');
    fields.forEach(field => {
      fireEvent.change(field, { target: { value: 'ABC XYZ' } });
      fireEvent.blur(field);
    });
    expect(defaultProps.onAddressChange).toHaveBeenCalledTimes(6);
    expect(defaultProps.onAddressFieldBlur).toHaveBeenCalledTimes(5);
  });

  it('should display error message when fields are empty', () => {
    renderComponent({
      ...defaultProps,
      fullName: { value: '', hasError: true, errorMessage: 'Enter a value' },
      addressLine1: { value: '', hasError: true, errorMessage: 'Enter a value' },
      city: { value: '', hasError: true, errorMessage: 'Enter a value' },
      dayPhone: { value: '', hasError: true, errorMessage: 'Enter a value' },
      state: { value: '', hasError: true, errorMessage: 'Enter a value' },
      zipCode: { value: '', hasError: true, errorMessage: 'Enter a value' },
    });
    expect(screen.queryAllByRole('alert').length).toBe(6);
  });

  it('Should display list of state', () => {
    renderComponent(defaultProps);
    const state = screen.getByRole('button');
    fireEvent.focus(state);
    expect(screen.getByRole('listbox')).toBeInTheDocument();
  });
});
