import { render, screen } from '@testing-library/react';
import datalayer from '@mfe/data-layer';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { GlobalProvider } from '../../../context/global/GlobalContext';
import { ResetPasswordProvider } from '../../../context/reset-password/ResetPasswordContext';
import { State } from '../../../context/reset-password/types';
import { useResetPassword } from '../../../hooks/useResetPassword';
import * as requests from '../../../requests/reset-password/resetPasswordRequests';
import { ResetPassword } from '../ResetPassword';

jest.mock('../../../hooks/useResetPassword');
jest.mock('../../../requests/reset-password/resetPasswordRequests', () => ({
  triggerResetPasswordGetReq: jest.fn(),
}));

const initialState = {
  resetPasswordDispatch: jest.fn(),
  resetPasswordState: {
    resetPasswordGetReq: {
      isSuccess: true,
      isFail: false,
      isTokenError: false,
    },
    resetPasswordPostReq: {
      isSuccess: false,
      isFail: false,
      isUsed: false,
    },
  } as State,
};

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <GlobalProvider isGuestView={false}>
      <LocalizationProvider locale='en-US' translations={translations} market='us'>
        <ResetPasswordProvider>
          <ResetPassword />
        </ResetPasswordProvider>
      </LocalizationProvider>
    </GlobalProvider>
  );
};

describe('ResetPassword', () => {
  beforeEach(() => {
    (useResetPassword as jest.Mock).mockReturnValue({
      ...initialState,
      resetPasswordState: {
        ...initialState.resetPasswordState,
        resetPasswordGetReq: { isSuccess: true, isFail: false },
      },
    });
    (requests.triggerResetPasswordGetReq as jest.Mock).mockReturnValue(null);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.resetAllMocks();
  });

  it('should render Reset Password View', () => {
    const state = { ...initialState };
    (useResetPassword as jest.Mock).mockReturnValue(state);
    renderComponent();
    expect(screen.getByText(/This link is invalid?/i)).toBeInTheDocument();
  });

  it('Tealium View event should fire', () => {
    renderComponent();
    expect(datalayer.view).toHaveBeenCalledTimes(1);
  });
});
