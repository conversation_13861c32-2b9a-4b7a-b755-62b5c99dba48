import { useState } from 'react';
import { Dropdown } from '@ecom-next/core/migration/drop-down';
import { Shimmer } from '../../common/loader/Shimmer';
import { useLoyaltyHub } from '../../../hooks/useLoyaltyHub';
import { RewardsItem } from '../../../context/loyalty-hub/types';

export type RewardsMarketplaceOptsProps = {
  isLoaded: boolean;
  rewardsItems: RewardsItem[];
  setRewardsItems: (rewardsItems: RewardsItem[]) => void;
  setSortOption: (option: string) => void;
  sortOption: string;
};

export const RewardsMarketplaceOpts = ({ isLoaded, setRewardsItems, setSortOption, sortOption }: RewardsMarketplaceOptsProps): JSX.Element => {
  const [availableOptions, setAvailableOptions] = useState(['Low to High', 'High to Low']);
  const [hasInteracted, setHasInteracted] = useState(false);

  const { loyaltyHubState } = useLoyaltyHub();

  const { rewards, user } = loyaltyHubState;

  const handleSortChange = (item: string) => {
    const value = item;
    setSortOption(value);

    let sorted = [...rewards];
    if (value === 'Low to High') {
      setAvailableOptions(['Low to High', 'High to Low', 'Featured']);
      sorted.sort((a, b) => a.pointsValue - b.pointsValue);
    } else if (value === 'High to Low') {
      setAvailableOptions(['Low to High', 'High to Low', 'Featured']);
      sorted.sort((a, b) => b.pointsValue - a.pointsValue);
    } else {
      sorted = [...rewards];
    }

    setRewardsItems(sorted);
    setHasInteracted(true);
  };

  const renderLoadingLeft = (): JSX.Element => {
    return !isLoaded ? (
      <Shimmer width={'w-[96px]'} height={'h-[44px]'} dataTestId='dropdown-loader' />
    ) : (
      <div className={'w-[140px]'}>
        <Dropdown label={'Sort By'} options={availableOptions} value={sortOption} noLabel={!hasInteracted} onChange={handleSortChange} className='w-[156px]' />
      </div>
    );
  };

  const renderLoadingRight = (): JSX.Element => {
    return !isLoaded ? (
      <Shimmer width={'w-[147px]'} height={'h-[44px]'} dataTestId='points-loader' />
    ) : (
      <p className='rounded-md bg-[#F3F3F3] p-[12px] text-sm'>
        Available: <span className='font-bold'>{user?.points || 'NA'} pts</span>
      </p>
    );
  };

  return (
    <div className='my-4 flex h-[44px] w-full flex-row items-center justify-between px-4 md:px-0'>
      <div className={`h-full ${!isLoaded ? `w-[96px]` : `w-[68px]`}`}>{renderLoadingLeft()}</div>
      <div className='h-full w-[147px]'>{renderLoadingRight()}</div>
    </div>
  );
};
