/* RewardsMarketplace */
.rewardsMarketplaceWrapper {
  margin-top: -4rem;
  max-width: 635px;
}

@media (max-width: 389px) {
  .rewardsMarketplaceWrapper {
    max-width: 100%;
  }
}

@media (max-width: 767px) {
  .rewardsMarketplaceWrapper {
    margin: 0 auto;
  }
}

@media (min-width: 390px) and (max-width: 1018px) {
  .rewardsMarketplaceWrapper {
    max-width: 390px;
    margin-top: -2rem;
  }
}

/* RewardsMarketplaceHero */
.marketplaceHeroWrapper {
  height: 241px;
  width: 100%;
  max-width: 635px;
  position: relative;
}

.desktopImage {
  display: block;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.mobileImage {
  display: none;
  height: 100%;
  width: 100%;
  object-fit: contain;
  object-position: left;
}

@media (max-width: 1018px) {
  .marketplaceHeroWrapper {
    max-width: 390px;
  }
  .desktopImage {
    display: none;
  }
  .mobileImage {
    display: block;
  }
}

/* RewardsMarketplaceGallery */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 635px;
  padding-bottom: 3rem;
}

.grid {
  display: grid;
  width: 100%;
  gap: 2rem 0.75rem;
  grid-template-columns: repeat(4, 1fr);
}

.gridItem {
  height: auto;
}

.gridItemAlt {
  margin-left: auto;
}

.productImg {
  background-color: #e5e7eb;
  width: 150px;
  min-height: 150px;
}

@media (max-width: 1018px) {
  .container {
    width: 390px;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .productImg {
    width: calc(50vw - 2rem);
  }
}
