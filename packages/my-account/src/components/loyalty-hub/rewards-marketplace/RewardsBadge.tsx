interface RewardsBadgeProps {
  label?: string;
}

export const RewardsBadge: React.FC<RewardsBadgeProps> = ({ label = 'Ending Soon' }) => {
  return (
    <div
      className={`text-font-size--1 font-font-weight-base-default -tracking-font-letter-spacing-base flex h-8 w-full items-center justify-center bg-[#666666]/80 px-1.5 py-1 text-white ${
        label.toLowerCase() === 'cardmember exclusive' ? 'min-w-[140px] sm:min-w-[130px]' : ''
      }`}
      title={label}
    >
      <span className='w-full text-center'>{label.length > 20 ? `${label.slice(0, 20)}…` : label}</span>
    </div>
  );
};
