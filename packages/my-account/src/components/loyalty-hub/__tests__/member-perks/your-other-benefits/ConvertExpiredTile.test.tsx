import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { ConvertExpiredTile } from '../../../member-perks/your-other-benefits/ConvertExpiredTile';

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <ConvertExpiredTile />
    </LocalizationProvider>
  );
};

describe('ConvertExpiredTile component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders ConvertExpiredTile component properly', () => {
    renderComponent();
    expect(screen.getByText('Convert Expired Bonus Cash to Points')).toBeInTheDocument();
    expect(screen.getByText('Convert unused Super Cash or GapCash to points. Add Bonus Cash code to add points to your Rewards account')).toBeInTheDocument();
  });
});
