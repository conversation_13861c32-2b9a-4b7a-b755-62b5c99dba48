import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { BrandExclusives } from '../../../member-perks/your-other-benefits/BrandExclusives';

const renderComponent = () => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <BrandExclusives />
    </LocalizationProvider>
  );
};

describe('BrandExclusives component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders BrandExclusives component properly', () => {
    renderComponent();
    expect(screen.getByText('Brand Exclusives')).toBeInTheDocument();
    expect(
      screen.getByText('Explore available exclusives you get as a member with our family of brands (Gap, Old Navy, Banana Republic, Athleta)')
    ).toBeInTheDocument();
  });
});
