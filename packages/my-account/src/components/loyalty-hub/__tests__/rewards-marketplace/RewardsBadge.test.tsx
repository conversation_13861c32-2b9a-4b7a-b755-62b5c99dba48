import { render, screen } from '@testing-library/react';
import { RewardsBadge } from '../../rewards-marketplace/RewardsBadge';
import '@testing-library/jest-dom';

describe('RewardsBadge', () => {
it('renders with default label', () => {
  render(<RewardsBadge />);
  const badge = screen.getByTitle('Ending Soon');
  expect(badge).toBeInTheDocument();
  expect(badge).toHaveAttribute('title', 'Ending Soon');
});

  it('renders with custom label', () => {
    render(<RewardsBadge label='Cardmember Exclusive' />);
    const badge = screen.getByTitle('Cardmember Exclusive');
expect(badge).toBeInTheDocument();
expect(badge).toHaveAttribute('title', 'Cardmember Exclusive');
  });

  it('has correct class names and styles', () => {
    render(<RewardsBadge />);
    const badge = screen.getByTitle('Ending Soon');
expect(badge).toHaveClass('text-font-size--1', 'flex', 'h-8', 'items-center', 'bg-[#666666]/80', 'px-1.5', 'py-1', 'text-white');
  });

  it('renders empty string label', () => {
    render(<RewardsBadge label='' />);
    const badge = screen.getByTitle('');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent('');
  });
  it('truncates label longer than 20 characters and adds ellipsis', () => {
    const longLabel = 'This is a very long label that should be truncated';
    render(<RewardsBadge label={longLabel} />);
    const badge = screen.getByTitle(longLabel);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent(`${longLabel.slice(0, 20)}…`);
  });

  it('does not truncate label with exactly 20 characters', () => {
    const exactLabel = 'This label is 20 cha';
    render(<RewardsBadge label={exactLabel} />);
    const badge = screen.getByTitle(exactLabel);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent(exactLabel);
  });

  it('does not truncate label shorter than 20 characters', () => {
    const shortLabel = 'Short Label';
    render(<RewardsBadge label={shortLabel} />);
    const badge = screen.getByTitle(shortLabel);
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent(shortLabel);
  });
});
