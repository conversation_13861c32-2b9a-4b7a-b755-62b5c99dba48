import React from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Button } from '@ecom-next/core/migration/button';
import * as utils from '../../../utils/loyalty-hub/loyaltyHubUtils';

export type CustomerOfferTileProps = {
  amount: number;
  barCode: string;
  brand: string;
  discountType: string;
  endDate: string;
  legalTerms: string;
  offerType: string;
  promoCode: string;
  promoDescription: string;
  promoId: string;
  promoName: string;
  startDate: string;
};

export const CustomerOfferTile = (props: CustomerOfferTileProps) => {
  const { promoName, promoDescription, startDate, endDate } = props;
  const { localize } = useLocalize();

  // setup variables
  const diffDays = utils.getDiffBetweenDateNow(endDate);

  // setup elements
  const renderDateRange = () => {
    const dateDescription = utils.setupDateRangeText(startDate, endDate, 'customerOfferTile', localize);
    return diffDays > 1 ? <div className='mb-4 text-[0.875rem] leading-none text-[#666]'>{dateDescription}</div> : <></>;
  };
  const renderExpiresSoonMessage = (): JSX.Element => {
    return diffDays === 1 ? (
      <div className='mb-4 text-[0.875rem] leading-none text-[red]'>{localize('loyaltyHub.memberPerks.customerOfferTile.expiresDate')}</div>
    ) : (
      <></>
    );
  };
  const renderViewCodeButton = () => {
    return diffDays <= 0 ? (
      <div className='flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-[2.25rem] w-auto max-w-[120px] cursor-pointer items-center justify-center overflow-hidden text-ellipsis whitespace-nowrap rounded border border-solid border-black bg-transparent !px-[0.75rem] !py-[0.375rem] text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black hover:!border hover:border-[#4780ae] hover:bg-white hover:text-[#4780ae]'
          kind='secondary'
          onClick={() => {}}
        >
          {localize('loyaltyHub.memberPerks.viewCode')}
        </Button>
      </div>
    ) : (
      <></>
    );
  };
  const renderActivateButton = () => {
    return diffDays === 1 ? (
      <div className='flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-[2.25rem] w-auto max-w-[120px] cursor-pointer items-center justify-center overflow-hidden text-ellipsis whitespace-nowrap rounded border border-solid border-black bg-transparent !px-[0.75rem] !py-[0.375rem] text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black hover:!border hover:border-[#4780ae] hover:bg-white hover:text-[#4780ae]'
          kind='secondary'
          onClick={() => {}}
        >
          {localize('loyaltyHub.memberPerks.activate')}
        </Button>
      </div>
    ) : (
      <></>
    );
  };
  const renderLearnMoreButton = () => {
    return diffDays > 1 ? (
      <div className='flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-auto w-auto max-w-[120px] !p-0 text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black'
          kind='flat'
          onClick={() => {}}
        >
          {localize('loyaltyHub.memberPerks.learnMore')}
        </Button>
      </div>
    ) : (
      <></>
    );
  };

  return (
    <div className='mb-4 box-border flex flex-col rounded-[0.25rem] p-4 shadow-[0_0.125rem_0.375rem_rgba(0,0,0,0.12),0_0_0.125rem_rgba(0,0,0,0.15),0_0.0625rem_0.125rem_rgba(0,0,0,0.12)] '>
      <h4 className='mb-4 font-semibold leading-none text-[0.875ren]'>{localize('loyaltyHub.memberPerks.customerOfferTile.iconMembersOnly')}</h4>
      <h3 className='mb-4 text-[1.25rem] font-semibold leading-none'>{promoName}</h3>
      {renderDateRange()}
      {renderExpiresSoonMessage()}
      <p className='leadig-none mb-4 text-[1rem] font-normal text-[#333]'>{promoDescription}</p>
      {renderViewCodeButton()}
      {renderActivateButton()}
      {renderLearnMoreButton()}
    </div>
  );
};
