import React from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Button } from '@ecom-next/core/migration/button';

export const BrandExclusives = () => {
  const { localize } = useLocalize();

  return (
    <div className='mb-4 box-border flex flex-col rounded-[0.25rem] p-4 shadow-[0_0.125rem_0.375rem_rgba(0,0,0,0.12),0_0_0.125rem_rgba(0,0,0,0.15),0_0.0625rem_0.125rem_rgba(0,0,0,0.12)]'>
      <h3 className='mb-4 text-[1.25rem] font-semibold leading-none'>{localize('loyaltyHub.memberPerks.brandExclusives.header')}</h3>
      <p className='text-[1rem] font-normal leading-[1.2rem] text-[#333]'>{localize('loyaltyHub.memberPerks.brandExclusives.description')}</p>
      <div className='mt-4 flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-auto w-auto max-w-[120px] !p-0 text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black'
          kind='flat'
          onClick={() => {}}
        >
          {localize('loyaltyHub.memberPerks.learnMore')}
        </Button>
      </div>
    </div>
  );
};
