'use client';
import { useEffect, useState, useCallback, ChangeEvent } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { Button } from '@ecom-next/core/migration/button';

import { usePageContext } from '@sitewide/hooks/usePageContext';
import { Market } from '@ecom-next/utils/server';
import { useGlobal } from '../../hooks/useGlobal';
import { Loader } from '../common/loader/Loader';
import { triggerAddCardReq, triggerEditCardReq, triggerGetSavedCardsReq } from '../../requests/saved-cards/savedCardsRequests';
import { useSavedCards } from '../../hooks/useSavedCards';
import * as ActionTypes from '../../constants/saved-cards/savedCardsActionTypes';
import { getStateByAbbrWithLocale, generateStateAbbrWithLocale } from '../../utils/address/addressUtil';
import { stateMap } from '../../constants/common/address/states';
import type { ErrorMap, FormData, FormFieldType } from '../../utils/validation/form/types';
import { AddressChangeType, AddressSelectPayload } from '../shipping-addresses/AddressFormFields';
import { CreditCardType } from '../common/legacy/CreditCardInput';
import { VerifyAddressModal } from '../shipping-addresses/VerifyAddressModal';
import { setImageByCardType, cardTypeLookup } from '../../utils/saved-cards/savedCardsUtil';
import { formatCreditCardNumber, formatCreditCardExpDate } from '../../utils/formatting/FormattingUtils';
import { validateAddress, validateCity, validatePhoneNumber, validateZipcode } from '../../utils/validation/address/addressValidation';
import { validateFullName } from '../../utils/validation/name/nameValidation';
import defaultCard from '../../assets/saved-cards/unknown-card.png';
import { BillingAddressPayload } from '../../context/saved-cards/types';
import { AddCardReqPayload, EditCardReqPayload } from '../../requests/saved-cards/types';
import { WALLET_TYPE_MAP } from '../../constants/saved-cards/savedCardsConstants';
import { fireTealiumViewTag, fireTealiumLinkTag } from '../../utils/tealium/tealiumUtil';
import { NoCreditCards } from './NoCreditCards';
import { CreditCard } from './CreditCard';
import { AddPaymentModal } from './AddPaymentModal';
import { GapCreditCardFooterLink } from './GapCreditCardFooterLink';
import { Cards, CreditCardIcon } from './types';

export const SavedCards = () => {
  const { localize } = useLocalize();
  const { market, brandCode, brandAbbr, ecomApiBaseUrl, locale } = usePageContext();
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;

  const { globalState, setNavigationType } = useGlobal();
  const { navigationType } = globalState;

  const initialFormState: FormData = {
    cardNumber: { value: '', errorMessage: '', hasError: false },
    expDate: { value: '', errorMessage: '', hasError: false },
    fullName: { value: '', errorMessage: '', hasError: false, isRequired: true },
    addressLine1: { value: '', errorMessage: '', hasError: false, isRequired: true },
    addressLine2: { value: '', errorMessage: '', hasError: false, isRequired: false },
    city: { value: '', errorMessage: '', hasError: false, isRequired: true },
    dayPhone: { value: '', errorMessage: '', hasError: false, isRequired: true },
    state: { value: '', errorMessage: '', hasError: false, isRequired: true },
    zipCode: { value: '', errorMessage: '', hasError: false, isRequired: true },
  };
  const [userEnteredAddress, setUserEnteredAddress] = useState({
    addressId: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    countryCode: market?.toUpperCase() as Market,
    dayPhone: '',
    firstName: '',
    isDefaultAddress: false,
    lastName: '',
    state: '',
    zipCode: '',
  });

  const [formData, setFormData] = useState<FormData>(initialFormState);
  const [isDefault, setIsDefault] = useState<boolean>(false);
  const [isAddPaymentModalOpen, setIsAddPaymentModalOpen] = useState(false);
  const [isReqInFlight, setIsReqInFlight] = useState<boolean>(true);
  const [paymentModalType, setPaymentModalType] = useState<string>('');
  const [isVerifyAddressModalOpen, setIsVerifyAddressModalOpen] = useState<boolean>(false);
  const [creditCardId, setCreditCardId] = useState<string>('');
  const [creditCard, setCreditCard] = useState<CreditCardIcon>({
    image: defaultCard.src,
    type: '',
    plcc: false,
  });

  const {
    savedCardsState: { creditCards, getCardsReq, addCardReq, editCardReq, deleteCardReq, savedCardsAvsReq, suggestedAddresses, isAvsMatchFound },
    savedCardsDispatch,
  } = useSavedCards();

  const { stateAbbreviations } = stateMap;

  const showServiceUnavailableMessage = addCardReq.isFail || editCardReq.isFail;
  const userHasCreditCards = !!creditCards.length;
  const isCanada = market === 'ca';

  const errorMap: ErrorMap = {
    'wallet.errors.required.cardNumber': localize('wallet.errors.required.cardNumber'),
    'wallet.errors.valid.cardNumber': localize('wallet.errors.valid.cardNumber'),
    'wallet.errors.required.expirationDate': localize('wallet.errors.required.expirationDate'),
    'wallet.errors.valid.expirationDate': localize('wallet.errors.valid.expirationDate'),
  };

  const fetchCards = useCallback(() => {
    savedCardsDispatch({ type: ActionTypes.RESET_CARDS });
    setIsReqInFlight(true);
    const payload = { market: market?.toUpperCase() as Market, ecomApiBaseUrl, savedCardsDispatch };
    triggerGetSavedCardsReq(payload);
  }, []);

  const toggleAddPaymentModal = (resetForm = true) => {
    if (resetForm) {
      setFormData(initialFormState);
      setCreditCard({ image: defaultCard.src, type: '', plcc: false });
      setPaymentModalType('');
      setCreditCardId('');
      setIsDefault(true);
    }
    setIsAddPaymentModalOpen(!isAddPaymentModalOpen);
  };

  const onDefaultChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { checked } = e.target;
    setIsDefault(checked);
  };

  const onCardEdit = (cards: Cards) => {
    const cc = { ...cards };
    setIsAddPaymentModalOpen(!isAddPaymentModalOpen);
    setPaymentModalType('edit');
    setIsDefault(cc.defaultCard);
    setCreditCardId(cc.id);
    setCreditCard({
      image: setImageByCardType(cc),
      type: cc.type,
      plcc: cc.plcc,
    });
    fireTealiumLinkTag({
      page: 'Saved Cards',
      eventName: 'payment_update',
      brandCode,
      brandTealium,
      recognitionStatus: 'authenticated',
    });
    setFormData(prevState => ({
      ...prevState,
      cardNumber: {
        value: `**** **** **** ${cc.lastFourDigits}`,
        errorMessage: '',
        hasError: false,
      },
      expDate: {
        value: `${cc.expirationMonth}/${Number(String(cc.expirationYear).slice(-2))}`,
        errorMessage: '',
        hasError: false,
      },
      fullName: {
        value: `${cc.billingAddress.firstName} ${cc.billingAddress.lastName}`,
        errorMessage: '',
        hasError: false,
        isRequired: true,
      },
      addressLine1: {
        value: cc.billingAddress.addressLine1,
        errorMessage: '',
        hasError: false,
        isRequired: true,
      },
      addressLine2: {
        value: cc.billingAddress.addressLine2,
        errorMessage: '',
        hasError: false,
        isRequired: false,
      },
      city: {
        value: cc.billingAddress.city,
        errorMessage: '',
        hasError: false,
        isRequired: true,
      },
      dayPhone: {
        value: cc.billingAddress.dayPhone,
        errorMessage: '',
        hasError: false,
        isRequired: true,
      },
      state: {
        value: getStateByAbbrWithLocale(cc.billingAddress.state as keyof typeof stateAbbreviations, market, locale),
        errorMessage: '',
        hasError: false,
        isRequired: true,
      },
      zipCode: {
        value: cc.billingAddress.zipCode,
        errorMessage: '',
        hasError: false,
        isRequired: true,
      },
    }));
  };

  const onCardNumberChange = useCallback(
    (hasError: boolean) =>
      ({ creditCardNum, errorMessage }: CreditCardType) => {
        const newCard = cardTypeLookup(creditCardNum, market);
        setFormData(prevState => ({
          ...prevState,
          cardNumber: {
            value: formatCreditCardNumber(creditCardNum),
            errorMessage: errorMessage ? errorMap[errorMessage] : '',
            hasError: hasError && !!errorMessage,
          },
        }));
        setCreditCard(newCard);
      },
    []
  );

  const onExpirationDateChange = useCallback(
    (hasError: boolean) =>
      ({ expirationDate, errorMessage }: CreditCardType) => {
        setFormData(prevState => ({
          ...prevState,
          expDate: {
            value: formatCreditCardExpDate(expirationDate),
            errorMessage: errorMessage ? errorMap[errorMessage] : errorMessage,
            hasError: creditCard && creditCard.plcc ? false : hasError && !!errorMessage,
          },
        }));
      },
    []
  );

  const addressChangeHandler = useCallback(
    (isBlur: boolean): ((e: AddressChangeType) => void) =>
      (e: AddressChangeType) => {
        let value: string, name: string, errorMessage: string, field: FormFieldType;
        if (typeof e === 'string') {
          name = 'state';
          field = {
            value: e,
            errorMessage: '',
            hasError: isBlur && !!e,
          };
        } else {
          name = e.target.name;
          value = e.target.value;
          switch (name) {
            case 'fullName':
              errorMessage = validateFullName(value);
              field = {
                value: isBlur ? value.trim() : value,
                errorMessage: localize(errorMessage),
                hasError: isBlur && !!errorMessage,
              };
              break;
            case 'addressLine1':
              errorMessage = validateAddress(value, market);
              field = {
                value: value,
                errorMessage: localize(errorMessage),
                hasError: isBlur && !!errorMessage,
              };
              break;
            case 'addressLine2':
              field = {
                value: value,
                errorMessage: '',
                hasError: false,
              };
              break;
            case 'city':
              errorMessage = validateCity(value, market);
              field = {
                value: value,
                errorMessage: localize(errorMessage),
                hasError: isBlur && !!errorMessage,
              };
              break;
            case 'zipCode':
              errorMessage = validateZipcode(value, market);
              field = {
                value: value,
                errorMessage: localize(errorMessage),
                hasError: isBlur && !!errorMessage,
              };
              break;
            case 'dayPhone':
              errorMessage = validatePhoneNumber(value);
              field = {
                value: value,
                errorMessage: localize(errorMessage),
                hasError: isBlur && !!errorMessage,
              };
              break;
            default:
              field = {
                value: value,
                errorMessage: '',
                hasError: false,
              };
              break;
          }
        }

        setFormData(prevState => ({
          ...prevState,
          [name]: field,
        }));
      },
    []
  );

  const handleAddressSelect = useCallback((address: AddressSelectPayload) => {
    const addressErrorMessage = validateAddress(address.addressLine1, market);
    const cityErrorMessage = validateCity(address.city, market);
    const zipCodeErrorMessage = validateZipcode(address.zipCode, market);

    setFormData(prevState => ({
      ...prevState,
      addressLine1: {
        value: address.addressLine1,
        errorMessage: localize(addressErrorMessage),
        hasError: !!addressErrorMessage,
      },
      city: {
        value: address.city,
        errorMessage: localize(cityErrorMessage),
        hasError: !!cityErrorMessage,
      },
      zipCode: {
        value: address.zipCode,
        errorMessage: localize(zipCodeErrorMessage),
        hasError: !!zipCodeErrorMessage,
      },
      state: {
        value: address.state,
        errorMessage: '',
        hasError: address.state === '',
      },
    }));
  }, []);

  const createCardReqPayload = (payload: BillingAddressPayload): AddCardReqPayload | EditCardReqPayload => {
    let body: AddCardReqPayload | EditCardReqPayload;
    if (paymentModalType === 'edit') {
      body = {
        id: creditCardId,
        default_card: isDefault,
        ecomApiBaseUrl,
        billing_address: payload,
        savedCardsDispatch,
      };
    } else {
      const creditCardNumber = formData.cardNumber.value.replace(/\s/g, '');
      let type = creditCard.type;
      type = WALLET_TYPE_MAP[type] ? WALLET_TYPE_MAP[type] : type;
      body = {
        id_type: 'VAULT',
        type,
        default_card: isDefault,
        last_four_digits: creditCardNumber.slice(-4),
        billing_address: payload,
        market,
        ecomApiBaseUrl,
        creditCardNumber,
        savedCardsDispatch,
      };
    }

    if (!creditCard.plcc) {
      const [month, year] = formData.expDate.value.split('/');
      body.expiration_month = month;
      body.expiration_year = new Date().getFullYear().toString().substring(0, 2) + year;
    }
    return body;
  };

  const onAddCard = (address: BillingAddressPayload) => {
    const payload: AddCardReqPayload | EditCardReqPayload = createCardReqPayload(address);
    if (paymentModalType === 'edit') {
      triggerEditCardReq(payload as EditCardReqPayload);
    } else {
      triggerAddCardReq(payload as AddCardReqPayload);
    }
  };

  const resetAvsRequest = () => {
    savedCardsDispatch({ type: ActionTypes.RESET_AVS_ADDRESS_REQ });
  };

  useEffect(fetchCards, []);

  useEffect(() => {
    if (getCardsReq.isFail || getCardsReq.isSuccess) {
      setIsReqInFlight(false);
    }
  }, [getCardsReq.isFail, getCardsReq.isSuccess]);

  /**
   * On Load Tealium View Event
   */

  useEffect(() => {
    const eventType = navigationType === 'hard' ? 'add' : 'view';
    fireTealiumViewTag({ page: 'Saved Cards', brandCode, brandTealium, recognitionStatus: 'authenticated', eventType });
    if (navigationType === 'hard') {
      setNavigationType('soft');
    }
  }, []);

  useEffect(() => {
    if (deleteCardReq.isSuccess) {
      savedCardsDispatch({ type: ActionTypes.RESET_CARDS });
      fetchCards();
    }
  }, [deleteCardReq.isSuccess]);

  useEffect(() => {
    if (savedCardsAvsReq.isSuccess && !isAvsMatchFound) {
      setIsVerifyAddressModalOpen(true);
      setUserEnteredAddress({
        ...userEnteredAddress,
        addressLine1: formData.addressLine1.value,
        addressLine2: formData.addressLine2.value,
        city: formData.city.value,
        dayPhone: formData.dayPhone.value,
        firstName: formData.fullName.value.split(' ')[0],
        lastName: formData.fullName.value.split(' ')[1],
        state: generateStateAbbrWithLocale(formData.state.value, market, locale),
        zipCode: formData.zipCode.value,
      });
    }
  }, [savedCardsAvsReq.isSuccess]);

  useEffect(() => {
    if (addCardReq.isSuccess || editCardReq.isSuccess) {
      setIsVerifyAddressModalOpen(false);
      fetchCards();
    }
  }, [addCardReq.isSuccess, editCardReq.isSuccess]);

  const renderNoCreditCards = (): JSX.Element => {
    return !isReqInFlight && !userHasCreditCards ? <NoCreditCards toggleAddPaymentModal={toggleAddPaymentModal} /> : <></>;
  };

  const renderGapCreditCardFooter = (): JSX.Element => {
    return !isReqInFlight && !isCanada ? <GapCreditCardFooterLink userHasCreditCards={userHasCreditCards} /> : <></>;
  };

  const renderCreditCardsList = (): JSX.Element => {
    return !isReqInFlight && userHasCreditCards ? (
      <div id='saved-cards-list-hui'>
        <div>
          <h1 className='font-sourcesans mb-8 text-2xl font-normal text-[#333]'>{localize('wallet.savedCards')}</h1>
        </div>
        <div id='card-container'>
          <ul>
            {creditCards.map(cc => (
              <CreditCard cc={cc} key={cc.id} onCardEdit={onCardEdit} isDefault={cc.defaultCard} />
            ))}
          </ul>
        </div>
        <div>
          <Button
            className='font-sourcesans text-cb-coreColor-dark border-cb-coreColor-dark hover:border-cb-textColor-medium hover:text-cb-textColor-medium hover:bg-cb-coreColor-white my-8 h-auto w-full min-w-[240px] whitespace-nowrap border-2 border-solid px-0 py-[0.8rem] text-center text-[0.9375rem] font-semibold uppercase leading-[normal] md:h-[2.75rem] md:w-[151px] md:min-w-[240px] md:p-[0.1rem]'
            kind='secondary'
            onClick={() => toggleAddPaymentModal()}
          >
            {localize('wallet.addNewCard')}
          </Button>
        </div>
      </div>
    ) : (
      <></>
    );
  };

  const renderAddPaymentModal = (): JSX.Element => {
    return isAddPaymentModalOpen ? (
      <AddPaymentModal
        formData={formData}
        isDefault={isDefault}
        creditCard={creditCard}
        onAddCard={onAddCard}
        setFormData={setFormData}
        modalType={paymentModalType}
        onDefaultChange={onDefaultChange}
        onCardNumberChange={onCardNumberChange}
        handleAddressSelect={handleAddressSelect}
        addressChangeHandler={addressChangeHandler}
        toggleAddPaymentModal={toggleAddPaymentModal}
        isAddPaymentModalOpen={isAddPaymentModalOpen}
        onExpirationDateChange={onExpirationDateChange}
        isVerifyAddressModalOpen={isVerifyAddressModalOpen}
      />
    ) : (
      <></>
    );
  };

  const renderAvsModal = (): JSX.Element => {
    return isVerifyAddressModalOpen ? (
      <VerifyAddressModal
        showServiceUnavailableMessage={showServiceUnavailableMessage}
        addressType='WALLET'
        address={userEnteredAddress}
        onAddCard={onAddCard}
        resetAvsRequest={resetAvsRequest}
        suggestedAddresses={suggestedAddresses}
      />
    ) : (
      <></>
    );
  };

  return (
    <div id='wallet-container' className='box-content bg-[white] px-4 pt-0 text-[#333] md:m-0 md:box-border md:max-w-[560px] md:p-2'>
      {isReqInFlight && <Loader />}
      {renderCreditCardsList()}
      {renderNoCreditCards()}
      {renderGapCreditCardFooter()}
      {renderAddPaymentModal()}
      {renderAvsModal()}
    </div>
  );
};
