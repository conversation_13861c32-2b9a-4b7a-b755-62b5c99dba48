import React, { FC, useState } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { creditCardsMapping, getBarclaysCardImage, isBarclaysCard } from '../../utils/saved-cards/savedCardsUtil';
import { CardDeleteConfirmationModal } from './CardDeleteConfirmationModal';
import type { Cards as CCProps } from './types';

export type CreditCardLogo = {
  src: string;
};

export type CreditCardProps = {
  cc: CCProps;
  isDefault: boolean;
  onCardEdit: (cc: CCProps) => void;
};

export const CreditCard: FC<CreditCardProps> = props => {
  const { cc, isDefault, onCardEdit } = props;
  const { fullType = '', type, lastFourDigits, expirationMonth, expirationYear } = cc as CCProps;
  const { localize } = useLocalize();
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  const toggleCardEditModal = () => {
    onCardEdit(cc);
  };

  const toggleCardDeleteModal = () => {
    setDeleteModalOpen(!deleteModalOpen);
  };

  const getCreditCardImage = (creditCardData: CCProps) => {
    if (isBarclaysCard(creditCardData)) {
      return getBarclaysCardImage(creditCardData);
    }
    return creditCardsMapping(fullType);
  };

  return (
    <div data-testid='creditCard' className='font-sourcesans border-b-[1px] border-[#cccccc] px-0 py-5 font-normal leading-6 text-[#333333] first:pt-0'>
      <div className='relative flex flex-row'>
        <div className='relative h-6 w-9 overflow-hidden'>
          <img className='absolute' src={getCreditCardImage(cc)} alt={type} />
        </div>
        <div className='flex text-black'>
          <h3 className='ml-[0.3215rem] text-base text-black'>{`${type}`}</h3>
          <p className='ml-[0.3215rem] text-base text-black'>{`****${lastFourDigits}`}</p>
        </div>
      </div>
      <div className='ml-[2.562rem] text-[0.938rem] text-[#666]'>
        {expirationMonth && expirationYear && `${localize('wallet.expires')}  ${expirationMonth}/${String(expirationYear).slice(-2)}`}
      </div>
      <div className='mt-4 flex flex-row items-start pb-0 text-[#000]'>
        <div className='flex flex-[2_1_auto] flex-row items-start'>
          <button className='flex-[0_0_auto] cursor-pointer text-[0.875rem] text-[#0466ca] hover:text-[#666]' onClick={() => toggleCardEditModal()}>
            {localize('wallet.edit')}
          </button>
          <span className='px-[0.3rem] leading-6 text-[#0466ca]'>|</span>
          <button className='flex-[0_0_auto] cursor-pointer text-[0.875rem] text-[#0466ca] hover:text-[#666]' onClick={toggleCardDeleteModal}>
            {localize('wallet.delete')}
          </button>
        </div>
        {isDefault && <p className='flex-auto items-end text-right text-sm leading-6 text-[#666]'>{localize('wallet.default')}</p>}
      </div>
      {deleteModalOpen && <CardDeleteConfirmationModal {...props} onClose={toggleCardDeleteModal} />}
    </div>
  );
};
