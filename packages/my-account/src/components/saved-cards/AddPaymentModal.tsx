import { FC, useState, useEffect, Fragment, SyntheticEvent } from 'react';
import { Modal } from '@ecom-next/core/legacy/modal';
import { But<PERSON> } from '@ecom-next/core/migration/button';
import { Checkbox } from '@ecom-next/core/legacy/checkbox';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { CreditCardInput } from '../common/legacy/CreditCardInput';
import { AddressFormFields } from '../shipping-addresses/AddressFormFields';
import { noop } from '../../utils/form/formUtil';
import { convertPayloadForAvs, generateStateAbbrWithLocale } from '../../utils/address/addressUtil';
import { toTitleCase } from '../../utils/shared/toTitleCaseUtil';
import { validateForm<PERSON>ith<PERSON>ey, validateFormWithKeys } from '../../utils/validation/form/formValidation';
import type { ErrorMap, FormData } from '../../utils/validation/form/types';
import { ErrorNotification } from '../common/legacy/Notifications';
import { triggerAVSReq } from '../../requests/avs/avsRequests';
import { useSavedCards } from '../../hooks/useSavedCards';
import { BillingAddressPayload } from '../../context/saved-cards/types';
import * as ActionTypes from '../../constants/saved-cards/savedCardsActionTypes';
import { fireTealiumLinkTag } from '../../utils/tealium/tealiumUtil';
import { SavedCardsAvsReqPayload } from '../../requests/avs/types';
import { AddPaymentModalProps } from './types';

export const AddPaymentModal: FC<AddPaymentModalProps> = props => {
  const {
    formData,
    isDefault,
    modalType,
    onAddCard,
    creditCard,
    setFormData,
    onDefaultChange,
    handleAddressSelect,
    addressChangeHandler,
    toggleAddPaymentModal,
    isAddPaymentModalOpen,
    onCardNumberChange,
    onExpirationDateChange,
    isVerifyAddressModalOpen,
  } = props;

  const { market, brandCode, brandAbbr, ecomApiBaseUrl, locale } = usePageContext();
  const {
    savedCardsState: { addCardReq, editCardReq, savedCardsAvsReq, isAvsMatchFound },
    savedCardsDispatch,
  } = useSavedCards();

  const { localize } = useLocalize();
  const brandTealium = brandAbbr === 'gpfs' ? 'gapfs' : brandAbbr;

  const blankErrorMap: ErrorMap = {
    cardNumber: localize('wallet.errors.required.cardNumber'),
    expDate: localize('wallet.errors.required.expirationDate'),
    fullName: localize('address.errors.required.fullName'),
    addressLine1: localize('address.errors.required.addressLine1'),
    city: localize('address.errors.required.townCity'),
    dayPhone: localize('address.errors.required.phoneNumber'),
    zipCode: localize('address.errors.required.US.zipcode'),
    state: localize('address.errors.required.US.state'),
  };

  const [isErrorMessageShown, setIsErrorMessageShown] = useState(false);
  const [isReqInFlight, setIsReqInFlight] = useState(false);

  const onModalClose = (resetForm = true) => {
    toggleAddPaymentModal(resetForm);
    setIsErrorMessageShown(false);
    setIsReqInFlight(false);
    savedCardsDispatch({ type: modalType === 'edit' ? ActionTypes.RESET_EDIT_CARDS : ActionTypes.RESET_ADD_CARDS });
  };

  useEffect(() => {
    if (addCardReq.isSuccess || editCardReq.isSuccess) {
      onModalClose(true);
      fireTealiumLinkTag({
        page: 'Saved Cards',
        eventName: 'save_card_click',
        brandCode,
        brandTealium,
        recognitionStatus: 'authenticated',
      });
    } else if (addCardReq.isFail || editCardReq.isFail) {
      setIsErrorMessageShown(true);
      setIsReqInFlight(false);
      fireTealiumLinkTag({
        page: 'Saved Cards',
        eventName: 'saved_card_failure',
        brandCode,
        brandTealium,
        recognitionStatus: 'authenticated',
      });
    }
  }, [addCardReq.isFail, addCardReq.isSuccess, editCardReq.isFail, editCardReq.isSuccess]);

  const buildAvsPayload = () => {
    const avsPayload = {
      address_id: '',
      address_line_1: formData?.addressLine1.value,
      address_line_2: formData?.addressLine2.value,
      city: formData?.city.value,
      state: formData?.state.value,
      zip_code: formData?.zipCode.value,
      country_code: market?.toUpperCase(),
    };
    return avsPayload;
  };

  const onAddEditCard = (address: BillingAddressPayload) => {
    onAddCard(address);
  };

  const getBillingAddressPayload = (form: FormData): BillingAddressPayload => {
    return {
      first_name: toTitleCase(form.fullName.value).split(' ')[0],
      last_name: toTitleCase(form.fullName.value).split(' ')[1],
      address_line_1: toTitleCase(form.addressLine1.value),
      address_line_2: form.addressLine2.value,
      city: toTitleCase(form.city.value),
      state: generateStateAbbrWithLocale(form.state.value, market, locale),
      zip_code: form.zipCode.value,
      day_phone: form.dayPhone.value.replace(/[- )(]/g, ''),
      country_code: market.toUpperCase(),
    };
  };

  useEffect(() => {
    if (isAvsMatchFound || savedCardsAvsReq.isFail) {
      const billingAddress: BillingAddressPayload = getBillingAddressPayload(formData);
      onAddEditCard(billingAddress);
      savedCardsDispatch({ type: 'RESET_AVS_ADDRESS_REQ' });
    } else if (savedCardsAvsReq.isSuccess) {
      onModalClose(false);
    }
  }, [isAvsMatchFound, savedCardsAvsReq.isFail]);

  const onFormSubmit = (e: SyntheticEvent) => {
    e && e.preventDefault();
    if (!isReqInFlight) {
      let formValidation;
      const editMode = modalType === 'edit';

      if (creditCard.plcc) {
        formValidation = validateFormWithKeys(formData, blankErrorMap, editMode ? ['cardNumber', 'expDate'] : ['expDate']);
      } else {
        formValidation = validateFormWithKey(formData, blankErrorMap, editMode ? 'cardNumber' : '');
      }

      const { newFormData, isFormValid } = formValidation;
      if (isFormValid) {
        const billingAddress: BillingAddressPayload = getBillingAddressPayload(newFormData);

        //AVS Request
        const transformAvsPayload = convertPayloadForAvs(buildAvsPayload());
        const payload: SavedCardsAvsReqPayload = {
          ecomApiBaseUrl: ecomApiBaseUrl,
          body: transformAvsPayload,
          dispatch: savedCardsDispatch,
        };
        setIsReqInFlight(true);
        if (market === 'ca') {
          onAddEditCard(billingAddress);
        } else {
          triggerAVSReq(payload);
        }
      } else {
        setFormData(newFormData);
      }
    }
  };

  const renderAddPaymentModal = (): JSX.Element => {
    const onAddressChange = addressChangeHandler(false);
    const onAddressBlur = addressChangeHandler(true);

    let title: string = localize('wallet.addModal.addPaymentMethod');
    let buttonSubmit: string = localize('wallet.addModal.saveCard');
    let editMode: boolean = false;

    if (modalType === 'edit') {
      title = localize('wallet.editModal.editPaymentMethod');
      buttonSubmit = localize('wallet.editModal.updateCard');
      editMode = true;
    }

    return (
      <Modal
        isOpen={isAddPaymentModalOpen}
        title={title}
        onClose={() => onModalClose(false)}
        closeButtonAriaLabel={localize('accessibility.ModalCloseButton')}
        className='add-payment-modal'
        id='saved-cards-add-edit-card-modal-hui'
      >
        <div className='p-10'>
          {isErrorMessageShown && <ErrorNotification className='font-sourcesans w-full' message={localize('wallet.errors.globalMessageError')} />}
          <h5 className='mb-[30px]'>
            <b>{localize('wallet.addModal.creditCardDetails')}</b>
          </h5>
          <form className='payment-form-template' noValidate onSubmit={onFormSubmit}>
            <div>
              <div>
                <div className='relative flex flex-row justify-around'>
                  <div className='relative mr-1 mt-0 h-9 min-w-[48px] overflow-hidden'>
                    <img
                      className='absolute top-0 min-h-[30px] min-w-[48px] transition-[top] duration-[0.5s] ease-[ease-in-out]'
                      src={creditCard.image}
                      alt='credit-card'
                    />
                  </div>
                  <CreditCardInput
                    type='tel'
                    id='cardNumber'
                    className='fs-exclud basis-[84%] text-[1.0625rem] [&_#cardNumber]:mt-0 [&_#cardNumber]:w-full [&_#cardNumber]:pt-1.5'
                    name='cardNumber'
                    value={formData.cardNumber.value}
                    hasError={formData.cardNumber.hasError}
                    errorMessage={formData.cardNumber.errorMessage}
                    label={localize('wallet.addModal.cardNumber')}
                    onChange={editMode ? noop : onCardNumberChange(false)}
                    onBlur={editMode ? noop : onCardNumberChange(true)}
                    editMode={editMode}
                  />
                </div>
              </div>
            </div>
            {!creditCard.plcc && (
              <CreditCardInput
                id='expirationDate'
                className='fs-exclud'
                name='expirationDate'
                type='tel'
                label={localize('wallet.addModal.expirationDate')}
                value={formData.expDate.value}
                hasError={formData.expDate.hasError}
                errorMessage={formData.expDate.errorMessage}
                onChange={onExpirationDateChange(false)}
                onBlur={onExpirationDateChange(true)}
              />
            )}
            <h5 className='mt-[1.8125rem]'>
              <b>{localize('wallet.addModal.billingAddress')}</b>
            </h5>
            <AddressFormFields
              addressLine1={formData.addressLine1}
              addressLine2={formData.addressLine2}
              city={formData.city}
              dayPhone={formData.dayPhone}
              fullName={formData.fullName}
              state={formData.state}
              zipCode={formData.zipCode}
              onAddressChange={onAddressChange}
              onAddressFieldBlur={onAddressBlur}
              handleAddressSelect={handleAddressSelect}
            />
            <div className='[&_label]:font-sourcesans mt-6 [&_label]:text-base [&_label]:md:text-[1.1rem]'>
              <Checkbox
                id='isDefault'
                className='before:!border-[#002554] before:!bg-[#002554]'
                labelText={localize('wallet.addModal.setAsDefault')}
                onChange={onDefaultChange}
                isChecked={isDefault}
              />
            </div>
            <div className='mx-auto my-0 w-[280px] pb-0'>
              <Button
                className='font-sourcesans bg-cb-coreColor-dark cursor-pointer items-center justify-center rounded-none font-semibold uppercase'
                kind='primary'
                type='submit'
                fullWidth={true}
                id='payment-save-card-button'
                isDisabled={isReqInFlight}
              >
                {buttonSubmit}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    );
  };

  return <Fragment>{!isVerifyAddressModalOpen && renderAddPaymentModal()}</Fragment>;
};
