import * as React from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { EmptyWalletIcon } from '../../assets/saved-cards/EmptyWalletIcon';

export type NoCreditCardsProps = {
  toggleAddPaymentModal: () => void;
};

export const NoCreditCards: React.FC<NoCreditCardsProps> = props => {
  const { localize } = useLocalize();
  const { toggleAddPaymentModal } = props;

  return (
    <div id='saved-cards-no-cards-hui' className='relative flex flex-col px-4 text-center font-normal'>
      <h1 className='mt-8 text-[2rem] font-light leading-10 text-[#333]'>{localize('wallet.savedCards')}</h1>
      <div className='mx-auto my-0 mt-12 h-[120px] w-[133px] md:mt-8'>
        <EmptyWalletIcon />
      </div>
      <p className='mx-auto my-0 mt-8 w-full max-w-[404px] break-words text-[1.4rem] font-light leading-7 text-black'>{localize('wallet.noCardsCopy')}</p>
      <div className='mx-auto my-0 mt-5 w-full max-w-[404px] break-words text-center text-base leading-6 text-black'>
        <button id='no-cred-cards' className='bg-[white]' onClick={() => toggleAddPaymentModal()}>
          <span className='cursor-pointer underline transition-[color] duration-[0.15s] ease-[ease] hover:text-[#666]'>{localize('wallet.addCardCopy')}</span>{' '}
          {localize('wallet.fasterCheckoutCopy')}
        </button>
      </div>
    </div>
  );
};
