import { render, screen } from '@testing-library/react';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { NoCreditCards, NoCreditCardsProps } from '../NoCreditCards';

const defaultProps = {
  toggleAddPaymentModal: jest.fn(),
};

const renderComponent = (props: NoCreditCardsProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <StitchStyleProvider brand={'gap'}>
        <NoCreditCards {...props} />
      </StitchStyleProvider>
    </LocalizationProvider>
  );
};

describe('No Credit Cards', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Saved Cards')).toBeInTheDocument();
  });

  it('should render text You currently dont have any payment methods saved', () => {
    renderComponent(defaultProps);
    expect(screen.getByText("You currently don't have any payment methods saved")).toBeInTheDocument();
  });

  it('should render text Add a credit or debit card', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Add a credit or debit card')).toBeInTheDocument();
  });

  it('should render text You currently dont have any payment methods saved', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('for a faster checkout experience!')).toBeInTheDocument();
  });
});
