import { render, screen } from '@testing-library/react';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { GapCreditCardFooterLink, GapCreditCardFooterLinkProps } from '../GapCreditCardFooterLink';

const defaultProps = {
  userHasCreditCards: false,
};

const renderComponent = (props: GapCreditCardFooterLinkProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en_US' translations={translations} market='us'>
      <StitchStyleProvider brand={'gap'}>
        <GapCreditCardFooterLink {...props} />
      </StitchStyleProvider>
    </LocalizationProvider>
  );
};

describe('Gap Credit Card Footer Link', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Manage my Gap Inc. Rewards Credit Cards')).toBeInTheDocument();
  });

  it('uses the correct class based if the user has credit cards', () => {
    renderComponent({ ...defaultProps, userHasCreditCards: true });
    expect(screen.getByText('Manage my Gap Inc. Rewards Credit Cards').closest('p')).toHaveClass(' text-center mt-0 mb-8 mx-0 p-0 md:text-left md:pt-0');
  });

  it('uses the correct class based if the user has no credit cards', () => {
    renderComponent(defaultProps);
    expect(screen.getByText('Manage my Gap Inc. Rewards Credit Cards').closest('p')).toHaveClass('text-center mt-0 mb-8 mx-0 pt-0 md:pt-6');
  });

  it('should have the path /my-account/gap-cards as href value in Manage my Gap Inc. Rewards Credit Cards', () => {
    renderComponent(defaultProps);
    const link = screen.getByText('Manage my Gap Inc. Rewards Credit Cards');
    expect(link).toBeInTheDocument();
    const linkEl = screen.getByRole('link', { name: 'Manage my Gap Inc. Rewards Credit Cards' });
    expect(linkEl).toHaveAttribute('href', '/my-account/gap-cards');
  });
});
