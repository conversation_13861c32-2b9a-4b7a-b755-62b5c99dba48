import { BillingAddressPayload } from '../../context/saved-cards/types';
import type { FormData } from '../../utils/validation/form/types';
import { CreditCardType, Payload } from '../common/legacy/CreditCardInput';
import { AddressSelectPayload, AddressChangeType } from '../shipping-addresses/AddressFormFields';

export type BillingAddress = {
  addressId?: string;
  addressLine1: string;
  addressLine2: string;
  addressLine3?: string;
  addressVerifiedStatus?: string;
  backwardCompatibleAddressId?: string;
  city: string;
  countryCode: string;
  dayPhone: string;
  dpvCode?: string;
  eveningPhone?: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  nickName?: string;
  state: string;
  title?: string;
  zipCode: string;
};

export type Cards = {
  billingAddress: BillingAddress;
  cardBrand: boolean;
  cardToken?: string;
  crediCardTier?: string;
  cvvValidated?: boolean;
  defaultCard: boolean;
  expirationMonth: string;
  expirationYear: string;
  fullType?: string;
  id: string;
  idType: string;
  isExpired?: boolean;
  issueDate?: string;
  lastFourDigits: string;
  lastUsedDate?: string;
  nearTierCode?: string;
  plcc?: boolean;
  temporary?: boolean;
  type: string;
};

export type CreditCardIcon = {
  image?: string;
  plcc?: boolean;
  type: string;
};

export type AddPaymentModalProps = {
  addressChangeHandler: (isBlur: boolean) => (e: AddressChangeType) => void;
  creditCard: CreditCardIcon;
  formData: FormData;
  handleAddressSelect: (address: AddressSelectPayload) => void;
  isAddPaymentModalOpen: boolean;
  isDefault: boolean;
  isVerifyAddressModalOpen: boolean;
  modalType?: string;
  onAddCard: (address: BillingAddressPayload) => void;
  onCardNumberChange: (hasError: boolean) => (payload: Payload | CreditCardType) => void;
  onDefaultChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onExpirationDateChange: (hasError: boolean) => (payload: Payload | CreditCardType) => void;
  setFormData: (formData: FormData) => void;
  toggleAddPaymentModal: (resetForm: boolean) => void;
};
