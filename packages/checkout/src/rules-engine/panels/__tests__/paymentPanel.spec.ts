import { DraftOrderContextValue } from '../../../contexts/types';
import { PanelStates } from '../../types';
import { getPanelState } from '../paymentPanel';

describe('Payment Panel state tests', () => {
  it('Should panel be required, if there is no payment information saved on draftOrder', () => {
    const draftOrder = { session: { recognition_status: 'guest' } } as unknown as DraftOrderContextValue;
    const panelState = getPanelState(draftOrder);
    expect(panelState).toBe(PanelStates.REQUIRED);
  });
  it('Should panel be expanded, if there is payment information saved on draftOrder and no error 710', () => {
    const draftOrder = {
      session: { recognition_status: 'guest' },
      paymentPanel: { paymentMethods: [{ isSelected: true }] },
    } as unknown as DraftOrderContextValue;
    const panelState = getPanelState(draftOrder);
    expect(panelState).toBe(PanelStates.EXPANDED);
  });

  it('Should panel be required, if there is payment information saved on draftOrder and contains error 710 and no cvv required', () => {
    const draftOrder = {
      session: { recognition_status: 'guest' },
      paymentPanel: { paymentMethods: [{ isSelected: true, errorList: [{ errorCode: '710' }], cardInfo: { cvvRequired: true } }] },
    } as unknown as DraftOrderContextValue;
    const panelState = getPanelState(draftOrder);
    expect(panelState).toBe(PanelStates.REQUIRED);
  });
});
