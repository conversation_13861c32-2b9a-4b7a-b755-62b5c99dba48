import { clientFetch } from '@ecom-next/utils/clientFetch';
import { DraftOrderContextValue } from '../../contexts/types';
import { createRequestBody, getPaymentToken } from '../getPaymentToken/getPaymentToken';
import { validateDraftOrderPayload } from '../getPaymentToken/validateDraftOrderPayload';
import { logNewRelicError } from '../newrelic-logger';

jest.mock('@ecom-next/utils/clientFetch');
jest.mock('../getPaymentToken/validateDraftOrderPayload');
jest.mock('../newrelic-logger');

describe('getPaymentToken', () => {
  const mockDraftOrderPayload = {
    checkoutPanel: {
      lineItems: [{ totalPrice: '100', quantity: 1, productStyleDescription: 'Jeans' }],
    },
    session: {
      email: '<EMAIL>',
    },
    shippingAddressPanel: {
      shippingAddressList: [
        {
          firstName: 'John',
          lastName: 'Doe',
          addressLine1: '123 Main St',
          city: 'City',
          state: 'CA',
          postalCode: '12345',
          country: 'US',
          phone: '2813308004',
          isSelected: true,
        },
      ],
    },
    orderSummaryPanel: { totalPrice: 100, estimatedTax: 10, shippingPrice: 5 },
  } as unknown as DraftOrderContextValue;

  const mockPaymentResponse = {
    token: 'token123',
    session_id: 'session456',
    token_type: 'Bearer',
    expires: '2025-01-01T00:00:00Z',
    redirect_checkout_url: 'https://example.com/checkout',
  };

  const ecomApiBaseUrl = 'https://api.example.com';
  const paymentMode = 'AFTERPAY';
  const requestHost = 'example.com';

  beforeEach(() => {
    jest.clearAllMocks();
    (clientFetch as jest.Mock).mockResolvedValue(mockPaymentResponse);
  });

  it('should create a valid request body', () => {
    const requestBody = createRequestBody(mockDraftOrderPayload, requestHost, paymentMode, 'gapfs', 'USD');

    expect(requestBody).toEqual({
      order_header: {
        brand: 'gapfs',
        channel: 'WEB',
        currency: 'USD',
        market_code: 'US',
        payment_mode: paymentMode,
      },
      merchant: {
        redirect_confirm_url: `https://${requestHost}/checkout/place-order`,
        redirect_cancel_url: `https://${requestHost}/checkout/place-order`,
      },
      order_details: {
        ship_charge_amount: '5.00',
        total_tax_amount: '10.00',
        order_total: 100,
        order_lines: [
          {
            item: {
              style_description: 'Jeans',
            },
            line_total: '100.00',
            quantity: 1,
          },
        ],
      },
      customer_details: {
        id: expect.any(String),
        first_name: 'John',
        last_name: 'Doe',
        email_address: '<EMAIL>',
        phone_number: '2813308004',
        billing_address: [
          {
            address_line1: '123 Main St',
            city_name: 'City',
            country_code: 'US',
            postal_code: '12345',
            state_province_code: 'CA',
          },
        ],
        shipping_address: [
          {
            address_line1: '123 Main St',
            city_name: 'City',
            country_code: 'US',
            postal_code: '12345',
            state_province_code: 'CA',
          },
        ],
      },
      audit_data: {
        client_app: 'ECOM',
        tracking_id: expect.any(String),
      },
    });
  });

  it('should return payment token on successful request', async () => {
    const response = await getPaymentToken({ ecomApiBaseUrl, body: mockDraftOrderPayload, paymentMode, requestHost, brand: 'gap', currency: 'USD' });

    expect(validateDraftOrderPayload).toHaveBeenCalledWith(mockDraftOrderPayload,'USD');
    expect(clientFetch).toHaveBeenCalledWith(
      `${ecomApiBaseUrl}/payment_initiations/v1/tokens`,
      expect.objectContaining({
        body: expect.any(String),
        credentials: 'include',
        headers: expect.objectContaining({
          'x-client-application-name': 'checkout-ui',
        }),
        method: 'POST',
      })
    );
    expect(response).toEqual(mockPaymentResponse);
  });

  it('should throw an error when clientFetch fails', async () => {
    const mockError = new Error('Network Error');
    (clientFetch as jest.Mock).mockRejectedValue(mockError);

    await expect(getPaymentToken({ ecomApiBaseUrl, body: mockDraftOrderPayload, paymentMode, requestHost, brand: 'on', currency: 'USD' })).rejects.toThrow(mockError);
    expect(logNewRelicError).toHaveBeenCalledWith(
      mockError,
      expect.objectContaining({
        caller: 'getPaymentToken()',
        feature: expect.any(String),
        message: expect.stringContaining('Error fetching third party payment token'),
      })
    );
  });

  it('should validate draftOrderPayload', async () => {
    await getPaymentToken({ ecomApiBaseUrl, body: mockDraftOrderPayload, paymentMode, requestHost, brand: 'gapfs', currency: 'USD' });
    expect(validateDraftOrderPayload).toHaveBeenCalledWith(mockDraftOrderPayload, 'USD');
  });
});
