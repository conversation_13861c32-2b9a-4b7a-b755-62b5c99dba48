import { clientFetch } from '@ecom-next/utils/clientFetch';
import { formatData } from './formatData';
import type { CompositeApiResponse, UpdatePaymentCompositeAPIRequestParams } from './types';
import { getCompositeApiHeaders } from './headers';
import { getPaymentTypeInfo } from './helpers/paymentsHelper';

export const updatePaymentMethod = async ({ res, ecomApiBaseUrl, context, body }: UpdatePaymentCompositeAPIRequestParams) => {
  if (!body) {
    throw new Error('Missing payload for updatePaymentMethod');
  }
  const { draftOrderId, paymentId, paymentMethod, paymentType, vendorOrderDetails } = body;

  if (!paymentType) {
    throw new Error('Missing paymentType from updatePaymentMethod payload');
  }

  const updatePaymentBody = {
    draft_order_id: draftOrderId,
    payment_method: {
      payment_type: paymentType,
      [paymentType?.toLowerCase() as string]: getPaymentTypeInfo(paymentType, paymentMethod, vendorOrderDetails),
    },
  };

  const data = await clientFetch<CompositeApiResponse>(`${ecomApiBaseUrl}/ui_composite_checkouts/v1/payments/${paymentId}`, {
    method: 'PUT',
    body: JSON.stringify(updatePaymentBody),
    headers: getCompositeApiHeaders(context),
    credentials: 'include',
  });

  return formatData({ res, data });
};
