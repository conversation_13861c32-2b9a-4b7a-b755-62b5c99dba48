import { PaymentMethod, SelectPaymentMethodPayload } from '../../../../contexts/types';
import { getCCBrand, getCCType } from '../utils/utils';

type selectPaymentPayloadProps = {
  draftOrderId: string;
  paymentId: string;
  paymentMethod: PaymentMethod;
};
export const selectPaymentPayload = ({ draftOrderId, paymentId, paymentMethod }: selectPaymentPayloadProps): SelectPaymentMethodPayload => {
  const { billingAddress, cardInfo } = paymentMethod || {};
  const { cardId, cardType, cardNumber, expirationMonth, expirationYear, temporaryCardIndicator = false, defaultCard = false } = cardInfo! || {};
  const creditCardType = getCCType(cardNumber!, cardInfo);
  const cardBrand = getCCBrand(cardNumber!, paymentMethod);
  return {
    draftOrderId,
    paymentId,
    paymentMethod: {
      draftOrderId,
      paymentId,
      cardInfo: {
        cardId: cardId!,
        cardType: cardType!,
        cardNumber: cardNumber!,
        expirationMonth: expirationMonth!,
        expirationYear: expirationYear!,
        temporaryCardIndicator: temporaryCardIndicator,
        isDefault: defaultCard || false,
        type: creditCardType,
        brand: cardBrand,
      },
      billingAddress: billingAddress,
    },
    paymentType: 'CREDIT_CARD',
  };
};
