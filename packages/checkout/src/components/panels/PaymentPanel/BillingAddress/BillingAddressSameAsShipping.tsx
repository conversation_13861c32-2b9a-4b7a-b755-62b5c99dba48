import { FC } from 'react';
import classNames from 'classnames';
import { Notification } from '@ecom-next/core/migration/notification';
import { Checkbox } from '@ecom-next/core/migration/checkbox';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useFormValidation } from '@ecom-next/my-account/validation';
import { useCheckout } from '../../../../contexts/CheckoutProvider';
import { ShippingAddress } from '../../../../contexts/types';
import { normalizePhone } from '../../../../utils/normalize';
import { ShippingAndBillingAddressFormFields } from '../../ShippingAddress/forms/ShippingAddressForm';
import { PaymentMethodAddState } from '../PaymentForms/types';

type BillingAddressSameAsShippingProps = { editClickState?: boolean; selectedAddress: ShippingAddress };

export const BillingAddressSameAsShipping: FC<BillingAddressSameAsShippingProps> = ({ selectedAddress, editClickState }) => {
  const { addressLine1, city, country, postalCode, state, phone } = selectedAddress || {};

  const { localize } = useLocalize();
  const billingAddressHeader = localize('payment.paymentMethodAddForm.billingAddressHeaderText');
  const billingAddressCheckBoxLabel = localize('payment.paymentMethodAddForm.billing.checkBox.labelText');
  const headerTextClass = classNames('cb-display-sm h-[19px] font-bold leading-[19px] mb-4');

  const { draftOrder } = useCheckout();
  const { bopisOnlyBag } = draftOrder?.paymentPanel?.conditionals || {};
  const { shippingAddressList } = draftOrder?.shippingAddressPanel || {};
  const isUpsAccessPoint = shippingAddressList?.some(address => address.addressLocationType === 'UPS' && address.isSelected);
  const showSameAsShippingCheckbox = !(isUpsAccessPoint || bopisOnlyBag) && selectedAddress;

  const { formData, setFormValue, setValidateFormOn, resetFormExceptFieldsError } = useFormValidation<PaymentMethodAddState>();

  const handleSetSameAsShipping = () => {
    if (!formData?.billingAddressCheckbox?.value) {
      resetFormExceptFieldsError(['cardNumber', 'expiration', 'cvv']);
      setValidateFormOn(0);
    }
    setFormValue('billingAddressCheckbox', !formData?.billingAddressCheckbox?.value);
  };

  return (
    <>
      <div className={headerTextClass}>{billingAddressHeader}</div>
      {showSameAsShippingCheckbox && (
        <div className='cb-base-compact mb-4 gap-2' data-testid='use-delivery-address-checkbox'>
          <Checkbox
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleSetSameAsShipping();
              }
            }}
            onChange={handleSetSameAsShipping}
            labelText={billingAddressCheckBoxLabel}
            isChecked={formData?.billingAddressCheckbox?.value}
          />
        </div>
      )}
      {formData?.billingAddressCheckbox?.value && showSameAsShippingCheckbox ? (
        <div className='max-h-[89px]'>
          <Notification isDismissible={false} kind='information' showIcon={false} inline={false} dismissButtonLabel=''>
            <div className='cb-base-compact fs-mask'>
              <p>{addressLine1}</p>
              <p>
                {city} {state} {country} {postalCode}
              </p>
              <p>{normalizePhone(phone)}</p>
            </div>
          </Notification>
        </div>
      ) : (
        <ShippingAndBillingAddressFormFields editClickState={editClickState} />
      )}
    </>
  );
};
