import { NotificationInfoIcon } from '@ecom-next/core/migration/icons';
import { Notification } from '@ecom-next/core/migration/notification';
import { DeliveryGroupLists, ShippingMethodDetails } from '../../../contexts/types';

export const OfferDetails = ({ offerDetails, shippingMethod }: { offerDetails: DeliveryGroupLists['offerDetails']; shippingMethod: ShippingMethodDetails }) => {
  const { shippingMethodName } = shippingMethod;
  const offerDetailsList = offerDetails?.replace(/\./, '&').split('&');

  const displayOfferDetails = offerDetailsList && shippingMethodName === 'No Rush';

  return (
    displayOfferDetails && (
      <div className='cb-base-note mt-1'>
        <Notification kind={'information'} showIcon={true} isDismissible={false} inline={true} icon={<NotificationInfoIcon />}>
          <span className='text-cb-textColor-link font-bold'>{offerDetailsList[0]}</span>
          <span>{offerDetailsList[1]}</span>
        </Notification>
      </div>
    )
  );
};
