'use client';
import { useRef, useState, useEffect } from 'react';
import { HbWidgetModal } from '@hubbox/web-components/dist/components/widget-modal/widget-modal.react.component.js';
import SingleWidgetManager from '@hubbox/single-widget-manager';
import { Locale } from '@ecom-next/utils/server';

import { CreateShippingAddressPayload, MarketCode } from '../../../contexts/types';
import { getFirstAndLastName, removeXMLWhiteSpace, toTitleCase } from '../../panels/ShippingAddress/utils';
import { HUB_BOX_CONFIG_ID, UPS_ACCESS_POINT_UTIL } from '../../../utils/constants';
// TODO: During integration Uncomment below line for handling rules engine for panel collapse/exapnd state handling
// import { useCheckoutUiStates } from '@ecom-next/checkout/CheckoutUiStateProvider';

export type HubBoxContainerProps = {
  addShippingAddressDispatcher: (formObj: CreateShippingAddressPayload) => void;
  deliveryGroupId: string | undefined;
  draftOrderId: string;
  locale: Locale;
  marketCode: MarketCode;
  saveUPSStoreName?: React.Dispatch<React.SetStateAction<string>>;
  setShowHubBoxWidget: React.Dispatch<React.SetStateAction<boolean>>;
  showHubBoxWidget: boolean;
};

const HubBoxContainer = ({
  marketCode,
  locale,
  showHubBoxWidget,
  setShowHubBoxWidget,
  saveUPSStoreName,
  deliveryGroupId,
  draftOrderId,
  addShippingAddressDispatcher,
}: HubBoxContainerProps) => {
  const widgetModalRef = useRef<HTMLElement>(null);

  const [isHbWidgetReady, setIsHbWidgetReady] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onHubboxComplete = (values: any) => {
    const {
      address: { street1, city, region, postcode, phone = '**********' },
      pickupPerson: { name },
    } = values;
    const { name: storeName } = values || {};
    const fullName = getFirstAndLastName(name);
    const firstName = toTitleCase(fullName[0])?.trim();
    const lastName = toTitleCase(fullName[1])?.trim();
    const formObj = {
      draftOrderId: draftOrderId,
      deliveryGroupId: deliveryGroupId,
      shippingAddress: {
        addressId: '',
        title: '',
        firstName: firstName,
        lastName: lastName,
        addressLine1: toTitleCase(removeXMLWhiteSpace(street1)),
        addressLine2: '',
        addressLine3: '',
        city: toTitleCase(city),
        state: region,
        country: marketCode,
        postalCode: postcode,
        phone: phone,
        isDefault: false,
        shipToUpsIndicator: true,
        addressLocationType: UPS_ACCESS_POINT_UTIL,
        verificationStatus: 'VERIFIED',
        deliveryPointValidation: 'CONFIRMED_DELIVERY_POINT',
        isSelected: false,
      },
    };
    addShippingAddressDispatcher(formObj);

    saveUPSStoreName?.(storeName);
    setShowHubBoxWidget(false);
    // TODO: During integration Uncomment below line for handling rules engine for panel collapse/exapnd state handling
    // setUiStates(state => ({ ...state, upsDetails: { isOpen : false} }));

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (widgetModalRef.current as any).open = false;
  };

  // TODO: During integration Uncomment below line for handling rules engine for panel collapse/exapnd state handling
  // const { setUiStates } = useCheckoutUiStates();

  const language = locale.split('_')[0] || 'en';

  useEffect(() => {
    const singleWidgetManager = new SingleWidgetManager({
      deferRender: true,
      iframeUrl: SingleWidgetManager.iframeUrls.PRODUCTION,
      healthCheck: false,
      isDebug: false,
      iframeParams: {
        configId: HUB_BOX_CONFIG_ID[marketCode],
        locale: language,
        defaultLocale: 'en',
      },
    });

    singleWidgetManager.events.subscribe(singleWidgetManager.topics.subscribe.BOOT_FAILED, () => {});

    singleWidgetManager.events.subscribe(singleWidgetManager.topics.subscribe.WIDGET_READY, () => {
      setIsHbWidgetReady(true);
    });

    singleWidgetManager.events.subscribe(singleWidgetManager.topics.subscribe.COLLECT_POINT_CONFIRMED, event => {
      onHubboxComplete(event.message);
    });

    if (widgetModalRef.current) {
      singleWidgetManager.events.emit(singleWidgetManager.topics.emit.RESET_STATE);
      /* eslint-disable @typescript-eslint/no-explicit-any */
      (widgetModalRef.current as any).locale = language;
      (widgetModalRef.current as any).open = true;
      widgetModalRef.current.appendChild(singleWidgetManager.iframeElem);
    }
  }, []);

  useEffect(() => {
    if (showHubBoxWidget && isHbWidgetReady) {
      setShowHubBoxWidget(false);
      // TODO: During integration Uncomment below line for handling rules engine for panel collapse/exapnd state handling
      // setUiStates(state => ({ ...state, upsDetails: { isOpen : false} }));
    }
    if (showHubBoxWidget && widgetModalRef.current) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      (widgetModalRef.current as any).open = true;
    }
  }, [showHubBoxWidget, isHbWidgetReady]);

  return (
    <div className='hubbox-widget-wrapper'>
      <HbWidgetModal ref={widgetModalRef} />
    </div>
  );
};

export default HubBoxContainer;
