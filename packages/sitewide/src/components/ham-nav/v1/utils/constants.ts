'use client';

export const features = {
  ENABLE_ON_GOTHAM_FONT: 'enable-on-gotham-font',
  FIND_GAP_CA_FONT_COLOR_UPDATE: 'find-gap-ca-font-color-update',
  FIND_GAP_US_FONT_COLOR_UPDATE: 'find-gap-us-font-color-update',
  SEARCH_KEYWORD_ANIMATION: 'search-keyword-animation',
  SITEWIDE_CLIENT_FETCH_NAV: 'sitewide-client-fetch-nav',
  SIZE_INCLUSIVITY_ON_HAMNAV: 'size-inclusivity-on-hamnav',
  SW_LOYALTY_ENROLL_CANADA: 'sw-loyalty-enroll-canada',
  CANADA_LOYALTY_UPDATES: 'canada-loyalty-updates',
  FIND_HAMNAV_SUBCATS: 'find-hamnav-subcats',
  BR_COLOR_2023: 'br-colors-2023',
  AT_REDESIGN_2023: 'at-redesign-2023',
};

export const FONT_COLOR_UPDATE_RGB = '#2b2b2b';

export const TEALIUM_EVENT = {
  SEARCH_BAR_CLICK: 'search_bar_click',
};
