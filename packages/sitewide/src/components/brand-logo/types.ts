import type { CSSProperties } from 'react';
import type { PageDataPromises } from '@sitewide/state-builder';

export type BrandLogoProps = {
  altText?: string;
  brandName?: string;
  className?: string;
  contentDataPromise?: PageDataPromises['marketingDataPromise'];
  darkLogoImgPath?: string;
  desktopOverride?: DesktopOverrideStyle;
  isSquare?: boolean;
  lightLogoImgPath?: string;
  style?: CSSProperties;
};

interface DesktopOverrideStyle {
  style?: CSSProperties;
}
