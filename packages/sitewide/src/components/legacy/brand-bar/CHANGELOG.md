# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [4.5.2](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.5.1...@core-ui/brand-bar@4.5.2) (2019-10-25)


### Bug Fixes

* **fnd-994:** conditionally add promo-drawer offset ([#2574](https://github.gapinc.com/ecomfrontend/core-ui/issues/2574)) ([6b964c0](https://github.gapinc.com/ecomfrontend/core-ui/commit/6b964c03627ea429dd37d795f015127a2a30f257))





# [4.5.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.4.199...@core-ui/brand-bar@4.5.0) (2019-10-18)


### Features

* **fnd-994:** mobile sticky brand bar ([#2508](https://github.gapinc.com/ecomfrontend/core-ui/issues/2508)) ([cd9f635](https://github.gapinc.com/ecomfrontend/core-ui/commit/cd9f635e123b28e7d3a08381617bd90a43dc7840))






# [4.4.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.3.32...@core-ui/brand-bar@4.4.0) (2018-12-04)


### Features

* **eval-774:** add fallback styling in BrandBar if CS fails ([#874](https://github.gapinc.com/ecomfrontend/core-ui/issues/874)) ([9adf7a8](https://github.gapinc.com/ecomfrontend/core-ui/commit/9adf7a8))





### Features

* DISC-656 skip links for all brands ([#698](https://github.gapinc.com/ecomfrontend/core-ui/issues/698)) ([9e3aa57](https://github.gapinc.com/ecomfrontend/core-ui/commit/9e3aa57))





# [4.2.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.1.34...@core-ui/brand-bar@4.2.0) (2018-10-17)


### Features

* DISC-655 Hamburger nav opens to current category ([#673](https://github.gapinc.com/ecomfrontend/core-ui/issues/673)) ([955c594](https://github.gapinc.com/ecomfrontend/core-ui/commit/955c594))





<a name="4.1.0"></a>
# [4.1.0](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.0.29...@core-ui/brand-bar@4.1.0) (2018-10-03)


### Features

* **EVAL-532:** reviews summary & consuming reviews summary, reviews container ([#485](https://github.gapinc.com/ecomfrontend/core-ui/issues/485)) ([9e8016a](https://github.gapinc.com/ecomfrontend/core-ui/commit/9e8016a))





<a name="4.0.7"></a>
## [4.0.7](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.0.6...@core-ui/brand-bar@4.0.7) (2018-09-18)


### Bug Fixes

* bump marketing-provider to 4.x ([#521](https://github.gapinc.com/ecomfrontend/core-ui/issues/521)) ([c9e3c4c](https://github.gapinc.com/ecomfrontend/core-ui/commit/c9e3c4c))





<a name="4.0.6"></a>
## [4.0.6](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.0.5...@core-ui/brand-bar@4.0.6) (2018-09-17)


### Bug Fixes

* personalization provider version ([#516](https://github.gapinc.com/ecomfrontend/core-ui/issues/516)) ([a3d47f7](https://github.gapinc.com/ecomfrontend/core-ui/commit/a3d47f7))





<a name="4.0.5"></a>
## [4.0.5](https://github.gapinc.com/ecomfrontend/core-ui/compare/@core-ui/brand-bar@4.0.4...@core-ui/brand-bar@4.0.5) (2018-09-17)


### Bug Fixes

* break css by page ([#513](https://github.gapinc.com/ecomfrontend/core-ui/issues/513)) ([8c0f0fc](https://github.gapinc.com/ecomfrontend/core-ui/commit/8c0f0fc))
