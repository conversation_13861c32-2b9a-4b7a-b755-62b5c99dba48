// @ts-nocheck
"use client";
import {CSSObject} from "@ecom-next/core/react-stitch"
import {WebHierarchyDivision, WebHierarchyCategory} from "@sitewide/components/legacy/types"

export type CategoryNavLink = {
  cid?: string
  id?: string
  hidden?: boolean
  link: string
  name: string
  type?: string
}

export type CurrentNavigationRoute = [
  WebHierarchyDivision,
  WebHierarchyCategory
]

export type TagLinkGroupLinkStyle = {
  fontSize: string
  padding: string
  borderRadius: number | string
  backgroundColor?: string
  color?: string
}

export type TagLinkGroupStyle = CSSObject & {
  li: {
    marginRight: number | string
    a: TagLinkGroupLinkStyle
  }
}
