// @ts-nocheck
"use client";
import {Brands, Theme} from "@ecom-next/core/react-stitch"

export const addThemeToBrandStyles = (
  brandKey = "gap",
  {color}: Theme,
  market = "us"
) => {
  const isCa = market === "ca"
  const GAP_GRAY = "#BBB"
  const BR_HOVER_BG_COLOR = "#F2F1EE"
  const MOBILE_BR_PADDING = isCa ? {padding: "0 15px"} : {padding: "0 10px"}
  const MOBILE_GAP = isCa
    ? {padding: "0 15px"}
    : {fontSize: "11px", padding: "0 10px"}

  const brandStyles = {
    [Brands.Gap]: {
      specific: {
        color: GAP_GRAY,
        fontSize: "12px",
        letterSpacing: "0.24px",
      },
      mobile: MOBILE_GAP,
      active: {color: color.wh, backgroundColor: "unset"},
      hover: {color: color.wh, backgroundColor: "unset"},
    },
    [Brands.GapFactoryStore]: {
      specific: {
        color: GAP_GRAY,
        fontSize: "12px",
        letterSpacing: "0.24px",
      },
      mobile: {padding: "0 15px"},
      active: {color: color.wh, backgroundColor: "unset"},
      hover: {color: color.wh, backgroundColor: "unset"},
    },
    [Brands.OldNavy]: {
      specific: {
        color: color.gray20,
        fontSize: "12px",
        letterSpacing: "0.24px",
        lineHeight: "20px",
        textTransform: "uppercase",
      },
      mobile: {padding: "0 10px"},
      active: {color: color.wh, backgroundColor: "unset"},
      hover: {color: color.wh, backgroundColor: "unset"},
    },
    [Brands.BananaRepublic]: {
      specific: {
        color: color.bk,
        fontSize: "12px",
        letterSpacing: "0.036px",
        lineHeight: "20px",
        textTransform: "uppercase",
      },
      mobile: {fontSize: "11px", ...MOBILE_BR_PADDING},
      active: {fontWeight: 600, backgroundColor: "unset"},
      hover: {backgroundColor: BR_HOVER_BG_COLOR},
    },
    [Brands.BananaRepublicFactoryStore]: {
      specific: {
        color: color.bk,
        fontSize: "12px",
        letterSpacing: "0.036px",
        lineHeight: "20px",
        textTransform: "uppercase",
      },
      mobile: {fontSize: "11px", padding: "0 15px"},
      active: {fontWeight: 600, backgroundColor: "unset"},
      hover: {backgroundColor: BR_HOVER_BG_COLOR},
    },
    [Brands.Athleta]: {
      specific: {
        color: color.gray20,
        fontSize: "14px",
        letterSpacing: "0.56px",
        lineHeight: "20px",
        textTransform: "uppercase",
      },
      mobile: {padding: "0 10px"},
      active: {color: color.wh, backgroundColor: "unset", fontWeight: 600},
      hover: {color: color.wh, backgroundColor: "unset"},
    },
  }

  return brandStyles[brandKey as keyof typeof brandStyles]
}
