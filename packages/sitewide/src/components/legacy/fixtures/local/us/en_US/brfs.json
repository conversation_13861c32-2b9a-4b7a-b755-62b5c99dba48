{"meta.description": "pageDescription defaulted", "meta.title.overide": "pageTitle defaulted", "home": {"type": "home", "name": "HomeMultiSimple", "components": [{"instanceName": "hp-spotlight-40_60_Off_Everything-070723", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultHeight": "calc(98vw*(574/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#cccccc"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230707_SITE_USCA_HP_01_TXT_SM_updated.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230707_SITE_USCA_HP_01_TXT_XL_updated.svg", "alt": "The Summer Sale. 40-60% Off Everything + Extra 50% Off Clearance. Shop Now. Limited time only. Excludes Leather and Suede Apparel & Accessories, Cashmere, Third-Party Merchandise and Gift Cards. DETAILS", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1145487&mlink=1038093,30014806,HP_BANNER_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "ctaList": {"className": "", "ctas": [{"composableButtonData": {"children": "Women's New Arrivals", "style": {"position": "absolute", "transform": "translateX(-50%)", "fontSize": "0.1rem", "left": "30%", "top": "61.5%", "height": "10%", "width": "39%", "opacity": 0}, "desktopStyle": {"left": "26%", "top": "67%", "height": "13%", "width": "16%"}}, "linkData": {"to": "/browse/category.do?cid=1145487&mlink=1038093,30014806,HP_BANNER_W_NA_CTA&clink=30014806", "title": ""}}, {"composableButtonData": {"children": "Men's New Arrivals", "style": {"position": "absolute", "transform": "translateX(-50%)", "fontSize": "0.1rem", "left": "70%", "top": "61.5%", "height": "10%", "width": "39%", "opacity": 0}, "desktopStyle": {"left": "42%", "top": "67%", "height": "13%", "width": "16%"}}, "linkData": {"to": "/browse/category.do?cid=1145413&mlink=1038093,30014806,HP_BANNER_M_NA_CTA&clink=30014806", "title": ""}}, {"composableButtonData": {"children": "Women's Clearance", "style": {"position": "absolute", "transform": "translateX(-50%)", "fontSize": "0.1rem", "left": "30%", "top": "72%", "height": "10%", "width": "39%", "opacity": 0}, "desktopStyle": {"left": "58%", "top": "67%", "height": "13%", "width": "16%"}}, "linkData": {"to": "/browse/category.do?cid=1045379&mlink=1038093,30014806,HP_BANNER_W_CLX_CTA&clink=30014806", "title": ""}}, {"composableButtonData": {"children": "Men's Clearance", "style": {"position": "absolute", "transform": "translateX(-50%)", "fontSize": "0.1rem", "left": "70%", "top": "72%", "height": "10%", "width": "39%", "opacity": 0}, "desktopStyle": {"left": "74%", "top": "67%", "height": "13%", "width": "16%"}}, "linkData": {"to": "/browse/category.do?cid=1045495&mlink=1038093,30014806,HP_BANNER_M_CLX_CTA&clink=30014806", "title": ""}}, {"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "", "iframeData": {"src": "/Asset_Archive/BFWeb/content/0030/014/806/assets/spotlight_legal.html", "title": "Legal Details"}}, "composableButtonData": {"children": "Details", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "transform": "translateX(-50%)", "height": "5%", "width": "90%", "left": "50%", "top": "90%", "opacity": 0}, "desktopStyle": {"height": "4%", "width": "54%", "left": "50%", "top": "90%"}}, "className": "", "style": {}, "desktopStyle": {}}], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "hp-01-WM_New_Arrivals-070723", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1150/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#cccccc"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230707_SITE_USCA_HP_02_IMG_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230707_SITE_USCA_HP_02_IMG_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045361&mlink=1038093,30014806,HP_1_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230707_SITE_USCA_HP_02_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230707_SITE_USCA_HP_02_TXT_XL.svg", "alt": "Just Arrived. Explore new summery silhouettes from shorts to dresses, cut from light-as-air fabrics in cool tones that balance the heat of sunny days. Shop Now.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "ctas": [{"composableButtonData": {"children": "Shop Women's New Arrivals", "style": {"position": "absolute", "transform": "translateX(-50%)", "fontSize": "0.1rem", "left": "29.5%", "top": "86.5%", "height": "5.5%", "width": "38%", "opacity": 0}, "desktopStyle": {"left": "42%", "top": "84%", "height": "6%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1045361&mlink=1038093,30014806,HP_1_W_CTA&clink=30014806", "title": ""}}, {"composableButtonData": {"children": "Shop Men's New Arrivals", "style": {"position": "absolute", "transform": "translateX(-50%)", "fontSize": "0.1rem", "left": "70%", "top": "86.5%", "height": "5.5%", "width": "38%", "opacity": 0}, "desktopStyle": {"left": "58%", "top": "84%", "height": "6%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1045503&mlink=1038093,30014806,HP_1_M_CTA&clink=30014806", "title": ""}}], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "hp-widget-New_Arrivals_Certona-062723", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1100/1920))", "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"position": "relative", "margin": "0 auto 0rem", "flexDirection": "column", "backgroundColor": "#ffffff", "borderTop": "0px solid #ffffff", "borderBottom": "0px solid #ffffff", "paddingBottom": "0rem"}, "components": [{"instanceName": "widget_certonaTitle", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_00_Certona_TXT_XL.svg", "alt": "New July Styles", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n .slick-list {\n overflow: hidden !important; \n} .fullBleedCertona .mkt-certona-recs {\n  max-width: none; \n} .fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon {\n  max-width: none; \n} .fullBleedCertona .mkt-certona-recs .mkt-certona-recs__hp-slider-containercommon .mkt-certona-recs__products {\n  max-width: none; \n} .fullBleedCertona{\n background-color:transparent; padding-bottom:1rem; \n} .fullBleedCertona #mui-certona-recs-container button:not(.slick-arrow) {\n bottom: -51px; padding:0; margin:0; \n} </style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "meta": {"lazy": true}, "data": {"props": {"style": {"position": "absolute", "bottom": "5%", "left": "0%", "width": "100%"}, "className": "fullBleedCertona"}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px", "width": "100%"}}, "data": {"customBrand": "BR", "source": "c<PERSON>a", "scheme": "brhome1_rr", "displayTitle": false, "fullWidth": true, "certonaTitle": {"title": "Just-In Picks For You", "style": {"mobile": {"color": "#000000", "display": "block", "fontSize": "1.5rem", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "1rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "centerMode": false, "defaultslidesToShowSlick": 3.5, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": true, "resslidesToShowSlick": 3.5, "resslidesToScrollSlick": 1, "arrows": true, "arrowVerticalPosition": "-10%", "autoplay": true, "autoplaySpeed": 2000, "speed": 500, "pauseOnHover": true, "infinite": true, "priceFlag": true, "prevArrowSlick": "/Asset_Archive/BFWeb/content/0028/076/267/assets/left_arrow_v1.svg", "nextArrowSlick": "/Asset_Archive/BFWeb/content/0028/076/267/assets/left_arrow_v1.svg", "prevArrowAlt": "previous recommendation", "nextArrowAlt": "next recommendation", "strikeThroughOriginalPriceFlag": true, "productTextStyles": {"productTitle": {"style": {"color": "#000000", "text-align": "center", "fontSize": "1vw", "font-weight": "700"}}, "productPrice": {"style": {"color": "#000000", "textAlign": "center", "fontSize": "1vw", "font-weight": "700"}}, "productSalePrice": {"style": {"color": "#000000", "textAlign": "center", "fontSize": "1vw", "fontWeight": "700"}}}, "productMarketingFlag": {"style": {"color": "#000000", "font-weight": "700", "textAlign": "center"}}, "productCardStyles": {"style": {"width": "auto", "margin": "0% auto 0% auto", "padding": "0px"}}, "productCardImageStyles": {}, "gridLayout": {}, "productsPerRow": {"desktop": 3.5}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"position": "relative", "flexDirection": "column", "backgroundColor": "#ffffff", "borderTop": "0px solid #ffffff", "borderBottom": "0px solid #ffffff", "paddingBottom": "0rem", "paddingTop": "0rem"}, "components": [{"instanceName": "widget_certonaTitle", "name": "LayeredContentModule", "type": "sitewide", "data": {"container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_00_Certona_TXT_SM.svg", "alt": "New July Styles", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "overlay": {}}}, {"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n .slick-list {\n overflow: hidden !important; \n} #mui-certona-recs-container button:not(.slick-arrow) {\n bottom: -20px; padding:0; margin:0; \n} </style>", "style": {}, "classes": ""}}, {"type": "builtin", "name": "div", "data": {"lazy": true, "props": {"style": {"position": "absolute", "bottom": "0%", "left": "0%", "width": "100%", "paddingBottom": "2rem"}}, "components": [{"name": "Recommendations", "type": "home", "tileStyle": {"desktop": {"marginRight": "0px"}}, "data": {"customBrand": "BR", "source": "c<PERSON>a", "scheme": "brhome1_rr", "displayTitle": false, "certonaTitle": {"title": "Just-In Picks For You", "style": {"mobile": {"color": "#e2e0dd", "display": "block", "fontWeight": "400", "marginBottom": "0.5rem", "paddingLeft": "0.5rem", "textTransform": "uppercase", "WebkitFontSmoothing": "antialiased"}}}, "layout": "carousel", "useMobileConfig": true, "arrows": false, "defaultslidesToShowSlick": 1.5, "defaultslidesToScrollSlick": 1, "displayPlayPauseButton": true, "resslidesToShowSlick": 1, "resslidesToScrollSlick": 1, "autoplay": true, "autoplaySpeed": 2000, "speed": 500, "infinite": true, "priceFlag": true, "strikeThroughOriginalPriceFlag": true, "prevArrowSlick": "/Asset_Archive/BRWeb/content/0018/964/493/assets/certona/Arrow_L.svg", "nextArrowSlick": "/Asset_Archive/BRWeb/content/0018/964/493/assets/certona/Arrow_R.svg", "productTextStyles": {"productTitle": {"style": {"color": "#000000", "fontSize": ".8125rem", "fontWeight": "700", "textAlign": "center"}}, "productMarketingFlag": {"style": {"color": "#000000", "font-weight": "700", "fontSize": ".8125rem", "textAlign": "center"}}, "productPrice": {"style": {"color": "#000000", "textAlign": "center", "fontWeight": "700", "fontSize": ".8125rem"}}, "productSalePrice": {"style": {"color": "#000000", "textAlign": "center", "fontWeight": "700", "fontSize": ".8125rem"}}}, "productCardStyles": {"style": {"width": "auto", "flex-grow": "1"}}, "gridLayout": {"style": {"desktop": {"display": "block"}, "mobile": {"display": "block"}}, "productsPerRow": {"desktop": 4, "mobile": 1}}}}]}}]}}}}, {"instanceName": "hp-03-Fresh_Look_New_Arrivals_Carousel-070723", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"defaultHeight": "calc(98vw*(1150/1920))", "style": {"flexDirection": "column", "backgroundColor": "#fff", "borderTop": "0px solid #e2e0dd", "borderBottom": "0px solid #e2e0dd", "marginBottom": "-1.1%"}, "components": [{"instanceName": "hp-05-desk-svg-overlay", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "absolute", "zIndex": "1", "marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_08_TXT_XL.svg", "alt": "A Fresh Look. Discover our favorite new looks in this bright, uplifting shade of yellow. Women's New Arrrivals.", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045361&mlink=1038093,30014806,HP_3_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "ctaList": {"className": "", "ctas": [], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "primary-animation", "name": "Carousel", "type": "sitewide", "tileStyle": {}, "data": {"defaultHeight": "1px", "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 1000, "autoplaySpeed": 5000, "fade": false, "displayPlayPauseBtn": true, "infinite": true, "arrowPosition": "15px", "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/left_arrow_slim_v1.svg?v=2", "nextArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/right_arrow_slim_v1.svg?v=2"}, "carouselStyle": {"padding": "0px"}, "buttonSetting": {"buttonStyle": {"position": "absolute", "height": "48px", "left": "0", "bottom": "2%", "width": "48px", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/play_icon_carousel.svg", "pauseBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/pause_icon_carousel.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative"}, "components": [{"instanceName": "hp-05-desk-slide-1", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_08_IMG1_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-05-desk-slide-2", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_08_IMG2_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "backgroundColor": "#fff", "borderTop": "0px solid #e2e0dd", "borderBottom": "0px solid #e2e0dd", "marginBottom": "-1.1%"}, "components": [{"instanceName": "hp-05-mob-svg-overlay", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "absolute", "zIndex": "1", "marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_08_TXT_SM.svg", "alt": "A Fresh Look. Discover our favorite new looks in this bright, uplifting shade of yellow. Women's New Arrrivals.", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045361&mlink=1038093,30014806,HP_3_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "ctaList": {"className": "", "ctas": [], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "primary-animation", "name": "Carousel", "type": "sitewide", "tileStyle": {}, "data": {"defaultHeight": "1px", "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 1000, "autoplaySpeed": 5000, "fade": false, "displayPlayPauseBtn": true, "infinite": true, "arrowPosition": "0px", "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/left_arrow_slim_v1.svg?v=2", "nextArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/right_arrow_slim_v1.svg?v=2"}, "carouselStyle": {"padding": "0px"}, "buttonSetting": {"buttonStyle": {"position": "absolute", "left": "92.5%", "top": "94%", "width": "7%", "height": "auto", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/play_icon_carousel.svg", "pauseBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/pause_icon_carousel.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative"}, "components": [{"instanceName": "hp-05-mob-slide-1", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_08_IMG1_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-05-mob-slide-2", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_08_IMG2_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-05-mob-slide-3", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_08_IMG3_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-05-mob-slide-4", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_08_IMG4_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}]}}]}}}}, {"instanceName": "hp-04-Occasion_Accessories_Carousel-070723", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(2300/1920))", "mobile": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#000", "flexDirection": "column", "position": "relative", "margin": "0 auto"}, "components": [{"instanceName": "occasion_accessories_carousel_mobile", "name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "padding": "0", "margin": "0 auto", "backgroundColor": "#a49f8c", "fontSize": "0"}, "mobile": {}}, "data": {"lazy": true, "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 1, "autoplaySpeed": 2000, "fade": true, "displayPlayPauseBtn": true, "infinite": true, "dots": false, "displayArrows": {"mobile": false, "desktop": false}, "dotsClass": "slick-dots", "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-prev-arrow_wht.svg", "nextArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-next-arrow_wht.svg", "css": {"& .slick-slide > div:first-of-type": {"display": "flex"}, "& .slick-dots": {"bottom": "2.5%"}, "& .slick-dots > li": {"margin": "0 1%", "width": "12%", "backgroundColor": "#ffffff61", "height": "2px", "overflow": "hidden"}, "& .slick-dots > li > button ": {"margin": "0 auto"}, "& .slick-dots > li.slick-active > button::before": {"opacity": "1", "backgroundColor": "#fff", "width": "100%", "left": "0%", "top": "0%", "transform": "none"}, "& .slick-dots > li > button::before ": {"font-size": "12px", "margin": "0px auto", "height": "100%", "backgroundColor": "transparent", "borderRadius": "0", "width": "100%", "opacity": "0.3"}, "& .slick-arrow.slick-prev > img": {"width": "40%"}, "& .slick-arrow.slick-next > img": {"width": "40%"}}, "pauseOnHover": false, "pauseOnFocus": false}, "buttonSetting": {"buttonStyle": {"position": "absolute", "height": "auto", "left": "92.5%", "top": "95%", "width": "7%", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/play_icon_carousel.svg", "pauseBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/pause_icon_carousel.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative", "padding": "0", ".slick-list": {"overflow": "hidden !important"}}, "carouselStyle": {"padding": "0px"}, "components": [{"instanceName": "occasion_accessories_slide_A", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG1_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG1_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_W_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "14%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "51%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}, {"instanceName": "occasion_accessories_slide_B", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG2_SM_update.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG2_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_W_ACC_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "14%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "51%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}, {"instanceName": "occasion_accessories_slide_C", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG3_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG3_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_W_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "14%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "51%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}, {"instanceName": "occasion_accessories_slide_D", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG4_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG4_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_W_ACC_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "14%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "86%", "left": "51%", "height": "6%", "width": "35%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}]}}]}}, "desktop": {"shouldDisplay": true, "data": {"defaultHeight": "calc(98vw*(2300/1920))", "style": {"width": "100%", "backgroundColor": "#000", "flexDirection": "column", "position": "relative", "margin": "0 auto", "padding": "0"}, "components": [{"instanceName": "occasion_accessories_carousel_desk", "name": "Carousel", "type": "sitewide", "tileStyle": {"desktop": {"width": "100%", "padding": "0", "margin": "0 auto", "backgroundColor": "#a49f8c", "fontSize": "0"}, "mobile": {}}, "data": {"lazy": true, "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 1, "autoplaySpeed": 2000, "fade": true, "displayPlayPauseBtn": true, "infinite": true, "dots": false, "displayArrows": {"mobile": false, "desktop": false}, "dotsClass": "slick-dots", "arrowPosition": "0px", "prevArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-prev-arrow_wht.svg", "nextArrowUrl": "/Asset_Archive/AllBrands/assets/components/carousel/desktop-next-arrow_wht.svg", "css": {"& .slick-slide > div:first-of-type": {"display": "flex"}, "& .slick-dots": {"bottom": "2.5%"}, "& .slick-dots > li": {"margin": "0 1%", "width": "12%", "backgroundColor": "#ffffff61", "height": "2px", "overflow": "hidden"}, "& .slick-dots > li > button ": {"margin": "0 auto"}, "& .slick-dots > li.slick-active > button::before": {"opacity": "1", "backgroundColor": "#fff", "width": "100%", "left": "0%", "top": "0%", "transform": "none"}, "& .slick-dots > li > button::before ": {"font-size": "12px", "margin": "0px auto", "height": "100%", "backgroundColor": "transparent", "borderRadius": "0", "width": "100%", "opacity": "0.3"}, "& .slick-arrow.slick-prev > img": {"width": "40%"}, "& .slick-arrow.slick-next > img": {"width": "40%"}}, "pauseOnHover": false, "pauseOnFocus": false}, "buttonSetting": {"buttonStyle": {"position": "absolute", "height": "auto", "left": "82.25%", "top": "79%", "width": "3%", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/play_icon_carousel.svg", "pauseBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/pause_icon_carousel.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative", "padding": "0", ".slick-list": {"overflow": "hidden !important"}}, "carouselStyle": {"padding": "0px"}, "components": [{"instanceName": "occasion_accessories_slide_A", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG1_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG1_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_W_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "35%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "50%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}, {"instanceName": "occasion_accessories_slide_B", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG2_SM_update.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG2_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_W_ACC_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "35%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "50%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}, {"instanceName": "occasion_accessories_slide_C", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG3_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG3_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_W_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "35%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "50%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}, {"instanceName": "occasion_accessories_slide_D", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"width": "100%", "position": "relative", "margin": "0 auto", "backgroundColor": "#a49f8c"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_IMG4_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_IMG4_XL.jpg", "alt": "", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_W_ACC_BG", "target": ""}, "className": "", "style": {"display": "flex", "fontSize": "0"}, "desktopStyle": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_04_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_04_TXT_XL.svg", "alt": "Moments of Celebration. Elevate summer occasions in refined yet relaxed suiting, eye-catching dresses, and refreshingly modern accessories. Shop Occasion. Shop Accessories.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}, "ctas": [{"composableButtonData": {"children": "Shop Occasion", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "35%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1191429&mlink=1038093,30014806,HP_4_OCC_CTA"}}, {"composableButtonData": {"children": "Shop Accessories", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "92%", "left": "50%", "height": "3%", "width": "15%"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014806,HP_4_ACC_CTA"}}]}}}]}}]}}}}, {"instanceName": "hp-02-SEASON_FOR_DRESSES-070723", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1150/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#cccccc"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230707_SITE_USCA_HP_03_IMG_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230707_SITE_USCA_HP_03_IMG_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045225&mlink=1038093,30014806,HP_2_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230707_SITE_USCA_HP_03_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230707_SITE_USCA_HP_03_TXT_XL.svg", "alt": "A Season for Dresses. Featuring stunning event-ready designs and easy, wrinkle-resistant fabrics to pack for your next getaway. Shop Dresses.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "ctas": [], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "hp-catnav-062723", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"small": "520px", "large": "406px"}, "desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "row", "flexWrap": "wrap", "justifyContent": "space-between", "margin": "0 auto 0", "padding": "0 0 0 0", "backgroundColor": "#8C5C43"}, "components": [{"instanceDesc": "catnav_01", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Dresses", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05A_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05A_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1045225&mlink=1038093,30014427,HP_CAT_1_W_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "100%", "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "100%"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "lineHeight": "1"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1045225&mlink=1038093,30014427,HP_CAT_1_W_BG&clink=30014427"}, "composableButtonData": {"children": "Shop Dresses", "font": "primary"}}]}}}, {"instanceDesc": "catnav_02", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Baby", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05B_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05B_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1185851&mlink=1038093,30014427,HP_CAT_2_W_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "100%", "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "100%"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "lineHeight": "1"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1185851&mlink=1038093,30014427,HP_CAT_2_W_BG&clink=30014427"}, "composableButtonData": {"children": "Shop Baby", "font": "primary"}}]}}}, {"instanceDesc": "catnav_03", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Pants", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05C_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05C_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1045402&mlink=1038093,30014427,HP_CAT_3_M_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "auto", "> div": {"fontSize": "unset"}, "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "max-content", "margin": "0 auto"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "width": "max-content", "margin": "0 auto"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Pants"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1045335&mlink=1038093,30014427,HP_CAT_3_W_CTA&clink=30014427"}, {"text": "Men", "href": "/browse/category.do?cid=1045402&mlink=1038093,30014427,HP_CAT_3_M_CTA&clink=30014427"}]}}]}}}, {"instanceDesc": "catnav_04", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop New Arrivals", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05D_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_05D_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1045361&mlink=1038093,30014427,HP_CAT_4_W_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "auto", "> div": {"fontSize": "unset"}, "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "max-content", "margin": "0 auto"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "width": "max-content", "margin": "0 auto"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop New Arrivals"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1045361&mlink=1038093,30014427,HP_CAT_4_W_CTA&clink=30014427"}, {"text": "Men", "href": "/browse/category.do?cid=1045503&mlink=1038093,30014427,HP_CAT_4_M_CTA&clink=30014427"}]}}]}}}, {"instanceDesc": "catnav_05", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Shorts", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06A_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06A_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1068364&mlink=1038093,30014427,HP_CAT_5_M_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "auto", "> div": {"fontSize": "unset"}, "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "max-content", "margin": "0 auto"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "width": "max-content", "margin": "0 auto"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Shorts"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1105538&mlink=1038093,30014427,HP_CAT_5_W_CTA&clink=30014427"}, {"text": "Men", "href": "/browse/category.do?cid=1068364&mlink=1038093,30014427,HP_CAT_5_M_CTA&clink=30014427"}]}}]}}}, {"instanceDesc": "catnav_06", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Accessories", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06B_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06B_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1045369&mlink=1038093,30014427,HP_CAT_6_W_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "auto", "> div": {"fontSize": "unset"}, "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "max-content", "margin": "0 auto"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "width": "max-content", "margin": "0 auto"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Accessories"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1045369&mlink=1038093,30014427,HP_CAT_6_W_CTA&clink=30014427"}, {"text": "Men", "href": "/browse/category.do?cid=1045457&mlink=1038093,30014427,HP_CAT_6_M_CTA&clink=30014427"}]}}]}}}, {"instanceDesc": "catnav_07", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Tops", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06C_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06C_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1091674&mlink=1038093,30014427,HP_CAT_7_W_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "100%", "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "100%"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "lineHeight": "1"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"linkData": {"to": "/browse/category.do?cid=1091674&mlink=1038093,30014427,HP_CAT_7_W_BG&clink=30014427"}, "composableButtonData": {"children": "Shop Tops", "font": "primary"}}]}}}, {"instanceDesc": "catnav_08", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {"mobile": {"width": "50%"}, "desktop": {"width": "25%"}}, "data": {"container": {"style": {"boxSizing": "border-box", "position": "relative"}, "desktopStyle": {"backgroundColor": "#000", "&:hover img": {"opacity": "0.6"}}}, "background": {"image": {"alt": "Shop Denim", "srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06D_IMG_XL.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/categories/BRFS230627_SITE_USCA_HP_06D_IMG_XL.jpg", "style": {"display": "block"}}, "linkData": {"to": "/browse/category.do?cid=1045229&mlink=1038093,30014427,HP_CAT_8_W_BG&clink=30014427"}}, "ctaList": {"mobilePositionAboveContent": false, "style": {"left": "50%", "padding": "0", "position": "absolute", "top": "46%", "transform": "translateX(-50%)", "zIndex": "31", "width": "auto", "> div": {"fontSize": "unset"}, "a, button": {"fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "transparent", "borderWidth": "0", "color": "#fff", "fontSize": "1em", "fontWeight": "500", "letterSpacing": "0", "padding": "0", "width": "max-content", "margin": "0 auto"}, "button": {"padding": "0 0 3px 0", "marginBottom": "0", "borderBottom": "none", "&:focus": {"outline": "0"}}, "ul": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "0 1px 1px 1px", "left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%", "li": {"borderColor": "#201008", "borderStyle": "solid", "borderWidth": "1px 0 0 0", "padding": "0", "&:last-child": {"borderTop": "1px solid #201008"}, "a": {"boxSizing": "border-box", "color": "#201008", "padding": "8px 0px", "width": "100%", "&:hover": {"backgroundColor": "#201008", "color": "#fff"}}}}}, "desktopStyle": {"a, button": {"fontSize": "1.5vw", "width": "max-content", "margin": "0 auto"}, "ul": {"left": "50%", "maxWidth": "100%", "minWidth": "80px", "position": "absolute", "transform": "translateX(-50%)", "width": "100%"}}, "ctas": [{"buttonDropdownData": {"heading": {"text": "Shop Denim"}, "submenu": [{"text": "Women", "href": "/browse/category.do?cid=1045229&mlink=1038093,30014427,HP_CAT_8_W_CTA&clink=30014427"}, {"text": "Men", "href": "/browse/category.do?cid=1045411&mlink=1038093,30014427,HP_CAT_8_M_CTA&clink=30014427"}]}}]}}}]}}}}, {"instanceName": "hp-05-Linen-070723", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(1150/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#cccccc"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_07_IMG_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_07_IMG_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1191428&mlink=1038093,30014806,HP_5_WM_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_07_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_07_TXT_XL.svg", "alt": "The Linen Shop. Versatile linen styles are lightweight, breathable, and dress up or down to match dress codes of every kind. Shop Linen.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "ctas": [], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "hp-06-Dress<PERSON>_of_the_season_Dress<PERSON>_Carousel-070723", "name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"defaultHeight": "calc(98vw*(1150/1920))", "style": {"flexDirection": "column", "backgroundColor": "#fff", "borderTop": "0px solid #e2e0dd", "borderBottom": "0px solid #e2e0dd", "marginBottom": "-1.1%"}, "components": [{"instanceName": "hp-02-desk-svg-overlay", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "absolute", "zIndex": "1", "marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_03_TXT_XL.svg", "alt": "Dresses of the Season. Signature prints, modern details, and summery takes on the little black dress. Shop Dresses.", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045225&mlink=1038093,30014806,HP_6_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "ctaList": {"className": "", "ctas": [], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "primary-animation", "name": "Carousel", "type": "sitewide", "tileStyle": {}, "data": {"defaultHeight": "1px", "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 1000, "autoplaySpeed": 5000, "fade": false, "displayPlayPauseBtn": true, "infinite": true, "arrowPosition": "15px", "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/left_arrow_slim_v1.svg?v=2", "nextArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/right_arrow_slim_v1.svg?v=2"}, "carouselStyle": {"padding": "0px"}, "buttonSetting": {"buttonStyle": {"position": "absolute", "height": "48px", "left": "0", "bottom": "2%", "width": "48px", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/play_icon_carousel.svg", "pauseBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/pause_icon_carousel.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative"}, "components": [{"instanceName": "hp-02-desk-slide-1", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_03_IMG1_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-02-desk-slide-2", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_03_IMG2_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-02-desk-slide-3", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_03_IMG3_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-02-desk-slide-4", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230627_SITE_USCA_HP_03_IMG4_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "backgroundColor": "#fff", "borderTop": "0px solid #e2e0dd", "borderBottom": "0px solid #e2e0dd", "marginBottom": "-1.1%"}, "components": [{"instanceName": "hp-02-mob-svg-overlay", "name": "LayeredContentModule", "type": "sitewide", "tileStyle": {}, "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"position": "absolute", "zIndex": "1", "marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_03_TXT_SM.svg", "alt": "Dresses of the Season. Signature prints, modern details, and summery takes on the little black dress. Shop Dresses.", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "/browse/category.do?cid=1045225&mlink=1038093,30014806,HP_6_W_BG&clink=30014806", "target": "", "title": ""}, "style": {}}, "ctaList": {"className": "", "ctas": [], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "primary-animation", "name": "Carousel", "type": "sitewide", "tileStyle": {}, "data": {"defaultHeight": "1px", "carouselOptions": {"slidesToShow": 1, "autoplay": true, "speed": 1000, "autoplaySpeed": 5000, "fade": false, "displayPlayPauseBtn": true, "infinite": true, "arrowPosition": "0px", "displayArrows": {"desktop": false, "mobile": false}, "pauseOnHover": false, "prevArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/left_arrow_slim_v1.svg?v=2", "nextArrowUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/right_arrow_slim_v1.svg?v=2"}, "carouselStyle": {"padding": "0px"}, "buttonSetting": {"buttonStyle": {"position": "absolute", "left": "92.5%", "top": "94.25%", "width": "7%", "height": "auto", "padding": "0rem", "zIndex": "2", "maxWidth": "8%"}, "buttonImagePath": {"playBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/play_icon_carousel.svg", "pauseBtnSrc": "/Asset_Archive/BFWeb/content/0030/014/806/assets/evergreen/pause_icon_carousel.svg"}, "playAltText": "Play carousel", "pauseAltText": "Pause carousel", "prevArrowAlt": "Previous Slide", "nextArrowAlt": "Next Slide"}, "style": {"position": "relative"}, "components": [{"instanceName": "hp-02-mob-slide-1", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_03_IMG1_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-02-mob-slide-2", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_03_IMG2_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-02-mob-slide-3", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_03_IMG3_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}, {"instanceName": "hp-02-mob-slide-4", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "0px", "container": {"style": {"marginBottom": "0"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230627_SITE_USCA_HP_03_IMG4_SM.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {}, "style": {}}}}]}}]}}}}, {"instanceName": "hp-card_acquisition-070723", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": "calc(98vw*(440/1920))", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "backgroundColor": "#cccccc"}}, "background": {"className": "", "desktopStyle": {}, "image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230502_USCA_May_Primary_Campaign_HP_10_IMG_SM.jpg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230502_USCA_May_Primary_Campaign_HP_10_IMG_XL.jpg", "alt": "", "style": {"display": "flex"}, "desktopStyle": {}}, "linkData": {"to": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?creditOffer=barclays&sitecode=BRFSHMPGBNR&mlink=5151,********,hp_widget_Card_Acq&clink=********", "target": "_blank", "title": ""}, "style": {}}, "overlay": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/SM/BRFS230502_USCA_May_Primary_Campaign_HP_10_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/XL/BRFS230502_USCA_May_Primary_Campaign_HP_10_TXT_XL.svg", "alt": "Banana Republic Rewards.  Take 20% Off* your first purchase when you open and use a Banana Republic Rewards Credit Card.  Exclusions apply.  Not a cardmember?  Apply now.", "style": {}, "desktopStyle": {}}, "ctaList": {"className": "", "ctas": [{"modalData": {"closeButtonAriaLabel": "close modal", "modalSize": "standard", "title": "", "iframeData": {"src": "/Asset_Archive/BRWeb/content/promo_drawer/assets/legal.html?promoId=936479", "title": "Legal Details"}}, "composableButtonData": {"children": "Details", "color": "primary", "capitalization": "uppercase", "size": "xl", "style": {"position": "absolute", "transform": "translateX(-50%)", "height": "8%", "width": "20%", "left": "50%", "top": "91%", "opacity": 0}, "desktopStyle": {"height": "9%", "width": "7%", "left": "75%", "top": "89%"}}, "className": "", "style": {}, "desktopStyle": {}}], "desktopStyle": {}, "mobilePositionAboveContent": false, "style": {}}}}, {"instanceName": "widget_pixlee", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "200px", "desktop": {"shouldDisplay": true, "data": {"style": {"position": "relative", "paddingBottom": "3.1rem", "display": "block", "backgroundColor": "#f6f4eb"}, "components": [{"instanceName": "widget_pixlee_header", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "300px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/headers/BRFS210907_SITE_US_HP_Pixlee_Header_TXT_XL.svg", "alt": "Campaign: Get inspired by our community.", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=1147206&mlink=5151,1,<PERSON>_Pixlee", "target": "", "title": "Get inspired by our community."}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}}}, {"name": "PixleeModule", "type": "sitewide", "data": {"apiKey": "", "widgetId": "3883441", "iframeMinHeight": "370px", "containerStyles": {"height": "370px", "width": "100%", "margin": "auto", "backgroundColor": "#f6f4eb"}}}, {"instanceName": "pixlee-subcopy-desktop", "type": "builtin", "name": "a", "tileStyle": {"desktop": {"position": "absolute", "top": "88%", "left": "50%", "transform": "translate(-50%,-50%)", "textAlign": "center", "zIndex": "2"}, "mobile": {}}, "data": {"props": {"href": "/browse/info.do?cid=1147206&mlink=5151,1,<PERSON>_Pixlee", "class": "", "style": {"fontSize": "1.05em", "fontWeight": "700", "lineHeight": "20px", "borderBottom": " 2px #000 solid", "color": "#000", "width": "10%", "textTransform": "uppercase", "letterSpacing": "2.2px"}}, "components": ["View Gallery"]}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"position": "relative", "paddingBottom": "2rem", "display": "block", "backgroundColor": "#f6f4eb"}, "components": [{"instanceName": "widget_pixlee_header", "name": "LayeredContentModule", "type": "sitewide", "data": {"lazy": true, "defaultHeight": "300px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0030/014/806/assets/headers/BRFS210907_SITE_US_HP_Pixlee_Header_TXT_SM_update.svg", "alt": "Campaign: Get inspired by our community.", "style": {"height": "100%"}, "desktopStyle": {}}, "linkData": {"to": "/browse/info.do?cid=1147206&mlink=5151,1,<PERSON>_Pixlee", "target": "", "title": "Get inspired by our community."}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}}}, {"name": "PixleeModule", "type": "sitewide", "data": {"apiKey": "", "widgetId": "3883441", "iframeMinHeight": "210px", "containerStyles": {"height": "210px", "width": "100%", "margin": "auto", "backgroundColor": "#f6f4eb"}}}, {"instanceName": "pixlee-subcopy-mobile", "type": "builtin", "name": "a", "tileStyle": {"mobile": {"position": "absolute", "top": "86%", "left": "50%", "transform": "translate(-50%,-50%)", "textAlign": "center", "zIndex": "2"}, "desktop": {}}, "data": {"props": {"href": "/browse/info.do?cid=1147206&mlink=5151,1,<PERSON>_Pixlee", "class": "", "style": {"fontSize": "0.975em", "fontWeight": "700", "lineHeight": "20px", "borderBottom": " 2px #000 solid", "color": "#000", "width": "10%", "textTransform": "uppercase", "letterSpacing": "2.2px"}}, "components": ["View Gallery"]}}]}}}}]}, "sitewide": {"desktopemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "dpg_emergency_banner_desk", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "mobileemergencybanner": {"type": "builtin", "name": "div", "data": {"components": [{"type": "builtin", "name": "div", "data": {"style": {}, "components": [{"instanceName": "dpg_emergency_banner_mob", "type": "builtin", "name": "div", "experimentRunning": true, "useGreyLoadingEffect": false, "mobile": {"height": 0}, "desktop": {"height": 0}, "data": {"shouldWaitForOptimizely": true, "lazy": false, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"small": true, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}}}]}}]}}, "below-topnav": {"type": "builtin", "name": "div", "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "HTMLInjectionComponent", "description": "CSS to support WCD-owned styles on Category pages", "type": "sitewide", "data": {"includePageTypes": ["category", "division"], "defaultHeight": "0px", "classes": "", "style": {"display": "none"}, "html": "<style>#category-page .limit-width h1 {text-transform:uppercase;}#category-page .wcd_seo-copy-block{color:#000;margin:0 auto 1.5rem;padding-left:1rem;padding-bottom:4rem}#category-page .wcd_seo-copy-block div,#utility-page .wcd_seo-copy-block div{padding-top:.7rem;text-transform:uppercase}#category-page .wcd_seo-copy-block a,#utility-page .wcd_seo-copy-block a{color:#00e}#utility-page .wcd_seo-copy-block{line-height:1.25;font-family:EuclidCircularB,Hiragino <PERSON>,Helvetica,Arial,sans-serif;font-size:12px;white-space:pre-line;max-width:835px;color:#000;margin:0 auto;padding:4rem 1rem}#category-page .wcd_jumplinks{flex-direction:row;margin-bottom:45px;background-color:#e9e8e3;pointer-events:auto;display:flex;justify-content:space-evenly;align-items:flex-start;width:100%}#category-page div:has(.wcd_jumplinks){margin-bottom:0}#category-page #main-content:has(.wcd_jumplinks)~.product-grid .filter_button,#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]{color:#000}#category-page #main-content:has(.wcd_jumplinks)~.product-grid .filter_button:hover,#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]:hover{color:#fff}@media(max-width:767px){#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect,#category-page #main-content:has(video)~.product-grid .filter_button,#category-page #main-content:has(video)~.product-grid select#sortBySelect{color:#000}#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect:hover,#category-page #main-content:has(video)~.product-grid .filter_button:hover,#category-page #main-content:has(video)~.product-grid select#sortBySelect:hover{color:#fff}#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect,#category-page #main-content:has(video)~.product-grid select#sortBySelect{background-image:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000;fill-rule:evenodd' /%3E%3C/svg%3E\")}#category-page #main-content:has(.wcd_jumplinks)~.product-grid select#sortBySelect:hover,#category-page #main-content:has(video)~.product-grid select#sortBySelect:hover{background-image:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23fff;fill-rule:evenodd' /%3E%3C/svg%3E\")}}#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]::before{background:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23000;fill-rule:evenodd' /%3E%3C/svg%3E\") 2px 18px no-repeat}#category-page #main-content:has(.wcd_jumplinks)~.product-grid [aria-haspopup=listbox]:hover::before{background:url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='9' height='6' viewBox='0 0 9 6'%3E%3Cpath d='M.003 1.533L1.118.529l3.385 3.047L7.89.529l1.114 1.004-4.5 4.05z' style='fill:%23fff;fill-rule:evenodd' /%3E%3C/svg%3E\") 2px 18px no-repeat}#category-page .wcd_jumplinks.wcd_scrollable-jumplinks{justify-content:flex-start;overflow-x:auto}#category-page .wcd_jumplinks>a{padding:1.25rem .8rem;margin:0;background:0 0;color:#000;border:none;font-weight:300;font-family:'Linotype Didot BR',BananaSerif,'Times New Roman',serif;flex:0 0 auto;font-size:1rem}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] button{padding:1.25rem 0;background-color:transparent;color:#000;font-family:'Linotype Didot BR',BananaSerif,'Times New Roman',serif;font-weight:300;font-size:1rem}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul{box-shadow:none}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul li{margin:0;border:0}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul li a{font-weight:400;background-color:#f6f4eb;font-size:.9rem;text-transform:none;color:#000}#category-page .wcd_jumplinks div[data-testid=button-dropdown-container] ul li a:hover{background-color:#ccc;color:#000}@media(min-width:768px){#category-page .wcd_jumplinks.wcd_scrollable-jumplinks{justify-content:space-evenly}#category-page .wcd_jumplinks>a{overflow-x:inherit;z-index:99}#category-page .wcd_jumplinks>a:hover{text-decoration:underline}}</style>"}}]}}}}]}}, "headline": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "headline-071423-pd-non-earner", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": true, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"instanceName": "headline-071423-desktop", "type": "sitewide", "name": "Headline", "experimentRunning": false, "isAsyncExperiment": false, "data": {"shouldWaitForOptimizely": false, "smallInnerBorderStyle": {"padding": "0 4rem"}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px", "backgroundColor": "#F6F4EB"}, "mobile": {"height": "0px"}}, "defaultHeight": "1px", "breakpoints": ["x-large"], "backgroundColor": "#F6F4EB", "text": [{"text": "40-60% OFF EVERYTHING + EXTRA 20% OFF PURCHASE   ", "className": "", "inlineStyle": {"fontSize": "1rem", "fontWeight": "bold", "fontStyle": "normal", "textTransform": "uppercase", "display": "inline", "color": "#000000", "marginRight": "1rem", "lineHeight": "0", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"text": "No Code Needed   ", "className": "", "inlineStyle": {"fontSize": "1rem", "fontWeight": "normal", "fontStyle": "normal", "textTransform": "none", "display": "inline", "color": "#000000", "marginRight": "1rem", "lineHeight": "0", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"text": "Limited Time Only   ", "className": "", "inlineStyle": {"fontSize": "1rem", "fontWeight": "normal", "fontStyle": "normal", "textTransform": "none", "display": "inline", "color": "#000000", "marginRight": "1rem", "lineHeight": "0", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}], "buttons": [], "links": [], "details": [{"text": "Exclusions Apply", "className": "wcd_headpromo__modalLink", "inlineStyle": {"textAlign": "center", "fontWeight": "normal", "letterSpacing": "0.07em", "padding": "0.9em 0px", "backgroundColor": "transparent", "borderWidth": "0px", "fontSize": "1rem", "display": "inline", "margin": "0px auto 0px 0.25em", "textDecoration": "underline", "textTransform": "none", "color": "#000000", "cursor": "pointer", "textDecorationSkipInk": "auto", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "position": "absolute", "top": "0px", "right": "1rem"}, "linkToModal": true}], "accordionContent": [], "modalUrl": "/Asset_Archive/BFWeb/content/0030/015/047/assets/legal_nonearner.html", "modalTitle": "", "modalCloseButtonAriaLabel": "Press button to close", "headlineIntroAriaLabel": "Entering promotional banner"}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"instanceName": "headline-071423", "type": "builtin", "name": "div", "meta": {"includePageTypes": ["ShoppingBag"]}, "data": {"props": {"style": {"background": "#F6F4EB", "color": "#000000", "textAlign": "center", "justifyContent": "center", "lineHeight": "1.35em", "padding": "0.5em 1.5em", "fontWeight": "normal", "letterSpacing": "0.07em", "textTransform": "uppercase", "font-size": "2.7vw"}}, "components": [{"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "fontWeight": "bold", "display": "inline", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "components": ["40-60% OFF EVERYTHING"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "block"}}, "components": [""]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "fontWeight": "bold", "display": "inline", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "components": ["PLUS, EXTRA 20% OFF PURCHASE "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "block"}}, "components": [""]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "none", "fontWeight": "normal", "display": "inline", "marginRight": "0.5em", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "components": ["No Code Needed"]}}, {"name": "ComposableButton", "data": {"modalProps": {"src": "/Asset_Archive/BFWeb/content/0030/015/047/assets/legal_nonearner.html", "height": "700px", "width": "100%", "closeButtonAriaLabel": "close legal details modal"}, "borderThickness": "thin", "bright": false, "capitalization": "uppercase", "className": "wcd_headpromo__modalLink", "color": "#000000", "crossBrand": false, "font": "secondary", "fullWidth": false, "variant": "underline", "buttonText": "Exclusions Apply", "style": {"font-size": "2vw", "font-weight": "normal", "text-transform": "uppercase", "padding": "0", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}]}}]}}}}]}}, "secondary-headline": {"type": "builtin", "name": "div", "data": {"components": []}}, "edfslarge": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "instanceName": "edfs-header-large", "name": "MktEdfsLarge", "experimentRunning": false, "tileStyle": {"display": "flex", "height": "40px", "alignItems": "center", "margin-top": "1px", "border": "2px solid red"}, "data": {"lazy": false, "styles": {"headline": {}, "detailsButton": {}}, "defaultData": {"text": [{"text": "FREE SHIPPING ON $50+ FOR REWARDS MEMBERS", "inlineStyle": {"color": "#000"}}], "detailsLink": "DETAILS"}, "modalTitle": "Shipping & Returns", "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-BRFS-Narvar092622.html", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "SIGN IN OR JOIN", "path": "/my-account/sign-in", "style": {}}}}]}}, "edfssmall": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "edfs-header-small", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": false, "defaultHeight": {"large": "80px", "small": "50px"}, "isVisible": {"large": false, "small": true}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column", "justifyContent": "flex-start", "alignItems": "stretch", "width": "100%", "margin": "0 auto", "background-color": "#f6f4eb"}, "components": [{"type": "sitewide", "name": "MktEdfsSmall", "data": {"lazy": false, "experimentRunning": false, "styles": {"headline": {}, "detailsButton": {}}, "defaultData": {"textStrong": "", "text": "Free Shipping On $50+ For Rewards Members", "detailsLink": "Details"}, "modalTitle": "Shipping & Returns", "modalUrl": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-BRFS-Narvar092622.html", "modalCloseButtonAriaLabel": "Close Popup", "signInCta": {"text": "Sign In Or Join", "path": "/my-account/sign-in", "style": {}}}}]}}}}]}}, "popup": {"type": "builtin", "name": "div", "data": {"components": [{"name": "DynamicModal", "type": "sitewide", "data": {"meta": {"excludePageTypes": ["profile-ui", "profileui", "Profile", "ShoppingBag", "info", "Information"]}, "defaultHeight": "0px", "closeButtonAriaLabel": "close email sign up modal", "localStorageKey": "BROL_hasSeenEmailAcquisition", "modalSize": "max", "title": "", "style": {}, "layoutData": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0rem", "position": "relative"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {}, "components": [{"type": "builtin", "name": "img", "tileStyle": {"desktop": {}}, "data": {"style": {"position": "relative", "display": "block", "width": "100%"}, "props": {"src": "/Asset_Archive/BFWeb/content/0029/160/225/assets/BRFS022822_emailAquisition_XL_IMG.jpg", "alt": "aa"}}}, {"type": "builtin", "name": "img", "tileStyle": {"desktop": {"flex": 1, "margin": "0 auto", "max-width": "100%"}}, "data": {"style": {"position": "absolute", "top": "0", "left": "0", "width": "100%"}, "props": {"src": "/Asset_Archive/BFWeb/content/0029/160/225/assets/BRFS220215_PRM_Email_Acq_Site_TXT_style1.svg?v=2", "alt": "Extra 15% off when you join our email list. Discover fresh style inspiration, exclusive offers and more."}}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"padding": "0", "margin": "0", "width": "48%", "position": "absolute", "top": "49%", "left": "50%"}}, "data": {"components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n form.email-registration__form label span{\n padding-top: 0.15em; padding-left:.5em; \n} form.email-registration__form label input{\n  padding-left:.5em; \n}   </style>", "style": {}, "classes": ""}}, {"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"width": "100%"}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "0", "margin": "0", "position": "relative"}}, "targetURL": "/profile/info.do?cid=1037797&mlink=1037812,19062081,email_v&clink=19062081", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:NA;PL:NA;AT:NA;BRO:BRFS_Popup_Signup;GPO:NA"]}, "customText": {}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "please enter a valid email address", "desktop": {"className": "sds_sp", "crossBrand": false, "inverse": false, "style": {"fontSize": "1em", "margin": "0", "top": "0.5rem", "backgroundColor": "#FFF", "border": "none"}}}, "submitButtonOptions": {"text": "Sign up now", "desktop": {"className": "sds_sp_vertical sds_uppercase", "size": "medium", "fullWidth": false, "crossBrand": false, "style": {"backgroundColor": "transparent", "color": "#fff", "borderBottom": "1px solid #fff", "fontSize": "1.4em", "padding": "0 0 .2em 0", "width": "auto", "marginTop": "5%", "position": "absolute", "left": "50%", "transform": "translateX(-50%)", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "errorNotificationAriaLabel": "Error"}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"position": "absolute", "bottom": "2%", "right": "2%"}}, "data": {"props": {"style": {"flexDirection": "row"}}, "components": [{"type": "sitewide", "name": "FlexHeadline", "data": {"lazy": false, "styles": {"root": {"base": {"display": "inline", "marginRight": "1rem"}}, "flexItemElement": {"base": {"display": "inline"}, "desktop": {}}}, "flexItems": [{"elements": [{"text": "*Terms and Conditions", "modalProps": {"src": "/Asset_Archive/BRWeb/content/promo_drawer/assets/legal.html?promoId=672633", "closeButtonAriaLabel": "close modal", "height": "400px"}, "style": {"base": {"textDecoration": "underline", "textTransform": "uppercase", "color": "#000"}, "desktop": {"fontSize": ".7rem"}}}], "style": {"base": {"display": "inline"}}}]}}, {"type": "builtin", "name": "a", "data": {"style": {"textDecoration": "underline", "textTransform": "uppercase", "color": "#000", "fontSize": ".7rem"}, "props": {"class": "", "href": "https://www.gapinc.com/en-us/consumer-privacy-policy", "target": "_blank"}, "components": ["Privacy Policy"]}}]}}]}}}}]}}}, "analytics": {"onClose": {"content_id": "dynamic_modal_close_content", "link_name": "dynamic_modal_close_link"}, "onOpen": {"content_id": "dynamic_modal_open_content", "link_name": "dynamic_modal_open_link"}}}}]}}, "promorover": {"type": "builtin", "name": "div", "data": {"components": [{"instanceName": "promorover_evergreen-segmentation", "name": "MktSticker", "type": "sitewide", "redpointExperimentRunning": true, "experimentRunning": true, "meta": {"includePageTypes": ["division", "product", "category", "search", "info", "home"]}, "data": {"shouldWaitForOptimizely": true, "isVisible": {"small": false, "large": true}, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {}, "mobile": {}}, "defaultHeight": "0px", "largeImg": "/Asset_Archive/BFWeb/content/0030/015/023/assets/BRSP220531_ILP_BarclaysTransition_Rover_Acq_TXT.svg", "altText": "Banana Republic Rewards.  Get 20% off your first purchase when you open and use a Banana Republic Rewards Credit Card.  Exclusions Apply.  Apply Now.", "localStorageKey": "BRFSEmailSticker_071423", "localStorageVal": "BRFSroverHasBeenClosed_071423", "stickerAriaLabel": "stickerAriaLabel", "stickerCloseButtonAriaLabel": "stickerCloseButtonAriaLabel", "href": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?creditOffer=barclays&sitecode=BRFSROV&mlink=1037811,********,rover_Card_Acq", "hrefTarget": "_blank"}}]}}, "prefooter": {"type": "builtin", "name": "div", "data": {"components": []}}, "countdown": {"type": "builtin", "name": "div", "data": {"components": [{"type": "sitewide", "name": "Countdown", "data": {"style": {"fontWeight": "700"}, "shouldDisplayDays": true, "ongoingHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "Sale", "style": {"desktop": {"padding-top": ".75rem", "padding-bottom": ".75rem", "color": "", "display": "inline-block"}, "mobile": {"padding-top": ".75rem", "padding-bottom": ".75rem", "color": ""}, "desktopAndMobile": {"display": "inline-block"}}}}, "endedHeadline": {"type": "sitewide", "name": "TextHeadline", "data": {"text": "", "style": {"desktop": {"padding-top": ".75rem", "padding-bottom": ".75rem", "color": "", "display": "inline-block"}, "mobile": {"padding-top": ".75rem", "padding-bottom": ".75rem", "color": ""}, "desktopAndMobile": {"display": "inline-block"}}}}, "endDate": "2023-07-24T02:59:59.628-04:00", "timeDisplayText": {"day": {"showAttribute": true, "showAtZero": true, "text": "Days", "textSingular": "Day"}, "hour": {"showAttribute": true, "showAtZero": true, "text": "Hrs", "textSingular": "Hr"}, "minute": {"showAttribute": true, "showAtZero": true, "text": "<PERSON>s", "textSingular": "Min"}, "second": {"showAttribute": true, "showAtZero": true, "text": "Secs", "textSingular": "Sec"}}}}]}}, "footer": {"type": "sitewide", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components": [{"type": "builtin", "name": "div", "data": {"props": {"className": "optly-hpEmailAndIcons-target", "style": {"backgroundColor": "#F6F4EB", "borderTop": "1px solid #000"}}, "components": [{"instanceName": "hpEmailAndIcons", "name": "LayoutComponent", "type": "sitewide", "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "189px"}, "desktop": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1400px", "paddingTop": "1.875rem", "margin": "0 auto"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"desktop": {"font-family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "display": "flex", "alignItems": "center", "maxWidth": "66.667%"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"fontSize": "3rem", "fontWeight": "100", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale", "maxWidth": "87.5%", "margin-left": "6.25%", "color": "#000"}, "components": ["Keep "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "3rem", "fontWeight": "100", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "lowercase", "-moz-osx-font-smoothing": "grayscale", "font-style": "italic", "color": "#000"}, "components": ["in "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "3rem", "fontWeight": "100", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale", "color": "#000"}, "components": ["Touch"]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"display": "flex", "alignItems": "center", "maxWidth": "50%"}}, "data": {"style": {"display": "flex", "width": "100%"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"desktop": {"display": "flex", "alignItems": "center", "maxWidth": "25%"}}, "data": {"style": {"width": "100%"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "0", "margin": "0 auto", "maxWidth": "75%"}, "mobile": {"padding": "0", "margin": "0"}}, "targetURL": "/profile/info.do?cid=1037797&mlink=48608,2804525,email_v&clink=2804525&Footer=true", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:NA;PL:NA;AT:NA;BRO:BRFS_Footer_Signup;GPO:NA"]}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Sign Up for Emails", "desktop": {"className": "emailSubmitBtn", "variant": "border", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"border-radius": "10px", "min-width": "50%", "fontSize": "0.75rem", "fontFamily": "Lynstone", "margin-top": "25px", "padding": "0px 25px", "height": "2.75rem", "letter-spacing": "2px", "backgroundColor": "transparent"}}, "mobile": {"className": "signButton label-a", "variant": "outline", "size": "small", "fullWidth": true, "crossBrand": false, "color": "primary", "style": {"border-radius": "10px", "min-width": "50%", "fontSize": "0.75rem", "fontFamily": "Lynstone", "height": "2.75rem", "letter-spacing": "2px", "backgroundColor": "transparent"}}}, "errorNotificationAriaLabel": "Error"}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"alignItems": "center", "maxWidth": "25%"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "sitewide", "name": "SMSForm", "data": {"phoneInputData": {"aria-label": "", "label": "Enter Your Phone Number"}, "apiParams": {"campaign_id": "8mp2I6WJ", "acquisition_id": "pbapRSuN", "src_gnrc_sms": "WEBSITE_SIGNUP", "src_spfc_sms": "BRFSUS_FOOTERSIGNUP"}, "buttonText": "Sign up for Texts", "styles": {"formStyles": {"align-items": "flex-end", "justify-content": "space-evenly", "width": "75%", "margin": "0 auto"}, "buttonStyles": {"border": "2px solid rgb(0, 0, 0)", "border-radius": "10px", "padding": "0px 25px", "background-color": "transparent", "color": "rgb(0, 0, 0)", "min-width": "50%;", "font-size": "0.75rem;", "margin-top": "25px;", "height": "2.75rem", "letter-spacing": "2px"}, "phoneInputStyles": {"width": "90%", "min-height": "0px"}}}}]}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"display": "flex", "alignItems": "center", "maxWidth": "50%"}}, "data": {"style": {"margin": "0 auto", "padding": "25px 0px 25px 55px"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"fontFamily": "Lynstone", "background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "12px", "line-height": "0.9rem"}, "components": ["*Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"fontFamily": "Lynstone", "background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "12px", "line-height": "0.9rem", "textDecoration": "underline"}, "href": "https://cs.waterfall.com/gap/terms/", "target": "_blank"}, "components": ["text terms & privacy policy"]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontFamily": "Lynstone", "background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "12px", "line-height": "0.9rem"}, "components": [". Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help."]}}]}}]}}, {"instanceName": "footer_icons", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "tileStyle": {"desktop": {"maxWidth": "50%", "flex-direction": "column", "alignSelf": "center"}}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0028/661/552/assets/footer/FooterSlice1_updated.svg", "desktopSrcUrl": "/Asset_Archive/BRWeb/content/0028/661/552/assets/footer/FooterSlice1_updated.svg", "alt": "Find a store.", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "mobilePositionAboveContent": false, "style": {"textAlign": "center"}, "desktopStyle": {}, "ctas": [{"composableButtonData": {"children": "Find a store", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "81.5%", "left": "23.5%", "height": "6%", "width": "53.5%"}, "desktopStyle": {"top": "19%", "left": "33%", "height": "15%", "width": "47%"}}, "linkData": {"to": "/stores?mlink=hp_widget_FAS"}}, {"composableButtonData": {"children": "Gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "81.5%", "left": "23.5%", "height": "6%", "width": "53.5%"}, "desktopStyle": {"top": "41%", "left": "33%", "height": "15%", "width": "47%"}}, "linkData": {"to": "/customerService/info.do?cid=1069654&mlink=hp_widget_GC"}}, {"composableButtonData": {"children": "Credit Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "81.5%", "left": "23.5%", "height": "6%", "width": "53.5%"}, "desktopStyle": {"top": "62%", "left": "33%", "height": "15%", "width": "47%"}}, "linkData": {"to": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?creditOffer=barclays&sitecode=BRFSUNIFTD"}}]}}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"margin": "0 auto", "paddingTop": "32px", "backgroundColor": "#F6F4EB"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"mobile": {"font-family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "display": "flex", "alignItems": "center"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"display": "block", "width": "90%", "margin": "0 auto", "textAlign": "center"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"fontSize": "8vw", "fontWeight": "400", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale", "color": "#000"}, "components": ["Keep "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "8vw", "fontWeight": "400", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "lowercase", "-moz-osx-font-smoothing": "grayscale", "font-style": "italic", "color": "#000"}, "components": ["in "]}}, {"type": "builtin", "name": "span", "data": {"style": {"fontSize": "8vw", "fontWeight": "400", "paddingBottom": "unset", "letterSpacing": "-0.02em", "line-height": "1.25", "text-transform": "uppercase", "-moz-osx-font-smoothing": "grayscale", "color": "#000"}, "components": ["Touch"]}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"display": "flex", "width": "100%", "flex-direction": "column"}, "components": [{"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"width": "100%"}, "components": [{"name": "EmailRegistrationForm", "type": "sitewide", "data": {"defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {}, "mobile": {"padding": "0", "margin": "0 auto", "maxWidth": "65%"}}, "targetURL": "/profile/info.do?cid=1037797&mlink=48608,2804525,email_v&clink=2804525&Footer=true", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:NA;PL:NA;AT:NA;BRO:BRFS_Footer_Signup;GPO:NA"]}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "please enter a valid email address", "desktop": {"className": "", "crossBrand": false, "inverse": false}, "mobile": {"className": "", "crossBrand": false, "inverse": false}}, "submitButtonOptions": {"text": "Sign Up for Emails", "mobile": {"className": "emailSubmitBtn", "variant": "border", "size": "medium", "fullWidth": false, "crossBrand": false, "color": "primary", "style": {"min-width": "50%", "fontSize": "0.75rem", "margin": "1rem auto", "height": "2.75rem", "letter-spacing": "2px", "display": "block", "backgroundColor": "transparent"}}}, "errorNotificationAriaLabel": "Error"}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"mobile": {"alignItems": "center"}}, "data": {"style": {"width": "100%"}, "components": [{"type": "sitewide", "name": "SMSForm", "data": {"phoneInputData": {"aria-label": "", "label": "Enter Your Phone Number"}, "apiParams": {"campaign_id": "8mp2I6WJ", "acquisition_id": "pbapRSuN", "src_gnrc_sms": "WEBSITE_SIGNUP", "src_spfc_sms": "BRFSUS_FOOTERSIGNUP"}, "buttonText": "Sign up for Texts", "styles": {"formStyles": {"align-items": "flex-end", "justify-content": "space-evenly", "width": "65%", "margin": "0 auto"}, "buttonStyles": {"border": "2px solid rgb(0, 0, 0)", "padding": "0.5em 0.8em", "background-color": "transparent", "color": "#000", "min-width": "50%;", "font-size": "0.75rem;", "margin": "1rem auto", "height": "2.75rem", "letter-spacing": "2px", "display": "block"}, "phoneInputStyles": {"width": "100%", "min-height": "0px"}}}}]}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"mobile": {"display": "flex", "alignItems": "center"}}, "data": {"style": {"width": "90%", "margin": "0 auto", "padding": "20px", "textAlign": "center", "line-height": "1"}, "components": [{"type": "builtin", "name": "span", "data": {"style": {"background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "10px"}, "components": ["*Msg & Data Rates May Apply. By entering your phone number, clicking submit, and completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "10px", "textDecoration": "underline"}, "href": "https://cs.waterfall.com/gap/terms/", "target": "_blank"}, "components": ["text terms & privacy policy"]}}, {"type": "builtin", "name": "span", "data": {"style": {"background": "transparent", "color": "#000", "display": "inline", "maxWidth": "87.5%", "margin": "auto", "font-size": "10px"}, "components": [". Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help."]}}]}}]}}]}}}}]}}, {"instanceName": "BRFS_prefooter-evergreen", "name": "LayoutComponent", "type": "sitewide", "experimentRunning": false, "data": {"lazy": true, "defaultHeight": {"large": "0px", "small": "0px"}, "desktop": {"shouldDisplay": true, "data": {"style": {"backgroundColor": "#F6F4EB"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {"maxWidth": "1400px", "margin": "auto"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"className": "optly-bottomBanner-rewards-target"}, "components": [{"type": "builtin", "name": "div", "data": {"style": {"display": "block", "background": "transparent", "color": "#000", "lineHeight": "1.25em", "margin": "25px 0px 25px 55px", "fontWeight": "normal", "letterSpacing": "0.04em", "font-size": "1rem", "fontFamily": "Lynstone"}, "components": [{"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["Rewards Members "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["get "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["free shipping "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["on all orders $50+ "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?mlink=1037813,********,FOOTER_REWARDS_SIGNIN"}, "components": ["Sign In"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" or "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?mlink=1037813,********,sitewidefooter_ILP_join"}, "components": ["Join"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" "]}}, {"name": "ComposableButton", "data": {"modalProps": {"src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-BRFS-Narvar092622.html", "height": "650px", "width": "100%", "color": "#000", "font-family": "<PERSON><PERSON><PERSON>, Arial, Sans-serif", "font-size": "10px", "line-height": "12px", "closeButtonAriaLabel": "close legal details modal"}, "borderThickness": "thin", "bright": false, "className": "wcd_headpromo__modalLink", "crossBrand": false, "font": "secondary", "fullWidth": false, "variant": "underline", "buttonText": "DETAILS", "style": {"color": "#000", "fontSize": ".75rem", "font-weight": "normal", "textTransform": "none", "marginLeft": "0.3rem", "padding": "0", "margin-bottom": "5px"}}}]}}]}}, {"instanceName": "footer_icons", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "tileStyle": {"desktop": {"display": "none"}, "mobile": {"margin-top": "1rem"}}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BFWeb/content/0029/633/770/assets/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BFWeb/content/0029/633/770/assets/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "alt": "Find a store.", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "mobilePositionAboveContent": false, "style": {"textAlign": "center"}, "desktopStyle": {}, "ctas": [{"composableButtonData": {"children": "Find a store", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "0%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "/stores?mlink=hp_widget_FAS"}}, {"composableButtonData": {"children": "Gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "33.33%", "height": "100%", "width": "33.34%"}}, "linkData": {"to": "/customerService/info.do?cid=1069654&mlink=hp_widget_GC"}}, {"composableButtonData": {"children": "Credit Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "66.67%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?creditOffer=barclays&sitecode=BRFSUNIFTD"}}]}}}]}}}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"display": "block", "background": "#F6F4EB", "color": "#000", "textAlign": "center", "justifyContent": "center", "lineHeight": "1.25em", "padding": "0.5em 1em 0.25em", "fontWeight": "normal", "letterSpacing": "0.04em", "font-size": "1rem"}, "components": [{"type": "builtin", "name": "div", "data": {"props": {"className": "optly-bottomBanner-rewards-target"}, "components": [{"type": "builtin", "name": "div", "data": {"style": {"display": "block", "background": "transparent", "color": "#000", "lineHeight": "1.25em", "padding": "0.5em 1em 0.25em", "fontWeight": "normal", "letterSpacing": "0.04em", "font-size": "1rem", "fontFamily": "Lynstone"}, "components": [{"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["Rewards Members "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["get "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"textTransform": "uppercase", "display": "inline"}}, "components": ["free shipping "]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": ["on all orders $50+ "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?mlink=1037813,********,FOOTER_REWARDS_SIGNIN"}, "components": ["Sign In"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" or "]}}, {"type": "builtin", "name": "a", "data": {"props": {"style": {"display": "inline", "textDecoration": "underline"}, "href": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?mlink=1037813,********,sitewidefooter_ILP_join"}, "components": ["Join"]}}, {"type": "builtin", "name": "span", "data": {"props": {"style": {"display": "inline"}}, "components": [" "]}}, {"name": "ComposableButton", "data": {"modalProps": {"src": "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-BRFS-Narvar092622.html", "height": "650px", "width": "100%", "color": "#000", "font-family": "<PERSON><PERSON><PERSON>, Arial, Sans-serif", "font-size": "10px", "line-height": "12px", "closeButtonAriaLabel": "close legal details modal"}, "borderThickness": "thin", "bright": false, "className": "wcd_headpromo__modalLink", "crossBrand": false, "font": "secondary", "fullWidth": false, "variant": "underline", "buttonText": "DETAILS", "style": {"color": "#000", "fontSize": ".75rem", "font-weight": "normal", "textTransform": "none", "marginLeft": "0.3rem", "padding": "0", "margin-bottom": "5px"}}}]}}, {"instanceName": "footer_icons", "name": "LayeredContentModule", "type": "sitewide", "experimentRunning": false, "tileStyle": {"desktop": {"display": "none"}, "mobile": {"margin-top": "1rem"}}, "data": {"shouldWaitForOptimizely": false, "placeholderSettings": {"useGreyLoadingEffect": false, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "lazy": false, "defaultHeight": "0px", "container": {"className": "", "desktopStyle": {}, "style": {"position": "relative", "margin": "0 auto"}}, "background": {"image": {"srcUrl": "/Asset_Archive/BRWeb/content/0029/555/919/assets/footer/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "desktopSrcUrl": "/Asset_Archive/BRWeb/content/0029/555/919/assets/footer/BRSP220202_SITE_US_UpdatedFooter_Icons_TXT_SM.svg", "alt": "Find a store.", "style": {"height": "100%"}, "desktopStyle": {}}, "className": "", "style": {"display": "flex"}, "desktopStyle": {}}, "ctaList": {"className": "", "mobilePositionAboveContent": false, "style": {"textAlign": "center"}, "desktopStyle": {}, "ctas": [{"composableButtonData": {"children": "Find a store", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "0%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "/stores?mlink=hp_widget_FAS"}}, {"composableButtonData": {"children": "Gift Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "33.33%", "height": "100%", "width": "33.34%"}}, "linkData": {"to": "/customerService/info.do?cid=1069654&mlink=hp_widget_GC"}}, {"composableButtonData": {"children": "Credit Card", "style": {"position": "absolute", "overflow": "hidden", "padding": "0", "opacity": "0", "top": "0%", "left": "66.67%", "height": "100%", "width": "33.33%"}}, "linkData": {"to": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?creditOffer=barclays&sitecode=BRFSUNIFTM"}}]}}}]}}]}}}}, {"name": "Footer", "type": "sitewide", "data": {"defaultHeight": {"large": "200px"}, "hideFirstRow": true, "lazy": true, "carousel": {"prevArrow": "", "nextArrow": "/Asset_Archive/BFWeb/content/0016/729/423/assets/arrow_right.svg", "slides": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "GET THE GIFT THAT ALWAYS FITS", "style": {"mobile": {"fontWeight": "500", "textAlign": "center", "whiteSpace": "pre-line", "padding": "5% 7%"}, "desktop": {"fontWeight": "500", "textAlign": "center", "whiteSpace": "pre-line", "padding": "5% 7%"}}, "className": {"mobile": "sds_heading-c", "desktop": "sds_heading-c"}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/Asset_Archive/BFWeb/content/0016/580/835/assets/BRSP_GiftCards.svg", "largeImg": "/Asset_Archive/BFWeb/content/0016/580/835/assets/BRSP_GiftCards.svg", "altText": "Gift Cards - one size fits all"}, "style": {"desktop": {"width": "auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/BFWeb/content/0016/580/835/assets/BRSP_GiftCards.svg", "largeImg": "/Asset_Archive/BFWeb/content/0016/580/835/assets/BRSP_GiftCards.svg", "altText": "Gift Cards - one size fits all", "link": {"url": "/customerService/info.do?cid=1069654", "tid": "footercarousel_giftcards"}}, "linksContainerStyle": {"desktop": {"left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {"textTransform": "uppercase"}}, "content": [{"tid": "findAStore", "url": "/customerService/info.do?cid=1069654&mlink=1095711,17778118,footercarousel_giftcards&clink=17778118", "text": "BUY NOW"}]}}}]}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "A NEW WAY TO SHOP", "style": {"mobile": {"textAlign": "center", "whiteSpace": "pre-line", "padding": "5% 7%"}, "desktop": {"textAlign": "center", "whiteSpace": "pre-line", "padding": "5% 7%"}}, "className": {"mobile": "sds_heading-c", "desktop": "sds_heading-c"}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/Asset_Archive/BFWeb/content/0028/205/366/assets/210125_BRFOL_US_AfterPay_Footer.jpg", "largeImg": "/Asset_Archive/BFWeb/content/0028/205/366/assets/210125_BRFOL_US_AfterPay_Footer.jpg", "altText": "Afterpay BG"}, "style": {"desktop": {"width": "auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/BFWeb/content/0028/205/366/assets/210125_BRFOL_US_AfterPay_Footer.jpg", "largeImg": "/Asset_Archive/BFWeb/content/0028/205/366/assets/210125_BRFOL_US_AfterPay_Footer.jpg", "altText": "Afterpay. Shop Now, Pay Later. Zero Interest!", "link": {"url": "/customerService/info.do?cid=1037875&cs=payment_options&mlink=1095711,17778118,footercarousel_afterpay&clink=17778118", "tid": "footercarousel_giftcards"}}, "linksContainerStyle": {"desktop": {"left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {"textTransform": "uppercase"}}, "content": [{"tid": "footercarousel_afterpay", "url": "/customerService/info.do?cid=1037875&cs=payment_options&mlink=1095711,17778118,footercarousel_afterpay&clink=17778118", "text": "LEARN MORE"}]}}}]}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "TextHeadline", "type": "sitewide", "data": {"text": "STORE LOCATOR", "style": {"mobile": {"textAlign": "center", "whiteSpace": "pre-line", "padding": "5% 7%"}, "desktop": {"textAlign": "center", "whiteSpace": "pre-line", "padding": "5% 7%"}}, "className": {"mobile": "sds_heading-c", "desktop": "sds_heading-c"}}}, {"type": "sitewide", "name": "SVGOverlay", "data": {"containerStyle": {"mobile": {"padding": "0 0"}, "desktop": {"padding": "0 0", "marginBottom": "4rem"}}, "background": {"content": {"smallImg": "/Asset_Archive/BFWeb/content/0015/724/961/assets/BRFS_StoreLocator.svg", "largeImg": "/Asset_Archive/BFWeb/content/0015/724/961/assets/BRFS_StoreLocator.svg", "altText": "Find A Store"}, "style": {"desktop": {"width": "auto"}}}, "svgoverlay": {"smallImg": "/Asset_Archive/BFWeb/content/0015/724/961/assets/BRFS_StoreLocator.svg", "largeImg": "/Asset_Archive/BFWeb/content/0015/724/961/assets/BRFS_StoreLocator.svg", "altText": "Find A Store", "link": {"url": "/customerService/storeLocator.do?mlink=1095711,17322994,footercarousel_storelocator&clink=17322994", "tid": "footercarousel_storelocator"}}, "linksContainerStyle": {"desktop": {"left": "0", "width": "100%", "boxSizing": "border-box"}}, "links": {"verticalStacking": false, "buttonClasses": "carousel__cta", "style": {"desktop": {}}, "content": [{"tid": "footercarousel_storelocator", "url": "/customerService/storeLocator.do?mlink=1095711,17322994,footercarousel_storelocator&clink=17322994", "text": "FIND A FACTORY STORE"}]}}}]}}}}], "carouselConfig": {"showArrowsAtMinWidth": "medium"}}, "footerCustomerSupport": {"desktop": {"columns": [{"header": {}, "links": []}, {"header": {"text": "Customer Support", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=1037835", "className": "brfLinks"}, {"type": "link", "text": "Returns", "to": "/customerService/info.do?cid=1037874", "className": "brfLinks"}, {"type": "link", "text": "Size Charts", "to": "/customerService/info.do?cid=1037878&cs=size_charts", "className": "brfLinks"}]}, {"header": {"text": "BR Rewards", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55298,********,footer_ILP_brrewards_logo", "rel": "noopener", "className": "brfLinks"}, {"type": "link", "text": "Explore Benefits", "to": "/customerService/info.do?cid=1098825&mlink=55298,********,footer_ILP_explore_benefits", "className": "brfLinks"}, {"type": "link", "text": "Pay Credit Card Bill", "to": "https://www.bananarepublic.barclaysus.com/payment", "className": "brfLinks"}, {"type": "link", "text": "Activate Credit Card", "to": "https://www.bananarepublic.barclaysus.com/activate", "target": "blank", "rel": "noopener", "className": "brfLinks"}, {"type": "link", "text": "Join <PERSON> Rewards—it’s Free", "to": "/my-account/sign-in?mlink=55298,********,footer_ILP_join", "target": "blank", "rel": "noopener", "style": {"marginTop": "0.5rem", "fontSize": "12px", "lineHeight": "12px", "padding": "4px 4px 1px"}}, {"type": "link", "text": "or Apply Now for a Credit Card", "to": "https://bananarepublicfactory.gapfactory.com/my-account/sign-in?creditOffer=barclays&sitecode=BRFSUNIFTD", "target": "blank", "rel": "noopener", "style": {"fontSize": "12px", "lineHeight": "12px", "padding": "1px 4px 4px"}}]}, {"header": {"text": "FIND US", "className": "brfLinksHeader"}, "links": [{"type": "link", "text": "**************", "to": "tel:************", "className": "brfLinks"}, {"type": "link", "text": "Email Sign-Up", "to": "/profile/info.do?cid=1037797", "className": "brfLinks"}, {"type": "link", "text": "Store Locator", "to": "https://bananarepublicfactory.gapfactory.com/stores", "className": "brfLinks"}, {"type": "link", "text": "Banana Republic", "to": "https://bananarepublic.gap.com/", "className": "brfLinks"}]}, {"header": {}, "links": []}]}, "mobile": {"links": [{"type": "link", "text": "Store Locator", "to": "https://bananarepublicfactory.gapfactory.com/stores", "className": "footer-item"}, {"type": "link", "text": "Customer Service", "to": "/customerService/info.do?cid=1037835", "className": "footer-item"}, {"type": "link", "text": "Orders & Returns", "to": "/my-account/order-history", "className": "footer-item"}, {"type": "accordion", "text": "Banana Republic Rewards", "accordionLinks": [{"type": "link", "text": "My Points and Rewards", "to": "/my-account/sign-in?targetURL=/loyalty/customer-value&mlink=55298,********,footer_ILP_brrewards_logo", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Explore Benefits", "to": "/customerService/info.do?cid=1098825&mlink=55298,********,footer_ILP_explore_benefits", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Pay Credit Card Bill", "to": "https://www.bananarepublic.barclaysus.com/payment", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Activate Credit Card", "to": "https://www.bananarepublic.barclaysus.com/activate", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Join <PERSON> Rewards—it’s Free", "to": "/my-account/sign-in?mlink=55298,********,footer_ILP_join", "target": "_blank", "className": "footer-item", "style": {"fontSize": "12px", "padding": "2px 4px"}}, {"type": "link", "text": "Apply Now for a Credit Card", "to": "/my-account/sign-in?creditOffer=barclays&sitecode=BRFSUNIFTM", "target": "blank", "rel": "noopener", "style": {"fontSize": "12px", "lineHeight": "12px", "padding": "1px 4px 4px"}}]}, {"type": "link", "text": "Email sign up", "to": "/profile/info.do?cid=1037797&Footer=true", "target": "_blank", "rel": "noopener", "className": "footer-item"}, {"type": "link", "text": "Shop Gap Factory", "to": "https://www.gapfactory.com/?ssiteID=brfs", "target": "_blank", "rel": "noopener", "className": "footer-item"}]}}, "copyRights": {"rows": [[{"text": "© 2022 Banana Republic, LLC"}, {"text": "Privacy Policy", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy", "target": "_blank"}, {"text": "Do Not Sell My Info", "doNotSell": true}, {"text": "Interest Based Ads", "to": "https://corporate.gapinc.com/en-us/consumer-privacy-policy?#Cookies", "target": "_blank"}, {"text": "Your California Privacy Rights", "to": "/customerService/info.do?cid=1038072&mlink=5001,8723809,18&clink=8723809#privacyRights", "target": "_self"}, {"text": "Terms of Use", "to": "/customerService/info.do?cid=1038071", "target": "_self"}, {"text": "Careers", "to": "http://jobs.bananarepublic.com", "target": "_blank"}, {"text": "Social Responsibility", "to": "https://www.gapinc.com/en-us/values/sustainability", "target": "_blank"}, {"text": "About Gap Inc.", "to": "http://www.gapinc.com/aboutus", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}], [{"text": "California Transparency in Supply Chains Act", "to": "https://www.gapinc.com/content/gapinc/html/sustainability/ca-transparency-insupplychainsact.html", "target": "_blank", "rel": "noopener nor<PERSON><PERSON><PERSON>"}, {"text": "Americans with Disabilities Act", "to": "/customerService/info.do?cid=1038089", "target": "_self"}]]}}}, {"name": "DynamicModal", "type": "sitewide", "data": {"meta": {"excludePageTypes": ["profile-ui", "profileui", "Profile", "ShoppingBag", "info", "Information"]}, "defaultHeight": "0px", "closeButtonAriaLabel": "close email sign up modal", "localStorageKey": "BROL_hasSeenEmailAcquisition", "modalSize": "max", "title": "", "style": {}, "layoutData": {"desktop": {"shouldDisplay": true, "data": {"style": {"display": "flex", "flexDirection": "column", "padding": "0rem", "position": "relative"}, "components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktop": {"shouldDisplay": true, "data": {"style": {}, "components": [{"type": "builtin", "name": "img", "tileStyle": {"desktop": {}}, "data": {"style": {"position": "relative", "display": "block", "width": "100%"}, "props": {"src": "/Asset_Archive/BFWeb/content/0029/160/225/assets/BRFS022822_emailAquisition_XL_IMG.jpg", "alt": "aa"}}}, {"type": "builtin", "name": "img", "tileStyle": {"desktop": {"flex": 1, "margin": "0 auto", "max-width": "100%"}}, "data": {"style": {"position": "absolute", "top": "0", "left": "0", "width": "100%"}, "props": {"src": "/Asset_Archive/BFWeb/content/0029/160/225/assets/BRFS220215_PRM_Email_Acq_Site_TXT_style1.svg?v=2", "alt": "Extra 15% off when you join our email list. Discover fresh style inspiration, exclusive offers and more."}}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"padding": "0", "margin": "0", "width": "48%", "position": "absolute", "top": "49%", "left": "50%"}}, "data": {"components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>\n form.email-registration__form label span{\n padding-top: 0.15em; padding-left:.5em; \n} form.email-registration__form label input{\n  padding-left:.5em; \n}   </style>", "style": {}, "classes": ""}}, {"name": "EmailRegistrationForm", "type": "sitewide", "tileStyle": {"width": "100%"}, "data": {"lazy": true, "defaultHeight": {"small": "0px", "large": "0px"}, "isVisible": {"large": true, "small": true}, "style": {"desktop": {"padding": "0", "margin": "0", "position": "relative"}}, "targetURL": "/profile/info.do?cid=1037797&mlink=1037812,19062081,email_v&clink=19062081", "hiddenFields": {"src_gnrc_cd": ["WEBSITE EMAIL SIGNUP"], "src_spfc_cd": ["GP:NA;BR:NA;ON:NA;PL:NA;AT:NA;BRO:BRFS_Popup_Signup;GPO:NA"]}, "customText": {}, "textInputOptions": {"label": "Enter Your Email Address", "errorMessage": "please enter a valid email address", "desktop": {"className": "sds_sp", "crossBrand": false, "inverse": false, "style": {"fontSize": "1em", "margin": "0", "top": "0.5rem", "backgroundColor": "#FFF", "border": "none"}}}, "submitButtonOptions": {"text": "Sign up now", "desktop": {"className": "sds_sp_vertical sds_uppercase", "size": "medium", "fullWidth": false, "crossBrand": false, "style": {"backgroundColor": "transparent", "color": "#fff", "borderBottom": "1px solid #fff", "fontSize": "1.4em", "padding": "0 0 .2em 0", "width": "auto", "marginTop": "5%", "position": "absolute", "left": "50%", "transform": "translateX(-50%)", "fontFamily": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "errorNotificationAriaLabel": "Error"}}]}}, {"type": "builtin", "name": "div", "tileStyle": {"desktop": {"position": "absolute", "bottom": "2%", "right": "2%"}}, "data": {"props": {"style": {"flexDirection": "row"}}, "components": [{"type": "sitewide", "name": "FlexHeadline", "data": {"lazy": false, "styles": {"root": {"base": {"display": "inline", "marginRight": "1rem"}}, "flexItemElement": {"base": {"display": "inline"}, "desktop": {}}}, "flexItems": [{"elements": [{"text": "*Terms and Conditions", "modalProps": {"src": "/Asset_Archive/BRWeb/content/promo_drawer/assets/legal.html?promoId=672633", "closeButtonAriaLabel": "close modal", "height": "400px"}, "style": {"base": {"textDecoration": "underline", "textTransform": "uppercase", "color": "#000"}, "desktop": {"fontSize": ".7rem"}}}], "style": {"base": {"display": "inline"}}}]}}, {"type": "builtin", "name": "a", "data": {"style": {"textDecoration": "underline", "textTransform": "uppercase", "color": "#000", "fontSize": ".7rem"}, "props": {"class": "", "href": "https://www.gapinc.com/en-us/consumer-privacy-policy", "target": "_blank"}, "components": ["Privacy Policy"]}}]}}]}}}}]}}}, "analytics": {"onClose": {"content_id": "dynamic_modal_close_content", "link_name": "dynamic_modal_close_link"}, "onOpen": {"content_id": "dynamic_modal_open_content", "link_name": "dynamic_modal_open_link"}}}}]}, "topnav": {"name": "div", "type": "builtin", "instanceName": "MegaNav_05022023", "data": {"style": {"width": "100%"}, "tabletStyle": {}, "desktopStyle": {}, "props": {}, "tabletProps": {}, "desktopProps": {}, "components": [{"name": "HTMLInjectionComponent", "type": "sitewide", "data": {"html": "<style>@media(min-width: 1024px) and (max-width: 1200px){ #topNavWrapper .topNavLink > div > a.divisionLink { font-size: 16px; } } @media(min-width: 1024px) { div[data-testid='header-only-logo-at-top'] {max-width: min(98vw - 32px, 1400px);} .meganav { box-shadow: 0px 10px 10px -4px rgba(0 0 0 / 15%);}}</style>", "style": {}, "classes": ""}}, {"instanceName": "MegaNav_12162021", "type": "sitewide", "name": "MegaNav", "data": {"isNavSticky": true, "classStyles": {"topnav-container > ul.topnav": "margin: 0 auto; max-width: 900px;", "topnav > li.topNavLink": "padding: 35px 0px 30px 0px;", "topNavLink > div > a.divisionLink": "font-family: BananaSerif; font-weight: 400; font-size: 16px; letter-spacing: 0.12em", "divisionLink[data-divisionname='Gifts']": "color: #C30F16;", "topnav a.catnav--item--link": "font-size: 12px;", "topnav a.sale.catnav--item--link": "color: rgb(51, 51, 51);", "topnav a.sale.catnav--item--link:hover": "border-bottom: 1px solid rgb(51, 51, 51);", "topnav .wcd-mensapparel .catnav--header>a": "border-bottom: .5px solid #929292; display: block; margin-bottom: 8px; padding: 0 0 .5rem;", "topnav .wcd-womensapparel .catnav--header>a": "border-bottom: .5px solid #929292; display: block; margin-bottom: 8px; padding: 0 0 .5rem;", "topnav .wcd-petitesapparel .catnav--header>a": "border-bottom: .5px solid #929292; display: block; margin-bottom: 8px; padding: 0 0 .5rem;"}, "activeDivisions": [{"name": "New", "divisionId": ["/browse/category.do?cid=1045361&mlink=17361761,topNavNA,visnav&clink=17361761", "1045361"], "megaNavOrder": [["1181040"], ["1181058"]], "exclusionIds": [], "customStyles": {}}, {"name": "Women", "divisionId": ["1044620"], "megaNavOrder": [["1110765", "1045357"], ["1044971"], ["1045368"], ["1045356", "1145735", "1182733"]], "exclusionIds": [], "customStyles": {"1045379": {"colorScheme": "sale"}}}, {"name": "Men", "divisionId": ["1044619"], "megaNavOrder": [["1110973", "1045440"], ["1045318"], ["1045444"], ["1045502", "1145733", "1182734"]], "exclusionIds": ["1063043", "1102425", "1101787"], "customStyles": {"1045495": {"colorScheme": "sale"}}}, {"name": "Linen", "divisionId": ["/browse/category.do?cid=1191428"]}, {"name": "Baby & Toddler", "divisionId": ["/browse/info.do?cid=1189820"], "megaNavOrder": [["1189951"]], "exclusionIds": [], "customStyles": {}}, {"name": "Clearance", "divisionId": ["1044622"], "megaNavOrder": [["<li class='catnav--item'><a data-categoryid='1045379' href='/browse/category.do?cid=1045379&mlink=17447894,flyout_women_clearance&clink=17447894&nav=meganav%3AClearance%3AWomen%20Clearance' class='catnav--item--link'><span>Women's Clearance</span></a></li>"], ["<li class='catnav--item'><a data-categoryid='1045495' href='/browse/category.do?cid=1045495&mlink=17447894,flyout_men_clearance&clink=17447894&nav=meganav%3AClearance%3AMens%20Clearance' class='catnav--item--link'><span>Men's Clearance</span></a></li>"], ["<li class='catnav--item'><a data-categoryid='1045489' href='/browse/category.do?cid=1045489&mlink=17447894,flyout_petites_clearance&clink=17447894&nav=meganav%3AClearance%3APetites%20Clearance' class='catnav--item--link'><span>Petites' Clearance</span></a></li>"], ["<li class='catnav--item'><a data-categoryid='1202832' href='/browse/category.do?cid=1202832&mlink=17447894,flyout_baby_clearance&clink=17447894&nav=meganav%3AClearance%3ABaby%20Clearance' class='catnav--item--link'><span>Baby Clearance</span></a></li>"]], "exclusionIds": [], "customStyles": {}}, {"name": "banana republic", "divisionId": ["https://bananarepublic.gap.com/"]}]}}]}}, "promodrawer": {"name": "LayoutComponent", "type": "sitewide", "instanceName": "BRFSPromo-PD_071423-default", "experimentRunning": true, "meta": {"excludePageTypes": ["LoyaltyValueCenter"]}, "data": {"shouldWaitForOptimizely": true, "placeholderSettings": {"useGreyLoadingEffect": true, "desktop": {"height": "0px"}, "mobile": {"height": "0px"}}, "desktop": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "PromoDrawerComponentV2", "type": "sitewide", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"shouldWaitForOptimizely": true, "buildInfo": ["********", "BRFOL"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": false, "includePageTypes": ["product", "home"]}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh-ljsdajlg {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh-ljsdajlg img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh-ljsdajlg .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh-ljsdajlg .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh-ljsdajlg .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh-ljsdajlg\">\n  <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_1_BKGD_BRFS_NE&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRFS230714_SITE_USCA_July_PD_40_60Everything.jpg\" alt=\"40-60% Off Everything. Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_1_W_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=1145413&amp;mlink=5151,********,PD_1_M_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "LIMITED TIME ONLY. EXCLUSIONS APPLY.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "981357", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "lhthpjv8"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh-lhthz012 {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh-lhthz012 img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh-lhthz012 .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh-lhthz012 .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh-lhthz012 .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh-lhthz012\">\n  <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_2_BKGD_BRFS_NE&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRFS230714_SITE_USCA_July_PD_EXT20.jpg\" alt=\"Extra 20% Off Purchase. Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_2_W_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=1145413&amp;mlink=5151,********,PD_2_M_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "discount automatically applied at checkout.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "LIMITED TIME ONLY. EXCLUSIONS APPLY.", "legalOverride": "", "genericCodeId": "981157", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "lhthpklh"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh-lhti2kex {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh-lhti2kex img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh-lhti2kex .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh-lhti2kex .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh-lhti2kex .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh-lhti2kex\">\n  <a href=\"/browse/category.do?cid=1045379&mlink=5151,********,PD_3_BKGD_BRFS_NE&clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRFS230714_SITE_USCA_July_PD_EXT40CLX.jpg\" alt=\"Extra 40% Off Clearance. Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=1045379&mlink=5151,********,PD_3_W_CTA_BRFS_NE&clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=1045495&mlink=5151,********,PD_3_M_CTA_BRFS_NE&clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "discount automatically applied at checkout.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "LIMITED TIME ONLY. EXCLUSIONS APPLY. ONLINE EXCLUSIVE.", "legalOverride": "", "genericCodeId": "981277", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "lhthplcm"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/my-account/sign-in?creditOffer=barclays&sitecode=BRFSPD&mlink=5151,********,PD_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\"> \n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRSP210720_ILP_Phase3_LightsOnElements_Drawer_03.jpg\" alt=\"Open & use your Banana Republic Rewards Credit Card Take 20%* Your first purchase.  Exclusions apply.  Plus, free shipping on orders of $50+*. Apply Now.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "898737", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "l5d3tyar"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "text": "{--! headerText !--}", "style": {"mobile": {"fontSize": ".8em"}}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}", "style": {"mobile": {"fontSize": ".75em"}}}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "subHeaderText": "(4 available)", "iconAltText": "Open icon"}, "closedState": {"headerText": "40-60% off everything", "subHeaderText": "plus, extra 20% off purchase", "iconAltText": "Closed icon"}, "aria-label": "40-60% off everything"}, "pd_id": "pdid_1534541049574", "analytics": {"onOpen": {"content_id": "promo_drawer_open_content", "link_name": "promo_drawer_open_link", "promo_drawer_autofire_config": "scroll"}}}}]}}, "mobile": {"shouldDisplay": true, "data": {"style": {"flexDirection": "column"}, "components": [{"name": "PromoDrawerComponentV2", "type": "sitewide", "sitewide-promodrawer-ciid": "********", "instanceName": "promoDrawer-********", "experimentRunning": false, "data": {"shouldWaitForOptimizely": true, "buildInfo": ["********", "BRFOL"], "style": {"height": "293px"}, "options": {"desktopVisible": true, "mobileVisible": true, "excludePageTypes": ["ShoppingBag", "checkout", "search", "info", "storeLocator", "sign_in", "order_history", "order_detail", "customer_value", "account_summary", "update_personal_info", "address_book", "express_account_settings", "credit_card_summary", "size<PERSON>hart", "profile", "LoyaltyValueCenter"]}, "autoFire": "scroll", "disabledAutoFirePageTypes": ["category"], "promos": [{"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh-ljsdajlg {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh-ljsdajlg img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh-ljsdajlg .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh-ljsdajlg .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh-ljsdajlg .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh-ljsdajlg\">\n  <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_1_BKGD_BRFS_NE&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRFS230714_SITE_USCA_July_PD_40_60Everything.jpg\" alt=\"40-60% Off Everything. Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_1_W_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=1145413&amp;mlink=5151,********,PD_1_M_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile1"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "LIMITED TIME ONLY. EXCLUSIONS APPLY.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "981357", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "lhthpjv8"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh-lhthz012 {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh-lhthz012 img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh-lhthz012 .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh-lhthz012 .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh-lhthz012 .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh-lhthz012\">\n  <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_2_BKGD_BRFS_NE&amp;clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRFS230714_SITE_USCA_July_PD_EXT20.jpg\" alt=\"Extra 20% Off Purchase. Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=1145487&amp;mlink=5151,********,PD_2_W_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=1145413&amp;mlink=5151,********,PD_2_M_CTA_BRFS_NE&amp;clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile2"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "discount automatically applied at checkout.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "LIMITED TIME ONLY. EXCLUSIONS APPLY.", "legalOverride": "", "genericCodeId": "981157", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "lhthpklh"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_two_cta-wh-lhti2kex {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n font-family: BananaSerif;\n}\n.pd_two_cta-wh-lhti2kex img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_two_cta-wh-lhti2kex .pd_two_cta-wh--cta-container {\n  bottom: 8%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\njustify-content:center;\n}\n.pd_two_cta-wh-lhti2kex .pd_two_cta-wh_button {\n    border-bottom: 1px solid #fff;\n  box-sizing: border-box;\n  color: #fff;\n  font-size: 12px;\n  text-align: center;\n  text-transform: uppercase;\n \n  letter-spacing: .1em;\n  font-weight: 700;\n}\n.pd_two_cta-wh_button:hover{\n  cursor:pointer;\n  text-decoration:none;\n}\n.pd_two_cta-wh-lhti2kex .pd_two_cta-wh_button:not(:first-child) {\n  margin-left: 12%;\n}\n</style>\n\n\n<div class=\"pd_two_cta-wh-lhti2kex\">\n  <a href=\"/browse/category.do?cid=1045379&mlink=5151,********,PD_3_BKGD_BRFS_NE&clink=********\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRFS230714_SITE_USCA_July_PD_EXT40CLX.jpg\" alt=\"Extra 40% Off Clearance. Shop now.\">\n  </a>\n  <div class=\"pd_two_cta-wh--cta-container\">\n    <a href=\"/browse/category.do?cid=1045379&mlink=5151,********,PD_3_W_CTA_BRFS_NE&clink=********\" class=\"pd_two_cta-wh_button\">Shop Women</a>\n    <a href=\"/browse/category.do?cid=1045495&mlink=5151,********,PD_3_M_CTA_BRFS_NE&clink=********\" class=\"pd_two_cta-wh_button\">Shop Men</a>\n  </div>\n</div>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile3"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "discount automatically applied at checkout.", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "LIMITED TIME ONLY. EXCLUSIONS APPLY. ONLINE EXCLUSIVE.", "legalOverride": "", "genericCodeId": "981277", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "lhthplcm"}, {"bannerContent": {"name": "HTMLInjectionComponent", "isBannerClickable": false, "type": "sitewide", "data": {"html": "<style>\n.pd_image {\n  background-color: #ffcccb; /* default */\n  color: #fff; /* default */\n  height: 100%;\n  position: relative;\n  width: 100%;\n}\n.pd_image img {\n  margin: 0 auto;\n  max-width: 100%;\n}\n.pd_image .pd_image--cta-container {\n  bottom: 4%;\n  box-sizing: border-box;\n  display: flex;\n  flex-flow: row nowrap;\n  padding: 0 3%;\n  position: absolute;\n  width: 100%;\n}\n.pd_image .pd_image_button {\n  background-color: #fff;\n  box-sizing: border-box;\n  color: #122344;\n  font-size: 10px;\n  font-weight: 600;\n  min-height: 24px;\n  padding: 6px 8px;\n  text-align: center;\n  text-transform: uppercase;\n  width: 48.5%;\n}\n.pd_image .pd_image_button:not(:first-child) {\n  margin-left: 3%;\n}\n</style>\n\n<a href=\"/my-account/sign-in?creditOffer=barclays&sitecode=BRFSPD&mlink=5151,********,PD_CARD_ACQ\" id=\"pd_atagwrap\" class=\"pd_atag-wrapper\" target=\"_blank\"> \n  <div class=\"pd_image\">\n    <img id=\"PDImageTag\" src=\"/Asset_Archive/BFWeb/content/0030/015/020/assets/BRSP210720_ILP_Phase3_LightsOnElements_Drawer_03.jpg\" alt=\"Open & use your Banana Republic Rewards Credit Card Take 20%* Your first purchase.  Exclusions apply.  Plus, free shipping on orders of $50+*. Apply Now.\">\n  </div>\n</a>\n", "style": {}, "classes": "promoDrawer__content__item__banner"}, "id": "pd_tile4"}, "applicationDetails": {"type": "auto", "overlay": "Code will be applied at bag", "defaultMessage": "", "isTappedMessage": "applied at checkout"}, "legalDetails": {"popupTextLink": "DETAILS", "legalOverride": "", "genericCodeId": "898737", "genericCode": "", "style": {"fontSize": "0.9em", "width": "100%"}}, "promoId": "l5d3tyar"}], "drawerToggle": {"template": {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [{"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__title", "mobile": "promoDrawer__title"}, "text": "{--! headerText !--}", "style": {"mobile": {"fontSize": ".8em"}}}}, {"name": "TextHeadline", "type": "sitewide", "data": {"className": {"desktop": "promoDrawer__subtitle", "mobile": "promoDrawer__subtitle"}, "text": "{--! subHeaderText !--}", "style": {"mobile": {"fontSize": ".75em"}}}}], "style": {"flex-direction": "column"}}}}}, {"name": "LayoutComponent", "type": "sitewide", "data": {"desktopAndMobile": {"shouldDisplay": true, "data": {"components": [], "style": {"transitionDuration": ".2s", "transitionTimingFunction": "ease-out"}, "classes": "promoDrawer__handlebar__icon"}}}}], "style": {}}}}}, "openedState": {"headerText": "my offers", "subHeaderText": "(4 available)", "iconAltText": "Open icon"}, "closedState": {"headerText": "40-60% off everything", "subHeaderText": "plus, extra 20% off purchase", "iconAltText": "Closed icon"}, "aria-label": "40-60% off everything"}, "pd_id": "pdid_1534541049574"}}]}}}}, "search": {"type": "sitewide", "name": "SearchSuggestions", "data": {"search-suggestions": ["Shirts", "Dresses", "Linen", "Shorts", "<PERSON>ts", "Baby"]}}, "logo": {"type": "sitewide", "name": "Logo", "darkLogoImgPath": "/Asset_Archive/BFWeb/content/0029/617/207/assets/BF_Logo.svg", "lightLogoImgPath": "/Asset_Archive/BFWeb/content/0029/617/207/assets/BF_Logo_light.svg", "logoImgPath": "/Asset_Archive/BFWeb/content/0029/617/207/assets/BF_Logo.svg", "altText": "Banana Republic Factory logo", "style": {"margin": "1rem auto", "width": "11rem"}, "desktopOverride": {"style": {"display": "block", "width": "14rem", "margin": "0 auto"}}}, "header": {"byCid": [{"details": "Pages that want transparent nav to overlay content", "configurationForCids": ["1093148", "1186121", "1186136", "1202933", "1186344", "1186353", "1167229", "1167230", "1045361", "1045503", "1185851", "1154201", "1148950", "1189820", "1153466", "1141151", "1205014", "1163769", "1140250", "1216113", "1045335", "1045346"], "stickyBackground": "transparent", "contrast": "light", "topNavBackground": "#F6F4EB", "fullBleedOptions": {"isFullBleedEnabled": true, "hasTransparencyLayer": true, "flyoutBackground": "#F6F4EB", "fullBleedContrast": "light"}}, {"details": "Pages that originally had transparent nav overlay content, but now need to have cream background. These are the pages that need a shorter first asset but design cannot support.", "configurationForCids": ["1069654"], "stickyBackground": "transparent", "contrast": "dark", "headerBackground": "#F6F4EB", "fullBleedOptions": {"isFullBleedEnabled": true, "hasTransparencyLayer": true, "flyoutBackground": "#F6F4EB", "fullBleedContrast": "dark"}}], "byPageType": [{"configurationForPageTypes": ["home"], "stickyBackground": "transparent", "contrast": "light", "fullBleedOptions": {"isFullBleedEnabled": true, "hasTransparencyLayer": true, "flyoutBackground": "#F6F4EB", "fullBleedContrast": "light"}}, {"configurationForPageTypes": ["category", "product", "Information", "customlandingpage", "search", "dynamicerror", "division"], "stickyBackground": "transparent", "headerBackground": "#F6F4EB", "flyoutBackground": "#F6F4EB", "contrast": "dark"}], "default": {"isUtilityLinksEnabled": false, "headerLayout": "onlyLogoAtTop", "stickyScrollDirection": "both", "isStickyEnabled": true}}}, "brand": "brfs", "type": "meta", "pmcsEdgeCacheTag": "brfs-homepage-en-us-stage"}