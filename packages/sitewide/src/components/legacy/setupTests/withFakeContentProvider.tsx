// @ts-nocheck
import {Brand} from "@sitewide/components/legacy/types"
import {useAppState} from "@ecom-next/sitewide/app-state-provider"
import AthletaLogo from "@sitewide/components/legacy/brand-logo/BrandLogos/at"
import BananaRepublicLogo from "@sitewide/components/legacy/brand-logo/BrandLogos/br"
import BananaRepublicFSLogo from "@sitewide/components/legacy/brand-logo/BrandLogos/brfs"
import Gap<PERSON>ogo from "@sitewide/components/legacy/brand-logo/BrandLogos/gap"
import OldNavyLogo from "@sitewide/components/legacy/brand-logo/BrandLogos/on"
import {BrandLogoProps} from "@sitewide/components/legacy/brand-logo/types"
import {ContentProvider} from "@sitewide/components/legacy/content/ContentProvider"
import {ContentComponent, ContentData} from "@sitewide/components/legacy/content/types"
import {defaultContentData} from "@sitewide/components/legacy/fixtures/DefaultContentData"

export const brandLogos: Record<
  Brand,
  (props: BrandLogoProps) => JSX.Element
> = {
  [Brand.Athleta]: AthletaLogo,
  [Brand.BananaRepublic]: BananaRepublicLogo,
  [Brand.BananaRepublicFactoryStore]: BananaRepublicFSLogo,
  [Brand.Gap]: GapLogo,
  [Brand.GapFactoryStore]: GapLogo,
  [Brand.OldNavy]: OldNavyLogo,
}

interface WithFakeContentProviderProps {
  children: JSX.Element
  contentData?: ContentData
}

export const FakeContentResolver = ({
  marketing,
}: {
  marketing: ContentComponent
}): JSX.Element => {
  const {brandName} = useAppState()
  if (marketing.name === "Logo") {
    const BrandLogo = brandLogos[brandName]
    return <BrandLogo />
  }
  return (
    <div data-testid={marketing.name}>
      {marketing.name} faked from WithFakeContentProvider
    </div>
  )
}

export const WithFakeContentProvider = ({
  children,
  contentData = defaultContentData,
}: WithFakeContentProviderProps): JSX.Element => {
  return (
    <ContentProvider
      contentData={contentData}
      ContentResolver={FakeContentResolver}
    >
      {children}
    </ContentProvider>
  )
}
