// @ts-nocheck
"use client";
import {forBrands, styled} from "@ecom-next/core/react-stitch"
import {Brands} from "@core-ui/react-stitch"
import {returnFontWeightByBrandCa, headerRedesign2024TextStyles} from "./utils"
import {useAppState} from "@ecom-next/sitewide/app-state-provider"
import {BR_2023_SHADOW_COLOR} from "@sitewide/components/legacy/sitewide-constants"
import useHeaderRedesign2024 from "../../sister-brands-bar-redesign-2024/useHeaderRedesign2024"

interface AccountDropdownItemProps {
  link: string
  text: string
  shouldSeparate?: boolean
  isCanadaLoyaltyMarket?: boolean
  shouldUseInverseBrColors: boolean | undefined
  isLastItem?: boolean
  isFirstItem?: boolean
  isFirstItemWithoutNewMembershipStatus?: boolean
}

interface ItemContainerProps {
  shouldSeparate?: boolean
  isCanadaLoyaltyMarket?: boolean
  shouldUseHeaderRedesign2024?: boolean
  shouldUseInverseBrColors: boolean | undefined
  isLastItem?: boolean
  isFirstItem?: boolean
  isFirstItemWithoutNewMembershipStatus?: boolean
}

const ItemContainer = styled.li<ItemContainerProps>(
  ({
    isCanadaLoyaltyMarket,
    shouldSeparate,
    shouldUseInverseBrColors,
    shouldUseHeaderRedesign2024,
    theme,
    isLastItem,
    isFirstItem,
    isFirstItemWithoutNewMembershipStatus,
  }) => {
    const defaultStyles = {
      ...theme.brandFont,
      borderTop:
        shouldSeparate && !shouldUseHeaderRedesign2024
          ? `1px solid ${theme.color.gray20}`
          : undefined,
      paddingTop:
        isCanadaLoyaltyMarket && shouldSeparate && !shouldUseHeaderRedesign2024
          ? ".6em"
          : undefined,
      color: theme.color.gray60,
      fontSize: "86.7%",
      lineHeight: 1.38,
    }

    const BRStyles = shouldUseInverseBrColors &&
      !shouldUseHeaderRedesign2024 && {
        borderTop: shouldSeparate
          ? `1px solid ${BR_2023_SHADOW_COLOR}`
          : undefined,
        color: theme.color.bk,
      }
    const brandStyles = forBrands(theme, {
      br: BRStyles,
      brfs: BRStyles,
    })

    const headerRedesign2024Styles = shouldUseHeaderRedesign2024 && {
      margin: isLastItem ? "12px 0 0 0" : "12px 0",
      ...(isFirstItem && {marginTop: "20px"}),
      ...(isFirstItemWithoutNewMembershipStatus && {marginTop: 0}),
    }
    return {...defaultStyles, ...brandStyles, ...headerRedesign2024Styles}
  }
)

const ItemLink = styled.a<{
  isCanadaLoyaltyMarket?: boolean
  brandName: string
  shouldUseHeaderRedesign2024: boolean
}>(({isCanadaLoyaltyMarket, brandName, shouldUseHeaderRedesign2024, theme}) => {
  return {
    ...theme.brandFont,
    display: "block",
    paddingTop: ".65em",
    paddingBottom: ".65em",
    fontWeight: isCanadaLoyaltyMarket
      ? returnFontWeightByBrandCa(brandName)
      : "inherit",
    ...(shouldUseHeaderRedesign2024 && {
      padding: 0,
      ...headerRedesign2024TextStyles[brandName as Brands],
    }),
  }
})

export const AccountDropdownItem = ({
  link,
  isCanadaLoyaltyMarket,
  text,
  shouldSeparate,
  shouldUseInverseBrColors,
  isLastItem,
  isFirstItem,
  isFirstItemWithoutNewMembershipStatus,
}: AccountDropdownItemProps): JSX.Element => {
  const {brandName} = useAppState()
  const {shouldUseHeaderRedesign2024} = useHeaderRedesign2024()

  return (
    <ItemContainer
      isCanadaLoyaltyMarket={isCanadaLoyaltyMarket}
      isFirstItem={isFirstItem}
      isFirstItemWithoutNewMembershipStatus={
        !!isFirstItemWithoutNewMembershipStatus
      }
      isLastItem={isLastItem}
      shouldSeparate={shouldSeparate}
      shouldUseHeaderRedesign2024={shouldUseHeaderRedesign2024}
      shouldUseInverseBrColors={shouldUseInverseBrColors}
    >
      <ItemLink
        brandName={brandName}
        href={link}
        isCanadaLoyaltyMarket={isCanadaLoyaltyMarket}
        shouldUseHeaderRedesign2024={shouldUseHeaderRedesign2024}
      >
        {text}
      </ItemLink>
    </ItemContainer>
  )
}
