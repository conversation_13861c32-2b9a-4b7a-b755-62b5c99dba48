// @ts-nocheck
import {LARGE, SMALL} from "@ecom-next/core/breakpoint-provider"
import { render, fireEvent, screen, act } from "@sitewide/components/legacy/setupTests/test-helpers";
import EdfsFooter from "../EdfsFooter"

const ariaLabelText = "Close"

const props = {
  data: {
    largeBreakPointContainerStyle: {maxWidth: "800px"},
    modalCloseButtonAriaLabel: ariaLabelText,
    modalTitle: "Everyday Free Shipping",
    modalUrl:
      "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-BR-Narvar101518.html",
    ssiteID: "GAP_Footer",
  },
}

describe("EdfsFooter", () => {
  describe("desktop edfs", () => {
    it("opens modal when clicking details button", async () => {
      render(<EdfsFooter {...props} />, {breakpoint: LARGE})
      await act(async () => { 

       fireEvent.click(screen.getByRole("button", {name: /details/i})); 

       })
      expect(screen.getByRole("dialog")).toBeInTheDocument()
    })
  })

  describe("mobile edfs", () => {
    it("opens modal when clicking content", async () => {
      render(<EdfsFooter {...props} />, {breakpoint: SMALL})
      await act(async () => { 

       fireEvent.click(screen.getByRole("presentation")); 

       })
      expect(screen.getByRole("dialog")).toBeInTheDocument()
    })
  })
})
