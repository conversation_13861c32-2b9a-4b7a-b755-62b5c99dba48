// @ts-nocheck
import {Color, Size, Variant} from "@ecom-next/core/legacy/fixed-button"
import {DynamicModalProps} from "../../../dynamic-modal/types"
import {EmailRegistrationFormProps} from "../types"

const profileUrl =
  "/profile/info.do?cid=82638&amp;gapSubmit=true&amp;mlink=5058,8095009,PersadoV8&amp;clink=8095009"

const hiddenFields = {
  sourceGenericCode: "WEBSITE EMAIL SIGNUP",
  sourceSpecificCodeMap:
    "GP:Site_Footer;BR:NA;ON:NA;PL:NA;AT:NA;BRFS:NA;GPO:NA",
}

const inputOptions = {
  label: "Enter Your Email Address",
  errorMessage: "enter a valid email address",
}

const titleOptions = {
  style: {
    fontSize: "1.5rem",
    fontWeight: "400",
  },
}

const subtitleOptions = {
  style: {
    fontSize: "1.067rem",
    color: "rgb(102, 102, 102)",
    fontWeight: 400,
  },
}

export const minimalJSON: EmailRegistrationFormProps = {
  targetURL: profileUrl,
  hiddenFields,
  textInputOptions: inputOptions,
  submitButtonOptions: {
    text: "Join",
    desktop: {
      style: {
        marginTop: "1rem",
        marginBottom: "1rem",
      },
    },
    mobile: {
      style: {
        marginTop: "1rem",
        marginBottom: "1rem",
      },
    },
  },
  errorNotificationAriaLabel: "Error",
}

export const customizedJSON: EmailRegistrationFormProps = {
  style: {
    desktop: {
      padding: "20px",
      border: "1px solid",
      backgroundColor: "#EDF1F2",
      margin: "0 1rem 1rem",
    },
    mobile: {
      padding: "20px",
      border: "1px solid",
      backgroundColor: "#EDF1F2",
      margin: "1rem",
    },
  },
  targetURL: profileUrl,
  hiddenFields,
  customText: {
    title: {
      data: {
        classes: "",
        style: {},
        html:
          '<h4 style="font-size: 1.5rem; color: #122344; text-transform: uppercase; font-weight: 400">Sign up for email</span><span><span>&amp; get 25% off</span><span>*</span></h4>', // NOSONAR
      },
    },
    subtitle: {
      data: {
        ...subtitleOptions,
        html: '<p">Why, hello there guys!</p>',
      },
    },
    disclaimerText: {
      data: {
        style: {
          marginBottom: "1rem",
          lineHeight: "1.4",
          fontSize: ".75rem",
        },
        html:
          '<p style="margin-bottom: 1rem"><span class="asterisk">*</span>Valid for first-time registrants only &amp; applies to <span style="white-space: nowrap">reg. price items only.</span> <a onclick="return contentItemLink(this,\'\',\'CS_Footer_PrivacyPolicy\');" href="https://corporate.gapinc.com/en-us/consumer-privacy-policy" style="cursor: pointer; color: #0a5694; text-decoration: none">Privacy Policy</a></p><p style="margin-bottom: 1rem">Yes! I would like to receive style news and exclusive offers from Gap Inc. and related companies and brands including Gap (Canada) Inc. and Old Navy (Canada) Inc., and Banana Republic (Canada) Inc.</p><p>You can withdraw your consent at any time. For more details see our <a onclick="return contentItemLink(this,\'\',\'CS_Footer_PrivacyPolicy\');" href="https://corporate.gapinc.com/en-us/consumer-privacy-policy" style="cursor: pointer; color: #0a5694; text-decoration: none">Privacy Policy</a> or <a href="https://corporate.gapinc.com/en-us/consumer-privacy-policy?locale=en_CA#contact" onclick="return contentItemLink(this,\'\',\'CS_Footer_ContactUs\');" style="cursor: pointer; color: #0a5694; text-decoration: none">Contact Us</a>.</p>', // NOSONAR
      },
    },
  },
  textInputOptions: {
    ...inputOptions,
    desktop: {
      crossBrand: false,
      inverse: false,
      style: {
        marginBottom: "1rem",
      },
    },
    mobile: {
      crossBrand: false,
      inverse: false,
      style: {
        marginBottom: "1rem",
      },
    },
  },
  submitButtonOptions: {
    text: "Join",
    desktop: {
      variant: Variant.border,
      size: Size.medium,
      fullWidth: false,
      crossBrand: false,
      color: Color.primary,
      style: {
        marginTop: "1rem",
        marginBottom: "1rem",
      },
    },
    mobile: {
      variant: Variant.outline,
      size: Size.small,
      fullWidth: true,
      crossBrand: false,
      color: Color.primary,
      style: {
        marginTop: "1rem",
        marginBottom: "1rem",
      },
    },
  },
  detailsLink: {
    content: {
      linkText: "Details",
      closeModalAriaLabel: "Close",
      tid: "",
      url:
        "/Asset_Archive/GPWeb/content/static-marketing/xbrand-edfs-content/edfsLegal-ON-Narvar-122419.html",
      modalSize: "max",
    },
    style: {
      desktop: {
        fontSize: ".75rem",
        cursor: "pointer",
      },
      mobile: {
        fontSize: ".75rem",
        cursor: "pointer",
      },
    },
    modalIframeStyle: {
      desktop: {
        height: "37.5rem",
      },
      mobile: {
        height: "20rem",
      },
    },
  },
  errorNotificationAriaLabel: "Error",
}

export const thankYouMessageJSON: EmailRegistrationFormProps = {
  targetURL: profileUrl,
  style: {
    desktop: {
      padding: "20px",
    },
    mobile: {
      padding: "20px",
    },
  },
  thankYouMessage: {
    title: {
      data: {
        ...titleOptions,
        html: "<h3>Thank You</h3>",
      },
    },
    message: {
      data: {
        ...subtitleOptions,
        html:
          "<p>Thanks for checking out the inline thank you message.  ProTip!  If placed inside of a modal, you can have the button on the thank you message close the modal by omitting the link parameter in the button object.</p>", // NOSONAR
      },
    },
    button: {
      text: "Manage Preferences",
      link: profileUrl,
    },
  },
  hiddenFields,
  customText: {
    title: {
      data: {
        classes: "",
        style: {},
        html:
          '<h4 style="font-size: 1.5rem; color: #122344; text-transform: uppercase; font-weight: 400">Form With Inline Thank You Message</h4>',
      },
    },
    subtitle: {
      data: {
        ...subtitleOptions,
        html:
          "<p>This form will present a thank you message inline instead of redirecting to a URL if a thankYouMessage object is defined.</p>",
      },
    },
  },
  textInputOptions: {
    ...inputOptions,
    desktop: {
      crossBrand: false,
      inverse: false,
      style: {
        marginBottom: "1rem",
      },
    },
    mobile: {
      crossBrand: false,
      inverse: false,
      style: {
        marginBottom: "1rem",
      },
    },
  },
  submitButtonOptions: {
    text: "See Thank You Message",
    desktop: {
      variant: Variant.border,
      size: Size.medium,
      fullWidth: false,
      crossBrand: false,
      color: Color.primary,
      style: {
        marginTop: "1rem",
        marginBottom: "1rem",
      },
    },
    mobile: {
      variant: Variant.outline,
      size: Size.small,
      fullWidth: true,
      crossBrand: false,
      color: Color.primary,
      style: {
        marginTop: "1rem",
        marginBottom: "1rem",
      },
    },
  },
  errorNotificationAriaLabel: "Error",
}

export const withinAModalJSON: DynamicModalProps = {
  layoutData: {
    desktop: {
      shouldDisplay: true,
      data: {
        style: {
          display: "flex",
          flexDirection: "column",
          padding: "1rem",
        },
        components: [
          {
            name: "LayoutComponent",
            type: "sitewide",
            data: {
              desktop: {
                shouldDisplay: true,
                data: {
                  style: {
                    display: "flex",
                    flexDirection: "row",
                    justifyContent: "space-between",
                  },
                  components: [
                    {
                      name: "EmailRegistrationForm",
                      type: "sitewide",
                      data: {
                        style: {
                          desktop: {
                            padding: "20px",
                          },
                          mobile: {
                            padding: "20px",
                          },
                        },
                        thankYouMessage: {
                          title: {
                            data: {
                              ...titleOptions,
                              html: "<h3>You made it!!!</h3>",
                            },
                          },
                          message: {
                            data: {
                              ...subtitleOptions,
                              html:
                                "<p>The button below will close this modal.</p>",
                            },
                          },
                          button: {
                            text: "Close The Modal",
                            closeModal: true,
                          },
                        },
                        hiddenFields,
                        customText: {
                          title: {
                            data: {
                              classes: "",
                              style: {},
                              html:
                                '<h4 style="font-size: 1.5rem; color: #122344; text-transform: uppercase; font-weight: 400">This Form Is In A Modal</h4>',
                            },
                          },
                          subtitle: {
                            data: {
                              ...subtitleOptions,
                              html:
                                "<p>This form will present a thank you message within the modal.</p>",
                            },
                          },
                        },
                        textInputOptions: {
                          ...inputOptions,
                          desktop: {
                            crossBrand: false,
                            inverse: false,
                            style: {
                              marginBottom: "1rem",
                            },
                          },
                          mobile: {
                            crossBrand: false,
                            inverse: false,
                            style: {
                              marginBottom: "1rem",
                            },
                          },
                        },
                        submitButtonOptions: {
                          text: "See Thank You Message",
                          desktop: {
                            variant: "border",
                            size: "medium",
                            fullWidth: false,
                            crossBrand: false,
                            color: "primary",
                            style: {
                              marginTop: "1rem",
                              marginBottom: "1rem",
                            },
                          },
                          mobile: {
                            variant: "outline",
                            size: "small",
                            fullWidth: true,
                            crossBrand: false,
                            color: "primary",
                            style: {
                              marginTop: "1rem",
                              marginBottom: "1rem",
                            },
                          },
                        },
                        errorNotificationAriaLabel: "Error",
                      },
                    },
                    {
                      type: "builtin",
                      name: "img",
                      data: {
                        props: {
                          src:
                            "/Asset_Archive/BRWeb/content/0018/703/005/assets/PD_USCAe_50.jpg",
                          alt: "Example Marketing Content Image",
                        },
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
      },
    },
  },
  localStorageKey: "emailPopup",
  closeButtonAriaLabel: "Close Modal",
  modalSize: "max",
  title: "",
  style: {
    padding: "0 40px",
    paddingBottom: "40px",
  },
  oneTimeView: false,
}
