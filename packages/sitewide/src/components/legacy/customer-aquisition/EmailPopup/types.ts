// @ts-nocheck
"use client";
import {CSSObject} from "@ecom-next/core/react-stitch"
import {FixedButtonProps} from "@ecom-next/core/legacy/fixed-button"
import {AnimationStatus} from "@ecom-next/core/legacy/button"
import {ModalProps} from "@ecom-next/core/legacy/modal"

export type LegalDetail = {
  className?: string
  copy: string
  href: string
  style: CSSObject
}

export type LegalDetailsProps = Partial<LegalDetail> & {
  data?: LegalDetail[]
}

export type HeadlineProps = {
  style?: CSSObject
  text: string
}

export type Description = {
  classStyle?: string
  text: string
  style?: CSSObject
}[]

export type SubmitButtonProps = FixedButtonProps & {
  className?: string
  status: AnimationStatus
}

type InputValue = {
  emailPlaceHolder: string
  spcBrndSrc: string
  targetURL: string
}

export type EmailPopupProps = {
  /**
   * reference : https://www.w3schools.com/cssref/css3_pr_background.asp
   */
  background?: string
  ctaCopy: string
  description: Description
  emailImage: string
  emailImageAltText: string
  emailImageLink: string
  errorNotificationAriaLabel: string
  inputVals: InputValue[]
  legalDetails: LegalDetailsProps
  modalCloseButtonAriaLabel: string
  modalHeadline: HeadlineProps
  modalSize?: ModalProps["modalSize"]
  modalTitle?: string
  submitButtonOptions?: {
    desktop?: SubmitButtonProps
  }
  validationMessage: string
  /**
   * Function that will be called when the modal is closed.
   */
  onClose?(): void
  onDisplay?(): void
  /**
   * Function that will be called when a valid email address is submitted.
   */
  onSubmit?(): void
}

export type AnalyticsEvents = {
  analytics: {
    on_close: {
      content_id: string
      link_name: string
      tracking_enabled: boolean
    }
    on_submit: {
      content_id: string
      link_name: string
      tracking_enabled: boolean
    }
  }
}
