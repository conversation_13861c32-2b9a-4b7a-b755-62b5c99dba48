// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SearchTerms /> when SearchTerms is rendered should render SearchTerms component correctly 1`] = `
.emotion-0 {
  display: inline;
}

.emotion-1 {
  font-size: 18px;
  text-transform: none;
  color: inherit;
  padding: 0.5rem 1rem 0.5rem 1rem;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-2 {
  -webkit-overflow-scrolling: touch;
  padding-bottom: 0.5rem;
  max-height: -webkit-fit-content;
  max-height: -moz-fit-content;
  max-height: fit-content;
}

.emotion-3 {
  padding: 0px 1rem 0px 1rem;
}

.emotion-4 {
  cursor: pointer;
  padding: 0.5rem 0px 0.5rem 0px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.emotion-4:hover .search-terms-item {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

.emotion-5 {
  vertical-align: top;
  color: #000;
  font-size: 14px;
  margin-left: 16px;
}

<div>
  <div
    data-testid="search-terms"
  >
    <div
      class="emotion-0"
    >
      <h2
        aria-label="Recent Searches"
        class="emotion-1"
        id="search-terms-header"
      >
        Recent Searches
      </h2>
      <button>
        Clear All
      </button>
    </div>
    <div
      class="emotion-2"
    >
      <ul
        aria-expanded="true"
        aria-labelledby="search-terms-header"
        class="emotion-3"
        data-testid="search-terms"
        id="search-results"
        role="listbox"
        tabindex="0"
      >
        <li
          aria-label="search_bar.placeholder hats"
          aria-selected="false"
          class="emotion-4"
          data-testid="search-terms-list-item"
          id="search-terms-suggestedOption0"
          role="option"
          tabindex="0"
        >
          <span
            class="search-terms-item emotion-5"
          >
            hats
          </span>
        </li>
        <li
          aria-label="search_bar.placeholder jeans"
          aria-selected="false"
          class="emotion-4"
          data-testid="search-terms-list-item"
          id="search-terms-suggestedOption1"
          role="option"
          tabindex="0"
        >
          <span
            class="search-terms-item emotion-5"
          >
            jeans
          </span>
        </li>
        <li
          aria-label="search_bar.placeholder shirts"
          aria-selected="false"
          class="emotion-4"
          data-testid="search-terms-list-item"
          id="search-terms-suggestedOption2"
          role="option"
          tabindex="0"
        >
          <span
            class="search-terms-item emotion-5"
          >
            shirts
          </span>
        </li>
      </ul>
    </div>
    <div>
      Children
    </div>
  </div>
</div>
`;
