// @ts-nocheck
"use client";
import {TextAlign} from "src/features/footer/types"

const spaceEvenlyStyle = "space-evenly"
const smallImageSvg =
  "/Asset_Archive/BRWeb/content/0014/917/015/assets/mobileICON2.svg"

export default {
  mobile: {
    shouldDisplay: true,
    data: {
      placeholderSettings: {
        useGreyLoadingEffect: false,
        mobile: {
          width: "100%",
        },
      },
      style: {
        justifyContent: spaceEvenlyStyle,
        alignItems: "center",
        marginBottom: "1.5rem",
      },
      components: [
        {
          type: "sitewide",
          name: "Image",
          tileStyle: {
            mobile: {
              flex: "1 1 15%",
              width: "15%",
              flexBasis: "unset",
              textAlign: "right" as TextAlign,
              margin: "0",
            },
            desktop: {},
          },
          data: {
            smallImg: smallImageSvg,
            largeImg: smallImageSvg,
            altText: "BR friends, let's text",
            style: {},
          },
        },
        {
          type: "sitewide",
          name: "<PERSON><PERSON><PERSON>",
          tileStyle: {
            mobile: {
              flex: "1 1 85%",
              width: "85%",
              flexBasis: "unset",
              maxWidth: "100%",
            },
            desktop: {},
          },
          data: {
            phoneInputData: {
              "aria-label": "",
              label: "Mobile Number",
            },
            apiParams: {
              campaign_id: "8mp2I6WJ",
              acquisition_id: "pbapRSuN",
              src_gnrc_sms: "WEBSITE_SIGNUP",
              src_spfc_sms: "BRFSUS_FOOTERSIGNUP",
            },
            styles: {
              formStyles: {
                display: "flex",
                alignItems: "flex-end",
                justifyContent: spaceEvenlyStyle,
              },
              buttonStyles: {
                marginRight: "10%",
              },
              phoneInputStyles: {
                textAlign: "left" as TextAlign,
                width: "auto",
                minHeight: "unset",
                fontSize: ".9rem",
                color: "red",
              },
            },
          },
        },
      ],
    },
  },
  desktop: {
    shouldDisplay: true,
    data: {
      placeholderSettings: {
        useGreyLoadingEffect: false,
        desktop: {
          width: "100%",
        },
      },
      style: {},
      components: [
        {
          type: "sitewide",
          name: "Image",
          tileStyle: {
            mobile: {},
            desktop: {
              textAlign: "right" as TextAlign,
              margin: "1rem 0.8rem auto 0",
            },
          },
          data: {
            smallImg: smallImageSvg,
            largeImg: smallImageSvg,
            altText: "BR friends, let's text",
          },
        },
        {
          type: "sitewide",
          name: "SMSForm",
          tileStyle: {
            mobile: {},
            desktop: {
              maxWidth: "100%",
            },
          },
          data: {
            phoneInputData: {
              "aria-label": "",
              label: "Mobile Number",
            },
            apiParams: {
              campaign_id: "8mp2I6WJ",
              acquisition_id: "pbapRSuN",
              src_gnrc_sms: "WEBSITE_SIGNUP",
              src_spfc_sms: "BRFSUS_FOOTERSIGNUP",
            },
            buttonText: "Sign Up For Texts",
            styles: {
              formStyles: {
                display: "flex",
                alignItems: "flex-end",
                justifyContent: spaceEvenlyStyle,
              },
              buttonStyles: {},
              phoneInputStyles: {},
            },
          },
        },
      ],
    },
  },
}
