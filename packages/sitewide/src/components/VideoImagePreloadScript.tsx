import { extractVideoFallbackImages } from '@ecom-next/marketing-ui/utils/extractVideoImages';

interface VideoImagePreloadScriptProps {
  marketingDataPromise?: Promise<any>;
}

/**
 * Server component that awaits marketing data and generates a script
 * to inject preload tags for video fallback images
 */
export default async function VideoImagePreloadScript({ 
  marketingDataPromise 
}: VideoImagePreloadScriptProps) {
  if (!marketingDataPromise) return null;
  
  try {
    // Await marketing data on the server
    const marketingData = await marketingDataPromise;
    
    // Extract video fallback images
    const videoImages = extractVideoFallbackImages(marketingData);
    
    // Limit to first 3 images to avoid too many preloads
    const imagesToPreload = videoImages.slice(0, 3);
    
    if (imagesToPreload.length === 0) return null;
    
    // Generate script that will inject preload tags
    const preloadScript = `
      (function() {
        const images = ${JSON.stringify(imagesToPreload)};
        const head = document.head;
        
        images.forEach(function(src) {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'image';
          link.fetchPriority = 'high';
          link.href = src;
          
          // Insert at the beginning of head for higher priority
          if (head.firstChild) {
            head.insertBefore(link, head.firstChild);
          } else {
            head.appendChild(link);
          }
        });
      })();
    `;
    
    return (
      <script
        dangerouslySetInnerHTML={{ __html: preloadScript }}
        data-purpose="video-image-preload"
      />
    );
  } catch (error) {
    // Silently fail - don't break the page
    console.error('Failed to extract video images for preloading:', error);
    return null;
  }
}