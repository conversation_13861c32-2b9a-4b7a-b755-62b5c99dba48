import { HeadlineTextProps, TextConfig } from './types';
import { classNameToTailwind } from './utils';

function HeadlineText({ textConfig }: Readonly<HeadlineTextProps>): JSX.Element {
  return (
    <>
      {Boolean(textConfig?.length) &&
        textConfig.map(({ className, inlineStyle, text }: TextConfig) => {
          const classes = `sw_headline__content-text ${classNameToTailwind(className)}`;

          return (
            <span key={text} aria-hidden='true' className={classes} style={inlineStyle}>
              {text}
            </span>
          );
        })}
    </>
  );
}

export default HeadlineText;
