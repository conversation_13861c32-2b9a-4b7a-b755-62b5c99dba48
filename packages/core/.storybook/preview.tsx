import React from 'react';
import { Preview } from '@storybook/react';
import { useEnabledFeatures, StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { FeatureFlagsProvider } from '../src/components/legacy/feature-flags';
import legacyDecorators from './legacy/decorators';

const decorators = [
  (Story, context) => {
    const { brand = 'gap' } = context;
    const enabledFeatures = useEnabledFeatures();

    return (
      <FeatureFlagsProvider enabledFeatures={enabledFeatures}>
        <StitchStyleProvider brand={brand}>
          <Story />
        </StitchStyleProvider>
      </FeatureFlagsProvider>
    );
  },
];
const preview: Preview = {
  decorators: process.env.STORYBOOK_USE_CORE_LEGACY === 'true' ? legacyDecorators : decorators,
  argTypes: {
    brand: {
      control: {
        type: 'select',
        labels: {
          at: 'Athleta',
          br: 'Banana Republic',
          brfs: 'Banana Republic Factory',
          gap: 'Gap',
          gapfs: 'Gap Factory',
          on: 'Old Navy',
        },
      },
      options: ['at', 'br', 'brfs', 'gap', 'gapfs', 'on'],
      description: 'View for different brand type.',
      table: {
        defaultValue: { summary: 'gap' },
        category: 'Brand',
      },
    },
  },
};

export default preview;
