// @ts-nocheck
"use client";
const SORT_BY_PRICE_DESKTOP_OPTIONS_FEATURED =
  "sortby.desktop.options.featured";
const SORT_BY_PRICE_DESKTOP_OPTIONS_LOW = "sortby.desktop.options.low";
const SORT_BY_PRICE_DESKTOP_OPTIONS_HIGH = "sortby.desktop.options.high";

const SORT_BY_PRICE_MOBILE_OPTIONS_FEATURED = "sortby.mobile.options.featured";
const SORT_BY_PRICE_MOBILE_OPTIONS_LOW = "sortby.mobile.options.low";
const SORT_BY_PRICE_MOBILE_OPTIONS_HIGH = "sortby.mobile.options.high";

export default function sortCollaborator(localize) {
  const desktop = {
    featured: localize(SORT_BY_PRICE_DESKTOP_OPTIONS_FEATURED),
    low: localize(SORT_BY_PRICE_DESKTOP_OPTIONS_LOW),
    high: localize(SORT_BY_PRICE_DESKTOP_OPTIONS_HIGH),
  };

  const mobile = {
    featured: localize(SORT_BY_PRICE_MOBILE_OPTIONS_FEATURED),
    low: localize(SORT_BY_PRICE_MOBILE_OPTIONS_LOW),
    high: localize(SORT_BY_PRICE_MOBILE_OPTIONS_HIGH),
  };

  return {
    getSortByOptionKeyFromValue(sortByValue) {
      return Object.keys(desktop).find(
        (key) => sortByValue === mobile[key] || sortByValue === desktop[key]
      );
    },
    getCurrentSortByOptions(isMobile) {
      return Object.keys(desktop).map((key) =>
        isMobile ? mobile[key] : desktop[key]
      );
    },
    getCurrentSortByObject(currentSelectedFacets, isMobile) {
      const sortByObject = {
        facetName: "sortBy",
        facetType: "sortBy",
        facetSelectType: "sortBy",
      };

      const optionId = currentSelectedFacets?.sortBy?.[0]?.searchFacetOptionId;
      if (optionId) {
        const option = isMobile
          ? mobile[optionId] ?? desktop[optionId]
          : desktop[optionId] ?? mobile[optionId];

        sortByObject.searchFacetOptionId = optionId;
        sortByObject.searchFacetOptionValue = option;
      }

      return sortByObject;
    },
    sortByOptions: desktop,
  };
}
