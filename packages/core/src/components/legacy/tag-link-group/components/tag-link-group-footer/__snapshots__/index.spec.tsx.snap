// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly Athleta 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly BananaRepublic 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly Gap 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly OldNavy 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;

exports[`<TagLinkGroupFooter /> Snapshots for small breakpoint renders See More state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  <div
    data-testid="tag-link-group-footer"
  >
    tag_link_group.see_more_related_categories_text
  </div>
</DocumentFragment>
`;
