'use client';
import React from 'react';
import { useEnabledFeatures } from '@ecom-next/core/react-stitch';
import { Brands } from '@ecom-next/core/legacy/utility';
import { ResponsiveIcon } from '../../components/Icon';
import { IconContainerProps } from '../../types';
import { AthletaSVG } from './PauseCircleIcon.at';

export const NEW_PAUSE_ICON = 'new-pause-icon';

const BR2024PauseSVG = (
  <svg data-testid={NEW_PAUSE_ICON} fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'>
    <g clipPath='url(#clip0_1271_34325)'>
      <path d='M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z' fill='#FFFFFF' />
      <path
        d='M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z'
        fill='#2C2824'
      />
    </g>
    <defs>
      <clipPath id='clip0_1271_34325'>
        <rect fill='white' height='24' width='24' />
      </clipPath>
    </defs>
  </svg>
);

const CrossBrandLargeViewBoxSVG = (
  <svg height='48' viewBox='0 0 48 48' width='48' xmlns='http://www.w3.org/2000/svg'>
    <path d='M48 24c0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0s24 10.745 24 24z' fill='#F2F2F2' />
    <path d='M15 15a3 3 0 116 0v18a3 3 0 11-6 0V15zM27 15a3 3 0 116 0v18a3 3 0 11-6 0V15z' />
  </svg>
);

const CrossBrandSmallViewBoxSVG = (
  <svg height='32' viewBox='0 0 32 32' width='32' xmlns='http://www.w3.org/2000/svg'>
    <path d='M32 16c0 8.837-7.163 16-16 16S0 24.837 0 16 7.163 0 16 0s16 7.163 16 16z' fill='#F2F2F2' />
    <path d='M10 10a2 2 0 114 0v12a2 2 0 11-4 0V10zM18 10a2 2 0 114 0v12a2 2 0 11-4 0V10z' />
  </svg>
);

export const PauseCircleIcon = (props: IconContainerProps): JSX.Element => {
  const isBRDesign2024 = useEnabledFeatures()['mui-br-redesign-2024'];

  const AthletaIcon = (isSmall?: boolean) => <AthletaSVG colorTheme={props.colorTheme} isSmall={isSmall} variant={props.variant} />;

  const brandIconLargeViewBoxSVGs = {
    [Brands.Athleta]: AthletaIcon(),
    [Brands.BananaRepublic]: isBRDesign2024 ? BR2024PauseSVG : CrossBrandLargeViewBoxSVG,
    [Brands.BananaRepublicFactoryStore]: isBRDesign2024 ? BR2024PauseSVG : CrossBrandLargeViewBoxSVG,
    [Brands.Gap]: CrossBrandLargeViewBoxSVG,
    [Brands.GapFactoryStore]: CrossBrandLargeViewBoxSVG,
    [Brands.OldNavy]: CrossBrandLargeViewBoxSVG,
    crossBrand: CrossBrandLargeViewBoxSVG,
  };

  const brandIconSmallViewBoxSVGs = {
    [Brands.Athleta]: AthletaIcon(true),
    [Brands.BananaRepublic]: isBRDesign2024 ? BR2024PauseSVG : CrossBrandSmallViewBoxSVG,
    [Brands.BananaRepublicFactoryStore]: isBRDesign2024 ? BR2024PauseSVG : CrossBrandSmallViewBoxSVG,
    [Brands.Gap]: CrossBrandSmallViewBoxSVG,
    [Brands.GapFactoryStore]: CrossBrandSmallViewBoxSVG,
    [Brands.OldNavy]: CrossBrandSmallViewBoxSVG,
    crossBrand: CrossBrandSmallViewBoxSVG,
  };

  return <ResponsiveIcon {...props} brandIconLargeViewBoxSVGs={brandIconLargeViewBoxSVGs} brandIconSmallViewBoxSVGs={brandIconSmallViewBoxSVGs} />;
};
