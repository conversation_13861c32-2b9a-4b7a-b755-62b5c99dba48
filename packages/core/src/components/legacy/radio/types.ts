// @ts-nocheck
"use client";
import {
  SerializedStyles,
  StyleFn,
  FormProps,
  CSSObject,
  Theme,
} from "@ecom-next/core/react-stitch";

type RadioElements =
  | "label"
  | "input"
  | "radioButton"
  | "labelText"
  | "helpText";

export type GetStyles = (elementKey: RadioElements) => CSSObject | undefined;

type CustomElementStyles =
  | CSSObject
  | ((props: CommonProps & {theme: Theme}) => CSSObject);

export type Styles = {[ElementKey in RadioElements]?: CustomElementStyles};

export type CommonProps = Pick<FormProps, "crossBrand" | "disabled"> & {
  /**
   * If `true`, the component is checked.
   */
  checked?: boolean;
  /**
   * If `true`, the component has small size.
   */
  isRadioSmall?: boolean;
  /**
   * If `true`, the component will have small background color applied when button is checked.
   */
  isBackgroundSizeSmall?: boolean;
};

export type RadioProps = CommonProps & {
  /**
   * Override or extend the styles applied to the component.
   */
  className?: string;
  /**
   * If provided, all inner components will be given prefixed className attributes.
   */
  classNamePrefix?: string;
  /**
   * Provides additional context in sentence form to help the user complete the input.
   */
  helpText?: React.ReactNode;
  /** The id of the input element */
  id: string;
  /**
   * Attributes applied to the `input` element.
   */
  inputProps?: Omit<React.HTMLProps<HTMLInputElement>, "as">;
  /**
   * The input's label.
   */
  label?: React.ReactNode;
  /**
   * Name attribute of the `input` element.
   */
  name?: string;
  /**
   * Callback fired when the state is changed.
     event: The event source of the callback. You can pull out the new value by accessing `event.target.value` (string). You can pull out the new checked state by accessing `event.target.checked` (boolean).
   */
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * If `true`, the `input` element will be required.
   */
  required?: boolean;
  /**
   * Css overrides for radio elements
   */
  styles?: Styles;
  /**
   * The value of the component. The DOM API casts this to a string.
   */
  value?: any;
  /**
   * Custom background color for Radio input
   */

  customBackgroundColor?: string;
};

export type RadioBackgroundProp = Pick<RadioProps, "customBackgroundColor">;
export type RadioStyleFn<T = SerializedStyles> = StyleFn<
  CommonProps & RadioBackgroundProp,
  T
>;
