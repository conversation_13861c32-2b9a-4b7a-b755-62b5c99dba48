// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dialog snapshots renders bottom position state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders bottom position, rounded corners state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders left position, rounded corners state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders right position, rounded corners state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Dialog snapshots renders top position, rounded corners state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #fff;
  border-radius: 8px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
}

.emotion-0:focus {
  outline: none;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;
