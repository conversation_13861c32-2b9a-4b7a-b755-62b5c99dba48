// @ts-nocheck
"use client";
import {
  styled,
  css,
  StyleFn,
  SerializedStyles,
} from "@ecom-next/core/react-stitch";
import {MEDIUM, LARGE, XLARGE} from "@ecom-next/core/breakpoint-provider";
import {InternalModalProps} from "../types";

export type DialogElement = HTMLDivElement;

type DialogProps = Pick<
  InternalModalProps,
  | "crossBrand"
  | "minWidth"
  | "modalSize"
  | "hasRoundedCorners"
  | "isPartialPage"
  | "isOverflowed"
  | "mobileFitSizeToContent"
> & {br2023ColorChange: boolean; isBRWhiteBackground: boolean};

type DialogStyleFn<TReturn = SerializedStyles> = StyleFn<DialogProps, TReturn>;

const partialPageStyles: DialogStyleFn = ({isPartialPage}) =>
  isPartialPage &&
  css`
    // Ensures there is clickable space around the partial page modal in desktop sizes
    max-height: 90%;
    width: 95%;
    overflow: hidden;
  `;

export const modalSizeStyles: DialogStyleFn = ({minWidth, modalSize}) => {
  switch (modalSize) {
    case "mini":
      return css`
        max-width: 250px;
        max-height: 180px;
      `;
    case "standard": {
      let maxWidth = "300px";
      if (minWidth(MEDIUM)) maxWidth = "400px";
      if (minWidth(LARGE)) maxWidth = "500px";
      if (minWidth(XLARGE)) maxWidth = "600px";
      return css`
        max-width: ${maxWidth};
      `;
    }
    case "max": {
      let styles = {maxWidth: "400px", maxHeight: "850px"};
      if (minWidth(MEDIUM)) styles = {maxWidth: "582px", maxHeight: "1200px"};
      if (minWidth(LARGE)) styles = {maxWidth: "760px", maxHeight: "1250px"};
      if (minWidth(XLARGE)) styles = {maxWidth: "1000px", maxHeight: "1300px"};
      return styles;
    }
    default:
      return null;
  }
};

const overflowStyle: DialogStyleFn = ({isOverflowed}) =>
  isOverflowed &&
  css`
    align-self: flex-start;
  `;

const largeBreakpointStyles: DialogStyleFn = (props) =>
  props.minWidth(LARGE) &&
  css`
    position: relative;
    margin: 2.5rem auto;
    align-self: center;
    align-items: center;
    overflow: hidden;
    min-height: 0;
    height: auto;
    ${modalSizeStyles(props)};
    ${partialPageStyles(props)};
  `;

const getBackgroundColor: DialogStyleFn = ({
  theme,
  br2023ColorChange,
  isBRWhiteBackground,
}) => {
  if (br2023ColorChange) {
    return isBRWhiteBackground
      ? `background-color: ${theme.color.wh};`
      : "background-color: #F6F4EB;";
  }
  if (isBRWhiteBackground) {
    return `background-color: ${theme.color.wh};`;
  }
  return "background-color: #fff;";
};

const setBorderRadius: DialogStyleFn = ({hasRoundedCorners, theme}) =>
  hasRoundedCorners &&
  css`
    border-radius: ${theme.borderRadius.modal};
  `;
const mobileSizeToContent: DialogStyleFn = ({
  minWidth,
  mobileFitSizeToContent,
}) =>
  !minWidth(LARGE) &&
  mobileFitSizeToContent &&
  css`
    min-height: 0;
    height: auto;
    overflow: hidden;
    max-width: calc(100% - 30px);
  `;

const Dialog = styled.div<DialogProps>`
  align-self: flex-start;
  ${getBackgroundColor}
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  margin: auto;
  max-height: initial;
  max-width: none;
  min-height: 100%;
  width: 100%;

  ${({theme}) => theme.utilities.dropShadow};
  ${setBorderRadius}
  ${largeBreakpointStyles};
  ${overflowStyle}
  &:focus {
    outline: none;
  }

  ${mobileSizeToContent}
`;

export default Dialog;
