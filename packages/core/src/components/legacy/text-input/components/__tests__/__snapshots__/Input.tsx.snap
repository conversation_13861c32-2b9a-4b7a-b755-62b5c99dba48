// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Input snapshots renders default state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders default state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders disabled state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse + disabled state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders inverse state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border: 1px solid #F6F4EB;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border: 1px solid #F6F4EB;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border: 1px solid #F6F4EB;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  border-color: #CCC;
  border: 1px solid #F6F4EB;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled + inverse state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: rgba(255, 255, 255, 0.5);
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  border-color: #CCC;
  border: 1px solid #FFFFFF;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  border-color: rgba(255, 255, 255, 0.5);
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + disabled state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #CCC;
  border-color: #CCC;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    disabled=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error + readOnly state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with error state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  border: 1px solid #D00000;
  padding-left: 0.5rem;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #D00000 0%,
      #D00000 100%,
      transparent 100%
    );
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly Athleta 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly BananaRepublic 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #F6F4EB;
  border-color: #F6F4EB;
  border-bottom: 1px solid #F6F4EB;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #F6F4EB 0%,
      #F6F4EB 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly Gap 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly OldNavy 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + inverse state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #FFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
  border-bottom: 1px solid #FFFFFF;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #FFFFFF 0%,
      #FFFFFF 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly Athleta 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly BananaRepublic 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly Gap 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly OldNavy 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus + readOnly state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  outline: none;
  border-bottom: none;
}

<input
    class="emotion-0"
    readonly=""
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly Athleta 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Avenir Next','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly Athleta in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly BananaRepublic 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  outline: none;
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly BananaRepublic in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly BananaRepublicFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lynstone',Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  outline: none;
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly BananaRepublicFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly Gap 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly Gap in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly GapFactoryStore 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly GapFactoryStore in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #333;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly OldNavy 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Gotham','Helvetica Neue',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 16px;
  color: #000;
  outline: none;
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Input snapshots renders with focus state correctly OldNavy in crossBrand 1`] = `
<DocumentFragment>
  @keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

@keyframes animation-0 {
  0% {
    -webkit-background-size: 1px 1px;
    background-size: 1px 1px;
  }

  100% {
    -webkit-background-size: 100% 1px;
    background-size: 100% 1px;
  }
}

.emotion-0 {
  font-family: 'Lato','Source Sans Pro',Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  margin-top: 2.215rem;
  box-sizing: border-box;
  padding: 0.375rem 0;
  display: block;
  width: 100%;
  height: 2.25rem;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid #666;
  background-color: transparent;
  background-repeat: no-repeat;
  -webkit-background-size: 0 0;
  background-size: 0 0;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #333 0%,
      #333 100%,
      transparent 100%
    );
  -webkit-background-position: bottom center;
  background-position: bottom center;
  font-size: 17px;
  color: #000;
  outline: none;
  background-image: linear-gradient(
      to left,
      transparent 0%,
      #0466CA 0%,
      #0466CA 100%,
      transparent 100%
    );
  -webkit-animation: animation-0 250ms linear forwards;
  animation: animation-0 250ms linear forwards;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;
