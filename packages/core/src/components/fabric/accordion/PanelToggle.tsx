'use client';
import React, { useContext } from 'react';
import classNames from 'classnames';
import { AccordionChevronIcon } from '@ecom-next/core/fabric/icons';
import { PanelContext } from './PanelContext';
import { PanelToggleProps } from './types';

export function PanelToggle({
  title,
  subTitle,
  children,
  iconClass = 'toggle-icon',
  className,
  hideDefaultToggles,
  animateIcon = true,
  expandedIcon = <AccordionChevronIcon className={iconClass} viewBox='0 0 10 6' width={10} height={6} iconDirection='up' />,
  collapsedIcon = <AccordionChevronIcon className={iconClass} viewBox='0 0 10 6' width={10} height={6} />,
}: PanelToggleProps): JSX.Element {
  const { expanded, toggle, showLessToggle, showMoreToggle } = useContext(PanelContext);

  const toggleButtonProps = {
    title,
    'aria-expanded': expanded,
    onClick: toggle,
  };

  const panelToggleStyle = classNames('fds_panel__toggle', {
    'cursor-default': hideDefaultToggles,
    [`${className}`]: className,
  });

  const panelToggleIconStyle = animateIcon
    ? classNames('fds_panel__toggle-icon', { 'fds_panel__toggle--expanded': expanded, 'fds_panel__toggle--collapsed': !expanded })
    : 'fds_panel__toggle-icon';

  return (
    <button {...toggleButtonProps} data-testid='PanelToggleButton' className={panelToggleStyle}>
      <div className='fds_panel__toggle-title'>
        <div className='fds_panel__title-toggle-wrapper'>
          <span className='fds_panel__title'>{title}</span>
          {!hideDefaultToggles && (
            <div data-testid={expanded ? 'PanelToggleIconExpanded' : 'PanelToggleIconCollapsed'} className={panelToggleIconStyle}>
              {animateIcon ? collapsedIcon : expanded ? expandedIcon : collapsedIcon}
            </div>
          )}
        </div>
        {subTitle && <span className='fds_panel__sub-title w-full text-left'>{subTitle}</span>}
      </div>
      <div
        className={classNames('fds_panel__toggle-show-more-less', {
          'collapsed-no-show': !expanded && (!showMoreToggle || !showLessToggle),
        })}
      >
        {children}
      </div>
    </button>
  );
}
