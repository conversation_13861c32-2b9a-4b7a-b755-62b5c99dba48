import { render, screen } from 'test-utils';
import { AccordionExcerpt } from '../AccordionExcerpt';
import panelDataMock from './data/panelDataMock.json';

describe('Accordion Excerpt component', () => {
  const onChangeMock = jest.fn();
  const { excerptPanels } = panelDataMock;

  it('should render Accordion Excerpt with panels', () => {
    render(<AccordionExcerpt className='accordion-custom' panels={excerptPanels} onChange={onChangeMock} />);

    const toggleElement = screen.getByText('Panel 1');
    const toggleButtonElement = toggleElement?.parentElement?.parentElement;
    const excerptElement = screen.getByText('Panel Excerpt 1');
    const detailsElement = screen.getByText('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor 1.');
    const panelElement = toggleButtonElement?.parentElement;
    const buttonElement = screen.getAllByRole('button')[0];
    const accordionElement = buttonElement?.parentElement?.parentElement;

    expect(accordionElement).toHaveClass('fds_accordion fds_accordion_excerpt accordion-custom');
    expect(panelElement).toBeInTheDocument();
    expect(toggleElement).toBeInTheDocument();
    expect(excerptElement).toBeInTheDocument();
    expect(detailsElement).toBeInTheDocument();
  });

  it('should render Accordion Excerpt with panels for cross brand', () => {
    render(<AccordionExcerpt className='accordion-custom' panels={excerptPanels} onChange={onChangeMock} isCrossBrand />);

    const toggleElement = screen.getByText('Panel 1');
    const toggleButtonElement = toggleElement?.parentElement?.parentElement;
    const excerptElement = screen.getByText('Panel Excerpt 1');
    const detailsElement = screen.getByText('Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor 1.');
    const panelElement = toggleButtonElement?.parentElement;
    const buttonElement = screen.getAllByRole('button')[0];
    const accordionElement = buttonElement?.parentElement?.parentElement;

    expect(accordionElement).toHaveClass('fds_accordion crossbrand fds_accordion_excerpt accordion-custom');
    expect(panelElement).toBeInTheDocument();
    expect(toggleElement).toBeInTheDocument();
    expect(excerptElement).toBeInTheDocument();
    expect(detailsElement).toBeInTheDocument();
  });
});
