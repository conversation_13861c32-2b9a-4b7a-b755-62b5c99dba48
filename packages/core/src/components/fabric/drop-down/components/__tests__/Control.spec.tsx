import React from 'react';
import noop from 'lodash/noop';
import identity from 'lodash/identity';
import { render } from 'test-utils';
import Control from '../Control';
import { ControlProps } from '../../types';
import { DropdownAttributes } from '../../builtins';

const baseProps = {
  getOptionLabel: identity,
  onClick: noop,
  onKeyDown: noop,
  label: '',
  onMenuOpen: noop,
  onMenuClose: noop,
  id: 'test',
};

describe('<Control />', () => {
  test('displays label text when provided', () => {
    const label = 'Select an option';
    const control = render(<Control {...baseProps} label={label} />);
    expect(control.getAllByRole('button').at(0)).toHaveTextContent(label);
  });

  test('displays value', () => {
    const props = {
      label: 'Select an option',
      value: 'A value',
    };
    const control = render(<Control {...baseProps} {...props} />);
    expect(control.getAllByRole('button').at(0)).toHaveTextContent(props.value);
  });

  test('has aria labelled by which matches the id of the prop passed in', () => {
    expect(
      render(<Control {...baseProps} menuIsOpen />)
        .getAllByRole('button')
        .at(0)
    ).toHaveAttribute('aria-labelledby', 'test');
  });

  test('has aria-expanded true when menuIsOpen', () =>
    expect(
      render(<Control {...baseProps} menuIsOpen />)
        .getAllByRole('button')
        .at(0)
    ).toHaveAttribute('aria-expanded', 'true'));

  test('has aria-expanded false when menuIsOpen is false', () =>
    expect(
      render(<Control {...baseProps} menuIsOpen={false} />)
        .getAllByRole('button')
        .at(0)
    ).toHaveAttribute('aria-expanded', 'false'));

  describe('getOptionLabel prop', () => {
    describe('when control does not have a value', () => {
      test('is not called', () => {
        const getOptionLabel = jest.fn();
        render(<Control {...baseProps} getOptionLabel={getOptionLabel} />);
        expect(getOptionLabel).not.toHaveBeenCalled();
      });
    });

    describe('when control has a value', () => {
      test("getOptionLabel's return value becomes the control's children", () => {
        const value = { label: 'value label', value: 'value' };
        const getOptionLabel: ControlProps['getOptionLabel'] = option => (option as DropdownAttributes).label;
        const control = render(<Control {...baseProps} getOptionLabel={getOptionLabel} value={value} />);
        expect(control.getAllByRole('button').at(0)).toHaveTextContent(value.label);
      });
    });
  });
});
