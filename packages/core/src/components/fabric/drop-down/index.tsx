import React from 'react';
import { isMobile } from 'react-device-detect';
import classnames from 'classnames';
import { DropdownComponents } from './components';
import Control from './components/Control';
import Menu from './components/Menu';
import NoOptionsMessage from './components/NoOptionsMessage';
import Option from './components/Option';
import { Actions, CommonDropdownProps, CommonProps, ControlProps, DefaultStateProps } from './types';
import DropdownBase, { DropdownBaseProps } from './DropdownBase';
import NativeDropdown from './native-dropdown';
import useStateManager from './stateManager';

export type DropdownProps = CommonDropdownProps &
  DefaultStateProps &
  Partial<Actions> &
  Pick<DropdownBaseProps, 'dropdownContainerClassName'> &
  Pick<ControlProps, 'menuClassName'> &
  CommonProps & {
    /**
     * If provided, all inner components will be given prefixed className.
     */
    classNamePrefix?: string;
    /**
     * This object includes some compositional components that are used
     * in `dropdown`. If you wish to overwrite a component, pass in an object
     * with the appropriate namespace, for example
     * components={{ Option: CustomOption, Control: CustomControl }}
     *
     * If you only wish to restyle a component, we recommend using the
     * `classNamePrefix` prop instead.
     */
    components?: Partial<DropdownComponents>;
    /**
     * The error message to display when dropdown is in an "error" state
     */
    errorMessage?: string;
    /**
     * Attributes applied to option elements, When native is `true`
     */
    optionProps?: React.HTMLProps<HTMLOptionElement>;
    /**
     * The value for the name attribute applied to the hidden select
     */
    selectName?: string;
    /**
     * Attributes applied to the select element, When native is `true`
     */
    selectProps?: React.HTMLProps<HTMLSelectElement>;
    /**
     * text displayed under the dropdown
     */
    supportText?: string;
    /**
     * Max height of the dropdown menu
     */
    maxHeight?: string;
  };

export function Dropdown(props: DropdownProps): JSX.Element {
  const stateManagedProps = useStateManager(props);
  const {
    isCrossBrand = false,
    disabled = false,
    errorMessage = 'This field is required.',
    hasError = false,
    supportText = '',
    focus,
    noOptionsMessage = 'No options found',
    label = 'Select',
    menuIsOpen,
    components: componentsProp,
    onFocus,
    value,
    noLabel = false,
    native = isMobile,
    dropdownMenuStyles = {},
    ...restProps
  } = { ...props, ...stateManagedProps };

  const hasErrorMessage = hasError && errorMessage;
  const hasValue = menuIsOpen || value !== undefined;
  const containerClassName = classnames('fds_dropdown-container', {
    'fds_dropdown-container--disabled': disabled,
    'fds_dropdown-container--error': hasError,
    'fds_dropdown-container__error-message': hasErrorMessage,
    'fds_dropdown-container--focus': focus,
    'fds_dropdown-container--has-value': hasValue,
    'box-border border-0': native,
    cb: isCrossBrand,
  });

  const supportAndErrorText = classnames('fds_dropdown-container__support-text pt-2 block', {
    'fds_dropdown-container__support-text--error': hasErrorMessage,
    'fds_dropdown-container__support-text--support': supportText,
    cb: isCrossBrand,
  });

  const commonProps = {
    isCrossBrand,
    disabled,
    focus,
    hasError,
    menuIsOpen,
    errorMessage,
    noOptionsMessage,
    label,
    onFocus,
    value,
    noLabel,
    dropdownMenuStyles,
  };

  const defaultComponents = {
    Control,
    Menu,
    NoOptionsMessage,
    Option,
  };

  const components = {
    ...defaultComponents,
    ...componentsProp,
  };

  const DropdownComponent = native ? NativeDropdown : DropdownBase;
  return (
    <>
      <div className={containerClassName}>
        <DropdownComponent selectName='' {...commonProps} {...restProps} components={components} />
      </div>
      {!hasErrorMessage && !disabled && supportText && <label className={supportAndErrorText}>{supportText}</label>}
      {hasErrorMessage && !disabled && <label className={supportAndErrorText}>{errorMessage}</label>}
    </>
  );
}
