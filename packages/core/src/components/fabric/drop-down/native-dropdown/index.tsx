import React, { use<PERSON><PERSON>back, ChangeEventHandler } from 'react';
import { isEmpty } from 'lodash';
import { CommonDropdownProps, CommonProps, Actions } from '../types';
import classnames from 'classnames';
import { getOptionAriaLabel, getOptionLabel, getOptionValue, optionKeyGenerator, OptionType } from './../builtins';

export const getOption = (selectedValue: string, options: OptionType[]): OptionType => {
  const selectedOption = typeof options[0] === 'number' ? parseInt(selectedValue, 10) : selectedValue;
  return options.find(option => getOptionValue(option) === selectedOption);
};

export type NativeDropdownProps = CommonDropdownProps &
  CommonProps & {
    onChange: Actions['onChange'];
    optionProps?: React.HTMLProps<HTMLOptionElement>;
    selectProps?: Omit<React.HTMLProps<HTMLSelectElement>, 'as'>;
  };

const NativeDropdown = ({
  disabled,
  hasError,
  value: valueProp,
  noOptionsMessage,
  onChange,
  options = [],
  optionProps,
  selectProps,
  ariaLabel,
}: NativeDropdownProps): JSX.Element => {
  const valueProps = valueProp && { value: getOptionValue(valueProp) };

  const handleChange = useCallback<ChangeEventHandler<HTMLSelectElement>>(
    e => onChange(getOption(e.target.value, options), e.target.selectedIndex),
    [onChange, options]
  );

  const styleProps = { disabled, hasError, native: true };

  const selectClass = classnames('fds_dropdown-container__native', {
    'fds_dropdown-container__native--error': hasError,
  });
  return (
    <select onChange={handleChange} aria-label={ariaLabel} {...valueProps} {...styleProps} {...selectProps} data-testid='select-field' className={selectClass}>
      {!isEmpty(options) ? (
        options.map((option, i) => {
          const optionText = getOptionLabel(option);
          return (
            <option
              data-testid='select-option'
              key={optionKeyGenerator(option, i)}
              aria-label={getOptionAriaLabel(option) as string}
              value={getOptionValue(option)}
              disabled={option?.disabled}
              selected={option?.selected}
              {...optionProps}
            >
              {optionText}
            </option>
          );
        })
      ) : (
        <option disabled>{noOptionsMessage}</option>
      )}
    </select>
  );
};

export default NativeDropdown;
