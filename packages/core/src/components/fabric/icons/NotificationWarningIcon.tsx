import React from 'react';
import { SVGIconType } from './types';

export const NotificationWarningIcon = ({
  width = 16,
  height = 17,
  viewBox = '0 0 16 17',
  fillColor = 'none',
  shapeColors = { outerCircleFillColor: '#F0B00B', exclaimCircleFillColor: 'white', rectFillColor: 'white' },
}: SVGIconType) => (
  <svg width={width} height={height} viewBox={viewBox} fill={fillColor} xmlns='http://www.w3.org/2000/svg' role='img'>
    <circle cx='8' cy='8.87891' r='8' fill={shapeColors.outerCircleFillColor} />
    <circle cx='7.90005' cy='11.9789' r='0.9' transform='rotate(-180 7.90005 11.9789)' fill={shapeColors.exclaimCircleFillColor} />
    <rect x='8.65015' y='9.87891' width='1.5' height='4.5' rx='0.75' transform='rotate(-180 8.65015 9.87891)' fill={shapeColors.rectFillColor} />
  </svg>
);
