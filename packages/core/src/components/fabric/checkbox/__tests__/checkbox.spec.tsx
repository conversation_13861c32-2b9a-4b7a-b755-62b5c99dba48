import React from 'react';
import { fireEvent, render, screen } from 'test-utils';
import { Checkbox } from '../index';

describe('Checkbox component', () => {
  it('renders without crashing', () => {
    render(<Checkbox label='i am a label' onChange={() => {}} />);
  });

  it('renders with correct label text', () => {
    const label = 'I am a checkbox';
    const { getByText } = render(<Checkbox label={label} />);
    expect(getByText(label)).toBeInTheDocument();
  });

  it('renders with checked state', () => {
    const { getByLabelText } = render(<Checkbox label='Option 1' checked />);
    const checkboxInput = getByLabelText('Option 1') as HTMLInputElement;
    expect(checkboxInput.checked).toBe(true);
  });

  it('renders with checked state and then the updated state', () => {
    const { getByLabelText, rerender } = render(<Checkbox label='Option 1' checked />);
    const checkboxInput = getByLabelText('Option 1') as HTMLInputElement;
    expect(checkboxInput.checked).toBe(true);

    rerender(<Checkbox label='Option 1' checked={false} />);
    expect(checkboxInput.checked).toBe(false);
  });

  it('renders with checked state as false', () => {
    const { getByLabelText } = render(<Checkbox label='Option 1' checked={false} />);
    const checkboxInput = getByLabelText('Option 1') as HTMLInputElement;
    expect(checkboxInput.checked).toBe(false);
  });

  it('renders with disable state as true', () => {
    const { getByLabelText } = render(<Checkbox label='Option 1' checked={false} disabled />);
    const checkboxInput = getByLabelText('Option 1') as HTMLInputElement;
    expect(checkboxInput.disabled).toBe(true);
  });

  it('renders with disable state as false', () => {
    const { getByLabelText } = render(<Checkbox label='Option 1' checked={false} disabled={false} />);
    const checkboxInput = getByLabelText('Option 1') as HTMLInputElement;
    expect(checkboxInput.disabled).toBe(false);
  });

  it('renders with crossbrand class name when isCrossbrand prop is set to true', () => {
    const { container } = render(<Checkbox label='Option 1' checked={false} isCrossbrand />);
    const divElement = container.getElementsByClassName('fds_checkbox-wrapper')[0];
    expect(divElement.className).toBe('fds_checkbox-wrapper cb');
  });

  it('renders with error class name when hasError prop is set to true', () => {
    const { container } = render(<Checkbox label='Option 1' checked={false} hasError />);
    const divElement = container.getElementsByClassName('fds_checkbox-wrapper')[0];
    expect(divElement.className).toBe('fds_checkbox-wrapper fds_checkbox-wrapper--error');
  });

  it('renders with disabled class name when disabled prop is set to true', () => {
    const { container } = render(<Checkbox label='Option 1' checked={false} disabled />);
    const divElement = container.getElementsByClassName('fds_checkbox-wrapper')[0];
    expect(divElement.className).toBe('fds_checkbox-wrapper fds_checkbox-wrapper--disabled');
  });

  it('should call the onChange call back with the right params', () => {
    const onChangeSpy = jest.fn();
    render(<Checkbox label='checkbox' onChange={onChangeSpy} />);
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    expect(onChangeSpy).toHaveBeenCalledWith({
      target: {
        checked: true,
        name: 'checkbox',
      },
    });
    expect(checkbox).toBeChecked();
  });
});
