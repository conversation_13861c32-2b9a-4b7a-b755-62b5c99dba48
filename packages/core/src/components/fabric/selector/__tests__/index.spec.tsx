import React from 'react';
import { fireEvent, render, screen } from 'test-utils';
import { Selector } from '../index';

describe('Selector component', () => {
  const defaultProps = {
    id: 'option1',
    group: 'radioGroup',
    singleSelect: true,
    className: 'flex items-center',
    isAvailable: true,
    disabled: false,
    unavailableText: 'Unavailable',
    checked: false,
    onChange: jest.fn(),
  };

  it('should render the Selector component', () => {
    render(<Selector {...defaultProps}>Option 1</Selector>);
    expect(screen.getByText('Option 1')).toBeInTheDocument();
  });

  it('should render as available when isAvailable is true', () => {
    render(
      <Selector {...defaultProps} isAvailable={true}>
        Option 1
      </Selector>
    );
    expect(screen.getByText('Option 1')).not.toHaveClass('unavailable');
  });

  it('should render as unavailable when isAvailable is false', () => {
    render(
      <Selector {...defaultProps} isAvailable={false}>
        Option 1
      </Selector>
    );
    expect(screen.getByText('Unavailable')).toBeInTheDocument();
  });

  it('should render as disabled when disabled is true', () => {
    render(
      <Selector {...defaultProps} disabled>
        Option 1
      </Selector>
    );
    const labelElement = screen.getByText('Option 1').closest('label');
    expect(labelElement).toHaveAttribute('aria-disabled', 'true');
  });

  it('should call onChange when clicked', () => {
    render(<Selector {...defaultProps}>Option 1</Selector>);
    fireEvent.click(screen.getByText('Option 1'));
    expect(defaultProps.onChange).toHaveBeenCalled();
  });

  it('should render the SVG element when isAvailable is false', () => {
    render(
      <Selector {...defaultProps} isAvailable={false}>
        Option 1
      </Selector>
    );
    const svgElement = document.querySelector('.fds_selector__slash');
    expect(svgElement).toBeInTheDocument();
  });

  it('should not render the SVG element when isAvailable is false and disabled is true', () => {
    render(
      <Selector {...defaultProps} isAvailable={false} disabled>
        Option 1
      </Selector>
    );
    const svgElement = document.querySelector('.fds_selector__slash');
    expect(svgElement).not.toBeInTheDocument();
  });

  it('should render input element as radio when singleSelect is true', () => {
    render(
      <Selector {...defaultProps} singleSelect={true}>
        Option 1
      </Selector>
    );
    const inputElement = screen.getByRole('radio');
    expect(inputElement).toBeInTheDocument();
  });

  it('should render input element as checkbox when singleSelect is false', () => {
    render(
      <Selector {...defaultProps} singleSelect={false}>
        Option 1
      </Selector>
    );
    const inputElement = screen.getByRole('checkbox');
    expect(inputElement).toBeInTheDocument();
  });

  it('renders with checked state and then the updated state', () => {
    const { rerender } = render(
      <Selector {...defaultProps} singleSelect={true}>
        Option 1
      </Selector>
    );
    const inputElement = screen.getByRole('radio');
    expect(inputElement).not.toBeChecked();

    rerender(
      <Selector {...defaultProps} checked={true} singleSelect={true}>
        Option 1
      </Selector>
    );
    expect(inputElement).toBeChecked();
  });
});
