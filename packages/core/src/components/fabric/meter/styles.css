@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer components {
  .fds_meter-bar {
    width: 100%;
    position: relative;
    border-radius: theme('borderRadius.border-radius-round');
    border: 0.5px solid theme('colors.color-border-subtle');
    background-color: theme('colors.color-background-subtle');

    .fds_meter-value {
      height: 100%;
      border-radius: theme('borderRadius.border-radius-round');
      background-color: theme('colors.color-type-informational');
    }
    &.crossbrand {
      border: 0.5px solid theme('colors.cb-color-border-inactive');
      background-color: theme('colors.cb-color-surface-inactive');

      .meter-value {
        background-color: theme('colors.cb-color-fill-informational');
      }
    }
  }
}
