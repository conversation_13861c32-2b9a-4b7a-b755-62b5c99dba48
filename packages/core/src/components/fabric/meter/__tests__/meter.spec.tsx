import React, { useState as useStateMock } from 'react';
import { render, screen, act } from 'test-utils';
import { Meter } from '../Meter';

jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn(),
}));

describe('Meter component', () => {
  const percentage: number = 50;
  const mockSetState = jest.fn();

  beforeEach(() => {
    useStateMock.mockImplementation((percentage: number) => [percentage, mockSetState]);
  });

  it('renders properly with default props and values', () => {
    render(<Meter percentage={33} />);

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(meterBar).toHaveClass('fds_meter-bar');
    expect(meterBar).toHaveAttribute('aria-label', 'meter');
    expect(meterBar).toHaveStyle('height: 0.5rem; width: 25%;');
    expect(meterValue).toHaveClass('fds_meter-value');
    expect(meterValue).toHaveStyle('width: 33%;');
  });

  it('renders properly with max. 100%', () => {
    render(<Meter percentage={150} />);

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(meterBar).toHaveClass('fds_meter-bar');
    expect(meterBar).toHaveAttribute('aria-label', 'meter');
    expect(meterValue).toHaveClass('fds_meter-value');
    expect(meterValue).toHaveStyle('width: 100%;');
  });

  it('renders properly with custom height and width props', () => {
    jest.useFakeTimers();
    jest.spyOn(global, 'setTimeout');

    render(<Meter aria-label='Custom Meter' percentage={percentage} height={1.5} width={50} />);

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(setTimeout).not.toHaveBeenCalled();
    expect(meterBar).toHaveClass('fds_meter-bar');
    expect(meterBar).toHaveAttribute('aria-label', 'Custom Meter');
    expect(meterBar).toHaveStyle('height: 1.5rem; width: 50%;');
    expect(meterValue).toHaveClass('fds_meter-value');
    expect(meterValue).toHaveStyle('width: 50%;');
  });

  it('renders properly for cross brand', () => {
    render(<Meter percentage={percentage} isCrossBrand />);

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(meterBar).toHaveClass('fds_meter-bar crossbrand');
  });

  it('renders properly for cross brand with custom class', () => {
    render(<Meter className='custom-meter' percentage={percentage} isCrossBrand />);

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(meterBar).toHaveClass('fds_meter-bar crossbrand custom-meter');
  });

  it('renders properly for animation with default values', () => {
    jest.useFakeTimers();
    jest.spyOn(global, 'setTimeout');
    render(<Meter percentage={percentage} isAnimated />);
    act(() => {
      jest.runAllTimers();
    });

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(setTimeout).toHaveBeenCalled();
    expect(mockSetState).toHaveBeenCalledWith(10);

    jest.useRealTimers();
  });

  it('renders properly for animation with custom values', () => {
    jest.useFakeTimers();
    render(<Meter percentage={percentage} isAnimated animationStep={20} />);
    act(() => {
      jest.runAllTimers();
    });

    const meterBar = screen.getByTestId('meter-bar');
    const meterValue = screen.getByTestId('meter-value');

    expect(meterBar).toBeInTheDocument();
    expect(meterValue).toBeInTheDocument();
    expect(mockSetState).toHaveBeenCalledWith(20);

    jest.useRealTimers();
  });
});
