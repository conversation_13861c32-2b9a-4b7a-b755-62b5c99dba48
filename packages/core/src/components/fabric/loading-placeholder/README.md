# Loading Placeholder

The Loading Placeholder component is used to indicate an ongoing activity before the content could be loaded.

## Usage

The Loading Placeholder is a reusable Core UI component which can be used to indicate an ongoing activity.

### Required Props

- None

### Optional Props

The Loading Placeholder component's style and behavior can be controlled using the optional props below. If these props are not provided, the default values will be used.

| Name              | Type      | Default                    | Description                                                                                                                                                                                                                                               |
| ----------------- | --------- | -------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `isCrossBrand`    | boolean   | `false`                    | If true, Loading Placeholder component will identify the theme as Cross Brand.                                                                                                                                                                            |
| `ratio`           | object    | `{ xValue: 1, yValue: 1 }` | An object with `xValue` and `yValue` numeric values to determine the ratio of the bottom padding.                                                                                                                                                         |
| `fixedSize`       | object    | N/A                        | An object with `width` and `height` string values to determine the size of the container.                                                                                                                                                                 |
| `loadingComplete` | boolean   | `false`                    | A boolean value to indicate whether loading is complete or not.                                                                                                                                                                                           |
| `animationDelay`  | string    | N/A                        | A string value in the format `delay-{numeric value}`. This must be one of the preset values: `delay-0`, `delay-75`, `delay-100`, `delay-150`, `delay-200`, `delay-300`, `delay-500`, `delay-700`, `delay-1000`, `delay-2000`, `delay-3000`, `delay-5000`. |
| `children`        | ReactNode | N/A                        | The content to be displayed when the loading completes.                                                                                                                                                                                                   |
| `className`       | string    | `fds_loading-placeholder`  | This prop can be used to customize the Loading Placeholder component further by overriding the default styles. Base style is `fds_loading-placeholder` with tailwind animation utility classes.                                                           |

## Component Styles

The styles for the Loading Placeholder component are defined in the file `packages/core/src/components/fabric/loading-placeholder/styles.css`.

- The base classname for the Loading Placeholder is `fds_loading-placeholder` with tailwind animation utility classes.
- For CrossBrand, the classname is `fds_loading-placeholder crossbrand` with tailwind animation utility classes.

## Using the Component

#### For main themes

```jsx
import { LoadingPlaceholder } from './index';

<LoadingPlaceholder>
  <div>Loaded content here!</div>
</LoadingPlaceholder>;
```

#### With overridden prop values

```jsx
<LoadingPlaceholder className='custom-loading' ratio={{ xValye: 2, yValue: 5 }} fixedSize={{ width: '50px', height: '70px' }} animationDelay='delay-1000'>
  <div>Loaded content here!</div>
</LoadingPlaceholder>
```

#### When loading completes

```jsx
<LoadingPlaceholder loadingComplete>
  <div>Loaded content here!</div>
</LoadingPlaceholder>
```

#### For Cross Brand

```jsx
<LoadingPlaceholder isCrossBrand>
  <div>Loaded content here!</div>
</LoadingPlaceholder>
```

## Custom styling variables

#### For main themes

- gradient from & to - `color-surface-subtle`
- gradient via - `color-page-background-hazy`
- animation - Uses custom animation keyframe `load-placeholder` with these animation properties: `0.9s infinite ease-in-out alternate`
- animation delay - Passed as an input prop with tailwind preset delay value like `delay-500`. A few custom animation delays have been included: `delay-2000`, `delay-3000`, and `delay-5000`.

#### For Cross Brand

- gradient from & to - `cb-color-surface-subtle`
- gradient via - `cb-color-page-background-hazy`
