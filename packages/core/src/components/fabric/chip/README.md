# Chip Component

## Description

Represent pieces of information entered by a user. They take user input and verify that input by converting the selection into chips that can be viewed and removed.

## Structure & Behavior

- default: default state
- hover: shows a hover state and adds a border
- focus: when focused, adds a border and changes the background color
- active: when clicked, changes the background color and adds a border

## Variants

- Default - Applies for all brands with their respective styling.
- Crossbrand - Styled as a crossbrand chip when `isCrossbrand` is true.

## Props

The `Chip` component accepts the following props:
(In addition to the below props, the `Chip` component also accepts all props for the HTML button element.)

### Required Props

- `attributeName`: The name of the attribute displayed on the chip.

### Optional Props

| Name           | Type       | Default | Description                                                                           |
| -------------- | ---------- | ------- | ------------------------------------------------------------------------------------- |
| `swatchColor`  | `string`   | `null`  | The color of the swatch displayed on the chip. Can accept a color name or a hex value |
| `isCrossbrand` | `boolean`  | `false` | Determines if the chip should be styled as a crossbrand chip                          |
| `onClick`      | `function` | `null`  | Click event handler                                                                   |
| `id`           | `string`   | `null`  | Unique identifier for the chip                                                        |

## Component Styles

The styles for the `Chip` component are defined in the file `packages/core/src/components/fabric/chip/styles.css`.

## Usage

### Basic Example

```typescript
import React from 'react';
import { Chip } from '@ecom-next/core/components/fabric/chip';

const Example = () => {
  const handleClick = (event) => {
    console.log('Chip clicked', event);
  };

  return (
    <Chip
      attributeName = "Example Attribute"
      swatchColor = "blue"
      isCrossbrand = { true }
      onClick = { handleClick }
    />
  );
};

export default Example;
```
