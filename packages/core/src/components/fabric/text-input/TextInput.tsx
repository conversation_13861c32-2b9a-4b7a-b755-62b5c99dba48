import React, { FC, useCallback, useState } from 'react';
import classNames from 'classnames';
import { useControlled } from '@ecom-next/core/utility/useControlled';
import { TextInputProps } from './types';
import Icon from './components/Icon';

export const TextInput: FC<TextInputProps> = React.forwardRef((props, ref) => {
  const {
    'aria-label': ariaLabel,
    id,
    isCrossbrand = false,
    className = undefined,
    defaultHasError = false,
    defaultValue = '',
    disabled = false,
    errorMessage,
    hasError: hasErrorProp,
    supportingTxt,
    isExpanded: isExpandedProp,
    label,
    onBlur,
    onChange,
    onFocus,
    pattern = '',
    readOnly = false,
    required = true,
    type = 'text',
    value: valueProp,
    leftIcon = undefined,
    rightIcon = undefined,
    placeholder = '',
    fsTracking = 'exclude',
    styles,
    maxCharacters,
    isRounded = false,
    ...other
  } = props;
  const [value, setValue] = useControlled({
    controlled: valueProp,
    default: defaultValue,
  });

  const [charCount, setCharCount] = useState(value.length);

  const getLabelClass = (
    leftIcon: FC | undefined,
    rightIcon: FC | undefined,
    hasError: boolean,
    value: string,
    isExpanded: boolean,
    externalLabelStyles?: string,
    focus?: boolean
  ) => {
    if (externalLabelStyles) {
      return classNames(externalLabelStyles, {
        'uppercase ': hasError || value,
        'peer-focus:uppercase': !hasError && !value,
        'duration-300': !value,
      });
    }
    return classNames(
      'fds_text-input__span',
      {
        'fds_text-input__span--error': hasError,
        'fds_text-input__span--value': value,
        'px-1.5 start-1 ml-0': !leftIcon,
        'start-0 peer-focus:-translate-x-0 peer-focus:px-1.5 peer-focus:ml-0 peer-focus:start-3': leftIcon || rightIcon,
        'ml-[34px]': !value && leftIcon,
        'px-1.5 ml-0': value && leftIcon,
        'ml-0': value && !leftIcon,
        '-translate-y-3.5': value,
        'duration-300': !value,
      },
      'absolute transform translate-x-2 z-10 -translate-y-3.5 origin-[0] peer-focus:px-1.5 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 focus:start-3 peer-focus:-translate-y-3.5 rtl:peer-focus:translate-x-1/4 rtl:peer-focus:left-auto start-1 bg-white bottom-0 h-5'
    );
  };

  const getInputClass = (leftIcon: FC | undefined, rightIcon: FC | undefined, hasError: boolean, fsTracking: string, externalInputStyles?: string) => {
    if (externalInputStyles) {
      return classNames(
        externalInputStyles,
        'flex mt-[2.215rem] box-border p-[0.375rem_0] d-block w-full h-[2.25rem] border-b rounded-none border-solid bg-transparent bg-no-repeat bg-[size:0_0] bg-[position:center_bottom] ',
        'focus:outline-none focus:animate-moving-bar peer ',
        {
          'pl-2 border border-err1 bg-[linear-gradient(to_left,transparent_0%,#D00000_0%,#D00000_100%,transparent_100%)]': hasError,
          'border-cb-coreColor-emphasis bg-[linear-gradient(to_left,transparent_0%,rgb(51,51,51)_0%,rgb(51,51,51)_100%,transparent_100%)]': !hasError,
        }
      );
    }
    return classNames(
      'fds_text-input__input',
      {
        'fds_text-input__input--error': hasError,
        'p-2.5': leftIcon,
        'p-2.5 pr-10': rightIcon,
        'fs-mask': fsTracking === 'mask',
        'fs-exclude': fsTracking === 'exclude',
      },
      'peer start-3 block appearance-none focus:outline-none focus:ring-0 mb-1 '
    );
  };

  const [hasErrorState, setHasError] = useState(defaultHasError);
  const [focus, setFocus] = useState(false);

  const hasError = hasErrorProp ?? hasErrorState;
  const showError = Boolean(!disabled && hasError && errorMessage);
  const isExpanded = Boolean(isExpandedProp || value || showError || focus);

  const elementProps = {
    disabled,
    readOnly,
  };

  const externalLabelStyles = styles ? styles({ isExpanded }).externalLabelStyles : '';
  const externalInputStyles = styles ? styles({ isExpanded }).externalInputStyles : '';

  const isValid = useCallback((inputValue: string): boolean => Boolean(inputValue.match(pattern)), [pattern]);

  const handleChange = useCallback(
    (event: React.FocusEvent<HTMLInputElement, Element>) => {
      onChange?.(event);
      const { value } = event.target;
      setValue(value);
      setCharCount(value.length);
      setHasError(hasError && !isValid(value));
    },
    [onChange, hasError, isValid, setValue]
  );

  const handleBlur = useCallback(
    (event: React.FocusEvent<HTMLInputElement, Element>) => {
      onBlur?.(event);
      setHasError(!isValid(value));
      setFocus(false);
    },
    [onBlur, isValid, value]
  );

  const handleFocus = useCallback(
    (event: React.FocusEvent<HTMLInputElement, Element>) => {
      onFocus?.(event);
      setFocus(true);
    },
    [onFocus, setFocus]
  );

  const labelClass = getLabelClass(leftIcon, rightIcon, hasError, value, isExpanded, externalLabelStyles, focus);
  const inputClass = getInputClass(leftIcon, rightIcon, hasError, fsTracking, externalInputStyles);
  const msgContainerClass = classNames('fds_text-input__message-container', {
    cb: isCrossbrand,
    'fds_text-input__message-container--error': hasError,
    'fds_text-input__message-container--disabled': disabled,
  });
  let parentClass = classNames('fds_text-input', {
    cb: isCrossbrand,
    'fds_text-input--rounded': isRounded,
    'fds_text-input--error': hasError,
    'fds_text-input--focus': focus,
    'fds_text-input--value': value,
    'fds_text-input__left-icon': leftIcon,
    'fds_text-input__right-icon': rightIcon,
    'fds_text-input--disabled': disabled,
  });
  if (className) {
    parentClass = externalInputStyles ? className : `relative block h-11 ${className}`;
  }

  return (
    <>
      <label aria-label={ariaLabel} {...elementProps} className={parentClass}>
        <input
          id={id}
          {...other}
          {...elementProps}
          className={inputClass}
          aria-invalid={showError}
          onBlur={handleBlur}
          onChange={handleChange}
          onFocus={handleFocus}
          readOnly={readOnly}
          required={required}
          type={type}
          value={value}
          placeholder={placeholder}
          ref={ref}
          maxLength={maxCharacters ? maxCharacters : undefined}
        />
        {label && (
          <span {...elementProps} className={labelClass}>
            {label}
          </span>
        )}
        {leftIcon && <Icon icon={leftIcon} iconType='left' />}
        {rightIcon && <Icon icon={rightIcon} iconType='right' />}
      </label>
      <div className={msgContainerClass}>
        <span>
          {showError && (
            <div role='alert' className='fds_text-input__error-message'>
              {errorMessage}
            </div>
          )}
          {supportingTxt && <div className='fds_text-input__support-message'>{supportingTxt}</div>}
        </span>
        {maxCharacters && (
          <span className='fds_text-input__char-count'>
            {charCount}/{maxCharacters}
          </span>
        )}
      </div>
    </>
  );
});

TextInput.displayName = 'TextInput';
