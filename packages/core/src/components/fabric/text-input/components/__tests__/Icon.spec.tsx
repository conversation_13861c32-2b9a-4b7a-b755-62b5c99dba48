import React from 'react';
import { render } from 'test-utils';
import Icon from '../Icon';

describe('Icon Component', () => {
  it('renders icon on the left correctly', () => {
    const { getByTestId, getByText } = render(<Icon icon={() => '⭐'} iconType='left' />);
    const iconContainer = getByTestId('icon-container');
    expect(iconContainer).toBeInTheDocument();
    expect(getByText('⭐')).toBeInTheDocument();
    expect(iconContainer).toHaveClass('left-0');
  });

  it('renders icon on the right correctly', () => {
    const { getByTestId, getByText } = render(<Icon icon={() => '⭐'} iconType='right' />);
    const iconContainer = getByTestId('icon-container');
    expect(iconContainer).toBeInTheDocument();
    expect(getByText('⭐')).toBeInTheDocument();
    expect(iconContainer).toHaveClass('right-0');
  });

  it('renders without icon when not passed', () => {
    const { getByTestId } = render(<Icon icon={() => null} iconType='left' />);
    const iconContainer = getByTestId('icon-container');
    expect(iconContainer).toBeInTheDocument();
    expect(iconContainer?.querySelector('svg')).toBeNull();
  });
});
