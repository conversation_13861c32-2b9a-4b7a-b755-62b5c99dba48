.fds-content__cta-button-text {
  font-family: theme('fontFamily.brand-base');
  font-size: theme('fontSize.font-size--1');
  font-weight: theme('fontWeight.font-weight-base-heavier');
  letter-spacing: theme('letterSpacing.content-font-letter-spacing-0');
  outline: none;
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-underline-offset: 4px;

  &.fds-content__cta-button-text--dark {
    color: theme('colors.content-button-dark-fill');
    text-decoration-color: theme('colors.content-button-dark-fill');
  }

  &.fds-content__cta-button-text--dark:hover {
    color: theme('colors.content-button-dark-hover');
    text-decoration-color: theme('colors.content-button-dark-hover');
  }

  &.fds-content__cta-button-text--dark:active {
    color: theme('colors.content-button-dark-hover');
    text-decoration-color: theme('colors.content-button-dark-hover');
  }

  &.fds-content__cta-button-text--dark:focus {
    color: theme('colors.content-button-dark-hover');
    text-decoration-color: theme('colors.content-button-dark-hover');
    outline: 1px solid theme('colors.color-border-default');
  }

  &.fds-content__cta-button-text--light {
    color: theme('colors.content-button-light-fill');
    text-decoration-color: theme('colors.content-button-light-fill');
  }

  &.fds-content__cta-button-text--light:hover {
    color: theme('colors.content-button-light-hover');
    text-decoration-color: theme('colors.content-button-light-hover');
  }

  &.fds-content__cta-button-text--light:active {
    color: theme('colors.content-button-light-hover');
    text-decoration-color: theme('colors.content-button-light-hover');
  }

  &.fds-content__cta-button-text--light:focus {
    color: theme('colors.content-button-light-hover');
    text-decoration-color: theme('colors.content-button-light-hover');
    outline: 1px solid theme('colors.content-button-light-fill');
  }
}
