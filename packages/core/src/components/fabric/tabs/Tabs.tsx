'use client';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { TabsProps } from './types';
import classNames from 'classnames';

export const Tabs: React.FC<TabsProps> = ({ children = [], onTabSelect, className, isCrossbrand }: TabsProps) => {
  const activeTabRef = useRef<HTMLButtonElement>(null);
  const selectedTab = children.find(child => child.props.isSelected) || children[0];
  const initialTabLabel = selectedTab?.props?.label;
  const [activeTab, setActiveTab] = useState(initialTabLabel);
  const [isUserInteraction, setIsUserInteraction] = useState(false);

  useEffect(() => {
    isUserInteraction && activeTabRef && activeTabRef.current?.focus();
  }, [activeTab]);

  const handleClick = useCallback(
    (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, label: string) => {
      e.preventDefault();
      setActiveTab(label);
      setIsUserInteraction(true);
      onTabSelect && onTabSelect(label);
    },
    [onTabSelect]
  );

  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    if (event.key === 'ArrowRight' || event.key === 'ArrowLeft') {
      event.preventDefault();
      const currentActiveChildNumber = children.findIndex(child => child.props.label === activeTab);
      const totalTabs = children.length;
      let newActiveTabNumber;
      if (event.key === 'ArrowRight') {
        newActiveTabNumber = (currentActiveChildNumber + 1) % totalTabs;
      } else {
        newActiveTabNumber = currentActiveChildNumber === 0 ? totalTabs - 1 : currentActiveChildNumber - 1;
      }
      setActiveTab(children[newActiveTabNumber].props.label);
      setIsUserInteraction(true);
      onTabSelect && onTabSelect(children[newActiveTabNumber].props.label);
    }
  };

  const tabPanelId = `${activeTab}-panel-id`.replace(' ', '-').toLowerCase();

  const tabs = children.map(child => {
    const {
      props: { label },
    } = child;
    const btnClassName = classNames('fds_tabs__tab-button', {
      'fds_tabs__tab-button--active': activeTab === label,
    });
    return (
      <button
        role='tab'
        aria-selected={activeTab === label}
        aria-controls={activeTab === label ? tabPanelId : undefined}
        tabIndex={activeTab === label ? 0 : -1}
        ref={activeTab === label ? activeTabRef : null}
        onClick={e => handleClick(e, label)}
        onKeyDown={e => handleKeyDown(e)}
        key={label.replace(' ', '-')}
        className={btnClassName}
      >
        <span>{child.props.label}</span>
      </button>
    );
  });

  const tabContent = children.filter(child => child.props.label === activeTab);

  return (
    <div className={classNames(className, 'fds_tabs__container', { cb: isCrossbrand })}>
      <div className='fds_tabs__list-container' role='tablist'>
        {tabs}
      </div>
      <div id={tabPanelId} className='fds_tabs__tab-content-container' role='tabpanel' tabIndex={0}>
        {tabContent}
      </div>
    </div>
  );
};
