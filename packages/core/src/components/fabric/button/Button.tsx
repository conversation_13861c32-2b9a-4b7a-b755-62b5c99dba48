import React, { forwardRef, useId } from 'react';
import LoadingAnimation from './components/loading-animation';
import { ButtonProps } from './types';
import classNames from 'classnames';

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { kind = 'primary', isDisabled = false, isLoading = false, isCaution = false, isCrossbrand = false, className = '', cautionMessage, children, ...others },
    ref
  ) => {
    const buttonClass = classNames('fds_button', `fds_button--${kind}`, { loading: isLoading, caution: isCaution, cb: isCrossbrand }, className);
    const isCautionState = kind === 'critical' && isCaution;
    const cautionMessageId = useId();
    const labelId = useId();

    return (
      <button
        ref={ref}
        className={buttonClass}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        aria-busy={isLoading}
        aria-live='polite'
        aria-describedby={isCautionState ? cautionMessageId : undefined}
        aria-labelledby={labelId}
        {...others}
      >
        {isLoading ? (
          <LoadingAnimation />
        ) : (
          <span id={labelId} className='fds_button__label'>
            {children}
          </span>
        )}
        {isCautionState && (
          <span className='sr-only' id={cautionMessageId}>
            {!!cautionMessage ? cautionMessage : 'This button action is currently unavailable'}
          </span>
        )}
      </button>
    );
  }
);

export { Button };
