import React from 'react';

export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  // Defines the style of the button, can be 'primary' or 'secondary'
  kind?: 'primary' | 'secondary' | 'critical' | 'group';

  // Indicates if the button is disabled
  isDisabled?: boolean;

  // Indicates if the button is in a loading state
  isLoading?: boolean;

  // Indicates if the button is in an caution state. Only available functionality for critical buttons
  isCaution?: boolean;

  // Indicates if the button is for cross-brand usage
  isCrossbrand?: boolean;

  // Provides screen reader accessible message for caution state if applicable
  cautionMessage?: string;
};
