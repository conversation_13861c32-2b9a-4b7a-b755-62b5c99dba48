import React from 'react';
import { render, screen } from 'test-utils';
import { Link } from '@ecom-next/core/components/fabric/link';

describe('LinkWrapper', () => {
  it('renders the Link with default props', () => {
    render(
      <Link href='/test' isNativeLink={false}>
        Test Link
      </Link>
    );
    const linkElement = screen.getByText('Test Link');
    expect(linkElement).toBeInTheDocument();
    expect(linkElement).toHaveClass('fds_link fds_link--inline');
  });

  it('renders the Link as a native <a> tag when isNativeLink is true', () => {
    render(
      <Link href='https://example.com' isNativeLink>
        Test Native Link
      </Link>
    );
    const linkElement = screen.getByText('Test Native Link');

    expect(linkElement).toBeInTheDocument();
    expect(linkElement.tagName).toBe('A');
    expect(linkElement).toHaveAttribute('href', 'https://example.com');
  });

  it('supports all native link attributes when isNativeLink is true', () => {
    render(
      <Link href='https://example.com' isNativeLink target='_blank' rel='noopener noreferrer' className='custom-class'>
        Test Native Link
      </Link>
    );
    const linkElement = screen.getByText('Test Native Link');

    expect(linkElement).toHaveAttribute('href', 'https://example.com');
    expect(linkElement).toHaveAttribute('target', '_blank');
    expect(linkElement).toHaveAttribute('rel', 'noopener noreferrer');
    expect(linkElement).toHaveClass('custom-class');
  });

  it('applies the correct class for kind prop', () => {
    render(
      <Link href='/test' kind='subtle' isNativeLink={false}>
        Test Link
      </Link>
    );
    const linkElement = screen.getByText('Test Link');
    expect(linkElement).toHaveClass('fds_link--subtle');
  });

  it('applies the crossbrand class when isCrossbrand is true', () => {
    render(
      <Link href='/test' isCrossbrand isNativeLink={false}>
        Test Link
      </Link>
    );
    const linkElement = screen.getByText('Test Link');
    expect(linkElement).toHaveClass('cb');
  });

  it('applies additional class names', () => {
    render(
      <Link href='/test' className='custom-class' isNativeLink={false}>
        Test Link
      </Link>
    );
    const linkElement = screen.getByText('Test Link');
    expect(linkElement).toHaveClass('custom-class');
  });

  it('should apply the disable visited style class when disableVisitedStyle is set to true', () => {
    render(
      <Link href='/test' kind='subtle' isNativeLink={false} disableVisitedStyle>
        Test Link
      </Link>
    );
    const linkElement = screen.getByText('Test Link');
    expect(linkElement).toHaveClass('fds_link--disable-visited-style');
  });
});
