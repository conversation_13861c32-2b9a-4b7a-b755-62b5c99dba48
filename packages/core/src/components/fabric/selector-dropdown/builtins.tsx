import type { ValueType } from './types';

export type DropdownAttributes = {
  value?: ValueType;
  label?: ValueType;
  ariaLabel?: ValueType;
  ariaSelected?: boolean;
  quantity?: number;
  disabled?: boolean;
  selected?: boolean;
};

export type OptionType = DropdownAttributes | ValueType;

export type OptionTypeGet = (option: OptionType) => ValueType | undefined;

export const getOptionLabel = (option: OptionType): ValueType | undefined => {
  if (typeof option === 'string' || typeof option === 'number') {
    return option;
  }
  if (option && typeof option === 'object') {
    if (option.label) {
      if (option.quantity) {
        return `${option.label} (${option.quantity})`;
      }
      return option.label;
    }
    if (option.value) {
      return option.value;
    }
    if (option.ariaLabel) {
      return option.ariaLabel;
    }
  }
  return option as ValueType;
};

export const getOptionValue = (option: OptionType): ValueType | undefined => {
  if (typeof option === 'string' || typeof option === 'number') {
    return option;
  }
  if (option && typeof option === 'object') {
    if (option.label) {
      if (option.quantity) {
        return `${option.label} (${option.quantity})`;
      }
      return option.label;
    }
    if (option.value) {
      return option.value;
    }
    if (option.ariaLabel) {
      return option.ariaLabel;
    }
  }
  return option as ValueType;
};

export const getOptionAriaLabel = (option: OptionType): ValueType | undefined => {
  if (typeof option === 'string' || typeof option === 'number') {
    return option;
  }
  if (option && typeof option === 'object' && option.ariaLabel) {
    return option.ariaLabel as ValueType;
  }
  return undefined;
};

export const optionKeyGenerator = (option: OptionType, index: number): string => `${option}__${index}`;
