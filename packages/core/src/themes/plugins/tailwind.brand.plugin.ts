import { Brand } from '@ecom-next/utils/server';
import plugin from 'tailwindcss/plugin';

export const tailwindBrandPlugin = (brand: Brand) =>
  plugin(({ addVariant }) => {
    addVariant(brand, `[data-brand="${brand}"] &`);
    // TODO: remove this following variant. Is for testing only.
    addVariant('hocus', ['&:hover', '&:focus']);
  });

const animationUtilities = {
  '.animate-custom-delay-0': {
    animationDelay: '0ms',
  },
  '.animate-custom-delay-250': {
    animationDelay: '250ms',
  },
  '.animate-custom-delay-500': {
    animationDelay: '500ms',
  },

  '.grid-rows-1': {
    gridTemplateRows: '1fr',
  },
  '.grid-rows-0': {
    gridTemplateRows: '0fr',
  },
  '.transition-grid': {
    transitionProperty: 'grid-template-rows',
  },
  '.duration-200': {
    transitionDuration: '200ms',
  },

  '.loading-animation': {
    animation: '1.8s infinite ease-in-out loadingkeyframes',
  },

  '@keyframes loadingkeyframes': {
    '0%': { border: '5px solid white' },
    '50%': { border: '28px solid #f2f2f2' },
    '100%': {
      border: '5px solid white',
    },
  },

  '.fade-in': {
    animation: '0.1s 1 ease-in-out fadeinkeyframes',
  },

  '@keyframes fadeinkeyframes': {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' },
  },

  '.fade-out': {
    animation: '1s 1 ease-in-out fadeoutkeyframes',
  },

  '@keyframes fadeoutkeyframes': {
    '100%': { opacity: '1' },
    '0%': { opacity: '0' },
  },
};

export const tailwindAnimationDelayPlugin = () =>
  plugin(({ addUtilities }) => {
    addUtilities(animationUtilities);
  });

export const searchInputPlugin = plugin.withOptions(() => {
  return function injectCssVariables({ addBase }) {
    addBase({
      '[type="search"]::-webkit-search-decoration': { display: 'none' },
      '[type="search"]::-webkit-search-results-button': { display: 'none' },
      '[type="search"]::-webkit-search-results-decoration': { display: 'none' },
    });
  };
});
