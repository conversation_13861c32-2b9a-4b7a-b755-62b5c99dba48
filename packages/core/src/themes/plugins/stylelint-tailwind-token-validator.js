const _ = require('lodash');
const stylelint = require('stylelint');
const atSemantic = require('../tools/figma/brand/semantic/figma/at.figma.extract.json');
const cbSemantic = require('../tools/figma/brand/semantic/figma/cb.figma.extract.json');
const atSemanticV1 = require('../tools/figma/brand/brand-toolkit/figma/at.figma.v1.json');
const globalSemanticV1 = require('../tools/figma/brand/brand-toolkit/figma/toolkit.figma.v1.json');

const flattenObjectKeys = obj => {
  return _.flatMap(_.keys(obj), key => {
    if (_.isObject(obj[key])) {
      return _.map(_.keys(obj[key]), subKey => `${key}.${subKey}`);
    }
    return key;
  });
};

const generateInvalidTokens = () => {
  const v0SemanticVariables = [...flattenObjectKeys(atSemantic), ...flattenObjectKeys(cbSemantic), 'fontFamily.brand', 'fontFamily.alt'];
  const v1SemanticVariables = [...flattenObjectKeys(atSemanticV1), ...flattenObjectKeys(globalSemanticV1)];
  return _.difference(v0SemanticVariables, v1SemanticVariables);
};

const invalidTokens = generateInvalidTokens();

const ruleName = 'tailwind-token-validator/no-invalid-theme-token';
const messages = stylelint.utils.ruleMessages(ruleName, {
  deprecatedToken: token => `Deprecated V0 Tailwind token "${token}". Only new V1 theme tokens are allowed.`,
});
const tailwindTokenValidator = () => {
  return (root, result) => {
    root.walkDecls(decl => {
      if (decl.value.includes('theme(')) {
        const tokenMatch = decl.value.match(/theme\(['"]([^'"]+)['"]\)/);
        if (tokenMatch) {
          const token = tokenMatch[1];
          if (invalidTokens.includes(token)) {
            stylelint.utils.report({
              message: messages.deprecatedToken(token),
              node: decl,
              result,
              ruleName,
            });
          }
        }
      }
    });
  };
};

module.exports = stylelint.createPlugin(ruleName, tailwindTokenValidator);
module.exports.ruleName = ruleName;
module.exports.messages = messages;
