import fs from 'node:fs';
import getCustomVariables from './get-custom-figma-variables.mjs';
import _ from 'lodash';
import findCommonVariableNames from './find-common-variable-names.mjs';
import { displayOverWrittenVariables, sortObject } from './figma-util.mjs';

export const mergeCustomVariablesWithFigmaGenerated = async () => {
  const figmaFileFolder = `${process.env.PWD}/src/themes/tools/figma/brand/semantic/figma`;
  const outputDir = `${process.env.PWD}/src/themes/tools/figma/brand/semantic/merged`;
  const fileNameMap = {
    br: 'br.figma.extract.json',
    at: 'at.figma.extract.json',
    gap: 'gap.figma.extract.json',
    on: 'on.figma.extract.json',
    cb: 'cb.figma.extract.json',
  };
  const outputFileNameMap = {
    br: 'br.figma.merged.json',
    at: 'at.figma.merged.json',
    gap: 'gap.figma.merged.json',
    on: 'on.figma.merged.json',
    cb: 'cb.figma.merged.json',
  };

  const customVariables = getCustomVariables();
  let overWrittenVariables = [];
  await Promise.all(
    Object.entries(fileNameMap).map(async ([brand, fileName]) => {
      const figmaExtractFilePath = `${figmaFileFolder}/${fileName}`;
      const outputWriteFilePath = `${outputDir}/${outputFileNameMap[brand]}`;
      if (fs.existsSync(figmaExtractFilePath)) {
        try {
          const figmaExtractContent = await fs.promises.readFile(figmaExtractFilePath, 'utf-8');
          const figmaExtractContentJSON = JSON.parse(figmaExtractContent);
          const customComponentVariablesContent = customVariables[brand];
          overWrittenVariables = [...overWrittenVariables, ...findCommonVariableNames(figmaExtractContentJSON, customComponentVariablesContent)];
          const mergedValues = _.merge(customComponentVariablesContent, figmaExtractContentJSON);
          const sortedMergedValues = sortObject(mergedValues);
          await fs.promises.writeFile(outputWriteFilePath, JSON.stringify(sortedMergedValues, null, 2));
        } catch (error) {
          console.error(`Error processing files for ${brand}:`, error);
        }
      } else {
        console.error(`File not found: ${figmaExtractFilePath}`);
      }
    })
  );
  displayOverWrittenVariables(overWrittenVariables);
};

if (process.argv[2] === 'runLocalMergeOnly') {
  mergeCustomVariablesWithFigmaGenerated().catch(console.error);
}
