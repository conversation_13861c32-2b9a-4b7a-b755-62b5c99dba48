import fs from 'node:fs';
import fsExtra from 'fs-extra';
import * as figmaUtil from './figma-util.mjs';

export const parseFoundationalVariables = ({ fabricSuperSetName, figmaData, outputDir }) => {
  const mainBrandCodes = ['at', 'br', 'gap', 'on'];
  const fileNameMap = {
    'Banana Republic': 'br.foundational.v1.css',
    Athleta: 'at.foundational.v1.css',
    Gap: 'gap.foundational.v1.css',
    'Old Navy': 'on.foundational.v1.css',
    Crossbrand: 'cb.foundational.v1.css',
    Global: 'global.foundational.v1.css',
  };
  const exclusionStringProps = ['text-align'];

  try {
    const fabricSuperSetNode = figmaUtil.getFabricSuperSetNode(fabricSuperSetName, figmaData.meta.variableCollections);

    if (!fabricSuperSetNode) {
      throw new Error(`Fabric SuperSet '${fabricSuperSetName}' not found!`);
    }

    const foundationalModeId = fabricSuperSetNode?.defaultModeId;
    const variablesList = Object.values(figmaData.meta.variables);
    const foundationalVariables = variablesList.reduce((memo, variable) => {
      const valuesByModes = Object.keys(variable.valuesByMode);

      if (valuesByModes.includes(foundationalModeId) && !variable.name?.toLowerCase().includes('nonbranded') && !variable.deletedButReferenced) {
        memo.push({
          name: variable.name,
          type: variable.resolvedType,
          value: variable.valuesByMode[foundationalModeId],
        });
      }

      return memo;
    }, []);

    const getVariableRef = id => {
      const variableItem = figmaData.meta.variables[id];

      if (!variableItem) {
        return undefined;
      }

      const variableItemModeValue = variableItem.valuesByMode[fabricSuperSetNode?.defaultModeId];

      if (variableItemModeValue?.type === 'VARIABLE_ALIAS') {
        return getVariableRef(variableItemModeValue?.value?.id);
      }

      return variableItemModeValue;
    };

    const sortedFoundationalVariables = figmaUtil.sortObjectArray(foundationalVariables, ['name']);

    const brandFoundationalVariablesMap = sortedFoundationalVariables.reduce((memo, foundationalVariable) => {
      const rawFoundationalVariableName = foundationalVariable.name;
      const brandName = figmaUtil.getFullBrandName(rawFoundationalVariableName);
      const itemNames = foundationalVariable.name?.split('/');
      const keyName = figmaUtil.formatVariableName(itemNames);
      let keyValue = foundationalVariable.value;

      if (figmaUtil.isVariableAlias(foundationalVariable)) {
        keyValue = getVariableRef(foundationalVariable?.value?.id);
      }

      if (figmaUtil.isVariableAColor(foundationalVariable)) {
        keyValue = figmaUtil.constructColor(keyValue);
      }

      if (figmaUtil.isVariableANumber(foundationalVariable)) {
        keyValue = figmaUtil.constructFloat(keyValue, !keyName.includes('font-weight') ? 'px' : '');
      }

      if (figmaUtil.isVariableAString(foundationalVariable) && !exclusionStringProps.find(prop => keyName.includes(prop))) {
        keyValue = keyName.includes('font-family') ? `'${keyValue.replaceAll("'", '').concat(' v1')}'` : `'${keyValue}'`;
      }

      // TODO: Remove this when the condensed font family name is created in Figma
      if (keyName === 'font-family-brand-old-navy-4') {
        keyValue = `'ON Sans Display Condensed v1'`;
      }

      (memo[brandName] || (memo[brandName] = [])).push({
        propertyName: keyName,
        propertyValue: keyValue,
      });
      return memo;
    }, {});

    fsExtra.emptyDirSync(outputDir);
    Object.keys(brandFoundationalVariablesMap).map(brandKey => {
      const brandFileName = fileNameMap[brandKey];
      if (brandFileName) {
        const brandCode = brandFileName.split('.').shift();
        let str = mainBrandCodes.includes(brandCode) ? `:root {\r\n\t--brand-name: ${brandCode};\r\n` : ':root {\r\n';

        const brandItems = brandFoundationalVariablesMap[brandKey];
        brandItems.map(brandItem => {
          str += `\t--${brandItem.propertyName}: ${brandItem.propertyValue};\r\n`;
        });
        const fileDestination = `${outputDir}/${brandFileName}`;
        str += '}';
        fs.writeFileSync(fileDestination, str);
      }
    });
  } catch (error) {
    throw new Error('Parsing foundational variables failed - ' + error.message);
  }
};
