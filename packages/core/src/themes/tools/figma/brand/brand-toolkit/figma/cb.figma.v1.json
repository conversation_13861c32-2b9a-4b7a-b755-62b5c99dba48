{"borderRadius": {"cb-border-radius-soft": "var(--border-radius-050)", "cb-content-button-border-radius": "var(--border-radius-000)", "cb-style-button-border-radius": "var(--border-radius-000)", "cb-style-button-floating-action-border-radius": "var(--border-radius-050)", "cb-style-modal-border-radius": "var(--border-radius-125)", "cb-style-product-image-thumbnail-border-radius": "var(--border-radius-000)", "cb-style-selector-swatch-border-radius": "var(--border-radius-9999)", "cb-style-seo-crosslink-border-radius-default": "var(--border-radius-9999)"}, "borderWidth": {"cb-style-button-hover-border-width": "var(--border-width-10)", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-lower-border-width": "var(--border-width-10)", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-upper-border-width": "var(--border-width-0)", "cb-style-selector-size-border-border-width": "var(--border-width-10)", "cb-style-selector-size-border-selected---border-width": "var(--border-width-10)", "cb-style-selector-size-strikethrough-border-width": "var(--border-width-10)", "cb-style-selector-swatch-border-width-focus": "var(--border-width-20)", "cb-style-selector-swatch-border-width-selected": "var(--border-width-10)", "cb-style-tabs-border-width": "var(--border-width-20)", "cb-style-tabs-selected-border-width": "var(--border-width-20)"}, "colors": {"cb-color-background-accent": "rgba(var(--color-black-and-white-black))", "cb-color-background-disabled": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-background-subtle": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-background-transparent---dark": "rgba(var(--color-black-and-white-black-30))", "cb-color-background-transparent---light": "rgba(var(--color-black-and-white-white-30))", "cb-color-border-accent": "rgba(var(--color-black-and-white-black))", "cb-color-border-default": "rgba(var(--color-gray-600))", "cb-color-border-disabled": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-border-info": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-border-subtle": "rgba(var(--color-gray-600))", "cb-color-fill-accent": "rgba(var(--color-brands-crossbrand-brand-2))", "cb-color-fill-action": "rgba(var(--color-black-and-white-black))", "cb-color-fill-dark": "rgba(var(--color-black-and-white-black))", "cb-color-fill-disabled": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-fill-subtle": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-gray-1": "rgba(var(--color-brands-crossbrand-gray-1))", "cb-color-gray-2": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-color-gray-3": "rgba(var(--color-gray-600))", "cb-color-gray-4": "rgba(var(--color-gray-1000))", "cb-color-gray-5": "rgba(var(--color-gray-1200))", "cb-color-icon-disabled": "rgba(var(--color-gray-800))", "cb-color-icon-subtle": "rgba(var(--color-gray-1000))", "cb-color-type-accent": "rgba(var(--color-black-and-white-black))", "cb-color-type-disabled": "rgba(var(--color-gray-800))", "cb-color-type-link": "rgba(var(--color-black-and-white-black))", "cb-color-type-sale": "rgba(var(--color-red-200))", "cb-color-type-subtle": "rgba(var(--color-gray-1000))", "cb-color-type-visited": "rgba(var(--color-black-and-white-black))", "cb-content-button-dark-fill": "rgba(var(--color-black-and-white-black))", "cb-content-button-dark-hover": "rgba(var(--color-gray-1000))", "cb-content-button-light-fill": "rgba(var(--color-black-and-white-white))", "cb-content-button-light-hover": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-content-color-border-default": "rgba(var(--color-black-and-white-black))", "cb-content-color-fill-dark": "rgba(var(--color-black-and-white-black))", "cb-content-color-fill-light": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-content-color-type-accent": "rgba(var(--color-black-and-white-black))", "cb-style-accordion-default": "rgba(var(--color-gray-600))", "cb-style-accordion-subtle": "rgba(var(--color-gray-600))", "cb-style-badge-fill-color": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-badge-font-color": "rgba(var(--color-black-and-white-black))", "cb-style-brand-fill": "rgba(var(--color-black-and-white-black))", "cb-style-button-critical-caution-fill": "rgba(var(--color-gray-1200))", "cb-style-button-critical-hover-fill": "rgba(var(--color-brands-crossbrand-brand-1))", "cb-style-button-primary-fill": "rgba(var(--color-black-and-white-black))", "cb-style-button-primary-font": "rgba(var(--color-black-and-white-white))", "cb-style-button-primary-hover-fill": "rgba(var(--color-gray-1200))", "cb-style-button-primary-icon": "rgba(var(--color-black-and-white-white))", "cb-style-button-primary-pressed-fill": "rgba(var(--color-gray-1200))", "cb-style-button-primary-pressed-font": "rgba(var(--color-black-and-white-white))", "cb-style-button-secondary-border": "rgba(var(--color-black-and-white-black))", "cb-style-button-secondary-hover-border": "rgba(var(--color-black-and-white-black))", "cb-style-button-secondary-hover-fill": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-button-secondary-hover-font": "rgba(var(--color-black-and-white-black))", "cb-style-button-secondary-hover-icon": "rgba(var(--color-black-and-white-black))", "cb-style-chips-active": "rgba(var(--color-black-and-white-white))", "cb-style-chips-default": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-chips-focused": "rgba(var(--color-gray-500))", "cb-style-dynamic-placeholder-fill": "rgba(var(--color-non-branded-nb-1))", "cb-style-footer-background": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-account-menu-font-color": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-account-menu-icon-color": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-bag-desktop-font-color": "rgba(var(--color-black-and-white-black))", "cb-style-global-header-bag-desktop-icon-color": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-bag-mobile-fill-color": "rgba(var(--color-red-200))", "cb-style-global-header-everyday-free-shipping-(edfs)-desktop-font-color": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-background-color": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-border-color": "rgba(var(--color-gray-200))", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-font-color": "rgba(var(--color-black-and-white-black))", "cb-style-global-header-sister-brand-bar-default-background-color": "rgba(var(--color-black-and-white-black))", "cb-style-global-header-sister-brand-bar-default-font-color": "rgba(var(--color-gray-600))", "cb-style-global-header-sister-brand-bar-hover-background-color": "rgba(var(--color-black-and-white-black))", "cb-style-global-header-sister-brand-bar-hover-font-color": "rgba(var(--color-black-and-white-white))", "cb-style-global-header-sister-brand-bar-selected-font-color": "rgba(var(--color-black-and-white-white))", "cb-style-modal-background": "rgba(var(--color-black-and-white-white))", "cb-style-modal-header": "rgba(var(--color-black-and-white-black))", "cb-style-product-card-marketing-flag": "rgba(var(--color-black-and-white-black))", "cb-style-product-price-%-off-font-color": "rgba(var(--color-gray-1400))", "cb-style-product-price-strikethrough": "rgba(var(--color-gray-1000))", "cb-style-product-style-flag-font-color": "rgba(var(--color-black-and-white-black))", "cb-style-product-style-flag-font-color-2": "rgba(var(--color-gray-1000))", "cb-style-selector-fulfillment-border-default": "rgba(var(--color-gray-600))", "cb-style-selector-fulfillment-border-selected": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-available-background-focus": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-selector-size-available-background-hover": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-selector-size-available-background-selected": "rgba(var(--color-gray-1400))", "cb-style-selector-size-available-default": "rgba(var(--color-black-and-white-white))", "cb-style-selector-size-available-default-(hover-available)": "rgba(var(--color-gray-1400))", "cb-style-selector-size-border-default": "rgba(var(--color-gray-1000))", "cb-style-selector-size-border-disable": "rgba(var(--color-gray-800))", "cb-style-selector-size-border-hover": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-border-hover-unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-border-selected": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-border-selected-unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-font-default": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-font-disable": "rgba(var(--color-gray-800))", "cb-style-selector-size-font-hover": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-font-hover--unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-font-link": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-font-selected": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-font-selected---unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-strikethrough-default---unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-strikethrough-hover---unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-strikethrough-selected---unavailable": "rgba(var(--color-black-and-white-black))", "cb-style-selector-size-unavailable-background-default": "rgba(var(--color-black-and-white-white))", "cb-style-selector-size-unavailable-background-disable": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-selector-size-unavailable-background-hover": "rgba(var(--color-brands-crossbrand-gray-2))", "cb-style-selector-size-unavailable-background-selected": "rgba(var(--color-gray-800))", "cb-style-selector-swatch-border-color": "rgba(var(--color-gray-600))", "cb-style-seo-crosslink-fill-default": "rgba(var(--color-brands-crossbrand-gray-1))", "cb-style-seo-crosslink-fill-hover": "rgba(var(--color-gray-1000))", "cb-style-seo-crosslink-fill-press": "rgba(var(--color-gray-1000))", "cb-style-tabs-border-color": "rgba(var(--color-gray-600))", "cb-style-tabs-font-color": "rgba(var(--color-gray-1000))"}, "fontFamily": {"cb-content-font-family-display": "var(--font-family-brand-crossbrand)", "cb-content-font-family-display-alt": "var(--font-family-brand-crossbrand)", "cb-font-family-base": "var(--font-family-brand-crossbrand)", "cb-font-family-display": "var(--font-family-brand-crossbrand)", "cb-style-mega-nav-font-family": "var(--font-family-brand-crossbrand)"}, "fontSize": {"cb-font-size--1": "var(--font-size-14)", "cb-font-size--2": "var(--font-size-12)", "cb-font-size-0": "var(--font-size-14)", "cb-font-size-1": "var(--font-size-15)", "cb-font-size-2": "var(--font-size-16)", "cb-font-size-3": "var(--font-size-18)", "cb-font-size-4": "var(--font-size-24)", "cb-font-size-5": "var(--font-size-40)", "cb-font-size-6": "var(--font-size-60)", "cb-font-size-7": "var(--font-size-90)", "cb-font-size-8": "var(--font-size-120)", "cb-style-alt-font-size--2": "var(--font-size-12)", "cb-style-count-font-size": "var(--font-size-12)", "cb-style-global-header-bag-mobile-font-size": "var(--font-size-10)", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-font-size": "var(--font-size-12)", "cb-style-global-header-sister-brand-bar-mobile-font-size": "var(--font-size-14)", "cb-style-mega-nav-font-size": "var(--font-size-16)", "cb-style-product-card-font-size": "var(--font-size-14)", "cb-style-product-price-purchase-price-font-size": "var(--font-size-15)", "cb-style-product-price-small-font-size": "var(--font-size-14)", "cb-style-selector-size-font-font-size": "var(--font-size-14)", "cb-style-text-title-small-font-size": "var(--font-size-15)"}, "fontWeight": {"cb-content-font-weight-display-alt-bold": "Bold", "cb-content-font-weight-display-default": "var(--font-weight-400)", "cb-font-weight-base-default": "var(--font-weight-400)", "cb-font-weight-base-default1": "var(--font-weight-regular)", "cb-font-weight-base-heavier": "var(--font-weight-700)", "cb-font-weight-base-heavier1": "var(--font-weight-medium)", "cb-font-weight-display-default": "var(--font-weight-400)", "cb-font-weight-display-heavier": "var(--font-weight-700)", "cb-style-badge-font-weight": "var(--font-weight-400)", "cb-style-global-header-account-menu-menu-items-font-weight": "var(--font-weight-400)", "cb-style-global-header-account-menu-rewards-font-weight": "var(--font-weight-400)", "cb-style-global-header-everyday-free-shipping-(edfs)-desktop-font-weight": "var(--font-weight-400)", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-font-weight": "var(--font-weight-400)", "cb-style-global-header-sister-brand-bar-default-font-weight": "var(--font-weight-400)", "cb-style-global-header-sister-brand-bar-selected-font-weight": "var(--font-weight-700)", "cb-style-mega-nav-font-weight": "var(--font-weight-400)", "cb-style-product-price-%-off-font-weight": "var(--font-weight-700)", "cb-style-product-price-purchase-price-font-weight": "var(--font-weight-700)"}, "gap": {"cb-style-global-header-sister-brand-bar-display-logic-gap": true, "cb-style-global-header-sister-brand-bar-display-logic-gap-link-state": "<PERSON><PERSON><PERSON>"}, "letterSpacing": {"cb-content-font-letter-spacing-0": "var(--font-letter-spacing-0)", "cb-content-font-letter-spacing-1%": "var(--font-letter-spacing-3)", "cb-font-letter-spacing-base": "var(--font-letter-spacing-0)", "cb-font-letter-spacing-display": "var(--font-letter-spacing-0)"}, "maxHeight": {"cb-style-global-header-everyday-free-shipping-(edfs)-mobile-max-height": "var(--spacing-750)"}, "minHeight": {"cb-style-button-min-height": "var(--spacing-550)", "cb-style-global-header-everyday-free-shipping-(edfs)-mobile-min-height": "var(--spacing-500)"}, "order": {"cb-style-global-header-account-menu-menu-items-orders-and-returns": "Orders & Returns"}, "padding": {"cb-content-button-padding-horizontal": "var(--spacing-300)", "cb-content-button-padding-vertical": "var(--spacing-200)", "cb-style-breadcrumb-horizontal-padding": "var(--spacing-0)", "cb-style-button-padding-horizontal": "var(--spacing-200)", "cb-style-mega-nav-top-padding": "var(--spacing-200)", "cb-style-tabs-padding-horizontal": "var(--spacing-200)", "cb-style-tabs-padding-vertical": "var(--spacing-100)"}, "spacing": {"cb-spacing-l": "var(--spacing-300)", "cb-spacing-m": "var(--spacing-200)", "cb-spacing-s": "var(--spacing-100)", "cb-spacing-xs": "var(--spacing-050)", "cb-style-accordion-spacing": "var(--font-size-24)", "cb-style-badge-spacing": "var(--spacing-050)"}, "textAlign": {"cb-style-page-title-text-align-horizontal-horizontal": "var(--text-align-horizontal-left)"}}