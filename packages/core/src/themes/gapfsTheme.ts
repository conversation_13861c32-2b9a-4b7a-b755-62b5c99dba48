import _ from 'lodash';
import gapFigmaSemantic from './tools/figma/brand/semantic/merged/gap.figma.merged.json';
import toolkitFigmaV1 from './tools/figma/brand/brand-toolkit/figma/toolkit.figma.v1.json';
import gapFigmaV1 from './tools/figma/brand/brand-toolkit/figma/gap.figma.v1.json';
import breakpoints from './breakpoints.json';

const clonedGapFigmaSemantic = _.cloneDeep(gapFigmaSemantic);
const gapMergedVariables = _.merge(clonedGapFigmaSemantic, toolkitFigmaV1, gapFigmaV1);
const { colors: gapFsFigmaColors } = gapMergedVariables;
const otherProperties = _.omit(gapMergedVariables, ['colors', 'fontFamily', 'family', 'display']);

export const legacyColors = {
  b1: '#2B2B2B',
  b2: '#2B2B2B',
  b3: '#2B2B2B',
  g1: '#333',
  g2: '#757575',
  g3: '#CCCCCC',
  g4: '#DFDFDF',
  g5: '#E9E9E9',
  wh: '#FFFFFF',
  bk: '#000000',
  r1: '#D00000',
  err1: '#D00000',
  inf: '#FF7807',
  valueDrawer: {
    blu1: '#003764',
    grn1: '#3AB200',
    ylw1: '#DBA61D',
  },
  inverse: {
    b1: '#FFFFFF',
    b2: '#CCCCCC',
    g1: '#555555',
    g2: '#999999',
  },
};

export const colors = {
  ...legacyColors,
  ...gapFsFigmaColors,
};

export const brandFont = 'var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif';
export const brandBaseFont = 'var(--font-family-brand-gap),Helvetica,Arial,Roboto,sans-serif';
export const crossBrandFont = ['var(--font-lato)', 'Helvetica', 'Arial', 'sans-serif'].join(',');

export const contentDisplayFont = ['var(--font-family-brand-gap)', 'sans-serif'].join(',');
export const contentDisplayFontAlt = ['var(--font-family-brand-gap)', 'sans-serif'].join(',');

export const theme = {
  extend: {
    fontFamily: {
      brand: brandFont,
      alt: brandFont,
      'brand-base': brandBaseFont,
      'brand-display': brandBaseFont,
      'content-font-family-display': contentDisplayFont,
      'content-font-family-display-alt': contentDisplayFontAlt,
      crossbrand: crossBrandFont,
      ...gapFigmaSemantic.fontFamily,
    },
    screens: {
      ...breakpoints,
      max569: { max: '569px' },
      max1220: { max: '1220px' },
      max1070: { max: '1070px' },
      max655: { max: '655px' },
    },
    colors,
    ...otherProperties,
  },
};
