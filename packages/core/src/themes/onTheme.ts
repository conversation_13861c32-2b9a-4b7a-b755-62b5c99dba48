import _ from 'lodash';
import onFigmaSemantic from './tools/figma/brand/semantic/merged/on.figma.merged.json';
import toolkitFigmaV1 from './tools/figma/brand/brand-toolkit/figma/toolkit.figma.v1.json';
import onFigmaV1 from './tools/figma/brand/brand-toolkit/figma/on.figma.v1.json';
import breakpoints from './breakpoints.json';

export const onV0Colors = onFigmaSemantic.colors;
export const onV1Colors = onFigmaV1.colors;

const clonedONFigmaSemantic = _.cloneDeep(onFigmaSemantic);
const onMergedVariables = _.merge(clonedONFigmaSemantic, toolkitFigmaV1, onFigmaV1);
const { colors: onFigmaColors } = onMergedVariables;
const otherProperties = _.omit(onMergedVariables, ['colors', 'fontFamily', 'family', 'display']);

export const legacyColors = {
  b1: '#003764',
  b2: '#003764',
  g1: '#000000',
  g2: '#666666',
  g3: '#979797',
  g4: '#CCCCCC',
  g5: '#F7F7F7',
  wh: '#FFFFFF',
  bk: '#000000',
  r1: '#D00000',
  err1: '#D00000',
  inf: '#FF7807',
  alpha00: 'transparent',
  valueDrawer: {
    blu1: '#003764',
    grn1: '#3AB200',
    ylw1: '#DBA61D',
  },
  inverse: {
    b1: '#FFFFFF',
    b2: '#CCCCCC',
    g1: '#555555',
    g2: '#999999',
  },
};

export const colors = {
  ...legacyColors,
  ...onFigmaColors,
};

export const brandFont = ['var(--font-family-font1)', 'sans-serif'].join(',');
export const brandBaseFont = ['var(--font-family-brand-old-navy)', 'sans-serif'].join(',');
export const crossBrandFont = ['var(--font-lato)', 'sans-serif'].join(',');

export const contentDisplayFont = ['var(--font-family-brand-old-navy-3)', 'sans-serif'].join(',');
export const contentDisplayFontAlt = ['var(--font-family-brand-old-navy-4)', 'sans-serif'].join(',');

export const theme = {
  extend: {
    fontFamily: {
      brand: brandFont,
      alt: brandFont,
      'brand-base': brandBaseFont,
      'brand-display': brandBaseFont,
      'content-font-family-display': contentDisplayFont,
      'content-font-family-display-alt': contentDisplayFontAlt,
      crossbrand: crossBrandFont,
      ...onFigmaSemantic.fontFamily,
    },
    screens: {
      ...breakpoints,
      max569: { max: '569px' },
      max1220: { max: '1220px' },
      max1070: { max: '1070px' },
      max655: { max: '655px' },
    },
    colors,
    ...otherProperties,
  },
};
