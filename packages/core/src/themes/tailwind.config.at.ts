import type { Config } from 'tailwindcss';
import cmsPlugin, { cmsSafelist } from '@ecom-next/marketing-ui/themes/typography-plugin';
import presets from './base/tailwind.presets';
import content from './content';
import { tailwindBrandPlugin, searchInputPlugin, tailwindAnimationDelayPlugin } from './plugins/tailwind.brand.plugin';
import { theme as atTheme } from './atTheme';

const config: Config = {
  safelist: [cmsSafelist],
  presets,
  content,
  theme: atTheme,
  plugins: [cmsPlugin({ brand: 'at' }), tailwindBrandPlugin('at'), searchInputPlugin({ brand: 'at' }), tailwindAnimationDelayPlugin()],
};

export default config;
