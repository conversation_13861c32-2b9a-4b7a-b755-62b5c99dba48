import { PresetsConfig } from 'tailwindcss/types/config';
import { tailwindAnimationDelayPlugin } from '../plugins/tailwind.brand.plugin';
import { baseColorPreset } from './tailwind.preset.base-color';
import { baseTypographyPreset } from './tailwind.preset.base-typography';
import { baseShadowPreset } from './tailwind.preset.base-shadow';

export const keyframes = {
  'expand-collapse': {
    '0%, 100%': {
      transform: 'scale(0)',
    },
    '50%': {
      transform: 'scale(1)',
    },
  },
  'fade-in': {
    '0%': {
      opacity: '0',
    },
    '100%': {
      visibility: 'visible',
      opacity: '1',
    },
  },
  'radio-bounce': {
    '0%': {
      transform: 'scale(0)',
    },
    '90%': {
      transform: 'scale(1.1)',
    },
    '100%': {
      transform: 'scale(1)',
    },
  },
  'moving-bar': {
    '0%': {
      'background-size': '1px 1px',
    },
    '100%': {
      'background-size': '100% 1px',
    },
  },
  'isolation-layer': {
    '0%': {
      opacity: '0%',
    },
    '100%': {
      opacity: '100%',
    },
  },
  'slide-down': {
    '0%': {
      transform: 'scaleY(-0)',
    },
    '100%': {
      transform: 'scaleY(1)',
    },
  },
  'load-placeholder': {
    '0%, 25%': { 'background-position': '100% 100%' },
    '90%, 100%': { 'background-position': '0% 0%' },
  },
};
export const animation = {
  'expand-collapse': 'expand-collapse 2s infinite ease-in-out alternate',
  'fade-in': 'fade-in 4s',
  'radio-bounce': 'radio-bounce 250ms cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  'iso-layer': 'isolation-layer 500ms',
  'slide-down': 'slide-down 250ms',
  'loading-placeholder': 'load-placeholder 0.9s infinite ease-in-out alternate',
};

const transitionPreset = {
  content: [],
  theme: {
    extend: {
      transitionDelay: {
        '2000': '2000ms',
        '3000': '3000ms',
        '5000': '5000ms',
      },
      transitionProperty: {
        height: 'height',
      },
      screens: {
        '1xl': '1440px',
      },
      animation,
      keyframes,
      zIndex: {
        deep: '-1',
        base: '0',
        tooltip: '1',
        popover: '5',
        'low-flyer': '200',
        'site-wide': '400',
        'high-flyer': '500',
        sticky: '605',
        overlay: '705',
        drawer: '800',
        modal: '805',
        notification: '810',
        'facet-popup': '900',
      },
    },
  },
  plugins: [tailwindAnimationDelayPlugin(), require('tailwindcss-animate')],
};

const presets: PresetsConfig[] = [baseShadowPreset, baseColorPreset, baseTypographyPreset, transitionPreset];

export default presets;
