import { Config } from 'tailwindcss';
import _ from 'lodash';
import cbFigmaSemantic from '../tools/figma/brand/semantic/merged/cb.figma.merged.json';

const lineHeight = {
  'lh-default': 'var(--crossbrand-line-height-default)',
  'lh-compact': 'var(--crossbrand-line-height-compact)',
  'lh-generous': 'var(--crossbrand-line-height-generous)',
  ...cbFigmaSemantic.lineHeight,
};

const letterSpacing = {
  'ls-minor': 'var(--crossbrand-letter-spacing-minor)',
  'ls-none': 'var(--crossbrand-letter-spacing-none)',
  ...cbFigmaSemantic.letterSpacing,
};

const fontSize = {
  'baseSize-xs': 'var(--crossbrand-font-size-base-xs)',
  'baseSize-sm': 'var(--crossbrand-font-size-base-sm)',
  'baseSize-md': 'var(--crossbrand-font-size-base-md)',
  'baseSize-lg': 'var(--crossbrand-font-size-base-lg)',
  'displaySize-sm': 'var(--crossbrand-font-size-display-sm)',
  'displaySize-md': 'var(--crossbrand-font-size-display-md)',
  'displaySize-lg': 'var(--crossbrand-font-size-display-lg)',
  'specialSize-sm': 'var(--crossbrand-font-size-special-sm)',
  'specialSize-md': 'var(--crossbrand-font-size-special-md)',
  'specialSize-lg': 'var(--crossbrand-font-size-special-lg)',
  ...cbFigmaSemantic.fontSize,
};

const fontWeight = {
  'wt-regular': 'var(--crossbrand-font-weight-regular)',
  'wt-bold': 'var(--crossbrand-font-weight-bold)',
  'wt-mid': 'var(--crossbrand-font-weight-mid)',
  ...cbFigmaSemantic.fontWeight,
};

const withoutColorsAndFonts = _.omit(cbFigmaSemantic, ['colors', 'fontWeight', 'fontSize', 'letterSpacing', 'lineHeight', 'fontFamily']);

export const baseTypographyPreset: Config = {
  content: [],
  theme: {
    extend: {
      fontFamily: {
        sourcesans: ['Source Sans Pro', 'Helvetica', 'Arial', 'Roboto', 'sans-serif'],
        ...cbFigmaSemantic.fontFamily,
      },
      fontSize,
      fontWeight,
      lineHeight,
      letterSpacing,
      ...withoutColorsAndFonts,
    },
  },
};
