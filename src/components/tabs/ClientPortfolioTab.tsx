import { useState, useMemo } from 'react';

import { MockClientGenerator } from '../../mocks/MockClientGenerator';
import { type Client, type ClientPortfolioSummary, type ClientService } from '../../models/Client';
import { type MarketingCampaign } from '../../models/MarketingCampaign';
import KPICard from '../common/KPICard';
import StatusIndicator from '../common/StatusIndicator';
import Table, { type TableColumn, type TableAction } from '../common/Table';
import Tooltip from '../common/Tooltip';
import ExpandableRow from '../portfolio/ExpandableRow';

const ClientPortfolioTab: React.FC = () => {
  // Initialize mock data
  const mockGenerator = new MockClientGenerator();
  const [clients] = useState<Client[]>(() => mockGenerator.generateClients(24));
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Calculate portfolio summary
  const portfolioSummary: ClientPortfolioSummary = useMemo(() => {
    const activeClients = clients.filter(c => c.status === 'active');
    const totalServiceRevenue = clients.reduce((sum, c) => sum + c.revenue.monthlyRecurring, 0);
    const totalCampaignRevenue = clients.reduce((sum, c) => sum + c.campaignMetrics.totalCampaignRevenue, 0);
    const totalCosts = clients.reduce((sum, c) => sum + c.revenue.monthlyCost + c.campaignMetrics.totalCampaignSpend, 0);
    const totalRevenue = totalServiceRevenue + totalCampaignRevenue;
    const netProfit = totalRevenue - totalCosts;
    const openTickets = clients.reduce((sum, c) => sum + c.supportHistory.filter(t => t.status === 'open' || t.status === 'in_progress').length, 0);
    const averageServiceUptime = clients.reduce((sum, c) => sum + c.services.reduce((sSum, s) => sSum + s.uptime, 0) / c.services.length, 0) / clients.length;
    const activeCampaigns = clients.reduce((sum, c) => sum + c.campaignMetrics.activeCampaigns, 0);
    const totalCampaignSpend = clients.reduce((sum, c) => sum + c.campaignMetrics.totalCampaignSpend, 0);
    const avgCampaignROI = clients.filter(c => c.campaignMetrics.totalCampaigns > 0).reduce((sum, c) => sum + c.campaignMetrics.campaignROI, 0) / Math.max(1, clients.filter(c => c.campaignMetrics.totalCampaigns > 0).length);

    return {
      totalClients: clients.length,
      activeClients: activeClients.length,
      totalServiceRevenue,
      totalCampaignRevenue,
      totalRevenue,
      totalCosts,
      netProfit,
      averageMargin: totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0,
      openTickets,
      averageServiceUptime,
      campaignSummary: {
        totalCampaigns: clients.reduce((sum, c) => sum + c.campaignMetrics.totalCampaigns, 0),
        activeCampaigns,
        totalSpent: totalCampaignSpend,
        totalRevenue: totalCampaignRevenue,
        averageROI: avgCampaignROI
      },
      churnRate: ((clients.length - activeClients.length) / clients.length) * 100,
      averageLifetimeValue: clients.reduce((sum, c) => sum + c.revenue.lifetimeValue, 0) / clients.length
    };
  }, [clients]);

  // Filter clients based on search and status
  const filteredClients = useMemo(() => {
    return clients.filter(client => {
      const matchesSearch = searchTerm === '' || 
        client.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.zipCode.includes(searchTerm);
      
      const matchesStatus = filterStatus === 'all' || client.status === filterStatus;
      
      return matchesSearch && matchesStatus;
    });
  }, [clients, searchTerm, filterStatus]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Format percentage
  const formatPercent = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  // Get service icons with tooltips
  const getServiceIcons = (services: ClientService[]): React.ReactNode => {
    const iconMap: Record<string, { icon: string; description: string }> = {
      website: { icon: '🌐', description: 'Website Development' },
      facebook: { icon: '📘', description: 'Facebook Page Management' },
      instagram: { icon: '📷', description: 'Instagram Profile Management' },
      seo: { icon: '⚡', description: 'Search Engine Optimization (SEO)' },
      ads: { icon: '🎯', description: 'Advertising Campaigns' },
      maintenance: { icon: '🔧', description: 'Website Maintenance & Support' },
      domain: { icon: '🌍', description: 'Domain Registration Services' },
      hosting: { icon: '🏠', description: 'Web Hosting Services' }
    };
    
    return (
      <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
        {services.map((s, index) => {
          const service = iconMap[s.type] || { icon: '📋', description: 'Other Service' };
          return (
            <Tooltip key={index} content={service.description}>
              <span style={{ fontSize: '16px', cursor: 'help' }}>{service.icon}</span>
            </Tooltip>
          );
        })}
      </div>
    );
  };

  // Get campaign icons with tooltips
  const getCampaignIcons = (campaigns: MarketingCampaign[]): React.ReactNode => {
    if (campaigns.length === 0) return '—';
    
    const platformIcons: Record<string, { icon: string; description: string }> = {
      google_ads: { icon: '🔍', description: 'Google Ads Campaign' },
      facebook_ads: { icon: '📘', description: 'Facebook Ads Campaign' },
      instagram_ads: { icon: '📷', description: 'Instagram Ads Campaign' }
    };
    
    const unique = [...new Set(campaigns.map(c => c.platform))];
    return (
      <div style={{ display: 'flex', gap: '4px', alignItems: 'center', justifyContent: 'center' }}>
        {unique.map((p, index) => {
          const platform = platformIcons[p] || { icon: '🎯', description: 'Marketing Campaign' };
          return (
            <Tooltip key={index} content={platform.description}>
              <span style={{ fontSize: '14px', cursor: 'help' }}>{platform.icon}</span>
            </Tooltip>
          );
        })}
        <span style={{ fontSize: '12px', marginLeft: '4px' }}>({campaigns.length})</span>
      </div>
    );
  };

  // Get health status
  const getHealthStatus = (client: Client): 'good' | 'warning' | 'error' => {
    const openTickets = client.supportHistory.filter(t => t.status === 'open' || t.status === 'in_progress').length;
    const avgUptime = client.services.reduce((sum, s) => sum + s.uptime, 0) / client.services.length;
    const campaignROI = client.campaignMetrics.campaignROI;
    
    if (openTickets > 2 || avgUptime < 90 || (client.campaigns.length > 0 && campaignROI < 50)) {
      return 'error';
    }
    if (openTickets > 0 || avgUptime < 98 || (client.campaigns.length > 0 && campaignROI < 100)) {
      return 'warning';
    }
    return 'good';
  };

  // Get last issue text
  const getLastIssue = (client: Client): string => {
    const openTickets = client.supportHistory.filter(t => t.status === 'open' || t.status === 'in_progress');
    if (openTickets.length === 0) return '—';
    
    const latest = openTickets.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];
    const hoursAgo = Math.floor((Date.now() - latest.createdAt.getTime()) / (1000 * 60 * 60));
    
    if (hoursAgo < 24) return `${hoursAgo}h ago`;
    return `${Math.floor(hoursAgo / 24)}d ago`;
  };

  // Table columns
  const columns: TableColumn<Client>[] = [
    {
      key: 'businessName',
      title: <Tooltip content="Client business name and account status">Client</Tooltip>,
      sortable: true,
      width: '180px',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: '600', fontSize: '14px' }}>{record.businessName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.contactPerson}</div>
        </div>
      )
    },
    {
      key: 'location',
      title: <Tooltip content="Client location (ZIP code and city)">Location</Tooltip>,
      width: '120px',
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '13px' }}>{record.zipCode}</div>
          <div style={{ fontSize: '11px', color: '#666' }}>{record.city}</div>
        </div>
      )
    },
    {
      key: 'phone',
      title: <Tooltip content="Primary contact phone number">Phone</Tooltip>,
      width: '130px',
      render: (value) => (
        <span style={{ fontSize: '12px', fontFamily: 'monospace' }}>{String(value)}</span>
      )
    },
    {
      key: 'services',
      title: <Tooltip content="Active services and monthly cost">Services</Tooltip>,
      width: '100px',
      align: 'center' as const,
      render: (_, record) => getServiceIcons(record.services)
    },
    {
      key: 'monthlyRecurring',
      title: <Tooltip content="Monthly Recurring Revenue from all services">Service MRR</Tooltip>,
      width: '100px',
      sortable: true,
      align: 'right' as const,
      render: (_, record) => (
        <span style={{ fontWeight: '600', color: '#1890ff' }}>
          {formatCurrency(record.revenue.monthlyRecurring)}
        </span>
      )
    },
    {
      key: 'campaigns',
      title: <Tooltip content="Marketing campaigns by platform with ROI">Campaigns</Tooltip>,
      width: '120px',
      align: 'center' as const,
      render: (_, record) => (
        <div>
          {getCampaignIcons(record.campaigns)}
          {record.campaignMetrics.totalCampaigns > 0 && (
            <div style={{ 
              fontSize: '11px', 
              color: record.campaignMetrics.campaignROI >= 100 ? '#52c41a' : '#ff4d4f',
              fontWeight: '600'
            }}>
              {formatPercent(record.campaignMetrics.campaignROI)} ROI
            </div>
          )}
        </div>
      )
    },
    {
      key: 'netProfit',
      title: <Tooltip content="Total profit after expenses and campaign costs">Net Profit</Tooltip>,
      width: '100px',
      sortable: true,
      align: 'right' as const,
      render: (_, record) => {
        const totalProfit = record.revenue.netProfit + (record.campaignMetrics.totalCampaignRevenue - record.campaignMetrics.totalCampaignSpend);
        return (
          <div>
            <div style={{ fontWeight: '600', color: '#52c41a' }}>
              {formatCurrency(totalProfit)}
            </div>
            <div style={{ fontSize: '11px', color: '#666' }}>
              {formatPercent((totalProfit / (record.revenue.monthlyRecurring + record.campaignMetrics.totalCampaignRevenue)) * 100)} margin
            </div>
          </div>
        );
      }
    },
    {
      key: 'health',
      title: <Tooltip content="Overall account health based on service uptime and support tickets">Health</Tooltip>,
      width: '80px',
      align: 'center' as const,
      render: (_, record) => (
        <StatusIndicator 
          type="health" 
          value={getHealthStatus(record)} 
          tooltip={`Services: ${record.services.length}, Open tickets: ${record.supportHistory.filter(t => t.status === 'open').length}`}
        />
      )
    },
    {
      key: 'tickets',
      title: <Tooltip content="Open support tickets / Total tickets">Tickets</Tooltip>,
      width: '80px',
      align: 'center' as const,
      render: (_, record) => {
        const openTickets = record.supportHistory.filter(t => t.status === 'open' || t.status === 'in_progress').length;
        const totalTickets = record.supportHistory.length;
        return (
          <div style={{ fontSize: '12px' }}>
            <span style={{ color: openTickets > 0 ? '#ff4d4f' : '#52c41a', fontWeight: '600' }}>
              {openTickets}
            </span>
            <span style={{ color: '#8c8c8c' }}>/{totalTickets}</span>
          </div>
        );
      }
    },
    {
      key: 'lastIssue',
      title: <Tooltip content="Time since last reported issue">Last Issue</Tooltip>,
      width: '90px',
      align: 'center' as const,
      render: (_, record) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {getLastIssue(record)}
        </span>
      )
    }
  ];

  // Table actions
  const actions: TableAction<Client>[] = [
    {
      key: 'details',
      title: <Tooltip content="View detailed client analytics"><span style={{ cursor: 'help' }}>📊</span></Tooltip>,
      onClick: (record) => console.warn('View details for', record.businessName)
    },
    {
      key: 'contact',
      title: <Tooltip content="Contact this client"><span style={{ cursor: 'help' }}>📞</span></Tooltip>,
      onClick: (record) => console.warn('Contact', record.businessName)
    }
  ];

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <h2 style={{ margin: '0 0 10px 0', color: '#1890ff' }}>
          <Tooltip content="Dashboard for managing all your client accounts and services">
            <span style={{ cursor: 'help' }}>📊 Client Portfolio Dashboard</span>
          </Tooltip>
        </h2>
        <p style={{ margin: '0', color: '#666', fontSize: '14px' }}>
          Monitor client services, campaign performance, and revenue metrics
        </p>
      </div>

      {/* KPI Cards */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '16px',
        marginBottom: '24px' 
      }}>
        <KPICard
          title="Total Clients"
          value={portfolioSummary.totalClients}
          subtitle={`${portfolioSummary.activeClients} active`}
          color="blue"
          icon="👥"
          iconTooltip="Total number of clients in your portfolio"
        />
        <KPICard
          title="Monthly Revenue"
          value={formatCurrency(portfolioSummary.totalRevenue)}
          subtitle={`${formatCurrency(portfolioSummary.totalServiceRevenue)} services + ${formatCurrency(portfolioSummary.totalCampaignRevenue)} campaigns`}
          color="green"
          icon="💰"
          iconTooltip="Total monthly revenue from all clients"
        />
        <KPICard
          title="Net Profit"
          value={formatCurrency(portfolioSummary.netProfit)}
          subtitle={`${formatPercent(portfolioSummary.averageMargin)} avg margin`}
          color="purple"
          icon="📈"
          iconTooltip="Net profit after all expenses"
        />
        <KPICard
          title="Campaign ROI"
          value={formatPercent(portfolioSummary.campaignSummary.averageROI)}
          subtitle={`${portfolioSummary.campaignSummary.activeCampaigns} active campaigns`}
          color="orange"
          icon="🎯"
          iconTooltip="Average return on investment for marketing campaigns"
        />
        <KPICard
          title="Service Health"
          value={formatPercent(portfolioSummary.averageServiceUptime)}
          subtitle="Average uptime"
          color={portfolioSummary.averageServiceUptime >= 98 ? "green" : portfolioSummary.averageServiceUptime >= 95 ? "orange" : "red"}
          icon="🟢"
          iconTooltip="Average uptime across all client services"
        />
        <KPICard
          title="Open Tickets"
          value={portfolioSummary.openTickets}
          subtitle="Require attention"
          color={portfolioSummary.openTickets === 0 ? "green" : portfolioSummary.openTickets <= 3 ? "orange" : "red"}
          icon="🎫"
          iconTooltip="Number of unresolved support tickets"
        />
      </div>

      {/* Filters */}
      <div style={{ 
        display: 'flex', 
        gap: '16px', 
        marginBottom: '20px',
        padding: '16px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px'
      }}>
        <input
          type="text"
          placeholder="Search clients..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            padding: '8px 12px',
            border: '1px solid #d0d0d0',
            borderRadius: '4px',
            fontSize: '14px',
            width: '300px'
          }}
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '8px 12px',
            border: '1px solid #d0d0d0',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        >
          <option value="all">All Statuses</option>
          <option value="active">Active</option>
          <option value="trial">Trial</option>
          <option value="paused">Paused</option>
          <option value="churned">Churned</option>
        </select>
        <div style={{ flex: 1 }} />
        <div style={{ fontSize: '14px', color: '#666', display: 'flex', alignItems: 'center' }}>
          Showing {filteredClients.length} of {clients.length} clients
        </div>
      </div>

      {/* Client Matrix Table */}
      <Table
        columns={columns}
        data={filteredClients}
        actions={actions}
        expandable={{
          expandedRowRender: (record) => (
            <ExpandableRow 
              client={record}
              onStartCampaign={(clientId) => console.warn('Start campaign for', clientId)}
              onManageCampaign={(campaignId) => console.warn('Manage campaign', campaignId)}
            />
          ),
          rowExpandable: () => true
        }}
        rowKey="id"
        emptyText="No clients found matching your filters"
      />
    </div>
  );
};

export default ClientPortfolioTab;