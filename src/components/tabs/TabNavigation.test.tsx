import { render, screen, fireEvent } from '@testing-library/react';
import { useState } from 'react';

import TabNavigation, { type TabKey, type Tab } from './TabNavigation';

const TestTabComponent = ({ tabs }: { tabs?: Tab[] }) => {
  const [activeTab, setActiveTab] = useState<TabKey>('search');

  return <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />;
};

describe('TabNavigation', () => {
  it('renders default tabs and switches between them', () => {
    render(<TestTabComponent />);
    
    // Check for initial tabs
    expect(screen.getByText('Business Search')).toBeInTheDocument();
    expect(screen.getByText('Client Portfolio')).toBeInTheDocument();

    // Click on the second tab
    const clientPortfolioTab = screen.getByText('Client Portfolio');
    fireEvent.click(clientPortfolioTab);

    // Verify the second tab is active
    expect(clientPortfolioTab.closest('button')).toHaveStyle('background-color: rgb(255, 255, 255)');
    expect(screen.getByText('Business Search').closest('button')).not.toHaveStyle('background-color: rgb(255, 255, 255)');
  });

  it('renders custom tabs with icons and badges', () => {
    const customTabs: Tab[] = [
      { key: 'search', label: 'Search', icon: '🔍', badge: '5' },
      { key: 'portfolio', label: 'Portfolio', icon: '📊', badge: 10 }
    ];

    render(<TestTabComponent tabs={customTabs} />);
    
    // Check for custom tabs
    expect(screen.getByText('Search')).toBeInTheDocument();
    expect(screen.getByText('Portfolio')).toBeInTheDocument();
    
    // Check for icons
    expect(screen.getByText('🔍')).toBeInTheDocument();
    expect(screen.getByText('📊')).toBeInTheDocument();
    
    // Check for badges
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
  });

  it('handles disabled tabs correctly', () => {
    const onTabChange = jest.fn();
    const tabsWithDisabled: Tab[] = [
      { key: 'search', label: 'Search' },
      { key: 'portfolio', label: 'Portfolio', disabled: true }
    ];

    render(<TabNavigation activeTab='search' onTabChange={onTabChange} tabs={tabsWithDisabled} />);
    
    const disabledTab = screen.getByText('Portfolio').closest('button');
    expect(disabledTab).toBeDisabled();
    
    // Try to click disabled tab
    fireEvent.click(disabledTab!);
    expect(onTabChange).not.toHaveBeenCalled();
  });

  it('applies custom className and style', () => {
    const customStyle = { backgroundColor: 'red' };
    const { container } = render(
      <TabNavigation 
        activeTab='search' 
        onTabChange={() => {}} 
        className="custom-tabs" 
        style={customStyle}
      />
    );
    
    const tabNavigation = container.querySelector('.tab-navigation');
    expect(tabNavigation).toHaveClass('custom-tabs');
    expect(tabNavigation).toHaveStyle('background-color: rgb(255, 0, 0)');
  });

  it('displays version information', () => {
    render(<TabNavigation activeTab='search' onTabChange={() => {}} />);
    expect(screen.getByText(/Business Search & Client Management v1.3.0/)).toBeInTheDocument();
  });

  it('handles hover effects on non-active tabs', () => {
    render(<TabNavComponent />);
    
    const searchTab = screen.getByText('Business Search').closest('button')!;
    const portfolioTab = screen.getByText('Client Portfolio').closest('button')!;
    
    // Hover over non-active tab
    fireEvent.mouseEnter(portfolioTab);
    expect(portfolioTab).toHaveStyle({ backgroundColor: 'rgb(240, 240, 240)', color: 'rgb(51, 51, 51)' });
    
    // Mouse leave should reset (testing that the event handler is called)
    fireEvent.mouseLeave(portfolioTab);
    
    // Hover on active tab should not change (testing that hover doesn't affect active tab)
    fireEvent.mouseEnter(searchTab);
    // Active tab should maintain its style
    expect(searchTab).toHaveStyle('background-color: rgb(255, 255, 255)');
  });

  it('calls onTabChange when clicking enabled tabs', () => {
    const onTabChange = jest.fn();
    
    render(<TabNavigation activeTab='search' onTabChange={onTabChange} />);
    
    fireEvent.click(screen.getByText('Client Portfolio'));
    expect(onTabChange).toHaveBeenCalledWith('portfolio');
  });

  it('renders tabs without icons when not provided', () => {
    const tabsWithoutIcons: Tab[] = [
      { key: 'search', label: 'Search' },
      { key: 'portfolio', label: 'Portfolio' }
    ];

    render(<TabNavigation activeTab='search' onTabChange={() => {}} tabs={tabsWithoutIcons} />);
    
    expect(screen.getByText('Search')).toBeInTheDocument();
    expect(screen.getByText('Portfolio')).toBeInTheDocument();
    expect(screen.queryByText('🔍')).not.toBeInTheDocument();
    expect(screen.queryByText('📊')).not.toBeInTheDocument();
  });

  it('renders tabs without badges when not provided', () => {
    render(<TabNavigation activeTab='search' onTabChange={() => {}} />);
    
    // Default tabs don't have badges
    expect(screen.queryByText('5')).not.toBeInTheDocument();
    expect(screen.queryByText('10')).not.toBeInTheDocument();
  });

  it('applies correct active tab styling', () => {
    render(<TabNavigation activeTab='portfolio' onTabChange={() => {}} />);
    
    const activeTab = screen.getByText('Client Portfolio').closest('button')!;
    const inactiveTab = screen.getByText('Business Search').closest('button')!;
    
    expect(activeTab).toHaveStyle({
      backgroundColor: 'rgb(255, 255, 255)',
      color: 'rgb(24, 144, 255)',
      fontWeight: '600'
    });
    
    // Check that inactive tab has different styling
    expect(inactiveTab).not.toHaveStyle({
      backgroundColor: 'rgb(255, 255, 255)'
    });
  });

  it('renders About button and calls onAboutClick when provided', () => {
    const mockOnAboutClick = jest.fn();
    render(
      <TabNavigation 
        activeTab="search" 
        onTabChange={jest.fn()} 
        onAboutClick={mockOnAboutClick}
      />
    );
    
    const aboutButton = screen.getByLabelText('About this application');
    expect(aboutButton).toBeInTheDocument();
    
    fireEvent.click(aboutButton);
    expect(mockOnAboutClick).toHaveBeenCalledTimes(1);
  });

  it('does not render About button when onAboutClick is not provided', () => {
    render(
      <TabNavigation 
        activeTab="search" 
        onTabChange={jest.fn()}
      />
    );
    
    expect(screen.queryByLabelText('About this application')).not.toBeInTheDocument();
  });
});

// Helper component for hover tests
const TabNavComponent = () => {
  const [activeTab, setActiveTab] = useState<TabKey>('search');
  return <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />;
}; 