import React from 'react';

const SearchForm = ({ onSearch }: { onSearch: (params: any) => void }) => {
  const handleSearch = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    const params = {
      zipCode: formData.get('zipCode') as string,
      radius: Number(formData.get('radius')),
      businessType: formData.get('businessType') as string,
      minRating: Number(formData.get('minRating')),
      maxPrice: Number(formData.get('maxPrice')),
      openNow: formData.get('openNow') === 'on',
    };
    onSearch(params);
  };

  return (
    <div>
      <h1>Business Search</h1>
      <form onSubmit={handleSearch}>
        <div>
          <label htmlFor="zipCode">Zip Code: </label>
          <input type="text" id="zipCode" name="zipCode" required pattern="[0-9]{5}" />
        </div>
        <div>
          <label htmlFor="radius">Radius (miles): </label>
          <input type="number" id="radius" name="radius" min="1" max="50" defaultValue="5" />
        </div>
        <div>
          <label htmlFor="businessType">Business Type: </label>
          <input type="text" id="businessType" name="businessType" placeholder="restaurant, coffee, etc." />
        </div>
        <div>
          <label htmlFor="minRating">Minimum Rating: </label>
          <input type="number" id="minRating" name="minRating" min="0" max="5" step="0.5" />
        </div>
        <div>
          <label htmlFor="maxPrice">Max Price Level: </label>
          <select id="maxPrice" name="maxPrice">
            <option value="">Any</option>
            <option value="1">$ (Inexpensive)</option>
            <option value="2">$$ (Moderate)</option>
            <option value="3">$$$ (Expensive)</option>
            <option value="4">$$$$ (Very Expensive)</option>
          </select>
        </div>
        <div>
          <label>
            <input type="checkbox" name="openNow" />
            Open Now Only
          </label>
        </div>
        <button type="submit">Search</button>
      </form>
    </div>
  );
};

export default SearchForm;
