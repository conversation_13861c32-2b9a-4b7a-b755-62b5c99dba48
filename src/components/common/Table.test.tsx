import { render, screen, fireEvent } from '@testing-library/react';

import Table, { type TableColumn } from './Table';

interface TestRecord {
  id: string;
  name: string;
  age: number;
  email: string;
  status: 'active' | 'inactive';
}

describe('Table', () => {
  const mockData: TestRecord[] = [
    { id: '1', name: '<PERSON>', age: 30, email: '<EMAIL>', status: 'active' },
    { id: '2', name: '<PERSON>', age: 25, email: '<EMAIL>', status: 'inactive' },
    { id: '3', name: '<PERSON>', age: 35, email: '<EMAIL>', status: 'active' },
  ];

  const columns: TableColumn<TestRecord>[] = [
    { key: 'name', title: 'Name', sortable: true },
    { key: 'age', title: 'Age', sortable: true },
    { key: 'email', title: 'Email', width: '200px' },
    { 
      key: 'status', 
      title: 'Status', 
      render: (value) => (
        <span style={{ color: value === 'active' ? 'green' : 'red' }}>
          {String(value)}
        </span>
      )
    },
  ];

  it('renders without crashing', () => {
    render(<Table columns={columns} data={mockData} />);
    expect(screen.getByRole('table')).toBeInTheDocument();
  });

  it('renders all column headers', () => {
    render(<Table columns={columns} data={mockData} />);
    expect(screen.getByText(/Name/)).toBeInTheDocument();
    expect(screen.getByText(/Age/)).toBeInTheDocument();
    expect(screen.getByText(/Email/)).toBeInTheDocument();
    expect(screen.getByText(/Status/)).toBeInTheDocument();
  });

  it('renders all data rows', () => {
    render(<Table columns={columns} data={mockData} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
    expect(screen.getByText('30')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
    expect(screen.getByText('35')).toBeInTheDocument();
  });

  it('applies custom column width', () => {
    render(<Table columns={columns} data={mockData} />);
    const emailHeaders = screen.getAllByText('Email');
    const emailHeader = emailHeaders.find(el => el.tagName === 'TH');
    expect(emailHeader).toHaveStyle({ width: '200px' });
  });

  it('uses custom render function', () => {
    render(<Table columns={columns} data={mockData} />);
    const activeStatuses = screen.getAllByText('active');
    activeStatuses.forEach(status => {
      expect(status).toHaveStyle({ color: 'rgb(0, 128, 0)' });
    });
    const inactiveStatus = screen.getByText('inactive');
    expect(inactiveStatus).toHaveStyle({ color: 'rgb(255, 0, 0)' });
  });

  it('shows sort indicators on sortable columns', () => {
    render(<Table columns={columns} data={mockData} />);
    const nameHeader = screen.getByText(/Name/).closest('th');
    expect(nameHeader).toHaveTextContent('↕'); // Unsorted indicator
  });

  it('sorts data when clicking sortable column headers', () => {
    render(<Table columns={columns} data={mockData} />);
    
    // Click on Name header to sort
    fireEvent.click(screen.getByText(/Name/));
    
    // Check that data is sorted (Bob, Jane, John)
    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('Bob Johnson');
    expect(rows[2]).toHaveTextContent('Jane Smith');
    expect(rows[3]).toHaveTextContent('John Doe');
  });

  it('toggles sort direction on multiple clicks', () => {
    render(<Table columns={columns} data={mockData} />);
    
    // First click - ascending
    fireEvent.click(screen.getByText(/Age/));
    let rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('25'); // Jane
    expect(rows[2]).toHaveTextContent('30'); // John
    expect(rows[3]).toHaveTextContent('35'); // Bob
    
    // Second click - descending
    fireEvent.click(screen.getByText(/Age/));
    rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('35'); // Bob
    expect(rows[2]).toHaveTextContent('30'); // John
    expect(rows[3]).toHaveTextContent('25'); // Jane
  });

  it('does not sort when clicking non-sortable columns', () => {
    render(<Table columns={columns} data={mockData} />);
    
    const originalOrder = screen.getAllByRole('row').map(row => row.textContent);
    
    // Click on Email header (not sortable)
    fireEvent.click(screen.getByText('Email'));
    
    const newOrder = screen.getAllByRole('row').map(row => row.textContent);
    expect(newOrder).toEqual(originalOrder);
  });

  it('handles empty data', () => {
    render(<Table columns={columns} data={[]} />);
    
    // Should show empty text
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('handles missing render function gracefully', () => {
    const simpleColumns: TableColumn<TestRecord>[] = [
      { key: 'name', title: 'Name' },
      { key: 'age', title: 'Age' },
    ];
    
    render(<Table columns={simpleColumns} data={mockData} />);
    
    // Should display raw values
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('30')).toBeInTheDocument();
  });

  it('handles non-string values correctly', () => {
    const numericData = [
      { id: 1, value: 100, active: true },
      { id: 2, value: 200, active: false },
    ];
    
    const numericColumns: TableColumn[] = [
      { key: 'id', title: 'ID' },
      { key: 'value', title: 'Value' },
      { key: 'active', title: 'Active' },
    ];
    
    render(<Table columns={numericColumns} data={numericData} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('true')).toBeInTheDocument();
    // false becomes empty string when rendered without custom render function
    expect(screen.getByText('200')).toBeInTheDocument(); // Verify second row exists
  });

  it('maintains stable sort when values are equal', () => {
    const dataWithDuplicates = [
      { id: '1', name: 'Alice', age: 30 },
      { id: '2', name: 'Bob', age: 30 },
      { id: '3', name: 'Charlie', age: 30 },
    ];
    
    const simpleColumns: TableColumn[] = [
      { key: 'name', title: 'Name' },
      { key: 'age', title: 'Age', sortable: true },
    ];
    
    render(<Table columns={simpleColumns} data={dataWithDuplicates} />);
    
    // Sort by age (all same)
    fireEvent.click(screen.getByText(/Age/));
    
    // Order should be maintained
    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('Alice');
    expect(rows[2]).toHaveTextContent('Bob');
    expect(rows[3]).toHaveTextContent('Charlie');
  });

  it('resets to original order after sorting different column', () => {
    render(<Table columns={columns} data={mockData} />);
    
    // Sort by name
    fireEvent.click(screen.getByText(/Name/));
    
    // Then sort by age
    fireEvent.click(screen.getByText(/Age/));
    
    // Name column should show unsorted indicator
    const nameHeader = screen.getByText(/Name/).closest('th');
    expect(nameHeader).toHaveTextContent('↕');
  });

  it('handles null and undefined values', () => {
    const dataWithNulls = [
      { id: '1', name: 'John', age: null, email: undefined },
    ];
    
    render(<Table columns={columns} data={dataWithNulls as any} />);
    
    // Should render empty strings for null/undefined
    const cells = screen.getAllByRole('cell');
    expect(cells.some(cell => cell.textContent === '')).toBe(true);
  });

  it('applies row index to render function', () => {
    const columnsWithIndex: TableColumn<TestRecord>[] = [
      { 
        key: 'name', 
        title: 'Name',
        render: (value, _, index) => `${index + 1}. ${value}`
      },
    ];
    
    render(<Table<TestRecord> columns={columnsWithIndex} data={mockData} />);
    
    expect(screen.getByText('1. John Doe')).toBeInTheDocument();
    expect(screen.getByText('2. Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('3. Bob Johnson')).toBeInTheDocument();
  });

  it('handles custom empty text', () => {
    render(<Table columns={columns} data={[]} emptyText="Custom empty message" />);
    expect(screen.getByText('Custom empty message')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<Table columns={columns} data={mockData} loading />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('handles row click events', () => {
    const handleRowClick = jest.fn();
    render(<Table columns={columns} data={mockData} onRowClick={handleRowClick} />);
    
    fireEvent.click(screen.getByText('John Doe').closest('tr')!);
    expect(handleRowClick).toHaveBeenCalledWith(mockData[0], 0);
  });

  it('handles custom row keys', () => {
    const dataWithCustomKeys = [
      { userId: 'abc', name: 'Alice' },
      { userId: 'def', name: 'Bob' },
    ];
    
    render(
      <Table 
        columns={[{ key: 'name', title: 'Name' }]} 
        data={dataWithCustomKeys} 
        rowKey="userId"
      />
    );
    
    expect(screen.getByText('Alice')).toBeInTheDocument();
    expect(screen.getByText('Bob')).toBeInTheDocument();
  });

  it('handles function-based row keys', () => {
    const getKey = jest.fn((record) => record.id);
    
    render(
      <Table 
        columns={columns} 
        data={mockData} 
        rowKey={getKey}
      />
    );
    
    expect(getKey).toHaveBeenCalledTimes(mockData.length);
  });

  it('handles column alignment', () => {
    const alignedColumns: TableColumn<TestRecord>[] = [
      { key: 'name', title: 'Name', align: 'left' },
      { key: 'age', title: 'Age', align: 'center' },
      { key: 'email', title: 'Email', align: 'right' },
    ];
    
    render(<Table<TestRecord> columns={alignedColumns} data={mockData} />);
    
    const headers = screen.getAllByRole('columnheader');
    expect(headers[0]).toHaveStyle({ textAlign: 'left' });
    expect(headers[1]).toHaveStyle({ textAlign: 'center' });
    expect(headers[2]).toHaveStyle({ textAlign: 'right' });
  });

  it('handles actions', () => {
    const handleEdit = jest.fn();
    const handleDelete = jest.fn();
    
    render(
      <Table 
        columns={columns} 
        data={mockData}
        actions={[
          { key: 'edit', title: 'Edit', onClick: handleEdit },
          { 
            key: 'delete', 
            title: 'Delete', 
            onClick: handleDelete,
            disabled: (record) => record.status === 'active'
          },
        ]}
      />
    );
    
    // Check actions column header
    expect(screen.getByText('Actions')).toBeInTheDocument();
    
    // Check action buttons
    const editButtons = screen.getAllByText('Edit');
    expect(editButtons).toHaveLength(3);
    
    // Click edit on first row
    fireEvent.click(editButtons[0]);
    expect(handleEdit).toHaveBeenCalledWith(mockData[0], 0);
    
    // Check disabled state
    const deleteButtons = screen.getAllByText('Delete');
    expect(deleteButtons[0]).toBeDisabled(); // First row is active
    expect(deleteButtons[1]).not.toBeDisabled(); // Second row is inactive
  });

  it('handles expandable rows', () => {
    const expandedContent = 'Expanded content for row';
    
    render(
      <Table 
        columns={columns} 
        data={mockData}
        expandable={{
          expandedRowRender: (record) => `${expandedContent} ${record.name}`,
          rowExpandable: (record) => record.status === 'active'
        }}
      />
    );
    
    // Check expand column header
    expect(screen.getByText('Expand')).toBeInTheDocument();
    
    // Find expand buttons (only for active rows)
    const expandButtons = screen.getAllByText('▶');
    expect(expandButtons).toHaveLength(2); // Two active rows
    
    // Click to expand first row
    fireEvent.click(expandButtons[0]);
    
    // Check expanded content
    expect(screen.getByText(`${expandedContent} John Doe`)).toBeInTheDocument();
    
    // Check button changed to collapse
    expect(screen.getByText('▼')).toBeInTheDocument();
    
    // Click to collapse
    fireEvent.click(screen.getByText('▼'));
    expect(screen.queryByText(`${expandedContent} John Doe`)).not.toBeInTheDocument();
  });

  it('prevents event propagation for buttons', () => {
    const handleRowClick = jest.fn();
    const handleActionClick = jest.fn();
    
    render(
      <Table 
        columns={columns} 
        data={mockData}
        onRowClick={handleRowClick}
        actions={[
          { key: 'edit', title: 'Edit', onClick: handleActionClick }
        ]}
      />
    );
    
    const editButton = screen.getAllByText('Edit')[0];
    fireEvent.click(editButton);
    
    expect(handleActionClick).toHaveBeenCalled();
    expect(handleRowClick).not.toHaveBeenCalled();
  });

  it('applies custom styles and className', () => {
    const { container } = render(
      <Table 
        columns={columns} 
        data={mockData}
        className="custom-table"
        style={{ marginTop: '20px' }}
      />
    );
    
    const tableContainer = container.querySelector('.table-container');
    expect(tableContainer).toHaveClass('table-container');
    expect(tableContainer).toHaveClass('custom-table');
    expect(tableContainer).toHaveStyle({ marginTop: '20px' });
  });
});