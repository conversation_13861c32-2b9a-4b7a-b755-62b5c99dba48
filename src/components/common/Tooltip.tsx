import { type ReactNode, useState, useRef, useEffect } from 'react';

export interface TooltipProps {
  content: ReactNode;
  children: ReactNode;
  position?: 'top' | 'right' | 'bottom' | 'left';
  delay?: number;
  disabled?: boolean;
  className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'top',
  delay = 200,
  disabled = false,
  className = ''
}) => {
  const [visible, setVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    if (disabled) return;
    
    timeoutRef.current = setTimeout(() => {
      setVisible(true);
    }, delay);
  };

  const handleMouseLeave = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setVisible(false);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getPositionStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      position: 'absolute',
      zIndex: 1000,
      padding: '8px 12px',
      backgroundColor: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      borderRadius: '4px',
      fontSize: '13px',
      whiteSpace: 'nowrap',
      pointerEvents: 'none',
      transition: 'opacity 0.2s',
      opacity: visible ? 1 : 0,
      visibility: visible ? 'visible' : 'hidden'
    };

    switch (position) {
      case 'top':
        return {
          ...baseStyles,
          bottom: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginBottom: '8px'
        };
      case 'right':
        return {
          ...baseStyles,
          left: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
          marginLeft: '8px'
        };
      case 'bottom':
        return {
          ...baseStyles,
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: '8px'
        };
      case 'left':
        return {
          ...baseStyles,
          right: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
          marginRight: '8px'
        };
      default:
        return baseStyles;
    }
  };

  // Arrow styles based on position
  const getArrowStyles = (): React.CSSProperties => {
    const baseArrowStyles: React.CSSProperties = {
      position: 'absolute',
      width: 0,
      height: 0,
      borderStyle: 'solid',
      borderColor: 'transparent'
    };

    switch (position) {
      case 'top':
        return {
          ...baseArrowStyles,
          bottom: '-6px',
          left: '50%',
          transform: 'translateX(-50%)',
          borderTopColor: 'rgba(0, 0, 0, 0.9)',
          borderWidth: '6px 6px 0 6px'
        };
      case 'right':
        return {
          ...baseArrowStyles,
          left: '-6px',
          top: '50%',
          transform: 'translateY(-50%)',
          borderRightColor: 'rgba(0, 0, 0, 0.9)',
          borderWidth: '6px 6px 6px 0'
        };
      case 'bottom':
        return {
          ...baseArrowStyles,
          top: '-6px',
          left: '50%',
          transform: 'translateX(-50%)',
          borderBottomColor: 'rgba(0, 0, 0, 0.9)',
          borderWidth: '0 6px 6px 6px'
        };
      case 'left':
        return {
          ...baseArrowStyles,
          right: '-6px',
          top: '50%',
          transform: 'translateY(-50%)',
          borderLeftColor: 'rgba(0, 0, 0, 0.9)',
          borderWidth: '6px 0 6px 6px'
        };
      default:
        return baseArrowStyles;
    }
  };

  return (
    <span
      style={{ position: 'relative', display: 'inline-block' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      {visible ? (
        <div
          role="tooltip"
          className={className}
          style={getPositionStyles()}
        >
          {content}
          <div style={getArrowStyles()} />
        </div>
      ) : null}
    </span>
  );
};

export default Tooltip;