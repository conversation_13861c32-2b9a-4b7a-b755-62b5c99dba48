import { useState } from 'react';

/**
 * Column definition for the table
 */
export interface TableColumn<T = Record<string, unknown>> {
  key: string;
  title: React.ReactNode;
  width?: string;
  sortable?: boolean;
  render?: (value: unknown, record: T, index: number) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
}

/**
 * Action button definition
 */
export interface TableAction<T = Record<string, unknown>> {
  key: string;
  title: React.ReactNode;
  onClick: (record: T, index: number) => void;
  disabled?: (record: T) => boolean;
  style?: React.CSSProperties;
}

/**
 * Table component props
 */
export interface TableProps<T = Record<string, unknown>> {
  columns: TableColumn<T>[];
  data: T[];
  actions?: TableAction<T>[];
  expandable?: {
    expandedRowRender: (record: T, index: number) => React.ReactNode;
    rowExpandable?: (record: T) => boolean;
  };
  loading?: boolean;
  emptyText?: string;
  rowKey?: string | ((record: T) => string | number);
  onRowClick?: (record: T, index: number) => void;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Generic reusable table component
 */
function Table<T = Record<string, unknown>>({
  columns,
  data,
  actions,
  expandable,
  loading = false,
  emptyText = 'No data available',
  rowKey = 'id',
  onRowClick,
  className = '',
  style = {}
}: TableProps<T>) {
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [expandedRows, setExpandedRows] = useState<Set<string | number>>(new Set());

  // Get row key value
  const getRowKey = (record: T, index: number): string | number => {
    if (typeof rowKey === 'function') {
      return rowKey(record);
    }
    return (record as any)[rowKey] || index;
  };

  // Handle column sorting
  const handleSort = (columnKey: string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(columnKey);
      setSortDirection('asc');
    }
  };

  // Handle row expansion
  const toggleRowExpansion = (rowKey: string | number) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(rowKey)) {
      newExpandedRows.delete(rowKey);
    } else {
      newExpandedRows.add(rowKey);
    }
    setExpandedRows(newExpandedRows);
  };

  // Sort data if needed
  const sortedData = [...data];
  if (sortColumn) {
    sortedData.sort((a, b) => {
      const aValue = a[sortColumn as keyof T];
      const bValue = b[sortColumn as keyof T];
      
      if (aValue === bValue) return 0;
      
      let comparison = 0;
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue);
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue;
      } else {
        comparison = String(aValue).localeCompare(String(bValue));
      }
      
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }

  // Get sort indicator
  const getSortIndicator = (columnKey: string) => {
    if (sortColumn !== columnKey) return ' ↕';
    return sortDirection === 'asc' ? ' ↑' : ' ↓';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        Loading...
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
        {emptyText}
      </div>
    );
  }

  return (
    <div className={`table-container ${className}`} style={style}>
      <table border={1} style={{ width: '100%', borderCollapse: 'collapse' }}>
        <thead>
          <tr style={{ backgroundColor: '#f5f5f5' }}>
            {expandable ? <th style={{ width: '40px', textAlign: 'center', padding: '8px' }}>
                Expand
              </th> : null}
            {columns.map((column) => (
              <th
                key={column.key}
                style={{
                  padding: '12px 8px',
                  textAlign: column.align || 'left',
                  width: column.width,
                  cursor: column.sortable ? 'pointer' : 'default',
                  backgroundColor: column.sortable ? '#e8e8e8' : '#f5f5f5'
                }}
                onClick={() => column.sortable && handleSort(column.key)}
              >
                {column.title}
                {column.sortable ? getSortIndicator(column.key) : null}
              </th>
            ))}
            {actions && actions.length > 0 ? <th style={{ padding: '12px 8px', textAlign: 'center' }}>
                Actions
              </th> : null}
          </tr>
        </thead>
        <tbody>
          {sortedData.map((record, index) => {
            const key = getRowKey(record, index);
            const isExpanded = expandedRows.has(key);
            const canExpand = !expandable?.rowExpandable || expandable.rowExpandable(record);

            return (
              <>
                <tr
                  key={key}
                  style={{
                    cursor: onRowClick ? 'pointer' : 'default',
                    backgroundColor: index % 2 === 0 ? '#ffffff' : '#f9f9f9'
                  }}
                  onClick={() => onRowClick?.(record, index)}
                >
                  {expandable ? <td style={{ textAlign: 'center', padding: '8px' }}>
                      {canExpand ? <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleRowExpansion(key);
                          }}
                          style={{
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            fontSize: '14px'
                          }}
                        >
                          {isExpanded ? '▼' : '▶'}
                        </button> : null}
                    </td> : null}
                  {columns.map((column) => {
                    const value = record[column.key as keyof T];
                    const content = column.render ? column.render(value, record, index) : String(value || '');
                    
                    return (
                      <td
                        key={column.key}
                        style={{
                          padding: '8px',
                          textAlign: column.align || 'left',
                          borderBottom: '1px solid #e0e0e0'
                        }}
                      >
                        {content}
                      </td>
                    );
                  })}
                  {actions && actions.length > 0 ? <td style={{ padding: '8px', textAlign: 'center' }}>
                      <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
                        {actions.map((action) => (
                          <button
                            key={action.key}
                            onClick={(e) => {
                              e.stopPropagation();
                              action.onClick(record, index);
                            }}
                            disabled={action.disabled?.(record)}
                            style={{
                              padding: '4px 8px',
                              fontSize: '12px',
                              cursor: action.disabled?.(record) ? 'not-allowed' : 'pointer',
                              opacity: action.disabled?.(record) ? 0.5 : 1,
                              ...action.style
                            }}
                          >
                            {action.title}
                          </button>
                        ))}
                      </div>
                    </td> : null}
                </tr>
                {expandable && isExpanded && canExpand ? <tr key={`${key}-expanded`}>
                    <td
                      colSpan={
                        columns.length + 
                        (actions && actions.length > 0 ? 1 : 0) + 
                        (expandable ? 1 : 0)
                      }
                      style={{
                        padding: '16px',
                        backgroundColor: '#f8f9fa',
                        borderLeft: '3px solid #007bff'
                      }}
                    >
                      {expandable.expandedRowRender(record, index)}
                    </td>
                  </tr> : null}
              </>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}

export default Table;