import { fireEvent, render, screen } from '@testing-library/react';

import AboutMe from './AboutMe';

describe('AboutMe', () => {
  it('renders the About Me modal when open', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    expect(screen.getByText('About Business Search Application')).toBeInTheDocument();
    expect(screen.getByText('Features')).toBeInTheDocument();
    expect(screen.getByText('Tech Stack')).toBeInTheDocument();
    expect(screen.getByText('Developer')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<AboutMe isOpen={false} onClose={jest.fn()} />);
    
    expect(screen.queryByText('About Business Search Application')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const mockOnClose = jest.fn();
    render(<AboutMe isOpen onClose={mockOnClose} />);
    
    const closeButton = screen.getByLabelText('Close');
    fireEvent.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when clicking outside the modal', () => {
    const mockOnClose = jest.fn();
    render(<AboutMe isOpen onClose={mockOnClose} />);
    
    const backdrop = screen.getByTestId('modal-backdrop');
    fireEvent.click(backdrop);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('does not close when clicking inside the modal content', () => {
    const mockOnClose = jest.fn();
    render(<AboutMe isOpen onClose={mockOnClose} />);
    
    const modalContent = screen.getByTestId('modal-content');
    fireEvent.click(modalContent);
    
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('displays all key features', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    // Business Search features
    expect(screen.getByText(/Google Places API integration/)).toBeInTheDocument();
    expect(screen.getByText(/Zip code-based search/)).toBeInTheDocument();
    expect(screen.getByText(/Real-time website verification/)).toBeInTheDocument();
    expect(screen.getByText(/Progressive search/)).toBeInTheDocument();
    
    // Client Portfolio features
    expect(screen.getByText(/Comprehensive client management/)).toBeInTheDocument();
    expect(screen.getByText(/Service tracking/)).toBeInTheDocument();
    expect(screen.getByText(/Marketing campaign management/)).toBeInTheDocument();
    expect(screen.getByText(/KPI monitoring/)).toBeInTheDocument();
  });

  it('displays the complete tech stack', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    // Frontend
    expect(screen.getByText(/React 18/)).toBeInTheDocument();
    expect(screen.getByText(/TypeScript/)).toBeInTheDocument();
    expect(screen.getByText(/Jest & React Testing Library/)).toBeInTheDocument();
    
    // Tools
    expect(screen.getByText(/ESLint 9/)).toBeInTheDocument();
    expect(screen.getByText(/Husky/)).toBeInTheDocument();
    expect(screen.getByText(/Git hooks/)).toBeInTheDocument();
  });

  it('displays developer information', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    expect(screen.getByText(/Built with/)).toBeInTheDocument();
    expect(screen.getByText(/by the development team/)).toBeInTheDocument();
  });

  it('displays current version and coverage', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    expect(screen.getByText(/Version:/)).toBeInTheDocument();
    expect(screen.getByText(/1.3.0/)).toBeInTheDocument();
    expect(screen.getByText(/Test Coverage:/)).toBeInTheDocument();
    expect(screen.getByText(/91.92%/)).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveAttribute('aria-labelledby', 'about-title');
    expect(modal).toHaveAttribute('aria-modal', 'true');
    
    const closeButton = screen.getByLabelText('Close');
    expect(closeButton).toBeInTheDocument();
  });

  it('handles escape key to close modal', () => {
    const mockOnClose = jest.fn();
    render(<AboutMe isOpen onClose={mockOnClose} />);
    
    fireEvent.keyDown(window, { key: 'Escape', code: 'Escape' });
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('displays GitHub repository link', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    const githubLink = screen.getByText(/View on GitHub/);
    expect(githubLink).toBeInTheDocument();
    expect(githubLink.closest('a')).toHaveAttribute('href', 'https://github.com/your-repo/business-search-app');
    expect(githubLink.closest('a')).toHaveAttribute('target', '_blank');
    expect(githubLink.closest('a')).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('displays key statistics', () => {
    render(<AboutMe isOpen onClose={jest.fn()} />);
    
    expect(screen.getByText(/Total Tests:/)).toBeInTheDocument();
    expect(screen.getByText(/483/)).toBeInTheDocument();
    expect(screen.getByText(/Components:/)).toBeInTheDocument();
    expect(screen.getByText(/20\+/)).toBeInTheDocument();
  });
});