import { render, screen, fireEvent } from '@testing-library/react';
import SearchForm from './SearchForm';

describe('SearchForm', () => {
    it('renders all form fields and a submit button', () => {
        render(<SearchForm onSearch={() => {}} />);
        
        expect(screen.getByLabelText(/zip code/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/radius/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/business type/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/minimum rating/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/max price level/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/open now only/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
    });

    it('calls onSearch with form data when submitted', () => {
        const mockOnSearch = jest.fn();
        render(<SearchForm onSearch={mockOnSearch} />);

        fireEvent.change(screen.getByLabelText(/zip code/i), { target: { value: '94105' } });
        fireEvent.change(screen.getByLabelText(/radius/i), { target: { value: '10' } });
        fireEvent.change(screen.getByLabelText(/business type/i), { target: { value: 'restaurant' } });
        fireEvent.change(screen.getByLabelText(/minimum rating/i), { target: { value: '4' } });
        fireEvent.change(screen.getByLabelText(/max price level/i), { target: { value: '3' } });
        fireEvent.click(screen.getByLabelText(/open now only/i));

        fireEvent.click(screen.getByRole('button', { name: /search/i }));

        expect(mockOnSearch).toHaveBeenCalledWith({
            zipCode: '94105',
            radius: 10,
            businessType: 'restaurant',
            minRating: 4,
            maxPrice: 3,
            openNow: true,
        });
    });

    it('does not submit if zip code is missing', () => {
        const mockOnSearch = jest.fn();
        render(<SearchForm onSearch={mockOnSearch} />);

        // The input is `required`, so the browser should prevent submission.
        // In JSDOM, we can check if the form is invalid.
        const form = screen.getByRole('button', { name: /search/i }).closest('form');
        expect(form).not.toBeNull();
        if (form) {
            // This is a proxy for the browser's own validation enforcement
            expect(form.checkValidity()).toBe(false);
        }
    });
}); 