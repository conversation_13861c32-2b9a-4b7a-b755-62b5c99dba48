/**
 * Financial Formulas Module
 * 
 * This module contains all financial calculations used in the wealth tracker.
 * Each formula is documented with its mathematical representation and variable definitions.
 */

/**
 * D-1: Future Value with Contributions
 * Formula: FV = PV·(1+r)^n + C·[(1+r)^n − 1]/r
 * 
 * @param pv - Present Value: Current portfolio balance (USD)
 * @param r - Monthly return rate (decimal, e.g., 0.02 for 2%)
 * @param c - Monthly contribution amount (USD)
 * @param n - Number of months
 * @returns Future Value (USD)
 */
export function futureValueWithContributions(pv: number, r: number, c: number, n: number): number {
  console.log(`futureValueWithContributions: pv=${pv}, r=${r}, c=${c}, n=${n}`);
  if (r === 0) {
    console.log(`futureValueWithContributions: r is 0, returning pv + c * n = ${pv + c * n}`);
    return pv + c * n;
  }
  
  const growthFactor = Math.pow(1 + r, n);
  console.log(`futureValueWithContributions: growthFactor=${growthFactor}`);
  const futureValue = pv * growthFactor + c * ((growthFactor - 1) / r);
  console.log(`futureValueWithContributions: futureValue=${futureValue}`);
  
  return futureValue;
}

/**
 * D-3: Months to Target
 * Formula: n = ln[(T·r + C)/(PV·r + C)] / ln(1+r)
 * 
 * @param pv - Present Value: Current portfolio balance (USD)
 * @param target - Target portfolio value (USD)
 * @param r - Monthly return rate (decimal)
 * @param c - Monthly contribution amount (USD)
 * @returns Number of months to reach target
 */
export function monthsToTarget(pv: number, target: number, r: number, c: number): number {
  console.log(`monthsToTarget: pv=${pv}, target=${target}, r=${r}, c=${c}`);
  if (pv >= target) {
    console.log(`monthsToTarget: pv >= target, returning 0`);
    return 0;
  }
  
  if (r <= 0) {
    console.log(`monthsToTarget: r <= 0`);
    // Simple linear case
    const needed = target - pv;
    console.log(`monthsToTarget: needed=${needed}`);
    return Math.ceil(needed / c);
  }
  
  // Use the logarithmic formula
  const numerator = Math.log((target * r + c) / (pv * r + c));
  console.log(`monthsToTarget: numerator=${numerator}`);
  const denominator = Math.log(1 + r);
  console.log(`monthsToTarget: denominator=${denominator}`);
  
  const months = Math.max(0, Math.ceil(numerator / denominator));
  console.log(`monthsToTarget: months=${months}`);
  return months;
}

/**
 * Calculate Internal Rate of Return using Newton's method
 * 
 * @param cashFlows - Array of cash flows (negative = outflow, positive = inflow)
 * @returns IRR as decimal (e.g., 0.10 for 10%)
 */
export function internalRateOfReturn(cashFlows: number[]): number {
  console.log(`internalRateOfReturn: cashFlows=${cashFlows}`);
  let irr = 0.1; // Initial guess
  const maxIterations = 100;
  const tolerance = 1e-7;
  
  for (let i = 0; i < maxIterations; i++) {
    let npv = 0;
    let dnpv = 0;
    
    for (let t = 0; t < cashFlows.length; t++) {
      const discountFactor = Math.pow(1 + irr, -t);
      npv += cashFlows[t] * discountFactor;
      dnpv -= t * cashFlows[t] * discountFactor / (1 + irr);
    }
    
    console.log(`internalRateOfReturn: iteration ${i}, npv=${npv}, dnpv=${dnpv}`);

    if (Math.abs(npv) < tolerance) {
      console.log(`internalRateOfReturn: npv within tolerance, returning irr=${irr}`);
      return irr;
    }
    
    if (Math.abs(dnpv) < tolerance) {
      console.log(`internalRateOfReturn: dnpv within tolerance, breaking`);
      break;
    }
    
    const newIrr = irr - npv / dnpv;
    console.log(`internalRateOfReturn: newIrr=${newIrr}`);

    if (Math.abs(newIrr - irr) < tolerance) {
      console.log(`internalRateOfReturn: newIrr within tolerance, returning newIrr=${newIrr}`);
      return newIrr;
    }
    
    irr = newIrr;
  }
  
  console.log(`internalRateOfReturn: max iterations reached, returning irr=${irr}`);
  return irr;
}

/**
 * Calculate IRR for portfolio with regular contributions
 *
 * This function calculates the internal rate of return for a series of portfolio values
 * with regular monthly contributions. It models the cash flows and uses the
 * `internalRateOfReturn` function to find the discount rate.
 *
 * @param portfolioValues - Array of portfolio values over time (e.g., monthly).
 * @param monthlyContribution - Regular monthly contribution amount.
 * @returns Monthly IRR as a decimal.
 */
export function calculatePortfolioIRR(portfolioValues: number[], monthlyContribution: number): number {
  console.log(`calculatePortfolioIRR: portfolioValues=${portfolioValues}, monthlyContribution=${monthlyContribution}`);
  if (portfolioValues.length < 2) {
    console.log(`calculatePortfolioIRR: less than 2 portfolio values, returning 0`);
    return 0;
  }

  // The cash flows are:
  // 1. The initial portfolio value as an outflow (negative).
  // 2. Each monthly contribution as an outflow (negative).
  // 3. The final portfolio value as an inflow (positive) at the end.
  const cashFlows: number[] = [-portfolioValues[0]];
  console.log(`calculatePortfolioIRR: initial cashFlows=${cashFlows}`);
  for (let i = 1; i < portfolioValues.length; i++) {
    cashFlows.push(-monthlyContribution);
  }
  cashFlows[cashFlows.length - 1] += portfolioValues[portfolioValues.length - 1];
  console.log(`calculatePortfolioIRR: final cashFlows=${cashFlows}`);

  const irr = internalRateOfReturn(cashFlows);
  console.log(`calculatePortfolioIRR: calculated irr=${irr}`);
  return irr;
}

/**
 * D-2: Contribution-Stripped Period Return
 * Formula: period_r = (FV − PV − n·C) / (PV + 0.5·n·C)
 * 
 * @param pv - Present Value: Starting balance (USD)
 * @param fv - Future Value: Ending balance (USD)
 * @param totalContribution - Total contributions during period (USD)
 * @param months - Number of months in period
 * @returns Period return as decimal
 */
export function contributionStrippedPeriodReturn(
  pv: number, 
  fv: number, 
  totalContribution: number, 
  months: number
): number {
  console.log(`contributionStrippedPeriodReturn: pv=${pv}, fv=${fv}, totalContribution=${totalContribution}, months=${months}`);
  const investmentGain = fv - pv - totalContribution;
  console.log(`contributionStrippedPeriodReturn: investmentGain=${investmentGain}`);
  const adjustedStart = pv + 0.5 * totalContribution;
  console.log(`contributionStrippedPeriodReturn: adjustedStart=${adjustedStart}`);
  
  if (adjustedStart <= 0) {
    console.log(`contributionStrippedPeriodReturn: adjustedStart <= 0, returning 0`);
    return 0;
  }
  
  const periodReturn = investmentGain / adjustedStart;
  console.log(`contributionStrippedPeriodReturn: periodReturn=${periodReturn}`);
  return periodReturn;
}

/**
 * Convert period return to monthly return
 * Formula: monthly_r = (1 + period_r)^(1/n) − 1
 * 
 * @param periodReturn - Return for the entire period
 * @param months - Number of months in period
 * @returns Monthly return as decimal
 */
export function periodToMonthlyReturn(periodReturn: number, months: number): number {
  console.log(`periodToMonthlyReturn: periodReturn=${periodReturn}, months=${months}`);
  if (months <= 0) {
    console.log(`periodToMonthlyReturn: months <= 0, returning 0`);
    return 0;
  }
  
  const monthlyReturn = Math.pow(1 + periodReturn, 1 / months) - 1;
  console.log(`periodToMonthlyReturn: monthlyReturn=${monthlyReturn}`);
  return monthlyReturn;
}

/**
 * Calculate mean and standard deviation of returns
 * 
 * @param returns - Array of return values
 * @returns Object with mean and stdDev
 */
export function calculateStatistics(returns: number[]): { mean: number; stdDev: number } {
  console.log(`calculateStatistics: returns=${returns}`);
  if (returns.length === 0) {
    console.log(`calculateStatistics: returns is empty, returning { mean: 0, stdDev: 0 }`);
    return { mean: 0, stdDev: 0 };
  }
  
  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  console.log(`calculateStatistics: mean=${mean}`);
  
  if (returns.length === 1) {
    console.log(`calculateStatistics: single return, returning { mean: ${mean}, stdDev: 0 }`);
    return { mean, stdDev: 0 };
  }
  
  const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (returns.length - 1);
  console.log(`calculateStatistics: variance=${variance}`);
  const stdDev = Math.sqrt(variance);
  console.log(`calculateStatistics: stdDev=${stdDev}`);
  
  return { mean, stdDev };
}

/**
 * Calculate scenario rates (Conservative, Baseline, Optimistic)
 * 
 * @param irr - Internal Rate of Return
 * @param mean - Mean return after stripping contributions
 * @param stdDev - Standard deviation of returns
 * @returns Object with scenario rates
 */
export function calculateScenarioRates(
  irr: number, 
  mean: number, 
  stdDev: number
): { conservative: number; baseline: number; optimistic: number } {
  console.log(`calculateScenarioRates: irr=${irr}, mean=${mean}, stdDev=${stdDev}`);
  const conservative = Math.max(0.005, irr - stdDev);
  console.log(`calculateScenarioRates: conservative=${conservative}`);
  const baseline = irr;
  console.log(`calculateScenarioRates: baseline=${baseline}`);
  const optimistic = mean;
  console.log(`calculateScenarioRates: optimistic=${optimistic}`);
  return {
    conservative,
    baseline,
    optimistic
  };
}

/**
 * Convert monthly return to annual return
 * Formula: annual_r = (1 + monthly_r)^12 − 1
 * 
 * @param monthlyReturn - Monthly return as decimal
 * @returns Annual return as decimal
 */
export function monthlyToAnnualReturn(monthlyReturn: number): number {
  console.log(`monthlyToAnnualReturn: monthlyReturn=${monthlyReturn}`);
  const annualReturn = Math.pow(1 + monthlyReturn, 12) - 1;
  console.log(`monthlyToAnnualReturn: annualReturn=${annualReturn}`);
  return annualReturn;
}

/**
 * Calculate future value including annual bonus
 * 
 * @param pv - Present Value (USD)
 * @param monthlyRate - Monthly return rate
 * @param monthlyContribution - Monthly contribution
 * @param annualBonus - Annual bonus amount
 * @param months - Total months
 * @param bonusMonth - Month when bonus is paid (1-12)
 * @returns Future value including bonus effects
 */
export function futureValueWithBonus(
  pv: number,
  monthlyRate: number,
  monthlyContribution: number,
  annualBonus: number,
  months: number,
  bonusMonth: number
): number {
  console.log(`futureValueWithBonus: pv=${pv}, monthlyRate=${monthlyRate}, monthlyContribution=${monthlyContribution}, annualBonus=${annualBonus}, months=${months}, bonusMonth=${bonusMonth}`);
  // Start with regular future value
  let fv = futureValueWithContributions(pv, monthlyRate, monthlyContribution, months);
  console.log(`futureValueWithBonus: fv before bonus=${fv}`);
  
  // Add bonus if we cross the bonus month
  const currentMonth = new Date().getMonth() + 1;
  const endMonth = (currentMonth + months - 1) % 12 + 1;
  console.log(`futureValueWithBonus: currentMonth=${currentMonth}, endMonth=${endMonth}`);

  // Check if we cross bonus month
  if (months >= 12 || (currentMonth <= bonusMonth && endMonth >= bonusMonth)) {
    console.log(`futureValueWithBonus: crosses bonus month`);
    const monthsAfterBonus = months - (bonusMonth - currentMonth);
    console.log(`futureValueWithBonus: monthsAfterBonus=${monthsAfterBonus}`);
    if (monthsAfterBonus > 0) {
      fv += annualBonus * Math.pow(1 + monthlyRate, monthsAfterBonus);
      console.log(`futureValueWithBonus: fv after bonus=${fv}`);
    }
  }
  
  return fv;
}

/**
 * D-4: Delta months between two contribution plans
 * Formula: Δn = n(C₂) − n(C₁)
 * 
 * @param pv - Present Value
 * @param target - Target value
 * @param rate - Monthly rate
 * @param contribution1 - First contribution amount
 * @param contribution2 - Second contribution amount
 * @returns Difference in months
 */
export function deltaMonthsBetweenContributions(
  pv: number,
  target: number,
  rate: number,
  contribution1: number,
  contribution2: number
): number {
  console.log(`deltaMonthsBetweenContributions: pv=${pv}, target=${target}, rate=${rate}, contribution1=${contribution1}, contribution2=${contribution2}`);
  const months1 = monthsToTarget(pv, target, rate, contribution1);
  console.log(`deltaMonthsBetweenContributions: months1=${months1}`);
  const months2 = monthsToTarget(pv, target, rate, contribution2);
  console.log(`deltaMonthsBetweenContributions: months2=${months2}`);
  
  const delta = months2 - months1;
  console.log(`deltaMonthsBetweenContributions: delta=${delta}`);
  return delta;
}

/**
 * F-5: Contribution Share
 * Formula: contrFrac = (PMT·n)/(FV − PV)
 * 
 * @param pv - Present Value
 * @param fv - Future Value
 * @param pmt - Monthly payment/contribution
 * @param n - Number of months
 * @returns Fraction of growth due to contributions
 */
export function contributionShare(
  pv: number,
  fv: number,
  pmt: number,
  n: number
): number {
  console.log(`contributionShare: pv=${pv}, fv=${fv}, pmt=${pmt}, n=${n}`);
  const totalGrowth = fv - pv;
  console.log(`contributionShare: totalGrowth=${totalGrowth}`);
  if (totalGrowth <= 0) {
    console.log(`contributionShare: totalGrowth <= 0, returning 0`);
    return 0;
  }
  
  const totalContributions = pmt * n;
  console.log(`contributionShare: totalContributions=${totalContributions}`);
  const share = totalContributions / totalGrowth;
  console.log(`contributionShare: share=${share}`);
  return share;
}

/**
 * F-6: Required Contribution for Target in n Months
 * Formula: PMT* = ((FV − PV·(1+r)^n) · r) / ((1+r)^n − 1)
 *
 * @param pv - Present Value: Current portfolio balance (USD)
 * @param fv - Future Value: Target portfolio value (USD)
 * @param r - Monthly return rate (decimal)
 * @param n - Number of months
 * @returns Required monthly contribution (USD)
 */
export function requiredContributionForTarget(pv: number, fv: number, r: number, n: number): number {
  console.log(`requiredContributionForTarget: pv=${pv}, fv=${fv}, r=${r}, n=${n}`);

  if (r === 0) {
    const needed = fv - pv;
    const pmt = needed / n;
    console.log(`requiredContributionForTarget: r is 0, returning needed/n = ${pmt}`);
    return pmt;
  }

  const growthFactor = Math.pow(1 + r, n);
  const numerator = (fv - pv * growthFactor) * r;
  const denominator = growthFactor - 1;
  const pmt = numerator / denominator;

  console.log(`requiredContributionForTarget: growthFactor=${growthFactor}, numerator=${numerator}, denominator=${denominator}, pmt=${pmt}`);
  return pmt;
}

/**
 * F-8: Enhanced FV Component Split (Initial vs Contributions)
 * Formula: g=(1+r)^n; FV_init = PV·g; FV_contr = PMT·((g−1)/r); share_contr = FV_contr / (FV_init+FV_contr)
 *
 * @param pv - Present Value: Current portfolio balance (USD)
 * @param r - Monthly return rate (decimal)
 * @param pmt - Monthly contribution (USD)
 * @param n - Number of months
 * @returns Object with detailed breakdown
 */
export function componentSplit(pv: number, r: number, pmt: number, n: number): {
  FV_total: number;
  FV_from_PV: number;
  FV_from_contr: number;
  share_contr: number;
} {
  console.log(`componentSplit: pv=${pv}, r=${r}, pmt=${pmt}, n=${n}`);

  const growthFactor = Math.pow(1 + r, n);
  const FV_from_PV = pv * growthFactor;

  let FV_from_contr: number;
  if (r === 0) {
    FV_from_contr = pmt * n;
  } else {
    FV_from_contr = pmt * ((growthFactor - 1) / r);
  }

  const FV_total = FV_from_PV + FV_from_contr;
  const share_contr = FV_total > 0 ? FV_from_contr / FV_total : 0;

  console.log(`componentSplit: FV_total=${FV_total}, FV_from_PV=${FV_from_PV}, FV_from_contr=${FV_from_contr}, share_contr=${share_contr}`);

  return {
    FV_total: Math.round(FV_total),
    FV_from_PV: Math.round(FV_from_PV),
    FV_from_contr: Math.round(FV_from_contr),
    share_contr: Math.round(share_contr * 1000) / 1000 // Round to 3 decimal places
  };
}

/**
 * F-9: Coast-FIRE Threshold Balance
 * Formula: PV_coast = (12·PMT) / ((1+r)^12 − 1)
 *
 * @param monthlyRate - Monthly return rate (decimal)
 * @param monthlyContribution - Monthly contribution amount (USD)
 * @returns Portfolio balance where annual growth ≈ annual contributions
 */
export function coastFireThreshold(monthlyRate: number, monthlyContribution: number): number {
  console.log(`coastFireThreshold: monthlyRate=${monthlyRate}, monthlyContribution=${monthlyContribution}`);

  if (monthlyRate <= 0) {
    console.log(`coastFireThreshold: monthlyRate <= 0, returning Infinity`);
    return Infinity; // No growth means you never reach coast-FIRE
  }

  const annualContribution = 12 * monthlyContribution;
  const annualGrowthFactor = Math.pow(1 + monthlyRate, 12);
  const annualGrowthRate = annualGrowthFactor - 1;

  const coastBalance = annualContribution / annualGrowthRate;

  console.log(`coastFireThreshold: annualContribution=${annualContribution}, annualGrowthRate=${annualGrowthRate}, coastBalance=${coastBalance}`);
  return Math.round(coastBalance);
}