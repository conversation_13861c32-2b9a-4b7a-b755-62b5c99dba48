export interface PortfolioEntry {
  date: string;
  trow: number;
  robinhood: number;
  etrade: number;
  teradata: number;
  fidelity?: number;
}

export interface PayslipEntry {
  date: string;
  gross: number;
  net: number;
  espp: number;
  roth_e: number;
  roth_r: number;
  total_invest: number;
}

export interface WealthData {
  portfolio_history: PortfolioEntry[];
  payslip_history: PayslipEntry[];
}

export interface ScenarioRates {
  [key: string]: number;
}

export interface Statistics {
  meanReturn: number;
  stdDev: number;
}