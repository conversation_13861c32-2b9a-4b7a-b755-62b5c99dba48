import { PayslipEntry } from './types';

export class PayslipParser {
  static parsePayslip(payslipText: string): PayslipEntry {
    console.log('parsePayslip: start');
    console.log('parsePayslip: input length=', payslipText.length);
    const lines = payslipText.split('\n').map(line => line.trim());
    console.log('parsePayslip: lines count=', lines.length);
    let result: Partial<PayslipEntry> = {};

    // Extract Check Date
    const checkDate = this.extractCheckDate(lines);
    console.log('parsePayslip: date=', checkDate);
    if (checkDate) result.date = checkDate;

    // Extract Gross Pay
    const gross = this.extractGrossPay(lines);
    console.log('parsePayslip: gross=', gross);
    if (gross !== null) result.gross = gross;

    // Extract Net Pay
    const net = this.extractNetPay(lines);
    console.log('parsePayslip: net=', net);
    if (net !== null) result.net = net;

    // Extract Investments (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>)
    const { espp, roth_e, roth_r } = this.extractInvestments(lines);
    console.log('parsePayslip: investments=', { espp, roth_e, roth_r });
    result.espp = espp;
    result.roth_e = roth_e;
    result.roth_r = roth_r;
    result.total_invest = espp + roth_e + roth_r;
    console.log('parsePayslip: total_invest=', result.total_invest);

    // Validate required fields
    if (!result.date || result.gross === undefined || result.net === undefined) {
      console.error('parsePayslip: missing required fields', result);
      throw new Error('Unable to parse required payslip fields');
    }

    console.log('parsePayslip: result=', result);
    return result as PayslipEntry;
  }

  private static extractCheckDate(lines: string[]): string | null {
    console.log('extractCheckDate: start');
    const checkDateLine = lines.find(line => line.includes('Check Date'));
    console.log('extractCheckDate: checkDateLine=', checkDateLine);
    if (checkDateLine) {
      const dateMatch = checkDateLine.match(/(\d{2}\/\d{2}\/\d{4})/);
      console.log('extractCheckDate: dateMatch=', dateMatch);
      if (dateMatch) {
        const [month, day, year] = dateMatch[1].split('/');
        const formatted = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        console.log('extractCheckDate: formatted date=', formatted);
        return formatted;
      }
      console.warn('extractCheckDate: date regex did not match');
    } else {
      console.warn('extractCheckDate: no check date line found');
    }
    return null;
  }

  private static extractGrossPay(lines: string[]): number | null {
    console.log('extractGrossPay: start');
    const grossLine = lines.find(line => line.includes('Gross Pay'));
    console.log('extractGrossPay: grossLine=', grossLine);
    if (grossLine) {
      const amountMatch = grossLine.match(/([\d,]+\.\d{2})/);
      console.log('extractGrossPay: amountMatch=', amountMatch);
      if (amountMatch) {
        const value = parseFloat(amountMatch[1].replace(/,/g, ''));
        console.log('extractGrossPay: parsed value=', value);
        return value;
      }
      console.warn('extractGrossPay: amount regex did not match');
    } else {
      console.warn('extractGrossPay: no gross pay line found');
    }
    return null;
  }

  private static extractNetPay(lines: string[]): number | null {
    console.log('extractNetPay: start');
    const netLine = lines.find(line => line.includes('Net Pay'));
    console.log('extractNetPay: netLine=', netLine);
    if (netLine) {
      const amountMatch = netLine.match(/([\d,]+\.\d{2})/);
      console.log('extractNetPay: amountMatch=', amountMatch);
      if (amountMatch) {
        const value = parseFloat(amountMatch[1].replace(/,/g, ''));
        console.log('extractNetPay: parsed value=', value);
        return value;
      }
      console.warn('extractNetPay: amount regex did not match');
    } else {
      console.warn('extractNetPay: no net pay line found');
    }
    return null;
  }

  private static extractAmountAfterLabel(lines: string[], label: string): number | null {
    console.log('extractAmountAfterLabel: start, label=', label);
    for (let i = 0; i < lines.length; i++) {
      console.log(`extractAmountAfterLabel: line ${i}=`, lines[i]);
      if (lines[i].includes(label)) {
        console.log('extractAmountAfterLabel: found label at line', i);
        let match = lines[i].match(/([\d,]+\.\d{2})/);
        if (!match && i + 1 < lines.length) {
          console.log('extractAmountAfterLabel: checking next line for amount');
          match = lines[i + 1].match(/([\d,]+\.\d{2})/);
        }
        console.log('extractAmountAfterLabel: match=', match);
        if (match) {
          const value = parseFloat(match[1].replace(/,/g, ''));
          console.log('extractAmountAfterLabel: parsed value=', value);
          return value;
        }
      }
    }
    console.warn('extractAmountAfterLabel: no amount found for label', label);
    return null;
  }

  private static extractInvestments(lines: string[]): { espp: number; roth_e: number; roth_r: number } {
    console.log('extractInvestments: start');
    let espp = 0;
    let roth_e = 0;
    let roth_r = 0;
    lines.forEach((raw, idx) => {
      const line = raw.trim();
      console.log(`extractInvestments: line ${idx}=`, line);
      if (line === 'ESPP') {
        espp = this.parseAmount(lines[idx + 1]) ?? 0;
        console.log('extractInvestments: ESPP amount=', espp);
      }
      if (line === 'GapShare Roth E') {
        roth_e = this.parseAmount(lines[idx + 1]) ?? 0;
        console.log('extractInvestments: Roth E amount=', roth_e);
      }
      if (line === 'GapShare Roth R') {
        roth_r = this.parseAmount(lines[idx + 1]) ?? 0;
        console.log('extractInvestments: Roth R amount=', roth_r);
      }
    });
    console.log('extractInvestments: result=', { espp, roth_e, roth_r });
    return { espp, roth_e, roth_r };
  }

  private static parseAmount(str?: string): number | null {
    console.log('parseAmount: input=', str);
    if (!str) {
      console.warn('parseAmount: empty input');
      return null;
    }
    const cleaned = str.replace(/[$,]/g, '');
    console.log('parseAmount: cleaned=', cleaned);
    const num = parseFloat(cleaned);
    console.log('parseAmount: parsed num=', num);
    if (isNaN(num)) {
      console.error('parseAmount: NaN parsed from', cleaned);
      return null;
    }
    return num;
  }

  static parseMultiplePayslips(texts: string[]): PayslipEntry[] {
    console.log('parseMultiplePayslips: start, count=', texts.length);
    const payslips: PayslipEntry[] = [];
    texts.forEach((t, idx) => {
      console.log('parseMultiplePayslips: parsing index', idx);
      try {
        payslips.push(this.parsePayslip(t));
      } catch (error) {
        console.error('parseMultiplePayslips: failed to parse payslip at index', idx, error);
      }
    });
    const sorted = payslips.sort((a, b) => a.date.localeCompare(b.date));
    console.log('parseMultiplePayslips: parsed count=', sorted.length);
    return sorted;
  }

  static validatePayslipData(entry: PayslipEntry): boolean {
    console.log('validatePayslipData: start, entry=', entry);
    const dateValid = /^\d{4}-\d{2}-\d{2}$/.test(entry.date);
    console.log('validatePayslipData: dateValid=', dateValid);
    if (!dateValid) return false;
    const nonNegative = entry.gross >= 0 && entry.net >= 0 && entry.espp >= 0 && entry.roth_e >= 0 && entry.roth_r >= 0;
    console.log('validatePayslipData: nonNegative=', nonNegative);
    if (!nonNegative) return false;
    const sum = entry.espp + entry.roth_e + entry.roth_r;
    const investValid = Math.abs(entry.total_invest - sum) < 0.01;
    console.log('validatePayslipData: investValid=', investValid);
    return investValid;
  }

  static formatPayslipData(entries: PayslipEntry[]): string {
    console.log('formatPayslipData: start, entriesCount=', entries.length);
    let output = 'export const GENERATED_PAYSLIP_DATA: PayslipEntry[] = [\n';
    entries.forEach((e, i) => {
      console.log('formatPayslipData: formatting entry', e);
      output += `  { date: \"${e.date}\", gross: ${e.gross}, net: ${e.net}, espp: ${e.espp}, roth_e: ${e.roth_e}, roth_r: ${e.roth_r}, total_invest: ${e.total_invest} }${i < entries.length - 1 ? ',' : ''}\n`;
    });
    output += '];';
    console.log('formatPayslipData: output length=', output.length);
    return output;
  }
}
