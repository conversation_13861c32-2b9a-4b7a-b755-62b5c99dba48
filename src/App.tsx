import { useState } from 'react';

import { BusinessSearchApp, type AppConfig } from './BusinessSearchApp';
import AboutMe from './components/AboutMe';
import BusinessSearchTab from './components/tabs/BusinessSearchTab';
import ClientPortfolioTab from './components/tabs/ClientPortfolioTab';
import TabNavigation, { type TabKey } from './components/tabs/TabNavigation';

// This would come from a config file or environment variables
const config: AppConfig = {
  apiKey: process.env.REACT_APP_GOOGLE_API_KEY || 'mock_key',
};

interface AppProps {
  businessSearchApp?: BusinessSearchApp;
}

function App({ businessSearchApp: injectedApp }: AppProps = {}) {
  const [activeTab, setActiveTab] = useState<TabKey>('search');
  const [showAbout, setShowAbout] = useState(false);
  
  // Initialize business search app
  const businessSearchApp = injectedApp || new BusinessSearchApp(config);

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#fafafa' }}>
      <TabNavigation 
        activeTab={activeTab} 
        onTabChange={setActiveTab}
        onAboutClick={() => setShowAbout(true)}
      />
      
      <div style={{ backgroundColor: '#ffffff', minHeight: 'calc(100vh - 60px)' }}>
        {activeTab === 'search' && (
          <BusinessSearchTab businessSearchApp={businessSearchApp} />
        )}
        
        {activeTab === 'portfolio' && (
          <ClientPortfolioTab />
        )}
      </div>

      <AboutMe 
        isOpen={showAbout} 
        onClose={() => setShowAbout(false)} 
      />
    </div>
  );
}

export default App;
