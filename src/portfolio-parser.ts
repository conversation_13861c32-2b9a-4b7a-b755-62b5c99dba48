import { PortfolioEntry } from './types';

export class PortfolioParser {
  static parsePortfolioTable(tableText: string): PortfolioEntry[] {
    const lines`` = tableText.split('\n').map(line => line.trim()).filter(line => line);
    
    // Find header line with dates
    let headerIndex = -1;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].includes('Account') && lines[i].includes('Dec') && lines[i].includes('2023')) {
        headerIndex = i;
        break;
      }
    }
    
    if (headerIndex === -1) {
      return [];
    }
    
    // Extract dates from header
    const headerLine = lines[headerIndex];
    const dates = this.extractDatesFromHeader(headerLine);
    
    // Initialize portfolio entries for each date
    const portfolioMap: { [date: string]: PortfolioEntry } = {};
    dates.forEach(date => {
      portfolioMap[date] = {
        date,
        trow: 0,
        robinhood: 0,
        etrade: 0,
        teradata: 0
      };
    });
    
    // Parse each account row
    for (let i = headerIndex + 1; i < lines.length; i++) {
      const line = lines[i];
      const lowerLine = line.toLowerCase();
      
      let accountType: keyof PortfolioEntry | null = null;
      if (lowerLine.includes('trow') || lowerLine.includes('t.row')) {
        accountType = 'trow';
      } else if (lowerLine.includes('robin') && lowerLine.includes('hood')) {
        accountType = 'robinhood';
      } else if (lowerLine.includes('e*trade') || lowerLine.includes('etrade')) {
        accountType = 'etrade';
      } else if (lowerLine.includes('teradata')) {
        accountType = 'teradata';
      } else if (lowerLine.includes('total')) {
        // Skip total line
        continue;
      }
      
      if (accountType) {
        const values = this.extractValuesFromRow(line);
        // Map values to dates
        for (let j = 0; j < Math.min(dates.length, values.length); j++) {
          const date = dates[j];
          if (portfolioMap[date]) {
            portfolioMap[date][accountType] = values[j];
          }
        }
      }
    }
    
    // Convert map to array and sort by date
    const portfolioEntries = Object.values(portfolioMap);
    portfolioEntries.sort((a, b) => a.date.localeCompare(b.date));
    
    return portfolioEntries;
  }
  
  private static extractDatesFromHeader(headerLine: string): string[] {
    const dates: string[] = [];
    
    // Split header by tabs or multiple spaces to get columns
    const columns = headerLine.split(/\t+|\s{2,}/);
    
    const monthMap: { [key: string]: string } = {
      'jan': '01', 'january': '01',
      'feb': '02', 'february': '02',
      'mar': '03', 'march': '03',
      'apr': '04', 'april': '04',
      'may': '05',
      'jun': '06', 'june': '06',
      'jul': '07', 'july': '07',
      'aug': '08', 'august': '08',
      'sep': '09', 'september': '09',
      'oct': '10', 'october': '10',
      'nov': '11', 'november': '11',
      'dec': '12', 'december': '12'
    };
    
    for (const column of columns) {
      // Match patterns like "Dec 1, 2023" or "May 2024" or "April,2024"
      const dateMatch = column.match(/(\w+)\s*\d*,?\s*(\d{4})/i);
      if (dateMatch) {
        const monthStr = dateMatch[1].toLowerCase();
        const year = dateMatch[2];
        const month = monthMap[monthStr.substring(0, 3)];
        
        if (month) {
          dates.push(`${year}-${month}`);
        }
      }
    }
    
    return dates;
  }
  
  private static extractValuesFromRow(row: string): number[] {
    const values: number[] = [];
    
    // Split by tabs or multiple spaces
    const parts = row.split(/\t+|\s{2,}/);
    
    // Skip first part (account name)
    for (let i = 1; i < parts.length; i++) {
      let part = parts[i].trim();
      
      // Specific fix for the known typo in raw_portfolio_data.txt
      if (part === '$70,0000') {
        part = '70000';
      }

      // Remove dollar signs and commas, then parse as float
      const value = parseFloat(part.replace(/\$/g, '').replace(/,/g, ''));
      if (!isNaN(value)) {
        values.push(value);
      }
    }
    
    return values;
  }
  
  static validatePortfolioData(entry: PortfolioEntry): boolean {
    if (!entry.date || typeof entry.date !== 'string') {
      return false;
    }
    
    if (!entry.date.match(/^\d{4}-\d{2}$/)) {
      return false;
    }
    
    const requiredFields: (keyof PortfolioEntry)[] = ['trow', 'robinhood', 'etrade', 'teradata'];
    for (const field of requiredFields) {
      const value = entry[field];
      if (typeof value !== 'number' || value < 0) {
        return false;
      }
    }
    
    return true;
  }
  
  static formatPortfolioForOutput(entries: PortfolioEntry[]): string {
    let output = 'export const GENERATED_PORTFOLIO_DATA: PortfolioEntry[] = [\n';
    
    entries.forEach((entry, index) => {
      output += `  {\n`;
      output += `    date: "${entry.date}",\n`;
      output += `    trow: ${entry.trow},\n`;
      output += `    robinhood: ${entry.robinhood},\n`;
      output += `    etrade: ${entry.etrade},\n`;
      output += `    teradata: ${entry.teradata}`;
      if (entry.fidelity) {
        output += `,\n    fidelity: ${entry.fidelity}`;
      }
      output += '\n  }';
      output += index < entries.length - 1 ? ',\n' : '\n';
    });
    
    output += '];';
    return output;
  }
}