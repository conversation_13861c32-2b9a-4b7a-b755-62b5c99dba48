import { DataManager } from '../../services/dataManager';
import { SearchResult, Business } from '../../models/Business';
import { CacheError } from '../../models/Errors';

// --- Mocks ---

// A simple, reliable localStorage mock.
let localStorageMock: { [key: string]: string } = {};

Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: (key: string) => {
      console.log(`🔍 localStorage.getItem called with key: ${key}, returning: ${localStorageMock[key] || null}`);
      return localStorageMock[key] || null;
    },
    setItem: (key: string, value: string) => {
      console.log(`💾 localStorage.setItem called with key: ${key}, value: ${value.substring(0, 100)}...`);
      localStorageMock[key] = value.toString();
    },
    removeItem: (key: string) => {
      console.log(`🗑️ localStorage.removeItem called with key: ${key}`);
      delete localStorageMock[key];
    },
    clear: () => {
      console.log('🧹 localStorage.clear called');
      localStorageMock = {};
    },
    key: (index: number) => {
      const keys = Object.keys(localStorageMock);
      console.log(`🔑 localStorage.key called with index: ${index}, returning: ${keys[index] || null}`);
      return keys[index] || null;
    },
    get length() {
      const length = Object.keys(localStorageMock).length;
      console.log(`📏 localStorage.length called, returning: ${length}`);
      return length;
    },
  },
  writable: true,
});

const mockSearchResult: SearchResult = {
  searchParams: {
    zipCode: '10001',
    radius: 10,
    businessType: 'restaurant',
    timestamp: new Date('2023-01-01T00:00:00Z'),
  },
  results: {
    withWebsites: [{ id: '1', name: 'Restaurant A', address: '123 Main St', websiteStatus: 'verified', category: ['restaurant'], distance: 1.5, metadata: { lastUpdated: new Date() } } as Business],
    withoutWebsites: [{ id: '2', name: 'Restaurant B', address: '456 Oak Ave', websiteStatus: 'none', category: ['restaurant'], distance: 2.1, metadata: { lastUpdated: new Date() } } as Business],
  },
  statistics: { totalFound: 2, withWebsiteCount: 1, withoutWebsiteCount: 1, websiteAdoptionRate: 0.5, searchDuration: 1500 },
};

// --- Tests ---

describe('DataManager', () => {
  let dataManager: DataManager;

  beforeEach(() => {
    console.log('🔧 Setting up DataManager test...');
    // Reset data and instantiate a new DataManager before each test
    localStorageMock = {};
    dataManager = new DataManager();
    console.log('✅ DataManager test setup complete');
  });

  describe('Core Functionality', () => {
    it('should save and retrieve a search result', () => {
      console.log('🧪 Testing save and retrieve search result');
      const key = dataManager.saveSearchResult(mockSearchResult);
      console.log(`📝 Saved search result with key: ${key}`);
      const retrieved = dataManager.getSearchResult(key);
      console.log(`📖 Retrieved search result: ${retrieved ? 'found' : 'null'}`);
      expect(retrieved).not.toBeNull();
      expect(retrieved?.searchParams.zipCode).toBe('10001');
      console.log('✅ Save and retrieve test passed');
    });

    it('should return null for a non-existent key', () => {
      console.log('🧪 Testing retrieval of non-existent key');
      const retrieved = dataManager.getSearchResult('non-existent-key');
      console.log(`📖 Retrieved non-existent key result: ${retrieved}`);
      expect(retrieved).toBeNull();
      console.log('✅ Non-existent key test passed');
    });

    it('should return all search results', () => {
      console.log('🧪 Testing getAllSearchResults');
      dataManager.saveSearchResult(mockSearchResult);
      dataManager.saveSearchResult({ ...mockSearchResult, searchParams: { ...mockSearchResult.searchParams, zipCode: '10002' }});
      const allResults = dataManager.getAllSearchResults();
      console.log(`📊 Found ${allResults.length} search results`);
      expect(allResults).toHaveLength(2);
      console.log('✅ GetAllSearchResults test passed');
    });

    it('should delete a specific search result', () => {
      console.log('🧪 Testing deleteSearchResult');
      const key = dataManager.saveSearchResult(mockSearchResult);
      console.log(`📝 Saved search result with key: ${key}`);
      dataManager.deleteSearchResult(key);
      console.log(`🗑️ Deleted search result with key: ${key}`);
      const retrieved = dataManager.getSearchResult(key);
      console.log(`📖 Retrieved deleted result: ${retrieved}`);
      expect(retrieved).toBeNull();
      console.log('✅ Delete test passed');
    });

    it('should clear all business-related data', () => {
      console.log('🧪 Testing clearAllData');
      dataManager.saveSearchResult(mockSearchResult);
      window.localStorage.setItem('other_data', 'should not be deleted');
      console.log('📝 Added other data to localStorage');
      dataManager.clearAllData();
      console.log('🧹 Cleared all business data');
      expect(dataManager.getAllSearchResults()).toHaveLength(0);
      expect(window.localStorage.getItem('other_data')).toBe('should not be deleted');
      console.log('✅ ClearAllData test passed');
    });
  });

  describe('Expiration and Cleanup', () => {
    it('should not retrieve expired data', () => {
      console.log('🧪 Testing expired data retrieval');
      const key = dataManager.saveSearchResult(mockSearchResult);
      const stored = JSON.parse(localStorageMock[key]);
      stored.expiresAt = Date.now() - 1000; // Manually expire
      localStorageMock[key] = JSON.stringify(stored);
      console.log(`⏰ Manually expired data for key: ${key}`);
      
      const retrieved = dataManager.getSearchResult(key);
      console.log(`📖 Retrieved expired result: ${retrieved}`);
      expect(retrieved).toBeNull();
      console.log('✅ Expired data test passed');
    });
    
    it('should remove expired entries on cleanup', () => {
      console.log('🧪 Testing cleanup of expired entries');
      dataManager.saveSearchResult(mockSearchResult); // Valid
      const expiredKey = dataManager.saveSearchResult({ ...mockSearchResult, searchParams: { ...mockSearchResult.searchParams, zipCode: '99999'}});
      
      const stored = JSON.parse(localStorageMock[expiredKey]);
      stored.expiresAt = Date.now() - 1000; // Manually expire
      localStorageMock[expiredKey] = JSON.stringify(stored);
      console.log(`⏰ Manually expired data for key: ${expiredKey}`);

      const removedCount = dataManager.cleanupExpiredData();
      console.log(`🧹 Cleaned up ${removedCount} expired entries`);
      expect(removedCount).toBe(1);
      expect(dataManager.getAllSearchResults()).toHaveLength(1);
      console.log('✅ Cleanup test passed');
    });
  });

  describe('Import and Export', () => {
    it('should export data correctly', () => {
      console.log('🧪 Testing exportData');
      dataManager.saveSearchResult(mockSearchResult);
      const exportData = dataManager.exportData();
      console.log(`📤 Exported data: ${exportData.substring(0, 100)}...`);
      const parsed = JSON.parse(exportData);
      expect(parsed.version).toBe('1.0');
      expect(parsed.searchResults).toHaveLength(1);
      console.log('✅ Export test passed');
    });

    it('should import data correctly, clearing existing data', () => {
      console.log('🧪 Testing importData');
      dataManager.saveSearchResult({ ...mockSearchResult, searchParams: { ...mockSearchResult.searchParams, zipCode: 'original' }});
      
      const newExportData = JSON.stringify({
          version: '1.0',
          exportDate: new Date().toISOString(),
          searchResults: [mockSearchResult]
      });
      console.log(`📥 Importing data: ${newExportData.substring(0, 100)}...`);
      
      dataManager.clearAllData(); // Explicitly clear before import
      const importedCount = dataManager.importData(newExportData);
      console.log(`📥 Imported ${importedCount} search results`);
      
      expect(importedCount).toBe(1);
      const results = dataManager.getAllSearchResults();
      expect(results).toHaveLength(1);
      expect(results[0].searchParams.zipCode).toBe('10001');
      console.log('✅ Import test passed');
    });

    it('should handle invalid import JSON', () => {
      console.log('🧪 Testing invalid JSON import');
      expect(() => dataManager.importData('invalid-json')).toThrow(CacheError);
      console.log('✅ Invalid JSON test passed');
    });
  });

  describe('Storage Info', () => {
    it('should provide correct storage information', () => {
      console.log('🧪 Testing getStorageInfo');
      dataManager.saveSearchResult(mockSearchResult);
      const info = dataManager.getStorageInfo();
      console.log(`📊 Storage info: ${JSON.stringify(info)}`);
      expect(info.totalEntries).toBe(1);
      expect(info.totalSizeBytes).toBeGreaterThan(0);
      console.log('✅ Storage info test passed');
    });
  });
});
