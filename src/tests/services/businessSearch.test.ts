import { type Coordinates } from '../../models/Business';
import { ValidationError } from '../../models/Errors';
import { BusinessSearchService } from '../../services/businessSearch';
import { GeocodingService } from '../../services/geocoding';
import { GooglePlacesService } from '../../services/googlePlaces';
import { WebsiteVerificationService } from '../../services/websiteVerification';

// Mock the services
jest.mock('../../services/geocoding');
jest.mock('../../services/googlePlaces');
jest.mock('../../services/websiteVerification');

const MockedGeocodingService = GeocodingService as jest.MockedClass<typeof GeocodingService>;
const MockedGooglePlacesService = GooglePlacesService as jest.MockedClass<typeof GooglePlacesService>;
const MockedWebsiteVerificationService = WebsiteVerificationService as jest.MockedClass<typeof WebsiteVerificationService>;

describe('BusinessSearchService', () => {
  let businessSearchService: BusinessSearchService;
  let geocodingService: jest.Mocked<GeocodingService>;
  let googlePlacesService: jest.Mocked<GooglePlacesService>;
  let mockWebsiteVerificationService: jest.Mocked<WebsiteVerificationService>;

  beforeEach(() => {
    console.log('🔧 Setting up BusinessSearchService test...');
    // Clear all mocks
    jest.clearAllMocks();

    // Create mock instances
    geocodingService = new MockedGeocodingService() as jest.Mocked<GeocodingService>;
    googlePlacesService = new MockedGooglePlacesService('dummy-api-key') as jest.Mocked<GooglePlacesService>;
    mockWebsiteVerificationService = new MockedWebsiteVerificationService() as jest.Mocked<WebsiteVerificationService>;

    businessSearchService = new BusinessSearchService(geocodingService, googlePlacesService, mockWebsiteVerificationService);

    // Further mock implementations as needed
    geocodingService.zipCodeToCoordinates = jest.fn();
    googlePlacesService.searchNearby = jest.fn();
    mockWebsiteVerificationService.verifyMultipleWebsites = jest.fn();
    console.log('✅ BusinessSearchService test setup complete');
  });

  describe('searchBusinesses', () => {
    const mockCoordinates: Coordinates = {
      latitude: 40.7128,
      longitude: -74.0060
    };

    const mockPlacesResult = {
      places: [
        {
          place_id: '1',
          name: 'Restaurant A',
          formatted_address: '123 Main St, New York, NY',
          website: 'https://restaurant-a.com',
          types: ['restaurant'],
          rating: 4.5,
          geometry: {
            location: { lat: 40.7128, lng: -74.0060 }
          }
        },
        {
          place_id: '2',
          name: 'Restaurant B',
          formatted_address: '456 Oak Ave, New York, NY',
          types: ['restaurant'],
          rating: 4.0,
          geometry: {
            location: { lat: 40.7130, lng: -74.0062 }
          }
        }
      ],
      totalResults: 2,
      nextPageToken: undefined
    };

    const mockVerificationResults = [
      {
        url: 'https://restaurant-a.com',
        status: 'verified' as const,
        accessible: true,
        confidence: 0.9,
        verifiedAt: new Date()
      }
    ];

    it('should search businesses successfully', async () => {
      console.log('🧪 Testing successful business search');
      // Setup mocks
      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue(mockVerificationResults);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      console.log(`📊 Search result: ${result.results.withWebsites.length} with websites, ${result.results.withoutWebsites.length} without websites`);
      expect(result.results.withWebsites).toHaveLength(1);
      expect(result.results.withoutWebsites).toHaveLength(1);
      expect(result.statistics.totalFound).toBe(2);
      expect(result.statistics.withWebsiteCount).toBe(1);
      expect(result.statistics.withoutWebsiteCount).toBe(1);
      expect(result.statistics.websiteAdoptionRate).toBe(0.5);

      // Verify service calls
      expect(geocodingService.zipCodeToCoordinates).toHaveBeenCalledWith('10001');
      expect(googlePlacesService.searchNearby).toHaveBeenCalledWith(
        mockCoordinates,
        16093.4, // 10 miles in meters
        'restaurant',
        {}
      );
      expect(mockWebsiteVerificationService.verifyMultipleWebsites).toHaveBeenCalledWith(
        ['https://restaurant-a.com'],
        5
      );
      console.log('✅ Successful business search test passed');
    });

    it('should handle businesses without websites', async () => {
      console.log('🧪 Testing businesses without websites');
      const placesWithoutWebsites = {
        places: [
          {
            place_id: '1',
            name: 'Restaurant A',
            formatted_address: '123 Main St, New York, NY',
            types: ['restaurant'],
            rating: 4.5,
            geometry: {
              location: { lat: 40.7128, lng: -74.0060 }
            }
          }
        ],
        totalResults: 1,
        nextPageToken: undefined
      };

      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(placesWithoutWebsites);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      console.log(`📊 Search result: ${result.results.withWebsites.length} with websites, ${result.results.withoutWebsites.length} without websites`);
      expect(result.results.withWebsites).toHaveLength(0);
      expect(result.results.withoutWebsites).toHaveLength(1);
      expect(result.statistics.websiteAdoptionRate).toBe(0);
      console.log('✅ Businesses without websites test passed');
    });

    it('should validate search parameters', async () => {
      console.log('🧪 Testing search parameter validation');
      await expect(businessSearchService.searchBusinesses({
        zipCode: 'invalid',
        radius: 10,
        businessType: 'restaurant'
      })).rejects.toThrow(ValidationError);

      await expect(businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 0,
        businessType: 'restaurant'
      })).rejects.toThrow(ValidationError);

      await expect(businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'invalid_type'
      })).rejects.toThrow(ValidationError);
      console.log('✅ Search parameter validation test passed');
    });

    it('should handle geocoding errors gracefully', async () => {
      console.log('🧪 Testing geocoding error handling');
      geocodingService.zipCodeToCoordinates.mockRejectedValue(new Error('Geocoding failed'));

      await expect(businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      })).rejects.toThrow('Geocoding failed');
      console.log('✅ Geocoding error handling test passed');
    });

    it('should calculate distances correctly', async () => {
      console.log('🧪 Testing distance calculation');
      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      // Check that distances are calculated
      expect(result.results.withoutWebsites[0].distance).toBeGreaterThanOrEqual(0);
      expect(result.results.withoutWebsites[1].distance).toBeGreaterThanOrEqual(0);
      console.log('✅ Distance calculation test passed');
    });

    it('should sort results by distance', async () => {
      console.log('🧪 Testing distance sorting');
      const placesWithDifferentDistances = {
        places: [
          {
            place_id: '1',
            name: 'Restaurant A',
            formatted_address: '123 Main St, New York, NY',
            types: ['restaurant'],
            rating: 4.5,
            geometry: {
              location: { lat: 40.7128, lng: -74.0060 }
            }
          },
          {
            place_id: '2',
            name: 'Restaurant B',
            formatted_address: '456 Oak Ave, New York, NY',
            types: ['restaurant'],
            rating: 4.0,
            geometry: {
              location: { lat: 40.7130, lng: -74.0062 }
            }
          }
        ],
        totalResults: 2,
        nextPageToken: undefined
      };

      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(placesWithDifferentDistances);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockResolvedValue([]);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      // Results should be sorted by distance (closest first)
      expect(result.results.withoutWebsites[0].distance).toBeLessThanOrEqual(result.results.withoutWebsites[1].distance);
      console.log('✅ Distance sorting test passed');
    });

    it('should handle website verification errors gracefully', async () => {
      console.log('🧪 Testing website verification error handling');
      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(mockPlacesResult);
      mockWebsiteVerificationService.verifyMultipleWebsites.mockRejectedValue(new Error('Verification failed'));

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      // Should still return results even if website verification fails
      expect(result.results.withoutWebsites).toHaveLength(2);
      expect(result.statistics.totalFound).toBe(2);
      console.log('✅ Website verification error handling test passed');
    });

    it('should handle empty search results', async () => {
      console.log('🧪 Testing empty search results');
      const emptyPlacesResult = {
        places: [],
        totalResults: 0,
        nextPageToken: undefined
      };

      geocodingService.zipCodeToCoordinates.mockResolvedValue(mockCoordinates);
      googlePlacesService.searchNearby.mockResolvedValue(emptyPlacesResult);

      const result = await businessSearchService.searchBusinesses({
        zipCode: '10001',
        radius: 10,
        businessType: 'restaurant'
      });

      expect(result.results.withWebsites).toHaveLength(0);
      expect(result.results.withoutWebsites).toHaveLength(0);
      expect(result.statistics.totalFound).toBe(0);
      expect(result.statistics.websiteAdoptionRate).toBe(0);
      console.log('✅ Empty search results test passed');
    });
  });

  describe('Search History', () => {
    it('should maintain search history', () => {
      console.log('🧪 Testing search history maintenance');
      const history = businessSearchService.getSearchHistory();
      expect(Array.isArray(history)).toBe(true);
      console.log('✅ Search history test passed');
    });

    it('should clear search history', () => {
      console.log('🧪 Testing search history clearing');
      businessSearchService.clearSearchHistory();
      const history = businessSearchService.getSearchHistory();
      expect(history).toHaveLength(0);
      console.log('✅ Search history clearing test passed');
    });
  });
});
