import React from 'react';
import ReactDOM from 'react-dom/client';

console.log('--- Simplified index.tsx is running ---');

try {
  const rootElement = document.getElementById('root');
  if (rootElement) {
    console.log('--- Root element found, rendering test content. ---');
    const root = ReactDOM.createRoot(rootElement);
    root.render(
      <React.StrictMode>
        <h1 style={{ fontFamily: 'sans-serif', color: '#333', padding: '2rem' }}>
          Test Render: It Works!
        </h1>
        <p style={{ fontFamily: 'sans-serif', color: '#555', padding: '0 2rem' }}>
          If you can see this, the build process is working correctly. The issue is within the application's components.
        </p>
      </React.StrictMode>
    );
  } else {
    console.error('--- CRITICAL: Root element #root not found! ---');
    document.body.innerHTML = '<h1 style="color: red;">Error: Root element not found.</h1>';
  }
} catch (e) {
  console.error('--- CRITICAL: Error during simplified render ---', e);
  document.body.innerHTML = '<h1 style="color: red;">Error: A critical exception occurred during render. Check console.</h1>';
} 