import { Event } from '../parser/types';
import { EventPlugin } from './EventPlugin';
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { join } from 'path';

/**
 * Configuration for a plugin instance
 */
export interface PluginConfiguration {
  id: string;
  pluginType: string;
  name: string;
  enabled: boolean;
  config: any;
}

/**
 * Modular event system that manages plugins
 */
export class ModularEventSystem {
  private plugins: Map<string, EventPlugin> = new Map();
  private pluginFactories: Map<string, () => EventPlugin> = new Map();

  constructor() {
    this.registerDefaultPluginFactories();
  }

  /**
   * Register a plugin factory
   */
  registerPluginFactory(type: string, factory: () => EventPlugin): void {
    this.pluginFactories.set(type, factory);
  }

  /**
   * Add a plugin instance
   */
  addPlugin(instanceId: string, plugin: EventPlugin): void {
    this.plugins.set(instanceId, plugin);
  }

  /**
   * Remove a plugin instance
   */
  removePlugin(instanceId: string): void {
    this.plugins.delete(instanceId);
  }

  /**
   * Get all events for a specific date
   */
  getEventsForDate(date: Date): Event[] {
    const allEvents: Event[] = [];

    for (const plugin of this.plugins.values()) {
      const events = plugin.generateEvents(date);
      allEvents.push(...events);
    }

    return allEvents;
  }

  /**
   * Get events for a date range
   */
  getEventsForDateRange(startDate: Date, endDate: Date): Event[] {
    const allEvents: Event[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const events = this.getEventsForDate(currentDate);
      allEvents.push(...events);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return allEvents;
  }

  /**
   * Load configuration from JSON file
   */
  loadConfiguration(configPath?: string): void {
    const path = configPath || join(process.cwd(), 'config', 'event-plugins.json');

    if (!existsSync(path)) {
      console.log('No plugin configuration found, using defaults');
      return;
    }

    try {
      const configData = readFileSync(path, 'utf-8');
      const config = JSON.parse(configData) as { plugins: PluginConfiguration[] };

      // Clear existing plugins
      this.plugins.clear();

      // Load each plugin
      for (const pluginConfig of config.plugins) {
        if (!pluginConfig.enabled) continue;

        const factory = this.pluginFactories.get(pluginConfig.pluginType);
        if (!factory) {
          console.warn(`Unknown plugin type: ${pluginConfig.pluginType}`);
          continue;
        }

        const plugin = factory();
        if (plugin.configure) {
          plugin.configure(pluginConfig.config);
        }

        this.addPlugin(pluginConfig.id, plugin);
        console.log(`📅 Loaded plugin: ${pluginConfig.name}`);
      }
    } catch (error) {
      console.error('Failed to load plugin configuration:', error);
    }
  }

  /**
   * Save current configuration to JSON file
   */
  saveConfiguration(configPath?: string): void {
    const path = configPath || join(process.cwd(), 'config', 'event-plugins.json');
    const config = { plugins: this.exportConfiguration() };

    writeFileSync(path, JSON.stringify(config, null, 2));
    console.log(`Configuration saved to ${path}`);
  }

  /**
   * Export current configuration
   */
  exportConfiguration(): PluginConfiguration[] {
    const configs: PluginConfiguration[] = [];

    for (const [id, plugin] of this.plugins.entries()) {
      // Find the plugin type by checking factories
      let pluginType = '';
      for (const [type, factory] of this.pluginFactories.entries()) {
        const testInstance = factory();
        if (testInstance.id === plugin.id) {
          pluginType = type;
          break;
        }
      }

      configs.push({
        id,
        pluginType,
        name: plugin.name,
        enabled: true,
        config: (plugin as any).config || {},
      });
    }

    return configs;
  }

  /**
   * Register default plugin factories
   */
  private registerDefaultPluginFactories(): void {
    // Import plugin classes dynamically to avoid circular dependencies
    const {
      NthWeekdayPlugin,
      WeeklyPlugin,
      MonthlyDatePlugin,
      AnnualDatePlugin,
      PatternPlugin,
    } = require('./EventPlugin');

    this.registerPluginFactory('nth-weekday', () => new NthWeekdayPlugin());
    this.registerPluginFactory('weekly', () => new WeeklyPlugin());
    this.registerPluginFactory('monthly-date', () => new MonthlyDatePlugin());
    this.registerPluginFactory('annual-date', () => new AnnualDatePlugin());
    this.registerPluginFactory('pattern', () => new PatternPlugin());
  }

  /**
   * Create default instance with common recurring events
   */
  static createDefault(): ModularEventSystem {
    const system = new ModularEventSystem();

    // Check if config file exists
    const configPath = join(process.cwd(), 'config', 'event-plugins.json');
    if (existsSync(configPath)) {
      system.loadConfiguration(configPath);
    } else {
      // Load default plugins
      const { NthWeekdayPlugin } = require('./EventPlugin');
      const { EventType, Location } = require('../parser/types');

      // Default Isha Satsang plugin
      const ishaSatsang = new NthWeekdayPlugin();
      ishaSatsang.configure({
        weekOfMonth: 2,
        dayOfWeek: 6, // Saturday
        eventDetails: {
          type: EventType.ISHA_SATSANG,
          location: Location.SOUTH_BAY,
          address: '3080 Olcott St #D232, Santa Clara, CA',
          startTime: '7:00 PM',
          endTime: '9:00 PM',
          description: 'Isha Satsang - Monthly spiritual gathering',
        },
      });

      system.addPlugin('isha-satsang', ishaSatsang);
    }

    return system;
  }
}
