import { Event } from '../parser/types';

/**
 * Base interface for event plugins
 * Implement this to add new types of events or recurring patterns
 */
export interface EventPlugin {
  /**
   * Unique identifier for this plugin
   */
  id: string;

  /**
   * Human-readable name for this plugin
   */
  name: string;

  /**
   * Check if this plugin should generate events for a given date
   */
  shouldGenerateEvents(date: Date): boolean;

  /**
   * Generate events for a given date
   */
  generateEvents(date: Date): Event[];

  /**
   * Optional: Configuration for this plugin
   */
  configure?(config: any): void;
}

/**
 * Abstract base class for event plugins with common functionality
 */
export abstract class BaseEventPlugin implements EventPlugin {
  abstract id: string;
  abstract name: string;
  protected config: any = {};

  abstract shouldGenerateEvents(date: Date): boolean;
  abstract generateEvents(date: Date): Event[];

  configure(config: any): void {
    this.config = { ...this.config, ...config };
  }
}

/**
 * Plugin for nth occurrence of a weekday in a month (e.g., 2nd Saturday)
 */
export class NthWeekdayPlugin extends BaseEventPlugin {
  id = 'nth-weekday';
  name = 'Nth Weekday Plugin';

  private weekOfMonth: number = 2; // Default to 2nd week
  private dayOfWeek: number = 6; // Default to Saturday
  private eventDetails: Partial<Event> = {};

  configure(config: {
    weekOfMonth: number;
    dayOfWeek: number;
    eventDetails: Partial<Event>;
  }): void {
    super.configure(config);
    this.weekOfMonth = config.weekOfMonth;
    this.dayOfWeek = config.dayOfWeek;
    this.eventDetails = config.eventDetails;
  }

  shouldGenerateEvents(date: Date): boolean {
    if (date.getDay() !== this.dayOfWeek) {
      return false;
    }

    const weekNumber = Math.ceil(date.getDate() / 7);

    if (this.weekOfMonth === -1) {
      // Last occurrence of the weekday
      const nextWeek = new Date(date);
      nextWeek.setDate(date.getDate() + 7);
      return nextWeek.getMonth() !== date.getMonth();
    }

    return weekNumber === this.weekOfMonth;
  }

  generateEvents(date: Date): Event[] {
    if (!this.shouldGenerateEvents(date)) {
      return [];
    }

    return [
      {
        date: new Date(date),
        ...this.eventDetails,
      } as Event,
    ];
  }
}

/**
 * Plugin for weekly recurring events
 */
export class WeeklyPlugin extends BaseEventPlugin {
  id = 'weekly';
  name = 'Weekly Plugin';

  private dayOfWeek: number = 0; // Default to Sunday
  private eventDetails: Partial<Event> = {};

  configure(config: { dayOfWeek: number; eventDetails: Partial<Event> }): void {
    super.configure(config);
    this.dayOfWeek = config.dayOfWeek;
    this.eventDetails = config.eventDetails;
  }

  shouldGenerateEvents(date: Date): boolean {
    return date.getDay() === this.dayOfWeek;
  }

  generateEvents(date: Date): Event[] {
    if (!this.shouldGenerateEvents(date)) {
      return [];
    }

    return [
      {
        date: new Date(date),
        ...this.eventDetails,
      } as Event,
    ];
  }
}

/**
 * Plugin for specific date patterns (e.g., every 15th of month)
 */
export class MonthlyDatePlugin extends BaseEventPlugin {
  id = 'monthly-date';
  name = 'Monthly Date Plugin';

  private dayOfMonth: number = 15; // Default to 15th
  private eventDetails: Partial<Event> = {};

  configure(config: { dayOfMonth: number; eventDetails: Partial<Event> }): void {
    super.configure(config);
    this.dayOfMonth = config.dayOfMonth;
    this.eventDetails = config.eventDetails;
  }

  shouldGenerateEvents(date: Date): boolean {
    return date.getDate() === this.dayOfMonth;
  }

  generateEvents(date: Date): Event[] {
    if (!this.shouldGenerateEvents(date)) {
      return [];
    }

    return [
      {
        date: new Date(date),
        ...this.eventDetails,
      } as Event,
    ];
  }
}

/**
 * Plugin for specific dates in the year
 */
export class AnnualDatePlugin extends BaseEventPlugin {
  id = 'annual-date';
  name = 'Annual Date Plugin';

  private month: number = 0; // 0-indexed month
  private day: number = 1;
  private eventDetails: Partial<Event> = {};

  configure(config: { month: number; day: number; eventDetails: Partial<Event> }): void {
    super.configure(config);
    this.month = config.month;
    this.day = config.day;
    this.eventDetails = config.eventDetails;
  }

  shouldGenerateEvents(date: Date): boolean {
    return date.getMonth() === this.month && date.getDate() === this.day;
  }

  generateEvents(date: Date): Event[] {
    if (!this.shouldGenerateEvents(date)) {
      return [];
    }

    return [
      {
        date: new Date(date),
        ...this.eventDetails,
      } as Event,
    ];
  }
}

/**
 * Plugin for complex date patterns using cron-like expressions
 */
export class PatternPlugin extends BaseEventPlugin {
  id = 'pattern';
  name = 'Pattern Plugin';

  private pattern: string = '';
  private eventDetails: Partial<Event> = {};
  private matchFunction?: (date: Date) => boolean;

  configure(config: {
    pattern: string;
    eventDetails: Partial<Event>;
    matchFunction?: (date: Date) => boolean;
  }): void {
    super.configure(config);
    this.pattern = config.pattern;
    this.eventDetails = config.eventDetails;
    this.matchFunction = config.matchFunction;
  }

  shouldGenerateEvents(date: Date): boolean {
    if (this.matchFunction) {
      return this.matchFunction(date);
    }

    // Simple pattern matching for now
    // Could be extended to support cron-like expressions
    switch (this.pattern) {
      case 'first-monday':
        return date.getDay() === 1 && date.getDate() <= 7;
      case 'last-friday':
        const nextWeek = new Date(date);
        nextWeek.setDate(date.getDate() + 7);
        return date.getDay() === 5 && nextWeek.getMonth() !== date.getMonth();
      default:
        return false;
    }
  }

  generateEvents(date: Date): Event[] {
    if (!this.shouldGenerateEvents(date)) {
      return [];
    }

    return [
      {
        date: new Date(date),
        ...this.eventDetails,
      } as Event,
    ];
  }
}
