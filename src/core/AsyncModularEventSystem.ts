import { Event } from '../parser/types';
import { EventPlugin } from './EventPlugin';
import { promises as fs } from 'fs';
import { join } from 'path';
import { PluginConfiguration } from './ModularEventSystem';

/**
 * Async version of ModularEventSystem with better performance
 */
export class AsyncModularEventSystem {
  private plugins: Map<string, EventPlugin> = new Map();
  private pluginFactories: Map<string, () => EventPlugin> = new Map();
  private pluginTypeCache: Map<EventPlugin, string> = new Map();

  constructor() {
    this.registerDefaultPluginFactories();
  }

  /**
   * Register a plugin factory
   */
  registerPluginFactory(type: string, factory: () => EventPlugin): void {
    this.pluginFactories.set(type, factory);
  }

  /**
   * Add a plugin instance
   */
  addPlugin(instanceId: string, plugin: EventPlugin, pluginType?: string): void {
    this.plugins.set(instanceId, plugin);
    if (pluginType) {
      this.pluginTypeCache.set(plugin, pluginType);
    }
  }

  /**
   * Remove a plugin instance
   */
  removePlugin(instanceId: string): void {
    const plugin = this.plugins.get(instanceId);
    if (plugin) {
      this.pluginTypeCache.delete(plugin);
      this.plugins.delete(instanceId);
    }
  }

  /**
   * Get all events for a specific date
   */
  getEventsForDate(date: Date): Event[] {
    const allEvents: Event[] = [];

    for (const plugin of this.plugins.values()) {
      const events = plugin.generateEvents(date);
      allEvents.push(...events);
    }

    return allEvents;
  }

  /**
   * Get events for a date range (optimized)
   */
  getEventsForDateRange(startDate: Date, endDate: Date): Event[] {
    const allEvents: Event[] = [];
    const currentDate = new Date(startDate);
    const endTime = endDate.getTime();

    // Pre-allocate array for better performance
    const tempEvents: Event[] = [];

    while (currentDate.getTime() <= endTime) {
      for (const plugin of this.plugins.values()) {
        const events = plugin.generateEvents(currentDate);
        if (events.length > 0) {
          tempEvents.push(...events);
        }
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return tempEvents;
  }

  /**
   * Load configuration from JSON file (async)
   */
  async loadConfiguration(configPath?: string): Promise<void> {
    const path = configPath || join(process.cwd(), 'config', 'event-plugins.json');

    try {
      const exists = await fs
        .access(path)
        .then(() => true)
        .catch(() => false);
      if (!exists) {
        console.log('No plugin configuration found, using defaults');
        return;
      }

      const configData = await fs.readFile(path, 'utf-8');
      const config = JSON.parse(configData) as { plugins: PluginConfiguration[] };

      // Clear existing plugins
      this.plugins.clear();
      this.pluginTypeCache.clear();

      // Load each plugin
      for (const pluginConfig of config.plugins) {
        if (!pluginConfig.enabled) continue;

        const factory = this.pluginFactories.get(pluginConfig.pluginType);
        if (!factory) {
          console.warn(`Unknown plugin type: ${pluginConfig.pluginType}`);
          continue;
        }

        const plugin = factory();
        if (plugin.configure) {
          plugin.configure(pluginConfig.config);
        }

        this.addPlugin(pluginConfig.id, plugin, pluginConfig.pluginType);
        console.log(`📅 Loaded plugin: ${pluginConfig.name}`);
      }
    } catch (error) {
      console.error('Failed to load plugin configuration:', error);
      throw error;
    }
  }

  /**
   * Save current configuration to JSON file (async)
   */
  async saveConfiguration(configPath?: string): Promise<void> {
    const path = configPath || join(process.cwd(), 'config', 'event-plugins.json');
    const config = { plugins: this.exportConfiguration() };

    await fs.writeFile(path, JSON.stringify(config, null, 2));
    console.log(`Configuration saved to ${path}`);
  }

  /**
   * Export current configuration (optimized)
   */
  exportConfiguration(): PluginConfiguration[] {
    const configs: PluginConfiguration[] = [];

    for (const [id, plugin] of this.plugins.entries()) {
      // Use cached plugin type instead of creating instances
      const pluginType = this.getPluginType(plugin);

      configs.push({
        id,
        pluginType,
        name: plugin.name,
        enabled: true,
        config: (plugin as any).config || {},
      });
    }

    return configs;
  }

  /**
   * Get plugin type (optimized with cache)
   */
  private getPluginType(plugin: EventPlugin): string {
    // Check cache first
    const cached = this.pluginTypeCache.get(plugin);
    if (cached) return cached;

    // Fallback to checking plugin id
    for (const [type, factory] of this.pluginFactories.entries()) {
      const testInstance = factory();
      if (testInstance.id === plugin.id) {
        this.pluginTypeCache.set(plugin, type);
        return type;
      }
    }

    return 'unknown';
  }

  /**
   * Register default plugin factories
   */
  private registerDefaultPluginFactories(): void {
    const {
      NthWeekdayPlugin,
      WeeklyPlugin,
      MonthlyDatePlugin,
      AnnualDatePlugin,
      PatternPlugin,
    } = require('./EventPlugin');

    this.registerPluginFactory('nth-weekday', () => new NthWeekdayPlugin());
    this.registerPluginFactory('weekly', () => new WeeklyPlugin());
    this.registerPluginFactory('monthly-date', () => new MonthlyDatePlugin());
    this.registerPluginFactory('annual-date', () => new AnnualDatePlugin());
    this.registerPluginFactory('pattern', () => new PatternPlugin());
  }

  /**
   * Create default instance with common recurring events (async)
   */
  static async createDefault(): Promise<AsyncModularEventSystem> {
    const system = new AsyncModularEventSystem();

    // Check if config file exists
    const configPath = join(process.cwd(), 'config', 'event-plugins.json');

    try {
      await system.loadConfiguration(configPath);
    } catch (error) {
      // Load default plugins
      const { NthWeekdayPlugin } = require('./EventPlugin');
      const { EventType, Location } = require('../parser/types');

      // Default Isha Satsang plugin
      const ishaSatsang = new NthWeekdayPlugin();
      ishaSatsang.configure({
        weekOfMonth: 2,
        dayOfWeek: 6, // Saturday
        eventDetails: {
          type: EventType.ISHA_SATSANG,
          location: Location.SOUTH_BAY,
          address: '3080 Olcott St #D232, Santa Clara, CA',
          startTime: '7:00 PM',
          endTime: '9:00 PM',
          description: 'Isha Satsang - Monthly spiritual gathering',
        },
      });

      system.addPlugin('isha-satsang', ishaSatsang, 'nth-weekday');
    }

    return system;
  }
}
