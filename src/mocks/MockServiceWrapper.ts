import { type Coordinates, type PlaceResult } from '../models/Business';

import { type MockDataGenerator } from './MockDataGenerator';

export class MockServiceWrapper {
  constructor(
    private realService: any,
    private mockGenerator: MockDataGenerator
  ) {}

  public async zipCodeToCoordinates(zipCode: string): Promise<Coordinates> {
    if (process.env.REACT_APP_USE_MOCK_DATA === 'true') {
      const mockData = this.mockGenerator.generateGeocodingResult(zipCode);
      if (mockData.status === 'OK' && mockData.results.length > 0) {
        return {
          latitude: mockData.results[0].geometry.location.lat,
          longitude: mockData.results[0].geometry.location.lng,
        };
      }
      throw new Error(`Mock geocoding failed for zip code: ${zipCode}`);
    }
    return this.realService.zipCodeToCoordinates(zipCode);
  }

  public async searchNearby(
    coordinates: Coordinates,
    radius: number,
    keyword: string
  ): Promise<{ places: PlaceResult[]; nextPageToken: string | undefined }> {
    if (process.env.REACT_APP_USE_MOCK_DATA === 'true') {
      const mockData = this.mockGenerator.generateBusinesses('mock-zip', radius, keyword);
      if (mockData.status === 'OK') {
        return {
          places: mockData.results,
          nextPageToken: undefined,
        };
      }
      throw new Error('Mock places search failed');
    }
    return this.realService.searchNearby(coordinates, radius, keyword);
  }

  public clearCache(): void {
    this.realService.clearCache();
  }
} 