import { MockDataGenerator } from './MockDataGenerator';

describe('MockDataGenerator', () => {
    const generator = new MockDataGenerator();

    describe('generateBusinesses', () => {
        it('generates the requested number of businesses', () => {
            const result = generator.generateBusinesses('94105', 'restaurant', 10);
            expect(result.results).toHaveLength(10);
            expect(result.status).toBe('OK');
        });

        it('includes zip code in business names', () => {
            const result = generator.generateBusinesses('94105', 'restaurant', 1);
            expect(result.results[0].name).toContain('94105');
        });

        it('uses industry-specific website adoption rates', () => {
            // This is probabilistic, so we test it over a large sample.
            const restaurantResults = generator.generateBusinesses('94105', 'restaurant', 1000);
            const medicalResults = generator.generateBusinesses('90210', 'medical', 1000);

            const restaurantWithWebsite = restaurantResults.results.filter((r: any) => r.website).length;
            const medicalWithWebsite = medicalResults.results.filter((r: any) => r.website).length;
            
            // Check if the rate is within a reasonable margin of error
            expect(restaurantWithWebsite / 1000).toBeCloseTo(0.55, 1);
            expect(medicalWithWebsite / 1000).toBeCloseTo(0.85, 1);
        });
    });

    describe('generateGeocodingResult', () => {
        it('returns coordinates for known zip codes', () => {
            const result = generator.generateGeocodingResult('90210');
            expect(result.status).toBe('OK');
            expect(result.results[0].geometry.location.lat).toBe(34.0901);
            expect(result.results[0].geometry.location.lng).toBe(-118.4065);
        });

        it('returns default coordinates for unknown zip codes', () => {
            const result = generator.generateGeocodingResult('00000');
            expect(result.results[0].geometry.location.lat).toBe(39.8283);
            expect(result.results[0].geometry.location.lng).toBe(-98.5795);
        });
    });

    describe('advanced features', () => {
        it('generates realistic open status', () => {
            // Restaurant should be open at 1pm (13:00)
            expect(generator.generateRealisticOpenStatus('restaurant', 13)).toBe(true);
            // Restaurant should be closed at 5am (05:00)
            expect(generator.generateRealisticOpenStatus('restaurant', 5)).toBe(false);
            // Bar should be open at 1am (01:00)
            expect(generator.generateRealisticOpenStatus('bar', 1)).toBe(true);
        });

        it('generates correlated data', () => {
            const data = generator.generateCorrelatedData();
            expect(data.rating).toBeGreaterThanOrEqual(3);
            expect(data.rating).toBeLessThanOrEqual(5);
            expect(data.reviewCount).toBeGreaterThan(0);
            expect(data.priceLevel).toBeGreaterThan(0);
        });
    });
}); 