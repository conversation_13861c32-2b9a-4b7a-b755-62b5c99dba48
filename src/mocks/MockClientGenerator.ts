import { type Client, type ClientService, type ClientRevenue, type ClientCampaignMetrics, type SupportTicket, type ClientStatus, type ServiceType, type ServiceStatus, type PaymentStatus } from '../models/Client';
import { type MarketingCampaign, type CampaignPlatform, type CampaignType, type CampaignStatus } from '../models/MarketingCampaign';

/**
 * Mock data generator for clients and campaigns
 */
export class MockClientGenerator {
  private businessNames = [
    "Alice's Florist", "Bob's Auto Repair", "Sara's Beauty Salon", "Tech Solutions Inc",
    "Mario's Pizza House", "Green Thumb Landscaping", "Precision Dental Care", "Elite Fitness Gym",
    "Golden Gate Bakery", "Bay Area Plumbing", "Sunset Photography", "Ocean View Restaurant",
    "Digital Marketing Pro", "Family Law Associates", "Fresh Market Grocery", "Custom Carpentry",
    "Wellness Spa & Massage", "Urban Coffee Roasters", "Pet Care Veterinary", "Home Security Plus",
    "Artisan Jewelry Studio", "Classic Barber Shop", "Modern Architecture Firm", "Eco-Friendly Cleaning"
  ];

  private contactPersons = [
    "<PERSON>", "<PERSON> Martinez", "<PERSON> Chen", "<PERSON> Kim",
    "<PERSON> Rossi", "<PERSON> Green", "Dr. Michael Brown", "<PERSON> Wilson",
    "<PERSON> Baker", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON> <PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON> Taylor", "<PERSON>", "Dr. <PERSON> White", "Tom <PERSON>",
    "Nicole Foster", "Mark Williams", "<PERSON> <PERSON>", "<PERSON> Clark"
  ];

  private zipCodes = [
    { zip: "94105", city: "San Francisco" },
    { zip: "94107", city: "San Francisco" },
    { zip: "94110", city: "San Francisco" },
    { zip: "94102", city: "San Francisco" },
    { zip: "94103", city: "San Francisco" },
    { zip: "94109", city: "San Francisco" },
    { zip: "94133", city: "San Francisco" },
    { zip: "94115", city: "San Francisco" },
    { zip: "94117", city: "San Francisco" },
    { zip: "94118", city: "San Francisco" },
    { zip: "94122", city: "San Francisco" },
    { zip: "94123", city: "San Francisco" }
  ];

  private servicePackages = [
    { types: ['website'], basePrice: 49 },
    { types: ['website', 'facebook'], basePrice: 79 },
    { types: ['website', 'instagram'], basePrice: 79 },
    { types: ['website', 'facebook', 'instagram'], basePrice: 109 },
    { types: ['website', 'seo'], basePrice: 149 },
    { types: ['website', 'facebook', 'instagram', 'seo'], basePrice: 199 },
    { types: ['website', 'facebook', 'instagram', 'seo', 'ads'], basePrice: 299 },
    { types: ['website', 'maintenance'], basePrice: 89 },
    { types: ['website', 'facebook', 'instagram', 'maintenance'], basePrice: 149 }
  ];

  /**
   * Generate a random client
   */
  generateClient(index: number): Client {
    const businessName = this.businessNames[index % this.businessNames.length];
    const contactPerson = this.contactPersons[index % this.contactPersons.length];
    const location = this.zipCodes[index % this.zipCodes.length];
    const servicePackage = this.servicePackages[Math.floor(Math.random() * this.servicePackages.length)];
    
    const clientId = `client-${String(index + 1).padStart(3, '0')}`;
    const businessId = `business-${String(index + 1).padStart(3, '0')}`;
    
    // Generate services
    const services = this.generateServices(servicePackage);
    
    // Generate revenue based on services
    const revenue = this.generateRevenue(services);
    
    // Generate campaigns
    const campaigns = this.generateCampaigns(clientId, services);
    
    // Generate campaign metrics
    const campaignMetrics = this.generateCampaignMetrics(campaigns);
    
    // Generate support tickets
    const supportHistory = this.generateSupportTickets(clientId, Math.floor(Math.random() * 6));
    
    // Determine client status
    const status = this.getClientStatus(revenue, campaigns, supportHistory);

    return {
      id: clientId,
      businessId,
      businessName,
      contactPerson,
      email: `${contactPerson.toLowerCase().replace(/[^a-z]/g, '')}@${businessName.toLowerCase().replace(/[^a-z]/g, '')}.com`,
      phone: this.generatePhoneNumber(),
      address: this.generateAddress(location),
      zipCode: location.zip,
      city: location.city,
      state: "CA",
      onboardDate: this.generateOnboardDate(),
      status,
      services,
      revenue,
      campaigns,
      campaignMetrics,
      supportHistory,
      notes: Math.random() > 0.7 ? this.generateNotes() : undefined,
      tags: Math.random() > 0.6 ? this.generateTags() : undefined,
      lastContactDate: Math.random() > 0.5 ? this.generateRecentDate(7) : undefined,
      nextFollowUpDate: Math.random() > 0.7 ? this.generateFutureDate(14) : undefined
    };
  }

  /**
   * Generate multiple clients
   */
  generateClients(count: number = 24): Client[] {
    return Array.from({ length: count }, (_, index) => this.generateClient(index));
  }

  /**
   * Generate services for a client
   */
  private generateServices(servicePackage: { types: string[], basePrice: number }): ClientService[] {
    const services: ClientService[] = [];
    let serviceIndex = 0;

    servicePackage.types.forEach((serviceType) => {
      const service: ClientService = {
        id: `service-${Date.now()}-${serviceIndex++}`,
        type: serviceType as ServiceType,
        name: this.getServiceName(serviceType as ServiceType),
        status: this.getServiceStatus(),
        monthlyPrice: this.getServicePrice(serviceType as ServiceType),
        startDate: this.generateServiceStartDate(),
        lastHealthCheck: this.generateRecentDate(1),
        uptime: this.generateUptime(),
        issueCount: Math.floor(Math.random() * 3),
        description: this.getServiceDescription(serviceType as ServiceType),
        url: this.getServiceUrl(serviceType as ServiceType)
      };
      services.push(service);
    });

    return services;
  }

  /**
   * Generate revenue based on services
   */
  private generateRevenue(services: ClientService[]): ClientRevenue {
    const monthlyRecurring = services.reduce((sum, service) => sum + service.monthlyPrice, 0);
    const monthlyCost = Math.floor(monthlyRecurring * (0.15 + Math.random() * 0.25)); // 15-40% costs
    const netProfit = monthlyRecurring - monthlyCost;
    const profitMargin = monthlyRecurring > 0 ? (netProfit / monthlyRecurring) * 100 : 0;

    return {
      monthlyRecurring,
      monthlyCost,
      netProfit,
      profitMargin,
      billingDate: Math.floor(Math.random() * 28) + 1,
      paymentStatus: this.getPaymentStatus(),
      lifetimeValue: Math.floor(monthlyRecurring * (6 + Math.random() * 18)), // 6-24 months LTV
      lastPaymentDate: this.generateRecentDate(30),
      nextBillingDate: this.generateFutureDate(30)
    };
  }

  /**
   * Generate marketing campaigns for a client
   */
  private generateCampaigns(clientId: string, services: ClientService[]): MarketingCampaign[] {
    const campaigns: MarketingCampaign[] = [];
    const hasAdsService = services.some(s => s.type === 'ads');
    
    if (!hasAdsService && Math.random() > 0.6) {
      return campaigns; // 40% chance of no campaigns if no ads service
    }

    const campaignCount = hasAdsService ? 1 + Math.floor(Math.random() * 3) : Math.floor(Math.random() * 2);
    
    for (let i = 0; i < campaignCount; i++) {
      const platform = this.getCampaignPlatform();
      const campaign = this.generateCampaign(clientId, platform, i);
      campaigns.push(campaign);
    }

    return campaigns;
  }

  /**
   * Generate a single campaign
   */
  private generateCampaign(clientId: string, platform: CampaignPlatform, index: number): MarketingCampaign {
    const dailyBudget = 5 + Math.floor(Math.random() * 45); // $5-$50 daily
    const daysRunning = 15 + Math.floor(Math.random() * 45); // 15-60 days
    const totalSpent = dailyBudget * daysRunning * (0.8 + Math.random() * 0.4); // 80-120% of budget
    
    const impressions = Math.floor(totalSpent * (100 + Math.random() * 200)); // 100-300 impressions per dollar
    const ctr = 0.5 + Math.random() * 4; // 0.5-4.5% CTR
    const clicks = Math.floor(impressions * (ctr / 100));
    const conversionRate = 1 + Math.random() * 8; // 1-9% conversion rate
    const conversions = Math.floor(clicks * (conversionRate / 100));
    const revenuePerConversion = 20 + Math.random() * 180; // $20-$200 per conversion
    const revenue = conversions * revenuePerConversion;

    return {
      id: `campaign-${clientId}-${platform}-${index}`,
      clientId,
      name: this.getCampaignName(platform),
      platform,
      type: this.getCampaignType(platform),
      status: this.getCampaignStatus(),
      budget: {
        dailyBudget,
        totalBudget: dailyBudget * 60, // 60-day budget
        totalSpent,
        remainingBudget: Math.max(0, (dailyBudget * 60) - totalSpent)
      },
      performance: {
        impressions,
        clicks,
        conversions,
        revenue,
        cost: totalSpent,
        roi: totalSpent > 0 ? ((revenue - totalSpent) / totalSpent) * 100 : 0,
        ctr,
        cpc: clicks > 0 ? totalSpent / clicks : 0,
        conversionRate
      },
      dates: {
        startDate: this.generateCampaignStartDate(),
        lastOptimized: this.generateRecentDate(7),
        createdAt: this.generateRecentDate(60)
      },
      targeting: {
        location: [`${this.zipCodes[Math.floor(Math.random() * this.zipCodes.length)].city}, CA`],
        demographics: {
          ageRange: this.getAgeRange(),
          interests: this.getInterests()
        },
        keywords: platform === 'google_ads' ? this.getKeywords() : undefined
      },
      objectives: this.getCampaignObjectives()
    };
  }

  /**
   * Generate campaign metrics summary
   */
  private generateCampaignMetrics(campaigns: MarketingCampaign[]): ClientCampaignMetrics {
    if (campaigns.length === 0) {
      return {
        totalCampaignSpend: 0,
        totalCampaignRevenue: 0,
        campaignROI: 0,
        activeCampaigns: 0,
        totalCampaigns: 0,
        averageConversionRate: 0
      };
    }

    const totalSpend = campaigns.reduce((sum, c) => sum + c.performance.cost, 0);
    const totalRevenue = campaigns.reduce((sum, c) => sum + c.performance.revenue, 0);
    const activeCampaigns = campaigns.filter(c => c.status === 'active').length;
    const avgConversionRate = campaigns.reduce((sum, c) => sum + c.performance.conversionRate, 0) / campaigns.length;

    return {
      totalCampaignSpend: totalSpend,
      totalCampaignRevenue: totalRevenue,
      campaignROI: totalSpend > 0 ? ((totalRevenue - totalSpend) / totalSpend) * 100 : 0,
      activeCampaigns,
      totalCampaigns: campaigns.length,
      averageConversionRate: avgConversionRate
    };
  }

  /**
   * Generate support tickets
   */
  private generateSupportTickets(clientId: string, count: number): SupportTicket[] {
    const tickets: SupportTicket[] = [];
    
    for (let i = 0; i < count; i++) {
      const createdAt = this.generateRecentDate(90);
      const isResolved = Math.random() > 0.3; // 70% resolved
      
      tickets.push({
        id: `ticket-${clientId}-${i}`,
        clientId,
        title: this.getTicketTitle(),
        description: this.getTicketDescription(),
        priority: this.getTicketPriority(),
        status: isResolved ? 'resolved' : (Math.random() > 0.5 ? 'in_progress' : 'open'),
        category: this.getTicketCategory(),
        createdAt,
        resolvedAt: isResolved ? new Date(createdAt.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000) : undefined,
        resolutionTime: isResolved ? 1 + Math.random() * 48 : undefined
      });
    }

    return tickets.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // Helper methods for generating realistic data
  private generatePhoneNumber(): string {
    const areaCodes = ['415', '510', '650', '408', '925', '707'];
    const areaCode = areaCodes[Math.floor(Math.random() * areaCodes.length)];
    const exchange = Math.floor(Math.random() * 900) + 100;
    const number = Math.floor(Math.random() * 9000) + 1000;
    return `(${areaCode}) ${exchange}-${number}`;
  }

  private generateAddress(location: { zip: string, city: string }): string {
    const streetNumbers = [123, 456, 789, 321, 654, 987, 111, 222, 333, 444, 555, 666];
    const streetNames = ['Main St', 'Oak Ave', 'Pine St', 'Elm Ave', 'Market St', 'Broadway', 'First St', 'Second Ave'];
    
    const streetNumber = streetNumbers[Math.floor(Math.random() * streetNumbers.length)];
    const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
    
    return `${streetNumber} ${streetName}, ${location.city}, CA ${location.zip}`;
  }

  private generateOnboardDate(): Date {
    const now = new Date();
    const monthsAgo = Math.floor(Math.random() * 24) + 1; // 1-24 months ago
    return new Date(now.getFullYear(), now.getMonth() - monthsAgo, Math.floor(Math.random() * 28) + 1);
  }

  private generateRecentDate(maxDaysAgo: number): Date {
    const now = new Date();
    const daysAgo = Math.floor(Math.random() * maxDaysAgo);
    return new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
  }

  private generateFutureDate(maxDaysFromNow: number): Date {
    const now = new Date();
    const daysFromNow = Math.floor(Math.random() * maxDaysFromNow) + 1;
    return new Date(now.getTime() + daysFromNow * 24 * 60 * 60 * 1000);
  }

  private generateServiceStartDate(): Date {
    const now = new Date();
    const daysAgo = Math.floor(Math.random() * 180) + 30; // 30-210 days ago
    return new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
  }

  private generateCampaignStartDate(): Date {
    const now = new Date();
    const daysAgo = Math.floor(Math.random() * 60) + 5; // 5-65 days ago
    return new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
  }

  private generateUptime(): number {
    const rand = Math.random();
    if (rand > 0.8) return 95 + Math.random() * 5; // 95-100% (good)
    if (rand > 0.6) return 85 + Math.random() * 10; // 85-95% (warning)
    return 70 + Math.random() * 15; // 70-85% (poor)
  }

  private getServiceName(type: ServiceType): string {
    const names = {
      website: 'Website Hosting & Maintenance',
      facebook: 'Facebook Page Management',
      instagram: 'Instagram Management',
      seo: 'SEO Optimization',
      ads: 'Digital Advertising',
      maintenance: 'Technical Support',
      domain: 'Domain & SSL',
      hosting: 'Web Hosting'
    };
    return names[type];
  }

  private getServicePrice(type: ServiceType): number {
    const basePrices = {
      website: 49,
      facebook: 29,
      instagram: 29,
      seo: 99,
      ads: 149,
      maintenance: 39,
      domain: 12,
      hosting: 19
    };
    return basePrices[type] + Math.floor(Math.random() * 20) - 10; // ±$10 variation
  }

  private getServiceStatus(): ServiceStatus {
    const rand = Math.random();
    if (rand > 0.85) return 'warning';
    if (rand > 0.95) return 'down';
    return 'active';
  }

  private getServiceDescription(type: ServiceType): string {
    const descriptions = {
      website: 'Professional website with hosting and regular updates',
      facebook: 'Facebook business page setup and content management',
      instagram: 'Instagram business profile with post scheduling',
      seo: 'Search engine optimization and local listings',
      ads: 'Google and social media advertising campaigns',
      maintenance: '24/7 technical support and maintenance',
      domain: 'Domain registration and SSL certificate',
      hosting: 'Reliable web hosting with backup services'
    };
    return descriptions[type];
  }

  private getServiceUrl(type: ServiceType): string | undefined {
    const businessName = this.businessNames[Math.floor(Math.random() * this.businessNames.length)]
      .toLowerCase().replace(/[^a-z]/g, '');
    
    switch (type) {
      case 'website':
        return `https://${businessName}.com`;
      case 'facebook':
        return `https://facebook.com/${businessName}`;
      case 'instagram':
        return `https://instagram.com/${businessName}`;
      default:
        return undefined;
    }
  }

  private getClientStatus(revenue: ClientRevenue, campaigns: MarketingCampaign[], tickets: SupportTicket[]): ClientStatus {
    const openTickets = tickets.filter(t => t.status === 'open' || t.status === 'in_progress').length;
    const campaignIssues = campaigns.filter(c => c.performance.roi < 50).length;
    
    if (revenue.paymentStatus === 'overdue' || openTickets > 2) return 'paused';
    if (revenue.profitMargin < 50 || campaignIssues > 1) return 'trial';
    return 'active';
  }

  private getPaymentStatus(): PaymentStatus {
    const rand = Math.random();
    if (rand > 0.95) return 'overdue';
    if (rand > 0.90) return 'pending';
    return 'current';
  }

  private getCampaignPlatform(): CampaignPlatform {
    const platforms: CampaignPlatform[] = ['google_ads', 'facebook_ads', 'instagram_ads'];
    const weights = [0.5, 0.3, 0.2]; // Google Ads more common
    
    const rand = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < platforms.length; i++) {
      cumulative += weights[i];
      if (rand <= cumulative) return platforms[i];
    }
    
    return platforms[0];
  }

  private getCampaignType(platform: CampaignPlatform): CampaignType {
    if (platform === 'google_ads') {
      return Math.random() > 0.7 ? 'display' : 'search';
    }
    return 'social';
  }

  private getCampaignStatus(): CampaignStatus {
    const rand = Math.random();
    if (rand > 0.8) return 'active';
    if (rand > 0.6) return 'paused';
    if (rand > 0.4) return 'completed';
    return 'draft';
  }

  private getCampaignName(platform: CampaignPlatform): string {
    const campaigns = {
      google_ads: ['Local Search Campaign', 'Brand Awareness', 'Service Promotion', 'Holiday Special'],
      facebook_ads: ['Community Engagement', 'Local Customers', 'Brand Building', 'Event Promotion'],
      instagram_ads: ['Visual Showcase', 'Story Promotion', 'Product Highlight', 'Lifestyle Content']
    };
    
    const options = campaigns[platform];
    return options[Math.floor(Math.random() * options.length)];
  }

  private getAgeRange(): string {
    const ranges = ['18-24', '25-34', '35-44', '45-54', '55-64', '65+'];
    return ranges[Math.floor(Math.random() * ranges.length)];
  }

  private getInterests(): string[] {
    const interests = ['Local Business', 'Shopping', 'Dining', 'Health & Wellness', 'Home Services', 'Technology'];
    return interests.slice(0, 2 + Math.floor(Math.random() * 3));
  }

  private getKeywords(): string[] {
    const keywords = ['local service', 'near me', 'best in city', 'professional', 'quality', 'affordable'];
    return keywords.slice(0, 3 + Math.floor(Math.random() * 3));
  }

  private getCampaignObjectives(): string[] {
    const objectives = ['Increase local awareness', 'Drive website traffic', 'Generate leads', 'Boost sales'];
    return objectives.slice(0, 2 + Math.floor(Math.random() * 2));
  }

  private getTicketTitle(): string {
    const titles = [
      'Website loading slowly',
      'Facebook page not updating',
      'Instagram posts not showing',
      'SEO ranking dropped',
      'Email not working',
      'Domain renewal question',
      'Add new service page',
      'Update contact information',
      'Fix broken link',
      'Campaign performance question'
    ];
    return titles[Math.floor(Math.random() * titles.length)];
  }

  private getTicketDescription(): string {
    return 'Customer reported issue with service. Investigation in progress.';
  }

  private getTicketPriority(): 'low' | 'medium' | 'high' | 'urgent' {
    const rand = Math.random();
    if (rand > 0.95) return 'urgent';
    if (rand > 0.8) return 'high';
    if (rand > 0.5) return 'medium';
    return 'low';
  }

  private getTicketCategory(): 'technical' | 'billing' | 'general' | 'feature_request' {
    const categories = ['technical', 'billing', 'general', 'feature_request'];
    const weights = [0.5, 0.2, 0.2, 0.1];
    
    const rand = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < categories.length; i++) {
      cumulative += weights[i];
      if (rand <= cumulative) return categories[i] as any;
    }
    
    return 'technical';
  }

  private generateNotes(): string {
    const notes = [
      'High-value client, prioritize requests',
      'Referred by existing customer',
      'Interested in expanding services',
      'Seasonal business - adjust campaigns accordingly',
      'Regular monthly check-ins scheduled'
    ];
    return notes[Math.floor(Math.random() * notes.length)];
  }

  private generateTags(): string[] {
    const allTags = ['VIP', 'Referral', 'High-Value', 'Seasonal', 'Growth-Potential', 'Long-Term', 'New-Client'];
    const count = 1 + Math.floor(Math.random() * 3);
    const shuffled = allTags.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }
}