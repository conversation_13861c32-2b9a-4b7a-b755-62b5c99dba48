import { BASE, EXPECT } from './data/fixtures';
import {
  futureValueWithContributions,
  monthsToTarget,
  deltaMonthsBetweenContributions,
  internalRateOfReturn,
  contributionShare,
  calculatePortfolioIRR,
} from '../formulas';

// ----- F-1 --------------------------------------------------------------
test('FV after 24 m @ 2.39 % & 6 250 USD/mo ≈ 1 195 415', () => {
  const fv = futureValueWithContributions(BASE.PV, BASE.RATE.base, BASE.PMT.p6250, 24);
  expect(Math.round(fv)).toBe(EXPECT.fv24mo);
});

// ----- F-2 (one sample per contribution level) -------------------------
test.each`
  pmtKey     | target     | want
  ${'p6250'} | ${'oneM'}  | ${EXPECT.nBaseline.p6250.oneM}
  ${'p5260'} | ${'twoM'}  | ${EXPECT.nBaseline.p5260.twoM}
  ${'p4200'} | ${'one5M'} | ${EXPECT.nBaseline.p4200.one5M}
`(`monthsToTarget $pmtKey to $target`, ({ pmtKey, target, want }) => {
  const n = Math.ceil(
    monthsToTarget(
      BASE.PV,
      BASE.GOAL[target as keyof typeof BASE.GOAL],
      BASE.RATE.base,
      BASE.PMT[pmtKey as keyof typeof BASE.PMT]
    )
  );
  expect(n).toBe(want);
});

// ----- F-3 (delay) -----------------------------------------------------
test('delay vs 6 250 USD/mo to 2 M (4 200 USD) = +2 m', () => {
  const d = deltaMonthsBetweenContributions(
    BASE.PV,
    BASE.GOAL.twoM,
    BASE.RATE.base,
    BASE.PMT.p6250,
    BASE.PMT.p4200
  );
  expect(d).toBe(EXPECT.delayVs6250.twoM.p4200);
});

// ----- F-4 (IRR) -------------------------------------------------------
test('Historical IRR ≈ 2.386 %/mo', () => {
  const portfolioValues = [
    407553,
    485000,
    495058,
    537000,
    542000,
    567000,
    600000,
    610000,
    682000,
    762000,
    786000,
  ];
  const monthlyContribution = 6250;
  const r = calculatePortfolioIRR(portfolioValues, monthlyContribution);
  expect(+r.toFixed(5)).toBeCloseTo(EXPECT.irrHist, 3);
});

// ----- F-5 (contribution share) ----------------------------------------
test('Contribution share to 1 M @ 6 250 USD = 0.28', () => {
  const n = EXPECT.nBaseline.p6250.oneM;
  const frac = contributionShare(BASE.PV, BASE.GOAL.oneM, BASE.PMT.p6250, n);
  expect(+frac.toFixed(2)).toBe(EXPECT.share.p6250);
});
