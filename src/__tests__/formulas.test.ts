import * as formulas from '../formulas';
import { BASE, EXPECT } from './data/fixtures';

describe('Financial Formulas', () => {
    describe('futureValueWithContributions', () => {
        it('should calculate future value with monthly contributions', () => {
            // Example: $100,000 initial, 2% monthly return, $1,000 monthly contribution, 12 months
            const result = formulas.futureValueWithContributions(100000, 0.02, 1000, 12);
            // Expected: 100000 * 1.02^12 + 1000 * ((1.02^12 - 1) / 0.02)
            expect(result).toBeCloseTo(140236.27, 2);
        });

        it('should handle zero interest rate', () => {
            const result = formulas.futureValueWithContributions(100000, 0, 1000, 12);
            expect(result).toBe(112000); // 100000 + 1000 * 12
        });

        it('should handle zero contributions', () => {
            const result = formulas.futureValueWithContributions(100000, 0.02, 0, 12);
            expect(result).toBeCloseTo(126824.18, 2);
        });
    });

    describe('monthsToTarget', () => {
        it('should calculate months to reach $1M with baseline rate', () => {
            // From user data: $786,000 current, $1,000,000 target, 2.39% monthly return
            const pv = 786000;
            const target = 1000000;
            const rate = 0.0239;

            // Test different contribution levels
            expect(formulas.monthsToTarget(pv, target, rate, 6250)).toBe(8);
            expect(formulas.monthsToTarget(pv, target, rate, 5260)).toBe(9);
            expect(formulas.monthsToTarget(pv, target, rate, 4200)).toBe(9);
        });

        it('should calculate months to reach $1.5M with baseline rate', () => {
            const pv = 786000;
            const target = 1500000;
            const rate = 0.0239;

            expect(formulas.monthsToTarget(pv, target, rate, 6250)).toBe(23);
            expect(formulas.monthsToTarget(pv, target, rate, 5260)).toBe(23);
            expect(formulas.monthsToTarget(pv, target, rate, 4200)).toBe(24);
        });

        it('should calculate months to reach $2M with baseline rate', () => {
            const pv = 786000;
            const target = 2000000;
            const rate = 0.0239;

            expect(formulas.monthsToTarget(pv, target, rate, 6250)).toBe(33);
            expect(formulas.monthsToTarget(pv, target, rate, 5260)).toBe(34);
            expect(formulas.monthsToTarget(pv, target, rate, 4200)).toBe(35);
        });

        it('should handle zero interest rate', () => {
            const result = formulas.monthsToTarget(100000, 200000, 0, 1000);
            expect(result).toBe(100); // (200000 - 100000) / 1000
        });

        it('should return 0 if already at or above target', () => {
            const result = formulas.monthsToTarget(200000, 100000, 0.02, 1000);
            expect(result).toBe(0);
        });
    });

    describe('internalRateOfReturn', () => {
        it('should calculate IRR for simple case', () => {
            const cashFlows = [-100000, 10000, 10000, 10000, 10000, 110000];
            const result = formulas.internalRateOfReturn(cashFlows);
            expect(result).toBeCloseTo(0.10, 2);
        });

        it('should handle portfolio growth with contributions', () => {
            // Portfolio values over time with monthly contributions
            const portfolioValues = [100000, 102000, 104500, 107200];
            const monthlyContribution = 1000;
            const result = formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
            expect(result).toBeGreaterThan(0);
            expect(result).toBeLessThan(0.05); // Reasonable monthly return
        });
    });

    describe('calculatePortfolioIRR', () => {
        it('should return 0 for less than 2 portfolio values', () => {
            expect(formulas.calculatePortfolioIRR([1000], 100)).toBe(0);
        });

        it('should calculate IRR for a simple growth scenario', () => {
            const values = [1000, 1100, 1210]; // 10% growth, no contributions
            expect(formulas.calculatePortfolioIRR(values, 0)).toBeCloseTo(0.1, 5);
        });

        it('should handle zero interest rate', () => {
            const values = [1000, 1100, 1200]; // $100 contribution each month
            expect(formulas.calculatePortfolioIRR(values, 100)).toBeCloseTo(0, 5);
        });

        it('should handle portfolio growth with contributions', () => {
            // Portfolio values over time with monthly contributions
            const portfolioValues = [100000, 102000, 104500, 107200];
            const monthlyContribution = 1000;
            const result = formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
            expect(result).toBeGreaterThan(0);
            expect(result).toBeLessThan(0.05); // Reasonable monthly return
        });
    });

    describe('contributionStrippedReturn', () => {
        it('should calculate return after removing contribution effects', () => {
            // Start: $100,000, End: $112,000, Contribution: $5,000, Period: 3 months
            const periodReturn = formulas.contributionStrippedPeriodReturn(100000, 112000, 5000, 3);
            const monthlyReturn = formulas.periodToMonthlyReturn(periodReturn, 3);

            expect(periodReturn).toBeCloseTo(0.0683, 3); // ~6.83% for the period
            expect(monthlyReturn).toBeCloseTo(0.0221, 3); // ~2.21% monthly
        });

        it('should return 0 if adjusted start is zero or negative', () => {
            expect(formulas.contributionStrippedPeriodReturn(0, 100, 0, 1)).toBe(0);
            expect(formulas.contributionStrippedPeriodReturn(-100, 100, 0, 1)).toBe(0);
        });
    });

    describe('periodToMonthlyReturn', () => {
        it('should return 0 for zero or negative months', () => {
            expect(formulas.periodToMonthlyReturn(0.1, 0)).toBe(0);
            expect(formulas.periodToMonthlyReturn(0.1, -1)).toBe(0);
        });
    });

    describe('calculateStatistics', () => {
        it('should calculate mean and standard deviation', () => {
            const returns = [0.02, 0.03, -0.01, 0.04, 0.02];
            const {mean, stdDev} = formulas.calculateStatistics(returns);

            expect(mean).toBeCloseTo(0.02, 4);
            expect(stdDev).toBeCloseTo(0.0187, 4);
        });

        it('should handle single value', () => {
            const {mean, stdDev} = formulas.calculateStatistics([0.02]);
            expect(mean).toBe(0.02);
            expect(stdDev).toBe(0);
        });

        it('should handle empty array', () => {
            const {mean, stdDev} = formulas.calculateStatistics([]);
            expect(mean).toBe(0);
            expect(stdDev).toBe(0);
        });
    });

    describe('scenarioRates', () => {
        it('should calculate conservative, baseline, and optimistic rates', () => {
            const irr = 0.02386; // 2.386% monthly
            const mean = 0.0261; // 2.61% monthly
            const stdDev = 0.0428; // 4.28 percentage points

            const scenarios = formulas.calculateScenarioRates(irr, mean, stdDev);

            // Conservative floor is 0.5% (0.005) as per formula
            expect(scenarios.conservative).toBe(0.005); // max(0.005, 2.386% - 4.28%)
            expect(scenarios.baseline).toBe(irr);
            expect(scenarios.optimistic).toBe(mean);
        });

        it('should handle negative conservative rate', () => {
            const scenarios = formulas.calculateScenarioRates(0.01, 0.02, 0.03);
            expect(scenarios.conservative).toBe(0.005); // max(0.005, -0.02)
        });
    });

    describe('annualizedReturn', () => {
        it('should convert monthly return to annual', () => {
            const monthlyReturn = 0.02;
            const annualReturn = formulas.monthlyToAnnualReturn(monthlyReturn);
            expect(annualReturn).toBeCloseTo(0.2682, 4); // (1.02^12 - 1)
        });
    });

    describe('futureValueWithBonus', () => {
        it('should calculate future value including annual bonus', () => {
            const pv = 100000;
            const monthlyRate = 0.02;
            const monthlyContribution = 1000;
            const annualBonus = 20000;
            const months = 14; // Cross one bonus period

            const result = formulas.futureValueWithBonus(
                pv, monthlyRate, monthlyContribution, annualBonus, months, 3
            );

            // Should be higher than without bonus
            const withoutBonus = formulas.futureValueWithContributions(pv, monthlyRate, monthlyContribution, months);
            expect(result).toBeGreaterThan(withoutBonus);
        });

        it('should not add bonus if bonus month is not crossed', () => {
            const pv = 100000;
            const monthlyRate = 0.02;
            const monthlyContribution = 1000;
            const annualBonus = 20000;
            const months = 6;
            // Current month is 7, bonus month is 3. We won't cross it in 6 months.
            const result = formulas.futureValueWithBonus(
                pv, monthlyRate, monthlyContribution, annualBonus, months, 3
            );
            const withoutBonus = formulas.futureValueWithContributions(pv, monthlyRate, monthlyContribution, months);
            expect(result).toBeCloseTo(withoutBonus);
        });
    });

    describe('deltaMonthsBetweenContributions', () => {
        it('should calculate delay for $1M target', () => {
            const pv = 786000;
            const target = 1000000;
            const rate = 0.0239;

            // Delay vs $6,250/mo baseline
            expect(formulas.deltaMonthsBetweenContributions(pv, target, rate, 6250, 5260)).toBe(1);
            expect(formulas.deltaMonthsBetweenContributions(pv, target, rate, 6250, 4200)).toBe(1);
        });

        it('should calculate delay for $1.5M target', () => {
            const pv = 786000;
            const target = 1500000;
            const rate = 0.0239;

            // Delay vs $6,250/mo baseline
            expect(formulas.deltaMonthsBetweenContributions(pv, target, rate, 6250, 5260)).toBe(0);
            expect(formulas.deltaMonthsBetweenContributions(pv, target, rate, 6250, 4200)).toBe(1);
        });

        it('should calculate delay for $2M target', () => {
            const pv = 786000;
            const target = 2000000;
            const rate = 0.0239;

            // Delay vs $6,250/mo baseline
            expect(formulas.deltaMonthsBetweenContributions(pv, target, rate, 6250, 5260)).toBe(1);
            expect(formulas.deltaMonthsBetweenContributions(pv, target, rate, 6250, 4200)).toBe(2);
        });
    });

    describe('contributionShare', () => {
        it('should calculate the fraction of growth from contributions', () => {
            expect(formulas.contributionShare(100, 200, 10, 5)).toBe(0.5);
        });

        it('should return 0 if there is no growth', () => {
            expect(formulas.contributionShare(100, 100, 10, 5)).toBe(0);
            expect(formulas.contributionShare(100, 90, 10, 5)).toBe(0);
        });
    });

    describe('Real Data Pack Values', () => {
        it('should match data pack IRR calculation', () => {
            // From data pack: Dec 2023 (407,553) to Jul 2025 (786,000)
            const portfolioValues = [407553, 485000, 495058, 537000, 542000, 567000, 600000, 610000, 682000, 762000, 786000];
            const monthlyContribution = 6250; // From data pack

            const irr = formulas.calculatePortfolioIRR(portfolioValues, monthlyContribution);
            expect(irr).toBeCloseTo(0.02386, 3); // Should match 2.386% from data pack
        });

        it('should calculate months to million with data pack values', () => {
            const currentValue = 786000; // From data pack
            const monthlyContribution = 6250;
            const conservativeRate = 0.0069; // From data pack

            const monthsToMillion = formulas.monthsToTarget(currentValue, 1000000, conservativeRate, monthlyContribution);
            expect(monthsToMillion).toBe(18); // Should be 18 months with conservative rate
        });
    });
});