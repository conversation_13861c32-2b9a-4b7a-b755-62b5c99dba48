import {PayslipEntry} from './types';

export class PayslipParser {
    static parsePayslip(payslipText: string): PayslipEntry {
        console.log('parsePayslip: start');
        const lines = payslipText.split('\n').map(line => line.trim());
        console.log('parsePayslip: lines count=', lines.length);
        let result: Partial<PayslipEntry> = {};

        const checkDate = this.extractCheckDate(lines);
        console.log('parsePayslip: date=', checkDate);
        if (checkDate) result.date = checkDate;

        const gross = this.extractGrossPay(lines);
        console.log('parsePayslip: gross=', gross);
        if (gross !== null) result.gross = gross;

        const net = this.extractNetPay(lines);
        console.log('parsePayslip: net=', net);
        if (net !== null) result.net = net;

        const investments = this.extractInvestments(lines);
        console.log('parsePayslip: investments=', investments);
        result.espp = investments.espp;
        result.roth_e = investments.roth_e;
        result.roth_r = investments.roth_r;
        result.total_invest = result.espp + result.roth_e + result.roth_r;
        console.log('parsePayslip: total_invest=', result.total_invest);

        if (!result.date || result.gross === undefined || result.net === undefined) {
            console.error('parsePayslip: missing required fields', result);
            throw new Error('Unable to parse required payslip fields');
        }

        console.log('parsePayslip: result=', result);
        return result as PayslipEntry;
    }

    private static extractCheckDate(lines: string[]): string | null {
        const start = lines.findIndex(l => l.includes('Check Date'));
        if (start === -1) return null;
        const end = lines.findIndex((l, idx) => idx > start && l.includes('Current and YTD Totals'));
        const dates: string[] = [];
        const stop = end === -1 ? lines.length : end;
        for (let i = start + 1; i < stop; i++) {
            const m = lines[i].match(/(\d{2}\/\d{2}\/\d{4})/);
            if (m) dates.push(m[1]);
        }
        if (!dates.length) return null;
        const [MM, DD, YYYY] = dates[dates.length - 1].split('/');
        return `${YYYY}-${MM.padStart(2, '0')}-${DD.padStart(2, '0')}`;
    }

    private static extractGrossPay(lines: string[]): number | null {
        const byLabel = this.extractAmountAfterLabel(lines, 'Gross Pay');
        if (byLabel !== null) return byLabel;
        const totalsIdx = lines.findIndex(l => l.includes('Current and YTD Totals'));
        const currIdx = totalsIdx >= 0 ? lines.indexOf('Current', totalsIdx + 1) : lines.indexOf('Current');
        if (currIdx >= 0 && currIdx + 2 < lines.length) {
            return this.parseAmount(lines[currIdx + 2]);
        }
        return null;
    }

    private static extractNetPay(lines: string[]): number | null {
        const byLabel = this.extractAmountAfterLabel(lines, 'Net Pay');
        if (byLabel !== null) return byLabel;
        const totalsIdx = lines.findIndex(l => l.includes('Current and YTD Totals'));
        const currIdx = totalsIdx >= 0 ? lines.indexOf('Current', totalsIdx + 1) : lines.indexOf('Current');
        if (currIdx >= 0 && currIdx + 6 < lines.length) {
            return this.parseAmount(lines[currIdx + 6]);
        }
        return null;
    }

    private static extractAmountAfterLabel(lines: string[], label: string): number | null {
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(label)) {
                let m = lines[i].match(/([\d,]+\.\d{2})/);
                if (!m && i + 1 < lines.length) {
                    m = lines[i + 1].match(/([\d,]+\.\d{2})/);
                }
                if (m) return parseFloat(m[1].replace(/,/g, ''));
            }
        }
        return null;
    }

    private static extractInvestments(lines: string[]): { espp: number; roth_e: number; roth_r: number } {
        console.log('extractInvestments: start');
        let espp = 0, roth_e = 0, roth_r = 0;
        const start = lines.findIndex(l => l.includes('Post Tax Deductions'));
        console.log('extractInvestments: postTaxIndex=', start);
        if (start === -1) return {espp, roth_e, roth_r};
        for (let i = start + 1; i < lines.length; i++) {
            const line = lines[i].trim();
            console.log('extractInvestments: scanning line', i, line);
            if (line.startsWith('Total')) break;
            if (line === 'ESPP' && i + 1 < lines.length) {
                espp = this.parseAmount(lines[i + 1]) ?? 0;
                console.log('extractInvestments: set espp=', espp);
            }
            if (line === 'GapShare Roth E' && i + 1 < lines.length) {
                roth_e = this.parseAmount(lines[i + 1]) ?? 0;
                console.log('extractInvestments: set roth_e=', roth_e);
            }
            if (line === 'GapShare Roth R' && i + 1 < lines.length) {
                roth_r = this.parseAmount(lines[i + 1]) ?? 0;
                console.log('extractInvestments: set roth_r=', roth_r);
            }
        }
        console.log('extractInvestments: result=', {espp, roth_e, roth_r});
        return {espp, roth_e, roth_r};
    }

    private static parseAmount(amountStr?: string): number | null {
        if (!amountStr) return null;
        const clean = amountStr.replace(/[\$,]/g, '');
        const n = parseFloat(clean);
        return isNaN(n) ? null : n;
    }

    static parseMultiplePayslips(texts: string[]): PayslipEntry[] {
        const payslips: PayslipEntry[] = [];
        for (const t of texts) {
            try {
                payslips.push(this.parsePayslip(t));
            } catch {
                console.warn('Failed to parse payslip.');
            }
        }
        payslips.sort((a, b) => a.date.localeCompare(b.date));
        return payslips;
    }

    static validatePayslipData(entry: PayslipEntry): boolean {
        if (!entry.date.match(/^\d{4}-\d{2}-\d{2}$/)) return false;
        if (entry.gross < 0 || entry.net < 0) return false;
        if (entry.espp < 0 || entry.roth_e < 0 || entry.roth_r < 0) return false;
        const calc = entry.espp + entry.roth_e + entry.roth_r;
        if (Math.abs(entry.total_invest - calc) > 0.01) return false;
        return true;
    }

    static formatPayslipData(entries: PayslipEntry[]): string {
        let out = 'export const GENERATED_PAYSLIP_DATA: PayslipEntry[] = [\n';
        entries.forEach((e, i) => {
            out += `  {\n`;
            out += `    date: "${e.date}", gross: ${e.gross}, net: ${e.net}, espp: ${e.espp}, roth_e: ${e.roth_e}, roth_r: ${e.roth_r}, total_invest: ${e.total_invest}\n`;
            out += i < entries.length - 1 ? '  },\n' : '  }\n';
        });
        out += '];';
        return out;
    }
}