import { INITIAL_PORTFOLIO_DATA } from '../data/portfolio-data';
import { INITIAL_PAYSLIP_DATA } from '../data/payslip-data';
import { WEALTH_CONSTANTS } from '../data/constants';
import { PayslipParser } from '../payslip-parser';

describe('Data Validation', () => {
  describe('Portfolio Data', () => {
    it('should have valid portfolio entries', () => {
      expect(INITIAL_PORTFOLIO_DATA).toBeDefined();
      expect(Array.isArray(INITIAL_PORTFOLIO_DATA)).toBe(true);
      expect(INITIAL_PORTFOLIO_DATA.length).toBeGreaterThan(0);
      
      INITIAL_PORTFOLIO_DATA.forEach(entry => {
        expect(entry).toHaveProperty('date');
        expect(entry).toHaveProperty('trow');
        expect(entry).toHaveProperty('robinhood');
        expect(entry).toHaveProperty('etrade');
        expect(entry).toHaveProperty('teradata');
        
        expect(typeof entry.date).toBe('string');
        expect(typeof entry.trow).toBe('number');
        expect(typeof entry.robinhood).toBe('number');
        expect(typeof entry.etrade).toBe('number');
        expect(typeof entry.teradata).toBe('number');
        
        expect(entry.trow).toBeGreaterThan(0);
        expect(entry.robinhood).toBeGreaterThan(0);
        expect(entry.etrade).toBeGreaterThan(0);
        expect(entry.teradata).toBeGreaterThan(0);
      });
    });

    it('should have dates in correct format', () => {
      INITIAL_PORTFOLIO_DATA.forEach(entry => {
        expect(entry.date).toMatch(/^\d{4}-\d{2}$/);
      });
    });

    it('should be sorted chronologically', () => {
      for (let i = 1; i < INITIAL_PORTFOLIO_DATA.length; i++) {
        const prevDate = new Date(INITIAL_PORTFOLIO_DATA[i - 1].date + '-01');
        const currDate = new Date(INITIAL_PORTFOLIO_DATA[i].date + '-01');
        expect(currDate >= prevDate).toBe(true);
      }
    });
  });

  describe('Payslip Data', () => {
    it('should have valid payslip entries', () => {
      expect(INITIAL_PAYSLIP_DATA).toBeDefined();
      expect(Array.isArray(INITIAL_PAYSLIP_DATA)).toBe(true);
      expect(INITIAL_PAYSLIP_DATA.length).toBeGreaterThan(0);
      
      INITIAL_PAYSLIP_DATA.forEach(entry => {
        expect(entry).toHaveProperty('date');
        expect(entry).toHaveProperty('gross');
        expect(entry).toHaveProperty('net');
        expect(entry).toHaveProperty('espp');
        expect(entry).toHaveProperty('roth_e');
        expect(entry).toHaveProperty('roth_r');
        expect(entry).toHaveProperty('total_invest');
        
        expect(typeof entry.date).toBe('string');
        expect(typeof entry.gross).toBe('number');
        expect(typeof entry.net).toBe('number');
        expect(typeof entry.espp).toBe('number');
        expect(typeof entry.roth_e).toBe('number');
        expect(typeof entry.roth_r).toBe('number');
        expect(typeof entry.total_invest).toBe('number');
        
        expect(entry.gross).toBeGreaterThan(0);
        expect(entry.net).toBeGreaterThan(0);
        expect(entry.espp).toBeGreaterThanOrEqual(0);
        expect(entry.roth_e).toBeGreaterThanOrEqual(0);
        expect(entry.roth_r).toBeGreaterThanOrEqual(0);
        expect(entry.total_invest).toBeGreaterThanOrEqual(0);
      });
    });

    it('should have dates in correct format', () => {
      INITIAL_PAYSLIP_DATA.forEach(entry => {
        expect(entry.date).toMatch(/^\d{4}-\d{2}-\d{2}$/);
      });
    });

    it('should have consistent total_invest calculation', () => {
      INITIAL_PAYSLIP_DATA.forEach(entry => {
        const calculatedTotal = entry.espp + entry.roth_e + entry.roth_r;
        expect(Math.abs(entry.total_invest - calculatedTotal)).toBeLessThan(0.01);
      });
    });

    it('should be sorted chronologically', () => {
      for (let i = 1; i < INITIAL_PAYSLIP_DATA.length; i++) {
        const prevDate = new Date(INITIAL_PAYSLIP_DATA[i - 1].date);
        const currDate = new Date(INITIAL_PAYSLIP_DATA[i].date);
        expect(currDate >= prevDate).toBe(true);
      }
    });

    it('should pass PayslipParser validation', () => {
      INITIAL_PAYSLIP_DATA.forEach(entry => {
        expect(PayslipParser.validatePayslipData(entry)).toBe(true);
      });
    });
  });

  describe('Constants', () => {
    it('should have valid wealth constants', () => {
      expect(WEALTH_CONSTANTS).toBeDefined();
      expect(typeof WEALTH_CONSTANTS.ANNUAL_BONUS_GROSS).toBe('number');
      expect(typeof WEALTH_CONSTANTS.ANNUAL_BONUS_NET).toBe('number');
      expect(typeof WEALTH_CONSTANTS.BONUS_MONTH).toBe('number');
      expect(typeof WEALTH_CONSTANTS.PAYCHECKS_PER_YEAR).toBe('number');
      expect(typeof WEALTH_CONSTANTS.MONTHLY_EXPENSES).toBe('number');
      
      expect(WEALTH_CONSTANTS.ANNUAL_BONUS_GROSS).toBeGreaterThan(0);
      expect(WEALTH_CONSTANTS.ANNUAL_BONUS_NET).toBeGreaterThan(0);
      expect(WEALTH_CONSTANTS.BONUS_MONTH).toBeGreaterThan(0);
      expect(WEALTH_CONSTANTS.BONUS_MONTH).toBeLessThanOrEqual(12);
      expect(WEALTH_CONSTANTS.PAYCHECKS_PER_YEAR).toBeGreaterThan(0);
      expect(WEALTH_CONSTANTS.MONTHLY_EXPENSES).toBeGreaterThan(0);
      
      expect(WEALTH_CONSTANTS.ANNUAL_BONUS_NET).toBeLessThan(WEALTH_CONSTANTS.ANNUAL_BONUS_GROSS);
    });
  });

  describe('Data Relationships', () => {
    it('should have reasonable net to gross ratios in payslips', () => {
      INITIAL_PAYSLIP_DATA.forEach(entry => {
        const netToGrossRatio = entry.net / entry.gross;
        expect(netToGrossRatio).toBeGreaterThan(0.1);
        expect(netToGrossRatio).toBeLessThan(1.0);
      });
    });

    it('should have investment rates within reasonable bounds', () => {
      INITIAL_PAYSLIP_DATA.forEach(entry => {
        const investmentRate = entry.total_invest / entry.gross;
        expect(investmentRate).toBeGreaterThanOrEqual(0);
        expect(investmentRate).toBeLessThan(0.8); // Should not exceed 80% of gross
      });
    });

    it('should have portfolio values showing general growth trend', () => {
      const firstEntry = INITIAL_PORTFOLIO_DATA[0];
      const lastEntry = INITIAL_PORTFOLIO_DATA[INITIAL_PORTFOLIO_DATA.length - 1];
      
      const firstTotal = firstEntry.trow + firstEntry.robinhood + firstEntry.etrade + firstEntry.teradata;
      const lastTotal = lastEntry.trow + lastEntry.robinhood + lastEntry.etrade + lastEntry.teradata;
      
      expect(lastTotal).toBeGreaterThan(firstTotal);
    });
  });

  describe('Cross-reference with sample data', () => {
    it('should match known payslip values', () => {
      // Test against the March 26, 2025 payslip from the raw data
      const march26Payslip = INITIAL_PAYSLIP_DATA.find(p => p.date === '2025-03-26');
      expect(march26Payslip).toBeDefined();
      
      if (march26Payslip) {
        expect(march26Payslip.gross).toBe(6419.23);
        expect(march26Payslip.net).toBe(1287.33);
        expect(march26Payslip.espp).toBe(962.89);
        expect(march26Payslip.roth_e).toBe(1669.00);
        expect(march26Payslip.roth_r).toBe(256.77);
        expect(march26Payslip.total_invest).toBe(2888.66);
      }
    });

    it('should match known portfolio values', () => {
      // Test against the July 2025 portfolio data
      const july2025Portfolio = INITIAL_PORTFOLIO_DATA.find(p => p.date === '2025-07');
      expect(july2025Portfolio).toBeDefined();
      
      if (july2025Portfolio) {
        expect(july2025Portfolio.trow).toBe(270000);
        expect(july2025Portfolio.robinhood).toBe(115000);
        expect(july2025Portfolio.etrade).toBe(366000);
        expect(july2025Portfolio.teradata).toBe(35000);
      }
    });
  });
});