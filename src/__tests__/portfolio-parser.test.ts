import { PortfolioParser } from '../portfolio-parser';
import { portfolioHistory } from './data/fixtures';

const realPortfolioData = `
Account    Dec 1, 2023    March 1, 2024    April,2024    May 2024    June 2024    July 2024    Nov 2024    Feb  2025    May 2025    Jun 2025    Jul 2025
Trow Retirement    $160,036.32    $185,000    $190,577    $200,000    $205,000    215000    218000    220000    250000    270000    270000
Robin Hood    $60,000.00    $70,000    $70,000    $75,000    $75,000    80000    85000    100000    100000    110000    115000
E*Trade    $155,517.34    $173,000    $202,481    $230,000    $230,000    240,000    265000    255000    300000    350000    366000
Teradata 401k    $32,000.00    $32,000.00    $32,000.00    $32,000.00    $32,000.00    32000    32000    32000    32000    32000    35000
Total Savings    $407,553.66    $485,000    $495,058    $537,000    $542,000    567,000    600000    610000    682000    762,000    786,000
`;

describe('PortfolioParser', () => {
  it('should parse portfolio table correctly', () => {
    const result = PortfolioParser.parsePortfolioTable(realPortfolioData);
    
    expect(result.length).toBeGreaterThan(0);
    
    // Check first entry (Dec 2023)
    const firstEntry = result.find(entry => entry.date === '2023-12');
    expect(firstEntry).toBeDefined();
    expect(firstEntry?.trow).toBe(160036.32);
    expect(firstEntry?.robinhood).toBe(60000.00);
    expect(firstEntry?.etrade).toBe(155517.34);
    expect(firstEntry?.teradata).toBe(32000.00);
    
    // Check last entry (Jul 2025)
    const lastEntry = result.find(entry => entry.date === '2025-07');
    expect(lastEntry).toBeDefined();
    expect(lastEntry?.trow).toBe(portfolioHistory[portfolioHistory.length - 1].trow);
    expect(lastEntry?.robinhood).toBe(portfolioHistory[portfolioHistory.length - 1].robinhood);
    expect(lastEntry?.etrade).toBe(portfolioHistory[portfolioHistory.length - 1].etrade);
    expect(lastEntry?.teradata).toBe(portfolioHistory[portfolioHistory.length - 1].teradata);
  });

  it('should validate portfolio data', () => {
    const result = PortfolioParser.parsePortfolioTable(realPortfolioData);
    expect(result.length).toBeGreaterThan(0);
    result.forEach(entry => {
      expect(PortfolioParser.validatePortfolioData(entry)).toBe(true);
    });
  });

  it('should generate data that matches existing portfolio data', () => {
    const result = PortfolioParser.parsePortfolioTable(realPortfolioData);
    
    // Check specific known values
    const jul2025 = result.find(entry => entry.date === '2025-07');
    expect(jul2025?.trow).toBe(portfolioHistory[portfolioHistory.length - 1].trow);
    expect(jul2025?.robinhood).toBe(portfolioHistory[portfolioHistory.length - 1].robinhood);
    expect(jul2025?.etrade).toBe(portfolioHistory[portfolioHistory.length - 1].etrade);
    expect(jul2025?.teradata).toBe(portfolioHistory[portfolioHistory.length - 1].teradata);
    
    const dec2023 = result.find(entry => entry.date === '2023-12');
    expect(dec2023?.trow).toBe(portfolioHistory[0].trow);
    expect(dec2023?.robinhood).toBe(portfolioHistory[0].robinhood);
    expect(dec2023?.etrade).toBe(portfolioHistory[0].etrade);
    expect(dec2023?.teradata).toBe(portfolioHistory[0].teradata);
  });

  it('should format portfolio data for output', () => {
    const result = PortfolioParser.parsePortfolioTable(realPortfolioData);
    const formatted = PortfolioParser.formatPortfolioForOutput(result);
    
    expect(formatted).toContain('export const GENERATED_PORTFOLIO_DATA');
    expect(formatted).toContain('date: "2023-12"');
    expect(formatted).toContain('trow: 160036.32');
    expect(formatted).toContain('robinhood: 60000');
    expect(formatted).toContain('etrade: 155517.34');
    expect(formatted).toContain('teradata: 32000');
  });
});

  