export const BASE = {
  PV: 786_000, // USD (Jul-2025)
  RATE: { conserv: 0.0069, base: 0.0239, optim: 0.0427 }, // monthly r
  PMT: { p6250: 6250, p5260: 5260, p4200: 4200 }, // USD / month
  GOAL: { oneM: 1_000_000, one5M: 1_500_000, twoM: 2_000_000 },
};

export const EXPECT = {
  // F-2 : monthsNeeded() (baseline rate 2.39 %)
  nBaseline: {
    p6250: { oneM: 8, one5M: 23, twoM: 33 },
    p5260: { oneM: 9, one5M: 23, twoM: 34 },
    p4200: { oneM: 9, one5M: 24, twoM: 35 },
  },

  // F-3 : delay vs 6 250 USD/mo (baseline rate)
  delayVs6250: {
    oneM: { p5260: 1, p4200: 1 },
    one5M: { p5260: 0, p4200: 1 },
    twoM: { p5260: 1, p4200: 2 },
  },

  // F-1 : future value after 24 months @2.39 %, 6 250 USD/mo
  fv24mo: 1_195_415, // USD (for sanity check)

  // F-4 : IRR solved from full 19-month history (+6 250 USD/mo)
  irrHist: 0.02386, // decimal ≈ 2.386 % / mo (3 dp)

  // F-5 : contribution share to 1 M @ baseline
  share: {
    p6250: 0.28, // ≈ 28 % of growth came from new money
    p5260: 0.31,
    p4200: 0.35,
  },
};
