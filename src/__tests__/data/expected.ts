export const expectedTestData = {
  meta: {
    start_balance: 786000,
    start_date_ISO: "2025-07-01",
    monthly_growth_rate: 0.0239,
    rounding: "ceil to next whole month",
    formula_used: "n = ln((FV*r + PMT)/(PV*r + PMT)) / ln(1+r)"
  },

  test_cases: [
    // $1,000,000 target
    { PMT: 6250, FV: 1000000, expect_months: 8,  expect_date: "2026-03-01", expect_delay_vs_6250: 0 },
    { PMT: 5260, FV: 1000000, expect_months: 9,  expect_date: "2026-04-01", expect_delay_vs_6250: 1 },
    { PMT: 4200, FV: 1000000, expect_months: 9,  expect_date: "2026-04-01", expect_delay_vs_6250: 1 },

    // $1,500,000 target
    { PMT: 6250, FV: 1500000, expect_months: 23, expect_date: "2027-06-01", expect_delay_vs_6250: 0 },
    { PMT: 5260, FV: 1500000, expect_months: 23, expect_date: "2027-06-01", expect_delay_vs_6250: 0 },
    { PMT: 4200, FV: 1500000, expect_months: 24, expect_date: "2027-07-01", expect_delay_vs_6250: 1 },

    // $2,000,000 target
    { PMT: 6250, FV: 2000000, expect_months: 33, expect_date: "2028-04-01", expect_delay_vs_6250: 0 },
    { PMT: 5260, FV: 2000000, expect_months: 34, expect_date: "2028-05-01", expect_delay_vs_6250: 1 },
    { PMT: 4200, FV: 2000000, expect_months: 35, expect_date: "2028-06-01", expect_delay_vs_6250: 2 }
  ],

  // Additional test cases for specific formulas
  formula_tests: [
    // D-1: Future Value tests
    {
      name: "D1_FV_8mo_6250",
      formula: "D-1",
      inputs: { PV: 786000, r: 0.0239, PMT: 6250, n: 8 },
      expect_FV: 1003863
    },
    {
      name: "D1_FV_23mo_6250", 
      formula: "D-1",
      inputs: { PV: 786000, r: 0.0239, PMT: 6250, n: 23 },
      expect_FV: 1542402
    },

    // D-3: Months-to-target tests
    { 
      name: "D3_to_1M_6250", 
      formula: "D-3",
      inputs: { PV: 786000, FV: 1000000, r: 0.0239, PMT: 6250 },
      expect_n: 8, 
      expect_date: "2026-03-01" 
    },
    { 
      name: "D3_to_1M_5260", 
      formula: "D-3",
      inputs: { PV: 786000, FV: 1000000, r: 0.0239, PMT: 5260 },
      expect_n: 9, 
      expect_date: "2026-04-01" 
    },
    { 
      name: "D3_to_1M_4200", 
      formula: "D-3",
      inputs: { PV: 786000, FV: 1000000, r: 0.0239, PMT: 4200 },
      expect_n: 9, 
      expect_date: "2026-04-01" 
    },
    { 
      name: "D3_to_1_5M_6250", 
      formula: "D-3",
      inputs: { PV: 786000, FV: 1500000, r: 0.0239, PMT: 6250 },
      expect_n: 23, 
      expect_date: "2027-06-01" 
    },
    { 
      name: "D3_to_2M_4200", 
      formula: "D-3",
      inputs: { PV: 786000, FV: 2000000, r: 0.0239, PMT: 4200 },
      expect_n: 35, 
      expect_date: "2028-06-01" 
    },

    // D-4: Delay between PMTs tests
    { 
      name: "D4_delay_2M_4200_vs_6250",
      formula: "D-4",
      inputs: {
        PV: 786000, 
        FV: 2000000, 
        r: 0.0239,
        PMT_fast: 6250, 
        PMT_slow: 4200
      },
      expect_Δn: 2 
    },

    // F-6: Required PMT tests
    { 
      name: "F6_reqC_2M_36mo",
      formula: "F-6",
      inputs: { PV: 786000, FV: 2000000, r: 0.0239, n: 36 },
      expect_PMT: 2862.44 
    },

    // F-8: Component split tests
    { 
      name: "F8_share_23mo",
      formula: "F-8",
      inputs: { PV: 786000, r: 0.0239, PMT: 6250, n: 23 },
      expect: {
        FV_total: 1542402,
        FV_from_PV: 1353390,
        FV_from_contr: 188012,
        share_contr: 0.122
      }
    },

    // F-9: Coast-fire threshold tests
    { 
      name: "F9_coast_thresh",
      formula: "F-9",
      inputs: { monthly_r: 0.0239, PMT: 6250 },
      expect_PV_coast: 229000 
    }
  ],

  variable_legend: {
    PV: "present value (starting balance, here $786,000)",
    PMT: "monthly contribution (your inputs: 6,250 / 5,260 / 4,200)",
    r: "monthly growth rate as decimal (0.0239 = 2.39%)",
    FV: "future value target",
    n: "months required (rounded ↑)",
    expect_date: "ISO yyyy‑mm‑dd date achieved (assumes payments at month‑end)"
  }
};
