export const BASE = {
  PV: 786_000, // USD (Jul-2025)
  RATE: { conserv: 0.0069, base: 0.0239, optim: 0.0427 }, // monthly r
  PMT: { p6250: 6250, p5260: 5260, p4200: 4200 }, // USD / month
  GOAL: { oneM: 1_000_000, one5M: 1_500_000, twoM: 2_000_000 },
};

export const EXPECT = {
  // F-2 : monthsNeeded() (baseline rate 2.39 %)
  nBaseline: {
    p6250: { oneM: 8, one5M: 23, twoM: 33 },
    p5260: { oneM: 9, one5M: 23, twoM: 34 },
    p4200: { oneM: 9, one5M: 24, twoM: 35 },
  },

  // F-3 : delay vs 6 250 USD/mo (baseline rate)
  delayVs6250: {
    oneM: { p5260: 1, p4200: 1 },
    one5M: { p5260: 0, p4200: 1 },
    twoM: { p5260: 1, p4200: 2 },
  },

  // F-1 : future value after 24 months @2.39 %, 6 250 USD/mo
  fv24mo: 1584944,

  // F-4 : IRR solved from full 19-month history (+6 250 USD/mo)
  irrHist: 0.02386, 

  // F-5 : contribution share to 1 M @ baseline
  share: {
    p6250: 0.23, 
    p5260: 0.31,
    p4200: 0.35,
  },
};

export const portfolioHistory = [
  { date: '2023-12', trow: 160036.32, robinhood: 60000.00, etrade: 155517.34, teradata: 32000.00 },
  { date: '2024-03', trow: 185000, robinhood: 70000, etrade: 173000, teradata: 32000 },
  { date: '2024-04', trow: 190577, robinhood: 70000, etrade: 202481, teradata: 32000 },
  { date: '2024-05', trow: 200000, robinhood: 75000, etrade: 230000, teradata: 32000 },
  { date: '2024-06', trow: 205000, robinhood: 75000, etrade: 230000, teradata: 32000 },
  { date: '2024-07', trow: 215000, robinhood: 80000, etrade: 240000, teradata: 32000 },
  { date: '2024-11', trow: 218000, robinhood: 85000, etrade: 265000, teradata: 32000 },
  { date: '2025-02', trow: 220000, robinhood: 100000, etrade: 255000, teradata: 32000 },
  { date: '2025-05', trow: 250000, robinhood: 100000, etrade: 300000, teradata: 32000 },
  { date: '2025-06', trow: 270000, robinhood: 110000, etrade: 350000, teradata: 32000 },
  { date: '2025-07', trow: 270000, robinhood: 115000, etrade: 366000, teradata: 35000 },
];