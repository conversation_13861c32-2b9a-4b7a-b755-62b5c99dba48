// Load environment variables

// Export main application class
export { BusinessSearchApp } from './BusinessSearchApp';

// Export types and interfaces
export type { AppConfig } from './BusinessSearchApp';
export type { SearchRequest, SearchStatistics } from './services/businessSearch';
export type {
  Business,
  SearchResult,
  SearchParams,
  Coordinates,
  WebsiteStatus,
  BusinessMetadata,
  CategorizedResults
} from './models/Business';

// Export error classes
export {
  AppError,
  ValidationError,
  ApiError,
  NetworkError,
  GeocodingError,
  WebsiteVerificationError,
  RateLimitError,
  CacheError,
  ConfigurationError
} from './models/Errors';

// Export utility functions
export {
  validateZipCode,
  validateRadius,
  validateBusinessType,
  validateUrl,
  validateSearchParams
} from './utils/validation';

export {
  normalizeUrl,
  isValidUrl,
  extractDomain,
  cleanUrl
} from './utils/url';

export {
  calculateDistance,
  calculateDistanceKm,
  findClosest,
  isWithinRadius,
  sortByDistance
} from './utils/distance';

// Export constants
export { BUSINESS_TYPES, ERROR_MESSAGES } from './constants';
