import * as dotenv from 'dotenv';
dotenv.config();

import { WeekendEventService } from './services/WeekendEventService';
import { WebFetcher } from './utils/webFetcher';
import { formatSecondSaturdays } from './utils/ishaSatsangUtils';
import { WeeklyScheduler } from './scheduler/WeeklyScheduler';

export async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  // Validate configuration on startup (except for help command)
  if (command !== 'help' && command !== 'h' && command !== undefined) {
    try {
      const { config } = await import('./utils/config');
      config.validateAndLoad();
    } catch (error) {
      console.error(
        '❌ Configuration Error:',
        error instanceof Error ? error.message : 'Unknown error'
      );
      console.error('\nPlease ensure all required environment variables are set in your .env file');
      process.exit(1);
    }
  }

  const service = new WeekendEventService();
  const scheduler = new WeeklyScheduler();

  switch (command) {
    case 'weekend':
    case 'w':
      await handleWeekendCommand(service, args.slice(1));
      break;

    case 'fetch':
    case 'f':
      await handleFetchCommand(args.slice(1));
      break;

    case 'isha':
    case 'satsang':
      handleIshaSatsangCommand(args.slice(1));
      break;

    case 'whatsapp':
    case 'wa':
      await handleWhatsAppCommand(scheduler, args.slice(1));
      break;

    case 'email':
    case 'e':
      await handleEmailCommand(args.slice(1));
      break;

    case 'schedule':
    case 'scheduler':
      await handleSchedulerCommand(scheduler, args.slice(1));
      break;

    case 'health':
      await handleHealthCommand();
      break;

    case 'help':
    case 'h':
    case undefined:
      showHelp();
      break;

    default:
      console.error(`Unknown command: ${command}`);
      showHelp();
      process.exit(1);
  }
}

export async function handleWeekendCommand(service: WeekendEventService, args: string[]) {
  try {
    let referenceDate = new Date();

    if (args.length > 0) {
      // Try to parse custom date
      const dateStr = args.join(' ');
      const customDate = new Date(dateStr);
      if (!isNaN(customDate.getTime())) {
        referenceDate = customDate;
      } else {
        console.error(`Invalid date format: ${dateStr}`);
        console.error('Use format like: "2025-07-11" or "July 11, 2025"');
        process.exit(1);
      }
    }

    const report = await service.formatWeekendReport(referenceDate);
    console.log(report);
  } catch (error) {
    console.error('Error generating weekend report:', error);
    process.exit(1);
  }
}

export async function handleFetchCommand(args: string[]) {
  if (args.length === 0) {
    console.error('Please provide a URL');
    process.exit(1);
  }

  const url = args[0];

  try {
    let content: string;

    if (WebFetcher.isGoogleDocUrl(url)) {
      const documentId = WebFetcher.extractDocumentId(url);
      if (!documentId) {
        console.error('Invalid Google Doc URL');
        process.exit(1);
      }
      console.log('Fetching Google Doc...');
      content = await WebFetcher.fetchGoogleDoc(documentId);
    } else {
      console.log('Fetching URL...');
      content = await WebFetcher.fetchUrl(url);
    }

    console.log('\n--- Fetched Content ---');
    console.log(content);
    console.log('\n--- End Content ---');

    // Optionally save to file
    if (args.includes('--save')) {
      const { writeFileSync } = await import('fs');
      const filename = `fetched-${Date.now()}.txt`;
      writeFileSync(filename, content);
      console.log(`\nContent saved to: ${filename}`);
    }
  } catch (error) {
    console.error('Failed to fetch content:', error);
    process.exit(1);
  }
}

export function handleIshaSatsangCommand(args: string[]) {
  const year = args.length > 0 ? parseInt(args[0]) : 2025;

  if (isNaN(year) || year < 2025 || year > 2030) {
    console.error('Please provide a valid year between 2025 and 2030');
    return;
  }

  console.log(`\nIsha Satsang Schedule for ${year} (Every Second Saturday):`);
  console.log('='.repeat(60));
  console.log(formatSecondSaturdays(year));
  console.log('\nTime: 7:00 PM – 9:00 PM');
  console.log('Location: South Bay Vipassana Hall, Santa Clara, CA');
  console.log('Description: Monthly spiritual gathering\n');
}

export async function handleWhatsAppCommand(scheduler: WeeklyScheduler, args: string[]) {
  const subCommand = args[0];

  switch (subCommand) {
    case 'test':
      await scheduler.sendTestMessage();
      break;

    case 'send':
      await scheduler.sendWeeklyUpdate();
      break;

    case 'send-custom':
      // Read custom message from stdin for test failures
      await handleCustomMessage();
      break;

    case 'error':
      // Send error notification
      const errorMessage = args.slice(1).join(' ') || 'Unknown error occurred';
      await handleErrorNotification(errorMessage);
      break;

    case undefined:
      console.error('WhatsApp subcommand required. Use: test, send, send-custom, error');
      console.log('\nAvailable WhatsApp commands:');
      console.log('  whatsapp test        Send a test message');
      console.log('  whatsapp send        Send current weekend events');
      console.log('  whatsapp send-custom Send custom message from stdin');
      console.log('  whatsapp error <msg> Send error notification');
      break;

    default:
      console.error(`Unknown WhatsApp command: ${subCommand}`);
      break;
  }
}

export async function handleCustomMessage() {
  const { WhatsAppService } = await import('./services/WhatsAppService');
  const whatsappService = new WhatsAppService();

  // Read from stdin
  let customMessage = '';
  process.stdin.setEncoding('utf8');

  return new Promise<void>(resolve => {
    process.stdin.on('readable', () => {
      const chunk = process.stdin.read();
      if (chunk !== null) {
        customMessage += chunk;
      }
    });

    process.stdin.on('end', async () => {
      if (customMessage.trim()) {
        const success = await whatsappService.sendWhatsAppMessage(customMessage.trim());
        if (success) {
          console.log('✅ Custom message sent successfully');
        } else {
          console.error('❌ Failed to send custom message');
          process.exit(1);
        }
      }
      resolve();
    });
  });
}

export async function handleSchedulerCommand(scheduler: WeeklyScheduler, args: string[]) {
  const subCommand = args[0];

  switch (subCommand) {
    case 'start':
      scheduler.start();
      break;

    case 'stop':
      scheduler.stop();
      break;

    case 'status':
      console.log(`Next scheduled run: ${scheduler.getNextRunTime()}`);
      break;

    case undefined:
      console.error('Scheduler subcommand required. Use: start, stop, status');
      console.log('\nAvailable scheduler commands:');
      console.log('  schedule start    Start weekly WhatsApp scheduler');
      console.log('  schedule stop     Stop weekly WhatsApp scheduler');
      console.log('  schedule status   Show next scheduled run time');
      break;

    default:
      console.error(`Unknown scheduler command: ${subCommand}`);
      break;
  }
}

export async function handleHealthCommand() {
  const { config } = await import('./utils/config');
  const { WhatsAppService } = await import('./services/WhatsAppService');
  const { LocationService } = await import('./services/LocationService');
  const { WebFetcher } = await import('./utils/webFetcher');

  console.log('🏥 VIP Health Check\n');
  console.log('='.repeat(50));

  let allHealthy = true;

  // 1. Configuration health check
  try {
    const healthResult = await config.performHealthCheck();

    console.log('\n📋 System Health:');
    healthResult.checks.forEach(check => {
      const icon = check.status === 'pass' ? '✅' : '❌';
      const duration = check.duration ? ` (${check.duration}ms)` : '';
      console.log(`  ${icon} ${check.name}: ${check.message}${duration}`);
    });

    if (!healthResult.healthy) {
      allHealthy = false;
    }
  } catch (error) {
    console.log('  ❌ Configuration: Failed to perform health check');
    allHealthy = false;
  }

  // 2. WhatsApp service health check
  console.log('\n📱 WhatsApp Service:');
  try {
    const whatsappService = new WhatsAppService();
    const health = await whatsappService.performHealthCheck();
    const icon = health.healthy ? '✅' : '❌';
    console.log(`  ${icon} Twilio: ${health.message}`);

    if (!health.healthy) {
      allHealthy = false;
    }
  } catch (error) {
    console.log('  ❌ WhatsApp: Service initialization failed');
    allHealthy = false;
  }

  // 3. Location service check
  console.log('\n📍 Location Service:');
  try {
    const locationService = new LocationService();
    const locationInfo = await locationService.getLocationInfo();
    const isInCA = await locationService.isInCalifornia();

    if (locationInfo) {
      console.log(
        `  ✅ Location: ${locationInfo.city}, ${locationInfo.region} (${isInCA ? 'In CA' : 'Not in CA'})`
      );
    } else {
      console.log('  ⚠️  Location: Could not determine location');
    }
  } catch (error) {
    console.log('  ❌ Location: Service unavailable');
  }

  // 4. Google Doc connectivity
  console.log('\n📄 Calendar Data:');
  try {
    const testDocId = '1IzhqoLq4i_5kMM_PsjYj9shT7UPRX8nR2uuQWQuDLuI';
    const startTime = Date.now();
    await WebFetcher.fetchGoogleDoc(testDocId);
    const duration = Date.now() - startTime;
    console.log(`  ✅ Google Docs: Accessible (${duration}ms)`);
  } catch (error) {
    console.log('  ❌ Google Docs: Cannot fetch calendar data');
    allHealthy = false;
  }

  // 5. Cron job status
  console.log('\n⏰ Scheduled Jobs:');
  const { exec } = await import('child_process');
  const { promisify } = await import('util');
  const execAsync = promisify(exec);

  try {
    const { stdout } = await execAsync('launchctl list | grep com.vip');
    const jobs = stdout.trim().split('\n');
    if (jobs.length > 0 && jobs[0]) {
      jobs.forEach(job => {
        console.log(`  ✅ ${job}`);
      });
    } else {
      console.log('  ⚠️  No scheduled jobs found');
    }
  } catch (error) {
    console.log('  ⚠️  No scheduled jobs found');
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  if (allHealthy) {
    console.log('✅ Overall Status: HEALTHY');
    process.exit(0);
  } else {
    console.log('❌ Overall Status: UNHEALTHY');
    process.exit(1);
  }
}

export async function handleEmailCommand(args: string[]) {
  const subCommand = args[0];
  const service = args[1]; // 'twilio' or 'gmail' 
  const toEmail = process.env.EMAIL_TO || '<EMAIL>';

  // Default to Twilio if not specified
  const useTwilio = service !== 'gmail';

  if (useTwilio) {
    const { TwilioEmailService } = await import('./services/TwilioEmailService');
    const emailService = new TwilioEmailService();

    switch (subCommand) {
      case 'test':
        await emailService.sendTestEmail(toEmail);
        break;

      case 'send':
        const { WeekendEventService } = await import('./services/WeekendEventService');
        const weekendService = new WeekendEventService();
        const report = await weekendService.formatWeekendReport(new Date());
        await emailService.sendWeekendEventsEmail(report, toEmail);
        break;

      case 'error':
        const errorMessage = args.slice(2).join(' ') || 'Unknown error occurred';
        await emailService.sendErrorNotification(errorMessage, toEmail);
        break;

      case undefined:
        console.error('Email subcommand required. Use: test, send, error');
        console.log('\nAvailable email commands:');
        console.log('  email test [twilio|gmail]        Send a test email');
        console.log('  email send [twilio|gmail]        Send current weekend events via email');
        console.log('  email error [twilio|gmail] <msg> Send error notification via email');
        console.log('\nDefault: uses Twilio SendGrid');
        break;

      default:
        console.error(`Unknown email command: ${subCommand}`);
        break;
    }
  } else {
    // Use Gmail service
    const { EmailService } = await import('./services/EmailService');
    const emailService = new EmailService();

    switch (subCommand) {
      case 'test':
        await emailService.sendTestEmail(toEmail);
        break;

      case 'send':
        const { WeekendEventService } = await import('./services/WeekendEventService');
        const weekendService = new WeekendEventService();
        const report = await weekendService.formatWeekendReport(new Date());
        await emailService.sendWeekendEventsEmail(report, toEmail);
        break;

      case 'error':
        const errorMessage = args.slice(2).join(' ') || 'Unknown error occurred';
        await emailService.sendErrorNotification(errorMessage, toEmail);
        break;
    }
  }
}

export async function handleErrorNotification(errorMessage: string) {
  const { WhatsAppService } = await import('./services/WhatsAppService');

  try {
    const whatsappService = new WhatsAppService();
    const success = await whatsappService.sendErrorNotification(errorMessage);

    if (success) {
      console.log('✅ Error notification sent');
    } else {
      console.error('❌ Failed to send error notification');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Error sending notification:', error);
    process.exit(1);
  }
}

export function showHelp() {
  console.log(`
Vipassana Calendar Parser (VIP)

Usage:
  npm run dev [command] [options]

Commands:
  weekend, w [date]     Show weekend events (3-hour sits and 1-day courses)
                        Optional date in format: "2025-07-11" or "July 11, 2025"
                        Default: upcoming weekend

  fetch, f <url>        Fetch calendar data from Google Doc URL
                        Example: npm run dev fetch https://docs.google.com/...

  isha, satsang [year]  Show all Isha Satsang dates (second Saturdays)
                        Optional year (2025-2030), default: 2025

  whatsapp, wa <cmd>    WhatsApp messaging commands
    test                Send a test WhatsApp message
    send                Send current weekend events via WhatsApp
    error <msg>         Send error notification

  schedule <cmd>        Weekly scheduler commands
    start               Start Monday WhatsApp notifications
    stop                Stop scheduled notifications
    status              Show next scheduled run time

  health                Run comprehensive health check

  help, h               Show this help message

Examples:
  npm run dev weekend                    # Upcoming weekend events  
  npm run dev w "2025-07-11"            # Specific weekend events
  npm run dev isha                       # All 2025 Isha Satsang dates
  npm run dev satsang 2026               # All 2026 Isha Satsang dates
  npm run dev fetch <google-doc-url>     # Fetch from Google Doc
  npm run dev whatsapp test              # Send test WhatsApp message
  npm run dev schedule start             # Start weekly notifications
  npm run dev health                     # Check system health

Current date: ${new Date().toLocaleDateString()}
`);
}

if (require.main === module) {
  main().catch(console.error);
}
