import winston from 'winston';
import { join } from 'path';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

export interface LogContext {
  module?: string;
  method?: string;
  eventId?: string;
  duration?: number | string;
  durationMs?: number;
  [key: string]: any;
}

class Logger {
  private winston: winston.Logger;
  private isTestMode: boolean;
  private performanceMarks: Map<string, number> = new Map();

  constructor() {
    this.isTestMode = process.env.NODE_ENV === 'test' || process.env.ENABLE_TEST_LOGS === 'true';

    const transports: winston.transport[] = [];

    // Console transport with colors
    transports.push(
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.timestamp(),
          winston.format.printf(({ timestamp, level, message, ...meta }: any) => {
            const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
            return `${timestamp} [${level}] ${message} ${metaStr}`;
          })
        ),
        silent: !this.isTestMode && process.env.NODE_ENV === 'production',
      })
    );

    // File transport for tests
    if (this.isTestMode) {
      transports.push(
        new winston.transports.File({
          filename: join(process.cwd(), 'logs', 'test-debug.log'),
          format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
          maxsize: 5242880, // 5MB
          maxFiles: 3,
        })
      );
    }

    // Production logging with rotation
    if (process.env.NODE_ENV === 'production' || process.env.ENABLE_FILE_LOGGING === 'true') {
      transports.push(
        new winston.transports.File({
          filename: join(process.cwd(), 'logs', 'error.log'),
          level: 'error',
          format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
          maxsize: 10485760, // 10MB
          maxFiles: 5,
        })
      );

      transports.push(
        new winston.transports.File({
          filename: join(process.cwd(), 'logs', 'combined.log'),
          format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
          maxsize: 10485760, // 10MB
          maxFiles: 7,
        })
      );
    }

    this.winston = winston.createLogger({
      level: this.isTestMode ? 'debug' : 'info',
      transports,
    });
  }

  // Performance timing helpers
  startTimer(label: string): void {
    if (this.isTestMode) {
      this.performanceMarks.set(label, performance.now());
      this.debug(`Timer started: ${label}`, { module: 'performance', label });
    }
  }

  endTimer(label: string): number {
    if (!this.performanceMarks.has(label)) {
      return 0;
    }

    const start = this.performanceMarks.get(label)!;
    const duration = performance.now() - start;
    this.performanceMarks.delete(label);

    if (this.isTestMode) {
      this.debug(`Timer ended: ${label}`, {
        module: 'performance',
        label,
        durationMs: duration,
        duration: `${duration.toFixed(2)}ms`,
      });
    }

    return duration;
  }

  // Core logging methods
  error(message: string, context?: LogContext | Error): void {
    if (context instanceof Error) {
      this.winston.error(message, {
        error: {
          message: context.message,
          stack: context.stack,
          name: context.name,
        },
      });
    } else {
      this.winston.error(message, context);
    }
  }

  warn(message: string, context?: LogContext): void {
    this.winston.warn(message, context);
  }

  info(message: string, context?: LogContext): void {
    this.winston.info(message, context);
  }

  debug(message: string, context?: LogContext): void {
    if (this.isTestMode) {
      this.winston.debug(message, context);
    }
  }

  verbose(message: string, context?: LogContext): void {
    if (this.isTestMode) {
      this.winston.verbose(message, context);
    }
  }

  // Specialized logging methods for different modules
  logCalendarParse(stage: string, data: any): void {
    if (this.isTestMode) {
      this.debug(`Calendar Parse: ${stage}`, {
        module: 'CalendarParser',
        stage,
        data,
      });
    }
  }

  logEventCreation(event: any): void {
    if (this.isTestMode) {
      this.debug('Event created', {
        module: 'EventCreation',
        event: {
          type: event.type,
          date: event.date,
          location: event.location,
          startTime: event.startTime,
        },
      });
    }
  }

  logAPICall(api: string, endpoint: string, status: number, duration: number): void {
    const context: LogContext = {
      module: 'API',
      api,
      endpoint,
      status,
      duration: `${duration}ms`,
    };

    if (status >= 400) {
      this.error(`API call failed: ${api}`, context);
    } else if (this.isTestMode) {
      this.debug(`API call: ${api}`, context);
    }
  }

  logFileOperation(operation: string, path: string, success: boolean, error?: Error): void {
    const context: LogContext = {
      module: 'FileSystem',
      operation,
      path,
      success,
    };

    if (!success && error) {
      this.error(`File operation failed: ${operation}`, { ...context, error: error.message });
    } else if (this.isTestMode) {
      this.debug(`File operation: ${operation}`, context);
    }
  }

  logPluginActivity(pluginId: string, action: string, details?: any): void {
    if (this.isTestMode) {
      this.debug(`Plugin activity: ${pluginId}`, {
        module: 'Plugin',
        pluginId,
        action,
        details,
      });
    }
  }

  logDateCalculation(operation: string, input: Date | string, result: any): void {
    if (this.isTestMode) {
      this.verbose(`Date calculation: ${operation}`, {
        module: 'DateUtils',
        operation,
        input: input instanceof Date ? input.toISOString() : input,
        result,
      });
    }
  }

  logCacheActivity(operation: 'hit' | 'miss' | 'set' | 'clear', key: string, details?: any): void {
    if (this.isTestMode) {
      this.verbose(`Cache ${operation}: ${key}`, {
        module: 'Cache',
        operation,
        key,
        details,
      });
    }
  }

  // Test-specific logging
  logTestScenario(scenario: string, input: any, expected: any, actual: any): void {
    if (this.isTestMode) {
      this.info(`Test scenario: ${scenario}`, {
        module: 'Test',
        scenario,
        input,
        expected,
        actual,
        passed: JSON.stringify(expected) === JSON.stringify(actual),
      });
    }
  }

  // Create a child logger with persistent context
  child(context: LogContext): Logger {
    const child = Object.create(this);
    child.winston = this.winston.child(context);
    return child;
  }

  // Check if test mode is enabled
  isInTestMode(): boolean {
    return this.isTestMode;
  }

  // Enable/disable test mode dynamically
  setTestMode(enabled: boolean): void {
    this.isTestMode = enabled;
    this.winston.level = enabled ? 'debug' : 'info';
  }
}

// Export singleton instance
export const logger = new Logger();

// Export convenience functions
export function startTimer(label: string): void {
  logger.startTimer(label);
}

export function endTimer(label: string): number {
  return logger.endTimer(label);
}

// Decorator for method logging
export function LogMethod(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
): PropertyDescriptor {
  const originalMethod = descriptor.value;

  descriptor.value = function (...args: any[]) {
    const className = target.constructor.name;
    const methodName = propertyKey;

    logger.debug(`Entering ${className}.${methodName}`, {
      module: className,
      method: methodName,
      args: logger.isInTestMode() ? args : undefined,
    });

    const timer = `${className}.${methodName}`;
    logger.startTimer(timer);

    try {
      const result = originalMethod.apply(this, args);

      // Handle both sync and async results
      if (result instanceof Promise) {
        return result
          .then(value => {
            const duration = logger.endTimer(timer);
            logger.debug(`Exiting ${className}.${methodName}`, {
              module: className,
              method: methodName,
              durationMs: duration,
              duration: `${duration.toFixed(2)}ms`,
              hasResult: value !== undefined,
            });
            return value;
          })
          .catch(error => {
            const duration = logger.endTimer(timer);
            logger.error(`Error in ${className}.${methodName}`, {
              module: className,
              method: methodName,
              durationMs: duration,
              duration: `${duration.toFixed(2)}ms`,
              error: error instanceof Error ? error.message : String(error),
            });
            throw error;
          });
      } else {
        const duration = logger.endTimer(timer);
        logger.debug(`Exiting ${className}.${methodName}`, {
          module: className,
          method: methodName,
          durationMs: duration,
          duration: `${duration.toFixed(2)}ms`,
          hasResult: result !== undefined,
        });
        return result;
      }
    } catch (error) {
      const duration = logger.endTimer(timer);
      logger.error(`Error in ${className}.${methodName}`, {
        module: className,
        method: methodName,
        durationMs: duration,
        duration: `${duration.toFixed(2)}ms`,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  };

  return descriptor;
}
