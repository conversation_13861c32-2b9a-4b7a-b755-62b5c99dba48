import { Coordinates } from '../models/Business';

/**
 * Calculates the distance between two coordinates using the Haversine formula
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in miles
 */
export function calculateDistance(coord1: Coordinates, coord2: Coordinates): number {
  const R = 3959; // Earth's radius in miles
  const dLat = toRadians(coord2.latitude - coord1.latitude);
  const dLon = toRadians(coord2.longitude - coord1.longitude);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(coord1.latitude)) * Math.cos(toRadians(coord2.latitude)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 100) / 100; // Round to 2 decimal places
}

/**
 * Converts degrees to radians
 * @param degrees - Angle in degrees
 * @returns Angle in radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculates the distance between two coordinates in kilometers
 * @param coord1 - First coordinate
 * @param coord2 - Second coordinate
 * @returns Distance in kilometers
 */
export function calculateDistanceKm(coord1: Coordinates, coord2: Coordinates): number {
  const distanceInMiles = calculateDistance(coord1, coord2);
  return Math.round(distanceInMiles * 1.60934 * 100) / 100; // Convert to km and round
}

/**
 * Finds the closest coordinate from an array of coordinates
 * @param target - Target coordinate
 * @param coordinates - Array of coordinates to search
 * @returns Closest coordinate and its distance
 */
export function findClosest(
  target: Coordinates,
  coordinates: Coordinates[]
): { coordinate: Coordinates; distance: number } | null {
  if (coordinates.length === 0) {
    return null;
  }

  let closest = coordinates[0];
  let minDistance = calculateDistance(target, closest);

  for (let i = 1; i < coordinates.length; i++) {
    const distance = calculateDistance(target, coordinates[i]);
    if (distance < minDistance) {
      minDistance = distance;
      closest = coordinates[i];
    }
  }

  return { coordinate: closest, distance: minDistance };
}

/**
 * Checks if a coordinate is within a certain radius of a center point
 * @param center - Center coordinate
 * @param point - Point to check
 * @param radiusMiles - Radius in miles
 * @returns True if point is within radius
 */
export function isWithinRadius(
  center: Coordinates,
  point: Coordinates,
  radiusMiles: number
): boolean {
  const distance = calculateDistance(center, point);
  return distance <= radiusMiles;
}

/**
 * Sorts an array of coordinates by distance from a target point
 * @param target - Target coordinate
 * @param coordinates - Array of coordinates to sort
 * @returns Sorted array with distances
 */
export function sortByDistance(
  target: Coordinates,
  coordinates: Coordinates[]
): Array<{ coordinate: Coordinates; distance: number }> {
  return coordinates
    .map(coord => ({
      coordinate: coord,
      distance: calculateDistance(target, coord)
    }))
    .sort((a, b) => a.distance - b.distance);
}
