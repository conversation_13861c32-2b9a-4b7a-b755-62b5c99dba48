import { ValidationError } from '../models/Errors';
import { VALIDATION_PATTERNS, SEARCH_CONFIG, BUSINESS_TYPES, ERROR_MESSAGES } from '../constants';
import { parsePhoneNumber, isValidPhoneNumber } from 'libphonenumber-js';
import validator from 'validator';
import zipcodes from 'zipcodes';

/**
 * Validates a zip code format using zipcodes package for US zip codes
 * @param zipCode - The zip code to validate
 * @throws ValidationError if zip code is invalid
 */
export function validateZipCode(zipCode: string): void {
  if (!zipCode) {
    throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
  }

  // First check basic format with regex
  if (!VALIDATION_PATTERNS.ZIP_CODE.test(zipCode)) {
    throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
  }

  // For 5-digit codes, also validate with zipcodes package for real zip codes
  // For ZIP+4, just use regex validation since zipcodes package may not have all combinations
  if (zipCode.length === 5) {
    const zipInfo = zipcodes.lookup(zipCode);
    if (!zipInfo) {
      throw new ValidationError(ERROR_MESSAGES.INVALID_ZIP_CODE, 'zipCode');
    }
  }
}

/**
 * Validates a search radius
 * @param radius - The radius to validate (in miles)
 * @throws ValidationError if radius is invalid
 */
export function validateRadius(radius: number): void {
  if (!Number.isInteger(radius) || radius < SEARCH_CONFIG.MIN_RADIUS || radius > SEARCH_CONFIG.MAX_RADIUS) {
    throw new ValidationError(ERROR_MESSAGES.INVALID_RADIUS, 'radius');
  }
}

/**
 * Validates a business type
 * @param businessType - The business type to validate
 * @throws ValidationError if business type is invalid
 */
export function validateBusinessType(businessType: string): void {
  if (!businessType || !BUSINESS_TYPES.includes(businessType as any)) {
    throw new ValidationError(ERROR_MESSAGES.INVALID_BUSINESS_TYPE, 'businessType');
  }
}

/**
 * Validates a URL format using validator.js for comprehensive validation
 * @param url - The URL to validate
 * @throws ValidationError if URL is invalid
 */
export function validateUrl(url: string): void {
  if (!url) {
    throw new ValidationError('Invalid URL format. Must start with http:// or https://', 'url');
  }

  // Use validator.js for robust URL validation
  if (!validator.isURL(url, {
    protocols: ['http', 'https'],
    require_protocol: true,
    require_valid_protocol: true,
    allow_underscores: true,
    allow_trailing_dot: false,
    allow_protocol_relative_urls: false
  })) {
    throw new ValidationError('Invalid URL format. Must start with http:// or https://', 'url');
  }
}

/**
 * Validates phone number format using libphonenumber-js for international support
 * @param phone - The phone number to validate
 * @throws ValidationError if phone number is invalid
 */
export function validatePhone(phone: string): void {
  if (!phone) {
    return; // Empty phone is allowed
  }

  // First try basic format validation - if it doesn't look like a phone number, reject it
  const basicPhonePattern = /^[\+]?[\d\s\-\(\)\.]{7,}$/;
  if (!basicPhonePattern.test(phone)) {
    throw new ValidationError('Invalid phone number format', 'phone');
  }

  // For US phone numbers, be more permissive
  const usPhonePattern = /^[\+]?1?[\s\-\(\)]?[\d]{3}[\s\-\(\)]?[\d]{3}[\s\-\(\)]?[\d]{4}$/;
  if (usPhonePattern.test(phone)) {
    return; // Valid US phone number
  }

  // Also check for common US formats with parentheses
  const usPhoneWithParens = /^[\+]?1?[\s\-]?\([\d]{3}\)[\s\-]?[\d]{3}[\s\-]?[\d]{4}$/;
  if (usPhoneWithParens.test(phone)) {
    return; // Valid US phone number with parentheses
  }

  // For international numbers, try more strict validation
  let isValid = false;

  try {
    // Try parsing with US country code first for US numbers
    const phoneNumber = parsePhoneNumber(phone, 'US');
    if (phoneNumber && phoneNumber.isValid()) {
      isValid = true;
    }
  } catch (error) {
    // Continue to next validation method
  }

  if (!isValid) {
    try {
      // Try parsing without country code (for international numbers with country code)
      const phoneNumber = parsePhoneNumber(phone);
      if (phoneNumber && phoneNumber.isValid()) {
        isValid = true;
      }
    } catch (error) {
      // Continue to next validation method
    }
  }

  if (!isValid) {
    try {
      // Try global validation for any format
      if (isValidPhoneNumber(phone, 'US')) {
        isValid = true;
      }
    } catch (error) {
      // Continue to next validation method
    }
  }

  // If none of the validation methods succeeded, throw error
  if (!isValid) {
    throw new ValidationError('Invalid phone number format', 'phone');
  }
}

/**
 * Validates email format using validator.js for RFC-compliant validation
 * @param email - The email to validate
 * @throws ValidationError if email is invalid
 */
export function validateEmail(email: string): void {
  if (!email) {
    return; // Empty email is allowed
  }

  // Use validator.js for RFC-compliant email validation
  if (!validator.isEmail(email, {
    allow_utf8_local_part: false,
    require_tld: true,
    allow_ip_domain: false,
    domain_specific_validation: true
  })) {
    throw new ValidationError('Invalid email format', 'email');
  }
}

/**
 * Validates search parameters
 * @param params - The search parameters to validate
 * @throws ValidationError if any parameter is invalid
 */
export function validateSearchParams(params: {
  zipCode: string;
  radius: number;
  businessType: string;
}): void {
  validateZipCode(params.zipCode);
  validateRadius(params.radius);
  validateBusinessType(params.businessType);
}

/**
 * Validates that a required configuration value exists
 * @param value - The configuration value
 * @param name - The name of the configuration
 * @throws ValidationError if value is missing
 */
export function validateRequired(value: any, name: string): void {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${name} is required`, name);
  }
}

/**
 * Validates that a number is within a specified range
 * @param value - The number to validate
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @param fieldName - Name of the field being validated
 * @throws ValidationError if value is out of range
 */
export function validateRange(value: number, min: number, max: number, fieldName: string): void {
  if (value < min || value > max) {
    throw new ValidationError(`${fieldName} must be between ${min} and ${max}`, fieldName);
  }
}
