import axios from 'axios';

export class WebFetcher {
  static async fetchGoogleDoc(documentId: string): Promise<string> {
    const urls = [
      `https://docs.google.com/document/d/${documentId}/export?format=txt`,
      `https://docs.google.com/document/d/${documentId}/pub`,
      `https://docs.google.com/document/u/0/d/${documentId}/export?format=txt`,
      `https://docs.google.com/document/d/${documentId}/edit?usp=sharing`,
    ];

    for (const url of urls) {
      try {
        console.log(`Trying to fetch from: ${url}`);
        const response = await axios.get(url, {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
          maxRedirects: 5,
          timeout: 10000,
        });

        console.log(`Response status: ${response.status}`);

        if (response.status === 200 && response.data) {
          const content = response.data;
          if (typeof content === 'string' && content.length > 100) {
            // Basic check for substantial content
            return content;
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch from ${url}:`, error);
      }
    }

    throw new Error(
      'Unable to fetch Google Doc content. Document may be private or require authentication.'
    );
  }

  static async fetchUrl(url: string): Promise<string> {
    try {
      const response = await axios.get(url, {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        maxRedirects: 5,
        timeout: 10000,
      });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `Failed to fetch from ${url}: ${error.response?.status} ${error.response?.statusText || error.message}`
        );
      }
      throw new Error(`Failed to fetch from ${url}: ${error}`);
    }
  }

  static extractDocumentId(url: string): string | null {
    const match = url.match(/\/document\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
  }

  static isGoogleDocUrl(url: string): boolean {
    return url.includes('docs.google.com') && url.includes('/document/');
  }
}
