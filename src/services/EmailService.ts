import nodemailer from 'nodemailer';
import { logger } from '../utils/logger';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD, // Use App Password for Gmail
      },
    });

    logger.info('Email service initialized', {
      module: 'EmailService',
      service: 'gmail',
    });
  }

  async sendWeekendEventsEmail(weekendReport: string, toEmail: string): Promise<boolean> {
    try {
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: toEmail,
        subject: '🧘 Vipassana Weekend Events',
        html: `
          <h2>🧘 Vipassana Weekend Events</h2>
          <pre style="font-family: Arial; white-space: pre-wrap;">${weekendReport}</pre>
          <br>
          <p style="color: #888;">📱 Generated by VIP Calendar Bot</p>
        `,
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info('Weekend events email sent successfully', {
        module: 'EmailService',
        messageId: info.messageId,
        to: toEmail,
      });

      console.log(`✅ Email sent successfully! Message ID: ${info.messageId}`);
      return true;
    } catch (error) {
      logger.error('Failed to send weekend events email', error instanceof Error ? error : new Error(String(error)));
      console.error(`❌ Failed to send email: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  async sendTestEmail(toEmail: string): Promise<boolean> {
    try {
      const testMessage = `
🧘 VIP Calendar Bot Test Email

This is a test to verify email integration is working correctly.

Sent at: ${new Date().toLocaleString()}
      `;

      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: toEmail,
        subject: '🧘 VIP Calendar Bot Test',
        text: testMessage,
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info('Test email sent successfully', {
        module: 'EmailService',
        messageId: info.messageId,
        to: toEmail,
      });

      console.log(`✅ Test email sent successfully! Message ID: ${info.messageId}`);
      return true;
    } catch (error) {
      logger.error('Failed to send test email', error instanceof Error ? error : new Error(String(error)));
      console.error(`❌ Failed to send test email: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }

  async sendErrorNotification(error: string, toEmail: string): Promise<boolean> {
    try {
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: toEmail,
        subject: '🚨 VIP Calendar Bot Error',
        html: `
          <h2>🚨 VIP Calendar Bot Error</h2>
          <pre style="font-family: monospace; background: #f5f5f5; padding: 10px;">${error}</pre>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        `,
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      logger.info('Error notification email sent', {
        module: 'EmailService',
        messageId: info.messageId,
        to: toEmail,
      });

      return true;
    } catch (e) {
      logger.error('Failed to send error notification email', e instanceof Error ? e : new Error(String(e)));
      return false;
    }
  }
}