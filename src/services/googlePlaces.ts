import { API_CONFIG } from '../constants';
import { type Coordinates, type PlaceResult } from '../models/Business';
import { ApiError, NetworkError, ConfigurationError } from '../models/Errors';
import { validateRequired, validateRange } from '../utils/validation';

/**
 * Google Places API search parameters
 */
export interface SearchParams {
  minprice?: number; // 0-4
  maxprice?: number; // 0-4
  opennow?: boolean;
  type?: string;
  keyword?: string;
}

/**
 * Search result from Google Places API
 */
export interface PlacesSearchResult {
  places: PlaceResult[];
  nextPageToken?: string;
  totalResults: number;
  searchParams?: Record<string, unknown>;
}

/**
 * Raw Google Places API result structure
 */
interface RawPlaceResult {
  place_id: string;
  name: string;
  formatted_address: string;
  formatted_phone_number?: string;
  website?: string;
  types: string[];
  rating?: number;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  opening_hours?: {
    open_now: boolean;
  };
  price_level?: number;
}

/**
 * Google Places API response interfaces
 */
interface PlacesApiResponse {
  results: RawPlaceResult[];
  status: string;
  error_message?: string;
  next_page_token?: string;
}

interface PlaceDetailsResponse {
  result: RawPlaceResult;
  status: string;
  error_message?: string;
}

/**
 * Service for interacting with Google Places API
 */
export class GooglePlacesService {
  private cache = new Map<string, PlaceResult>();
  private readonly apiKey: string;
  private readonly baseUrl: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.GOOGLE_PLACES_API_KEY || '';
    this.baseUrl = API_CONFIG.GOOGLE_PLACES_BASE_URL;

    if (!this.apiKey) {
      throw new ConfigurationError('Google Places API key is required', 'GOOGLE_PLACES_API_KEY');
    }
  }

  /**
   * Searches for places near a location
   * @param coordinates - Center point for search
   * @param radius - Search radius in meters
   * @param type - Type of place to search for
   * @param params - Additional search parameters
   * @returns Promise resolving to search results
   */
  async searchNearby(
    coordinates: Coordinates,
    radius: number,
    type: string,
    params: SearchParams = {}
  ): Promise<PlacesSearchResult> {
    // Validate parameters
    validateRequired(type, 'type');
    validateRange(radius, 1, 50000, 'radius');

    if (params.minprice !== undefined) {
      validateRange(params.minprice, 0, 4, 'minprice');
    }
    if (params.maxprice !== undefined) {
      validateRange(params.maxprice, 0, 4, 'maxprice');
    }

    const searchParams = new URLSearchParams({
      location: `${coordinates.latitude},${coordinates.longitude}`,
      radius: radius.toString(),
      type,
      key: this.apiKey,
    });

    // Add optional parameters
    if (params.minprice !== undefined) {
      searchParams.append('minprice', params.minprice.toString());
    }
    if (params.maxprice !== undefined) {
      searchParams.append('maxprice', params.maxprice.toString());
    }
    if (params.opennow !== undefined) {
      searchParams.append('opennow', params.opennow.toString());
    }
    if (params.keyword) {
      searchParams.append('keyword', params.keyword);
    }

    const url = `${this.baseUrl}/nearbysearch/json?${searchParams.toString()}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `Places API request failed: ${response.statusText}`,
          response.status,
          'Google Places API'
        );
      }

      const data: PlacesApiResponse = await response.json();

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        throw new ApiError(
          data.error_message || `Places API error: ${data.status}`,
          response.status,
          'Google Places API'
        );
      }

      const places = data.results.map(this.transformPlaceResult);

      return {
        places,
        nextPageToken: data.next_page_token,
        totalResults: places.length,
        searchParams: {
          coordinates,
          radius,
          type,
          ...params,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error) {
        throw new NetworkError(`Network error during places search: ${error.message}`, error);
      }

      throw new NetworkError(`Unknown error during places search: ${String(error)}`);
    }
  }

  /**
   * Searches for places using text query
   * @param query - Text search query
   * @param location - Optional location bias
   * @param radius - Optional search radius in meters
   * @returns Promise resolving to search results
   */
  async searchByText(
    query: string,
    location?: Coordinates,
    radius?: number
  ): Promise<PlacesSearchResult> {
    validateRequired(query, 'query');

    const searchParams = new URLSearchParams({
      query,
      key: this.apiKey,
    });

    if (location) {
      searchParams.append('location', `${location.latitude},${location.longitude}`);
    }
    if (radius) {
      searchParams.append('radius', radius.toString());
    }

    const url = `${this.baseUrl}/textsearch/json?${searchParams.toString()}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `Places text search failed: ${response.statusText}`,
          response.status,
          'Google Places API'
        );
      }

      const data: PlacesApiResponse = await response.json();

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        throw new ApiError(
          data.error_message || `Places text search error: ${data.status}`,
          response.status,
          'Google Places API'
        );
      }

      const places = data.results.map(this.transformPlaceResult);

      return {
        places,
        nextPageToken: data.next_page_token,
        totalResults: places.length,
        searchParams: {
          query,
          location,
          radius,
        },
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error) {
        throw new NetworkError(`Network error during text search: ${error.message}`, error);
      }

      throw new NetworkError(`Unknown error during text search: ${String(error)}`);
    }
  }

  /**
   * Gets detailed information about a place
   * @param placeId - The place ID
   * @param fields - Optional fields to retrieve
   * @returns Promise resolving to place details
   */
  async getPlaceDetails(placeId: string, fields?: string[]): Promise<PlaceResult> {
    validateRequired(placeId, 'placeId');

    // Check cache first
    const cacheKey = `details_${placeId}`;
    const cached = this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    const searchParams = new URLSearchParams({
      place_id: placeId,
      key: this.apiKey,
    });

    if (fields && fields.length > 0) {
      searchParams.append('fields', fields.join(','));
    }

    const url = `${this.baseUrl}/details/json?${searchParams.toString()}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `Place details request failed: ${response.statusText}`,
          response.status,
          'Google Places API'
        );
      }

      const data: PlaceDetailsResponse = await response.json();

      if (data.status !== 'OK') {
        throw new ApiError(
          data.error_message || `Place details error: ${data.status}`,
          response.status,
          'Google Places API'
        );
      }

      const place = this.transformPlaceResult(data.result);

      // Cache the result
      this.cache.set(cacheKey, place);

      return place;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error) {
        throw new NetworkError(`Network error during place details: ${error.message}`, error);
      }

      throw new NetworkError(`Unknown error during place details: ${String(error)}`);
    }
  }

  /**
   * Gets the next page of search results
   * @param pageToken - The next page token from previous search
   * @returns Promise resolving to next page of results
   */
  async getNextPage(pageToken: string): Promise<PlacesSearchResult> {
    validateRequired(pageToken, 'pageToken');

    const searchParams = new URLSearchParams({
      pagetoken: pageToken,
      key: this.apiKey,
    });

    const url = `${this.baseUrl}/nearbysearch/json?${searchParams.toString()}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiError(
          `Next page request failed: ${response.statusText}`,
          response.status,
          'Google Places API'
        );
      }

      const data: PlacesApiResponse = await response.json();

      if (data.status !== 'OK' && data.status !== 'ZERO_RESULTS') {
        throw new ApiError(
          data.error_message || `Next page error: ${data.status}`,
          response.status,
          'Google Places API'
        );
      }

      const places = data.results.map(this.transformPlaceResult);

      return {
        places,
        nextPageToken: data.next_page_token,
        totalResults: places.length,
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error) {
        throw new NetworkError(`Network error during next page: ${error.message}`, error);
      }

      throw new NetworkError(`Unknown error during next page: ${String(error)}`);
    }
  }

  /**
   * Transforms Google Places API result to our PlaceResult format
   * @param apiResult - Raw result from Google Places API
   * @returns Transformed PlaceResult
   */
  private transformPlaceResult(apiResult: any): PlaceResult {
    return {
      place_id: apiResult.place_id,
      name: apiResult.name,
      formatted_address: apiResult.formatted_address || '',
      formatted_phone_number: apiResult.formatted_phone_number,
      website: apiResult.website,
      types: apiResult.types || [],
      rating: apiResult.rating,
      geometry: {
        location: {
          lat: apiResult.geometry?.location?.lat || 0,
          lng: apiResult.geometry?.location?.lng || 0,
        },
      },
    };
  }

  /**
   * Clears the places cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Gets the current cache size
   * @returns Number of cached entries
   */
  getCacheSize(): number {
    return this.cache.size;
  }
}
