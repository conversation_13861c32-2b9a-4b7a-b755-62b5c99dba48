export const mockBrandSites = {
  '1': {
    link: 'https://www.wip.prod.gaptecholapps.com?ssiteID=GAP',
    unsecureUrl: 'https://www.wip.prod.gaptecholapps.com',
    secureUrl: 'https://secure.www.wip.prod.gaptecholapps.com',
    displayName: 'gap.com',
    brandDisplayName: 'gap.com',
    brandCode: '1',
    brandAbbr: 'GAP',
  },
  '2': {
    link: 'https://brol.wip.prod.gaptecholapps.com?ssiteID=GAP',
    unsecureUrl: 'https://brol.wip.prod.gaptecholapps.com',
    secureUrl: 'https://secure.brol.wip.prod.gaptecholapps.com',
    displayName: 'bananarepublic.com',
    brandDisplayName: 'bananarepublic.com',
    brandCode: '2',
    brandAbbr: 'BR',
  },
  '3': {
    link: 'https://onol.wip.prod.gaptecholapps.com?ssiteID=GAP',
    unsecureUrl: 'https://onol.wip.prod.gaptecholapps.com',
    secureUrl: 'https://secure.onol.wip.prod.gaptecholapps.com',
    displayName: 'oldnavy.com',
    brandDisplayName: 'oldnavy.com',
    brandCode: '3',
    brandAbbr: 'ON',
  },
  '10': {
    link: 'https://atol.wip.prod.gaptecholapps.com?ssiteID=GAP',
    unsecureUrl: 'https://atol.wip.prod.gaptecholapps.com',
    secureUrl: 'https://secure.atol.wip.prod.gaptecholapps.com',
    displayName: 'Athleta.com',
    brandDisplayName: 'Athleta.com',
    brandCode: '10',
    brandAbbr: 'AT',
  },
  '34': {
    link: 'https://www.wip.prod.factory-gaptecholapps.com?ssiteID=BRFS',
    unsecureUrl: 'https://www.wip.prod.factory-gaptecholapps.com',
    secureUrl: 'https://secure.www.wip.prod.factory-gaptecholapps.com',
    displayName: 'gapfactory.com',
    brandDisplayName: 'gapfactory.com',
    brandCode: '34',
    brandAbbr: 'GAPFS',
  },
  '35': {
    link: 'https://brfol.wip.prod.factory-gaptecholapps.com?ssiteID=BRFS',
    unsecureUrl: 'https://brfol.wip.prod.factory-gaptecholapps.com',
    secureUrl: 'https://secure.brfol.wip.prod.factory-gaptecholapps.com',
    displayName: 'bananarepublicfactory.com',
    brandDisplayName: 'bananarepublicfactory.com',
    brandCode: '35',
    brandAbbr: 'BRFS',
  },
  '7': {
    link: 'https://www.wip.prod.gaptecholapps.ca?ssiteID=GAP',
    unsecureUrl: 'https://www.wip.prod.gaptecholapps.ca',
    secureUrl: 'https://secure.www.wip.prod.gaptecholapps.ca',
    displayName: 'gap.com',
    brandDisplayName: 'gap.com',
    brandCode: '7',
    brandAbbr: 'GAP',
  },
  '8': {
    link: 'https://brol.wip.prod.gaptecholapps.ca?ssiteID=GAP',
    unsecureUrl: 'https://brol.wip.prod.gaptecholapps.ca',
    secureUrl: 'https://secure.brol.wip.prod.gaptecholapps.ca',
    displayName: 'bananarepublic.com',
    brandDisplayName: 'bananarepublic.com',
    brandCode: '8',
    brandAbbr: 'BR',
  },
  '9': {
    link: 'https://onol.wip.prod.gaptecholapps.ca?ssiteID=GAP',
    unsecureUrl: 'https://onol.wip.prod.gaptecholapps.ca',
    secureUrl: 'https://secure.onol.wip.prod.gaptecholapps.ca',
    displayName: 'oldnavy.com',
    brandDisplayName: 'oldnavy.com',
    brandCode: '9',
    brandAbbr: 'ON',
  },
  '39': {
    link: 'https://atol.wip.prod.gaptecholapps.ca?ssiteID=GAP',
    unsecureUrl: 'https://atol.wip.prod.gaptecholapps.ca',
    secureUrl: 'https://secure.atol.wip.prod.gaptecholapps.ca',
    displayName: 'Athleta.com',
    brandDisplayName: 'Athleta.com',
    brandCode: '39',
    brandAbbr: 'AT',
  },
  '50': {
    displayName: 'bananarepublicfactory.gapfactory.ca',
    brandDisplayName: 'bananarepublicfactory.gapfactory.ca',
    brandCode: '50',
    brandAbbr: 'BRFS',
    link: 'https://brfol.wip.prod.factory-gaptecholapps.ca?ssiteID=BRFS',
    unsecureUrl: 'https://brfol.wip.prod.factory-gaptecholapps.ca',
    secureUrl: 'https://secure.brfol.wip.prod.factory-gaptecholapps.ca',
  },
};

export const marketBrandCodeMap = {
  us: {
    br: '2',
    on: '3',
    gap: '1',
    at: '10',
    brfs: '34',
    gapfs: '35',
  },
  ca: {
    br: '8',
    on: '9',
    gap: '7',
    at: '39',
    brfs: '50',
    gapfs: '35',
  },
};
