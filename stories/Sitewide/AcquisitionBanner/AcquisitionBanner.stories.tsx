import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { PersonalizationContextData } from '@ecom-next/core/legacy/personalization-provider/types';
import AcquisitionBanner from '@sitewide/components/acquisition/acquisition-banner/AcquisitionBanner';
import { BreakpointProvider } from '@ecom-next/core/breakpoint-provider';
import { MarketingProvider } from '@ecom-next/marketing-ui/marketing-provider';
import { Meta } from '@storybook/react';
import { AcquisitionBannerProps } from '@sitewide/components/acquisition/acquisition-banner/types';
import CmsMarketing from '../../../packages/marketing-ui/src/components/legacy-mui-entry';
import JsonMarketing from '../../../packages/marketing-ui/src/components/json-marketing';
import StoryWrapper, { Props } from '../../StoryWrapper';
import { acquisitionBannerRealWorldData, personalizationData } from './data/storyData';

const ComponentAcquisitionBannerWrapper = (props: Props & AcquisitionBannerProps): JSX.Element => {
  const { acquisitionBannerData, personalizationContextData } = props;
  return (
    <StoryWrapper
      {...props}
      StoryComponent={() => (
        <BreakpointProvider>
          <MarketingProvider value={{}} jsonMarketingComponent={JsonMarketing} cmsMarketingComponent={CmsMarketing}>
            <PersonalizationContext.Provider value={personalizationContextData as PersonalizationContextData}>
              <AcquisitionBanner data={acquisitionBannerData} />
            </PersonalizationContext.Provider>
          </MarketingProvider>
        </BreakpointProvider>
      )}
    />
  );
};

const meta = {
  title: 'Sitewide/AcquisitionBanner',
  component: ComponentAcquisitionBannerWrapper,
  parameters: {
    props: {
      propTables: [AcquisitionBanner],
    },
  },
} satisfies Meta<typeof ComponentAcquisitionBanner>;

export default meta;

export const ComponentAcquisitionBanner = {
  args: {
    acquisitionBannerData: acquisitionBannerRealWorldData,
    personalizationContextData: personalizationData,
    brand: 'on',
  },
};
