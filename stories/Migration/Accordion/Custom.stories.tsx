import type { Meta } from '@storybook/react';
import { Accordion, Panel, PanelDetails, PanelToggle } from '@ecom-next/core/migration/accordion';
import StoryWrapper, { Props } from '../../StoryWrapper';

const CustomAccordionWrapper = (props: Props) => {
  return (
    <StoryWrapper
      {...props}
      innerCSS='body { background: #f1f1f2; }'
      StoryComponent={() => (
        <div className='wrapper' style={{ width: '400px' }}>
          <Accordion {...props}>
            <Panel id='1'>
              <PanelToggle title='Panel 1' className='[&>*:first-child]:flex-1' />
              <PanelDetails>Hello from details</PanelDetails>
            </Panel>
            <Panel id='2'>
              <PanelToggle title='Panel 2' className='[&>*:first-child]:flex-1' />
              <PanelDetails>Hello from details</PanelDetails>
            </Panel>
          </Accordion>
        </div>
      )}
    />
  );
};

const meta: Meta = {
  title: 'Migration/Accordion',
  component: CustomAccordionWrapper,
};

export default meta;

export const CustomStyles = {
  args: {
    data: {},
    brand: 'at',
    isDesktop: true,
    withChevron: true,
    withPlusMinus: false,
  },
};
