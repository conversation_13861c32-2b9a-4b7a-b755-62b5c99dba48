import { Meta } from '@storybook/react';
import { Slider, SliderProps } from '@ecom-next/core/fabric/slider';
import React, { useState } from 'react';
import StoryWrapper, { Props } from '../../StoryWrapper';

const SliderWrapper = (props: Props & SliderProps) => <StoryWrapper {...props} StoryComponent={() => <Slider {...props} />} />;

const meta: Meta = {
  title: 'Fabric DS/Slider',
  component: SliderWrapper,
  parameters: {
    controls: { expanded: true },
  },
  argTypes: {
    name: {
      control: 'text',
      description: 'Name used to identify and apply styling to the slider',
      table: { defaultValue: { summary: 'slider' } },
    },
    min: {
      control: 'number',
      description: 'Minimum value for the slider',
      table: { defaultValue: { summary: 0 } },
    },
    max: {
      control: 'number',
      description: 'Maximum value for the slider',
      table: { defaultValue: { summary: 100 } },
    },
    step: {
      control: 'number',
      description: 'Change interval used within the slider',
      table: { defaultValue: { summary: 1 } },
    },
    debounceTime: {
      control: 'number',
      description: 'Debounce delay in milliseconds for onChange',
      table: { defaultValue: { summary: 0 } },
    },
    minVal: {
      control: 'number',
      description: 'Minimum selected value',
    },
    maxVal: {
      control: 'number',
      description: 'Maximum selected value',
    },
    onChange: { action: 'change called' },
    onAfterChange: { action: 'afterChange called' },
  },
};

export default meta;

export const UncontrolledSlider = {
  args: {
    brand: 'at',
    name: 'uncontrolled-slider',
    min: 0,
    max: 100,
    step: 1,
    debounceTime: 0,
  },
  render: (props: Props & SliderProps) => {
    const UncontrolledComponent = () => {
      const handleAfterChange = ({ min, max }: { max: number; min: number }) => {
        console.log('Uncontrolled slider final value:', min, max); // eslint-disable-line no-console
      };

      return (
        <div className='flex flex-col gap-4'>
          <Slider
            {...props}
            onAfterChange={handleAfterChange}
            onChange={({ min, max }) => {
              console.log('Uncontrolled onChange (dragging):', min, max); // eslint-disable-line no-console
            }}
          />
        </div>
      );
    };

    return <StoryWrapper {...props} StoryComponent={UncontrolledComponent} />;
  },
};

export const ControlledSlider = {
  args: {
    brand: 'at',
    name: 'controlled-slider',
    min: 0,
    max: 100,
    step: 1,
    debounceTime: 0,
  },
  render: (props: Props & SliderProps) => {
    const ControlledComponent = () => {
      const [range, setRange] = useState({ min: 20, max: 80 });

      const handleChange = ({ min, max }: { max: number; min: number }) => {
        setRange({ min, max });
      };

      const handleAfterChange = ({ min, max }: { max: number; min: number }) => {
        console.log('User released thumb at:', min, max); // eslint-disable-line no-console
      };

      return (
        <div className='flex flex-col gap-4'>
          <div>
            Selected Range: {range.min} - {range.max}
          </div>
          <Slider {...props} minVal={range.min} maxVal={range.max} onChange={handleChange} onAfterChange={handleAfterChange} />
        </div>
      );
    };

    return <StoryWrapper {...props} StoryComponent={ControlledComponent} />;
  },
};
