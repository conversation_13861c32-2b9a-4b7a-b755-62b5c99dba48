import React from 'react';
import { Checkbox } from '@ecom-next/core/components/fabric/checkbox';
import type { FilterDrawerMultiSelectPanelType } from './types';

const FilterDrawerMultiSelectPanel: React.FC<FilterDrawerMultiSelectPanelType> = props => {
  const { inputOptions, updatePanel } = props;

  return (
    <div className='text-color-font-default mt-spacing-l gap-utk-spacing-xl flex w-full flex-col'>
      {inputOptions.map((checkbox, index) => (
        <Checkbox key={index} label={checkbox.label} checked={checkbox.checked} onChange={() => updatePanel(checkbox.label)} />
      ))}
    </div>
  );
};

export { FilterDrawerMultiSelectPanel };
