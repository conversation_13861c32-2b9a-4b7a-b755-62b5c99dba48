import { Button } from '@ecom-next/core/migration/button';
import { useState } from 'react';
import classNames from 'classnames';
import StoryWrapper, { Props } from '../../StoryWrapper';

type storyProps = {
  isCrossBrand: boolean;
};

const IsoWrapper = (props: Props & storyProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const divStyle = {
    height: '150vh',
  };
  const classes = classNames(
    {
      crossbrand: props.isCrossBrand,
    },
    'fds_isolation-layer'
  );
  return (
    <StoryWrapper
      {...props}
      StoryComponent={() => (
        <div style={divStyle}>
          <Button kind='primary' onClick={() => setIsOpen(true)}>
            Click Me
          </Button>
          {isOpen && <div className={classes} onClick={() => setIsOpen(false)} />}
        </div>
      )}
    />
  );
};
const meta = {
  title: 'Fabric DS/IsolationLayer',
  component: IsoWrapper,
  parameters: {
    controls: { expanded: true },
  },
  argTypes: {
    isCrossBrand: {
      control: { type: 'boolean' },
      options: [true, false],
      description: 'will display the crossbrand version of the isolation layer',
      defaultValue: { summary: false },
    },
  },
};

export default meta;

export const Default = {
  args: {
    brand: 'at',
    isCrossBrand: false,
  },
};
