import { ButtonIconQuickAddProps, ButtonIconQuickAdd } from '@ecom-next/core/components/fabric/button-icon-quick-add';
import StoryWrapper, { Props } from '../../../StoryWrapper';

const ButtonIconQuickAddWrapper = (props: Props & ButtonIconQuickAddProps) => (
  <div className='flex h-screen w-screen items-center justify-center'>
    <div className='flex h-fit w-fit items-center justify-center bg-cyan-50 px-[4%] py-[2%]'>
      <StoryWrapper {...props} StoryComponent={() => <ButtonIconQuickAdd {...props} />} />
    </div>
  </div>
);

const meta = {
  title: 'Fabric DS/ButtonIcons/ButtonIconQuickAdd',
  component: ButtonIconQuickAddWrapper,
  parameters: {
    controls: { expanded: true },
  },
  argTypes: {
    ariaLabel: {
      control: 'text',
      description: 'Set the aria label text for the button.',
      table: {
        defaultValue: { summary: 'Click to take action' },
      },
    },
    disabled: {
      control: { type: 'boolean' },
      options: [true, false],
      description: 'Toggle to disable the button.',
      table: {
        defaultValue: { summary: false },
      },
    },
  },
};

export default meta;

export const Default = {
  args: {
    brand: 'at',
    disabled: false,
    ariaLabel: 'Click to take action',
  },
};

export const Showcase = {
  args: {
    brand: 'at',
    isCrossBrand: false,
  },
  parameters: {
    controls: {
      exclude: ['ariaLabel', 'disabled'],
    },
  },
  render: (props: Props & ButtonIconQuickAddProps & { id?: string }) => (
    <div className='flex h-screen w-screen items-center justify-center'>
      <div className='grid gap-8 bg-cyan-50 p-8'>
        <StoryWrapper
          {...props}
          StoryComponent={() => (
            <div className='flex gap-8'>
              <div className='flex flex-col items-center'>
                <ButtonIconQuickAdd {...props} />
                <span className='mt-2 text-xs text-gray-500'>Default</span>
              </div>
              <div className='flex flex-col items-center'>
                <ButtonIconQuickAdd {...props} id='hover' className='hover' />
                <span className='mt-2 text-xs text-gray-500'>Hover</span>
              </div>
              <div className='flex flex-col items-center'>
                <ButtonIconQuickAdd {...props} id='active' className='active' />
                <span className='mt-2 text-xs text-gray-500'>Active</span>
              </div>
              <div className='flex flex-col items-center'>
                <ButtonIconQuickAdd {...props} disabled />
                <span className='mt-2 text-xs text-gray-500'>Disabled</span>
              </div>
            </div>
          )}
        />
      </div>
    </div>
  ),
};
