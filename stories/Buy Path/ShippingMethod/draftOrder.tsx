import type { DraftOrder } from '@ecom-next/checkout/contexts/types';

export const draftOrder: DraftOrder = {
  panels: {
    shippingAddressPanel: {
      shippingAddressList: [
        {
          isSelected: true,
        },
      ],
    },
    deliveryGroupPanel: {
      deliveryGroupLists: [
        // Gap Regular products
        {
          deliveryGroupId: 'GapRegularGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              itemId: 'a8348534d755447dafb4bc746297eec5',
              brand: 'ON',
              twoCharBrandCode: 'on',
              productSkuId: '7655680720001',
              productName: 'High Rise Boyfriend Joggers',
              customerChoiceNumber: '765568072',
              productTypeName: 'petite pants',
              productStyleId: '765568',
              color: 'true black',
              size: 'S',
              colorStyleNumber: '765568072',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: true,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'High Rise Boyfriend Joggers',
              primaryCategoryName: 'Pants',
              productFlags: [
                {
                  message: 'RETURN_BY_MAIL',
                  type: '',
                  date: '',
                },
              ],
              productURL: 'browse/product.do?pid=7655680720001',
              price: {
                regularPrice: 49.95,
                salePrice: 49.95,
                discountedPrice: 49.95,
                percentageOff: 0,
              },
              regularPrice: '49.95',
              salePrice: '49.95',
              discountedPrice: '49.95',
              totalPrice: '49.95',
              quantity: '1',
              imagePath: 'https://www1.assets-gap.com/webcontent/0053/515/718/cn53515718.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '000300782',
              webVendorName: 'YAKJIN TRADING CORP',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
            {
              itemId: '899452d0ae954d2fa8a505e0daca488f',
              brand: 'BR',
              twoCharBrandCode: 'br',
              productSkuId: '7288200022602',
              productName: '90s Straight Jeans',
              customerChoiceNumber: '728820002',
              productTypeName: 'petite jeans',
              productStyleId: '728820',
              color: 'true black',
              size: '26 Regular',
              colorStyleNumber: '728820002',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 5,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: false,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: "'90s Straight Jeans",
              primaryCategoryName: 'Jeans',
              productFlags: [
                {
                  message: 'EXCLUDED_FROM_PROMOTION',
                  type: '',
                  date: '',
                },
              ],
              productURL: 'browse/product.do?pid=7288200022602',
              price: {
                regularPrice: 79.95,
                salePrice: 79.95,
                discountedPrice: 79.95,
                percentageOff: 0,
              },
              regularPrice: '79.95',
              salePrice: '79.95',
              discountedPrice: '79.95',
              totalPrice: '79.95',
              quantity: '1',
              imagePath: 'https://www1.assets-gap.com/webcontent/0053/661/271/cn53661271.jpg',
              inventoryStatus: 'RESERVED',
              vendorId: '000000817',
              webVendorName: 'CRYSTAL APPAREL LTD',
              showSellerName: false,
              excludedFromPromotion: true,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
          ],
          offerDetails: "NOT A GIFT?.\nWe'll do our best to ship as quickly as possible. Our fulfillment teams would appreciate the extra time.",
          isBackOrder: false,
          isMadeToOrder: false,
          isDropShipItem: false,
          webVendorName: '',
          shippingMethodList: [
            {
              shippingMethodName: 'No Rush',
              shippingTypeDescription: '7-9 business days',
              shippingPrice: 0,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 38697,
              deliveryDate: '16th',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'May',
              maxDays: 9,
              minDays: 7,
            },
            {
              shippingMethodName: 'Basic',
              shippingTypeDescription: '5-7 business days',
              shippingPrice: 0,
              shippingTypeId: 7,
              isSelected: false,
              isEnabled: true,
              shippingId: 38704,
              deliveryDate: '14th',
              deliveryWeekDay: 'Tuesday,',
              deliveryMonth: 'May',
              maxDays: 7,
              minDays: 5,
            },
            {
              shippingMethodName: 'Standard',
              shippingTypeDescription: '3-5 business days',
              shippingPrice: 0,
              shippingTypeId: 1,
              isSelected: true,
              isEnabled: true,
              shippingId: 38702,
              deliveryDate: '10th',
              deliveryWeekDay: 'Friday,',
              deliveryMonth: 'May',
              maxDays: 5,
              minDays: 3,
            },
            {
              shippingMethodName: 'Express',
              shippingTypeDescription: '2-3 business days',
              shippingPrice: 17,
              shippingTypeId: 3,
              isSelected: false,
              isEnabled: true,
              shippingId: 38698,
              deliveryDate: '8th',
              deliveryWeekDay: 'Wednesday,',
              deliveryMonth: 'May',
              maxDays: 3,
              minDays: 2,
            },
            {
              shippingMethodName: 'Priority',
              shippingTypeDescription: '1 business day',
              shippingPrice: 25,
              shippingTypeId: 5,
              isSelected: false,
              isEnabled: true,
              shippingId: 38700,
              deliveryDate: '6th',
              deliveryWeekDay: 'Monday,',
              deliveryMonth: 'May',
              maxDays: 1,
              minDays: 1,
            },
          ],
        },
        // Dropship product
        {
          deliveryGroupId: '3000355RegularGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              itemId: 'c56747076a454131b98094e7e6a5200d',
              brand: 'GP',
              twoCharBrandCode: 'gp',
              productSkuId: '1165090020000',
              productName: 'BabyGap Goods',
              customerChoiceNumber: '116509002',
              productTypeName: 'Dropship',
              productStyleId: '116509',
              color: 'Green',
              size: 'One Size',
              colorStyleNumber: '116509002',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 0,
              isBackOrderItem: false,
              backOrdershippingDate: false,
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: true,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'Baby Gap Goods',
              productFlags: [
                {
                  message: 'MADE_TO_ORDER',
                  type: '',
                  date: '',
                },
                {
                  message: 'EXCLUDED_FROM_PROMOTION',
                  type: '',
                  date: '',
                },
                {
                  message: 'RETURN_BY_MAIL',
                  type: '',
                  date: '',
                },
              ],
              primaryCategoryName: 'Toys, Gear, & Nursery',
              productURL: 'https://www.gap.com/browse/product.do?pid=7224080020000',
              price: {
                regularPrice: 888,
                salePrice: 888,
                discountedPrice: 222,
                percentageOff: 0,
              },
              regularPrice: '888',
              salePrice: '888',
              discountedPrice: '888',
              totalPrice: '888',
              quantity: '1',
              imagePath: 'https://www1.assets-gap.com/webcontent/0054/242/403/cn54242403.jpg',
              inventoryStatus: 'IN_STOCK',
              vendorId: '3000355',
              webVendorName: 'GAP UAT TEST DEV TEAM',
              showSellerName: true,
              excludedFromPromotion: true,
              madeToOrder: true,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: 'M',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
          ],
          offerDetails: '',
          isBackOrder: false,
          isMadeToOrder: true,
          isDropShipItem: true,
          webVendorName: 'GAP UAT TEST DEV TEAM',
          shippingMethodList: [
            {
              shippingMethodName: 'Standard',
              shippingTypeDescription: '5 - 6 business days ',
              shippingPrice: 0,
              shippingTypeId: 1,
              isSelected: true,
              isEnabled: true,
              shippingId: 43760,
              deliveryDate: '23rd',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'May',
              maxDays: 6,
              minDays: 5,
            },
          ],
        },
        // Backorder products
        {
          deliveryGroupId: 'BackOrderGroup',
          pickupOrderType: 'shipItemsOnly',
          lineItemList: [
            {
              itemId: '5b966e4ba6c54d08bcaccc4514679d23',
              brand: 'AT',
              twoCharBrandCode: 'at',
              productSkuId: '6575381220002',
              productName: 'Elation Ultra High Rise 7/8 Tight',
              customerChoiceNumber: '657538122',
              productTypeName: 'womens pants',
              productStyleId: '599750',
              color: 'Blue Ombre',
              size: 'S',
              colorStyleNumber: '657538122',
              variantDescription: 'Regular',
              merchandiseType: '7',
              inventoryStatusId: 4,
              isBackOrderItem: true,
              backOrdershippingDate: {
                deliveryDate: '15th',
                deliveryWeekDay: 'Monday,',
                deliveryMonth: 'Jul',
              },
              autoAdded: false,
              noReturnItem: false,
              returnByMailItem: false,
              noIntlShipping: true,
              marketingFlag: '0',
              giftWrappable: true,
              productStyleDescription: 'Elation Ultra High Rise 7/8 Tight',
              primaryCategoryName: 'Pants',
              productFlags: [
                {
                  message: 'BACKORDER',
                  type: '',
                  date: 'Monday, July 15',
                },
                {
                  message: 'EXCLUDED_FROM_PROMOTION',
                  type: '',
                  date: '',
                },
                {
                  message: 'RETURN_BY_MAIL',
                  type: '',
                  date: '',
                },
              ],
              productURL: 'browse/product.do?pid=6575381220002',
              price: {
                regularPrice: 89,
                salePrice: 89,
                discountedPrice: 89,
                percentageOff: 0,
              },
              regularPrice: '89',
              salePrice: '89',
              discountedPrice: '89',
              totalPrice: '178',
              quantity: '2',
              // imagePath: 'webcontent/0054/729/344/cn54729344.jpg',
              imagePath: 'https://www1.assets-gap.com/webcontent/0054/729/344/cn54729344.jpg',
              inventoryStatus: 'ON_ORDER',
              vendorId: '000305922',
              webVendorName: 'ECLAT TEXTILE CO LTD',
              showSellerName: false,
              excludedFromPromotion: false,
              madeToOrder: false,
              estimatedShippingDate: '',
              eligibleReturnLocationCode: '',
              isExcludedFromRewardFreeShipping: false,
              vendorStyleNumber: '90748',
              vendorUPCCode: '',
              appliedDiscounts: [],
              storeId: '',
            },
          ],
          offerDetails: '',
          isBackOrder: true,
          isMadeToOrder: false,
          isDropShipItem: false,
          webVendorName: '',
          shippingMethodList: [
            {
              shippingMethodName: 'Basic',
              shippingTypeDescription: '5-7 business days',
              shippingPrice: 0,
              shippingTypeId: 7,
              isSelected: true,
              isEnabled: true,
              deliveryDate: '13th',
              deliveryWeekDay: 'Thursday,',
              deliveryMonth: 'September',
              shippingId: 10941,
              maxDays: 7,
              minDays: 5,
            },
          ],
        },
      ],
      summaryView: [
        {
          id: 'GapRegularGroup',
          name: 'Standard',
          itemCount: 2,
          deliveryBy: 'By Friday, May 10th',
          deliveryWeekDay: 'Friday,',
          deliveryDate: '10th',
          deliveryMonth: 'May',
          shippingPrice: 0,
        },
        {
          id: '3000355RegularGroup',
          name: 'Standard',
          itemCount: 1,
          deliveryBy: 'By Monday, May 13th',
          deliveryWeekDay: 'Monday,',
          deliveryDate: '13th',
          deliveryMonth: 'May',
          shippingPrice: 0,
        },
      ],
      currency: 'USD',
      isEasyEnrollEligible: true,
      errors: [],
      hasDropshipItems: true,
      has4101Error: false,
    },
    pickupPanel: {
      storePickupInfoList: [],
    },
  },
  draftOrderId: 'b3b3b3b3-3b3b-3b3b-3b3b-3b3b3b3b3b3b',
  orderNumber: '1234567890',
  session: {
    email: '<EMAIL>',
    recognition_status: 'authenticated'
  },
} as unknown as DraftOrder;
