[2025-07-14 10:19:25] 🚀 Starting Monday WhatsApp notification job...
[2025-07-14 10:19:25] 📅 Monday at correct time - sending message
[2025-07-14 10:19:25] ✅ Message send approved - proceeding...
[2025-07-14 10:19:25] 📍 Fetching fresh location data...
[2025-07-14 10:19:25] 📍 Not in California (San Francisco, 📍 Fetching fresh location data...
CA) - Skipping WhatsApp message
[2025-07-14 10:19:25] ℹ️  Set VIP_BYPASS_LOCATION_CHECK=true to bypass location check
[2025-07-14 10:42:37] 🚀 Starting Monday WhatsApp notification job...
[2025-07-14 10:42:37] 📅 Monday at correct time - sending message
[2025-07-14 10:42:37] ✅ Message send approved - proceeding...
[2025-07-14 10:42:37] 📍 Using cached location: San Francisco, CA
[2025-07-14 10:42:37] 📍 Not in California (San Francisco, 📍 Using cached location: San Francisco, CA
CA) - Skipping WhatsApp message
[2025-07-14 10:42:37] ℹ️  Set VIP_BYPASS_LOCATION_CHECK=true to bypass location check
[2025-07-14 10:43:50] 🚀 Starting Monday WhatsApp notification job...
[2025-07-14 10:43:50] 📅 Monday at correct time - sending message
[2025-07-14 10:43:50] ✅ Message send approved - proceeding...
[2025-07-14 10:43:50] 📍 Using cached location: San Francisco, CA
[2025-07-14 10:43:50] 📍 Location confirmed: San Francisco, CA
[2025-07-14 10:43:50] 📁 Loading environment variables from .env
[2025-07-14 10:43:51] 📦 Installing npm dependencies...

up to date, audited 396 packages in 1s

64 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[2025-07-14 10:43:52] 🔨 Source newer than dist, build needed
[2025-07-14 10:43:52] 🔨 Building TypeScript project...

> vip@1.0.0 build
> tsc

[2025-07-14 10:43:54] 🏥 Running health check...

> vip@1.0.0 dev
> node dist/index.js health

[dotenv@17.2.0] injecting env (0) from .env (tip: ⚙️  override existing env vars with { override: true })
2025-07-14T17:43:55.736Z [[32minfo[39m] Validating environment configuration... 
2025-07-14T17:43:55.737Z [[32minfo[39m] Configuration validated successfully {
  "environment": "development",
  "locationBypass": false,
  "googleDocId": "1IzhqoLq4i..."
}
📅 Loaded recurring events from config/recurring-events.json
📅 Loaded plugin: Isha Satsang - Second Saturday
📅 Loaded recurring events from config/recurring-events.json
📅 Loaded plugin: Isha Satsang - Second Saturday
2025-07-14T17:43:55.740Z [[32minfo[39m] WhatsApp service initialized {
  "module": "WhatsAppService",
  "fromNumber": "whatsapp:+14155238886",
  "toNumber": "whatsapp:+*******3866"
}
🏥 VIP Health Check

==================================================

📋 System Health:
  ✅ configuration: Configuration loaded successfully
  ✅ filesystem: Log directory is writable
  ✅ calendar_backup: Backup calendar data available
  ✅ node_version: Node.js v22.9.0
  ✅ memory: Heap: 28MB / 54MB (52%)

📱 WhatsApp Service:
2025-07-14T17:43:55.748Z [[32minfo[39m] WhatsApp service initialized {
  "module": "WhatsAppService",
  "fromNumber": "whatsapp:+14155238886",
  "toNumber": "whatsapp:+*******3866"
}
  ✅ Twilio: Using cached health status

📍 Location Service:
📍 User location: San Francisco, CA
  ✅ Location: San Francisco, CA (Not in CA)

📄 Calendar Data:
Trying to fetch from: https://docs.google.com/document/d/1IzhqoLq4i_5kMM_PsjYj9shT7UPRX8nR2uuQWQuDLuI/export?format=txt
Response status: 200
  ✅ Google Docs: Accessible (1344ms)

⏰ Scheduled Jobs:
  ✅ -	0	com.vip.monday-whatsapp
  ✅ -	0	com.vip.thursday-whatsapp

==================================================
✅ Overall Status: HEALTHY
[2025-07-14 10:43:57] ✅ Health check passed
[2025-07-14 10:43:57] 📱 Sending WhatsApp message...

> vip@1.0.0 dev
> node dist/index.js whatsapp send

[dotenv@17.2.0] injecting env (0) from .env (tip: 🔐 prevent building .env in docker: https://dotenvx.com/prebuild)
2025-07-14T17:43:58.167Z [[32minfo[39m] Validating environment configuration... 
2025-07-14T17:43:58.168Z [[32minfo[39m] Configuration validated successfully {
  "environment": "development",
  "locationBypass": false,
  "googleDocId": "1IzhqoLq4i..."
}
📅 Loaded recurring events from config/recurring-events.json
📅 Loaded plugin: Isha Satsang - Second Saturday
📅 Loaded recurring events from config/recurring-events.json
📅 Loaded plugin: Isha Satsang - Second Saturday
2025-07-14T17:43:58.169Z [[32minfo[39m] WhatsApp service initialized {
  "module": "WhatsAppService",
  "fromNumber": "whatsapp:+14155238886",
  "toNumber": "whatsapp:+*******3866"
}
📍 Checking user location...
📍 User location: San Francisco, CA
📍 User is not in California (San Francisco, CA). Skipping weekly update.
[2025-07-14 10:43:58] ✅ SUCCESS: WhatsApp message sent successfully
[2025-07-14 10:43:58] 📝 Recorded successful run at Mon Jul 14 10:43:50 PDT 2025
[2025-07-14 10:43:58] 🎉 Monday WhatsApp notification job completed successfully
[2025-07-15 09:00:05] 🚀 Starting Monday WhatsApp notification job...
[2025-07-15 09:00:05] ℹ️  Not Monday (day 2), last run was 0 days ago
[2025-07-15 09:00:05] 🚫 Skipping message send based on timing logic
